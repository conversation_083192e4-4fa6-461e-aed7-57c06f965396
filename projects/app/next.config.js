/** @type {import('next').NextConfig} */
const { i18n } = require('./next-i18next.config');
const path = require('path');

const nextConfig = {
  i18n,
  output: 'standalone',
  basePath: '/evaluate', // 对于第一个应用

  reactStrictMode: process.env.NODE_ENV === 'development' ? false : true,
  compress: true,
  rewrites:
    process.env.NODE_ENV === 'development'
      ? async () => {
          return [
            {
              source: '/huayun-ai/:path*',
              destination: `${process.env.API_URL}/huayun-ai/:path*`,
              basePath: false
            },
            {
              source: '/huayun-evaluation/:path*',
              destination: `${process.env.API_EVALUATE}/huayun-evaluation/:path*`,
              basePath: false
            },
            {
              source: '/huayun-exam/:path*',
              destination: `${process.env.API_EVALUATE}/huayun-exam/:path*`,
              basePath: false
            },
            {
              source: '/huayun-tool/:path*',
              destination: `${process.env.PANDOC_URL}/huayun-tool/:path*`,
              basePath: false
            }
          ];
        }
      : undefined,
  webpack(config, { isServer }) {
    if (!isServer) {
      config.resolve = {
        ...config.resolve,
        fallback: {
          ...config.resolve.fallback,
          fs: false
        }
      };
    }
    Object.assign(config.resolve.alias, {
      '@mongodb-js/zstd': false,
      '@aws-sdk/credential-providers': false,
      snappy: false,
      aws4: false,
      'mongodb-client-encryption': false,
      kerberos: false,
      'supports-color': false,
      'bson-ext': false,
      'pg-native': false
    });
    config.module = {
      ...config.module,
      rules: config.module.rules.concat([
        {
          test: /\.svg$/i,
          issuer: /\.[jt]sx?$/,
          use: ['@svgr/webpack']
        }
      ]),
      exprContextCritical: false,
      unknownContextCritical: false
    };

    return config;
  },
  transpilePackages: ['@fastgpt/*', 'ahooks'],
  experimental: {
    serverComponentsExternalPackages: [
      'mongoose',
      'pg',
      'react',
      '@chakra-ui/react',
      '@lexical/react'
    ],
    outputFileTracingRoot: path.join(__dirname, '../../')
  }
};

module.exports = nextConfig;
