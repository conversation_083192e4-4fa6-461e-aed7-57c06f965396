{"name": "app", "version": "1.0.0", "private": false, "scripts": {"dev": "next dev -p 8083", "build": "next build", "start": "next start", "lint": "next lint", "check": "npx tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.3.4", "@chakra-ui/anatomy": "^2.2.1", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.1.5", "@chakra-ui/react": "^2.8.1", "@chakra-ui/styled-system": "^2.9.1", "@chakra-ui/system": "^2.6.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortaine/fetch-event-source": "^3.0.6", "@lexical/react": "0.12.6", "@lexical/selection": "^0.17.0", "@lexical/text": "0.12.6", "@lexical/utils": "0.12.6", "@monaco-editor/react": "^4.6.0", "@tanstack/react-query": "^4.24.10", "ahooks": "3.7.11", "antd": "5.13.3", "antd-style": "^3.7.1", "axios": "^1.7.2", "browser-image-compression": "^2.0.2", "click-to-react-component": "^1.1.2", "cron-parser": "^4.9.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "element-resize-detector": "^1.2.4", "formidable": "^2.1.1", "framer-motion": "^9.1.7", "html2canvas": "^1.4.1", "hyperdown": "^2.4.29", "i18next": "^22.5.1", "immer": "^9.0.19", "jschardet": "^3.0.0", "jsonwebtoken": "^9.0.2", "lexical": "0.12.6", "lodash": "^4.17.21", "markmap-lib": "^0.17.0", "markmap-view": "^0.17.0", "mermaid": "^10.2.3", "mobile-detect": "^1.4.5", "nanoid": "^4.0.1", "next": "13.5.2", "next-i18next": "^13.3.0", "nprogress": "^0.2.0", "openai": "4.28.0", "papaparse": "^5.4.1", "qrcode": "^1.5.3", "react": "18.2.0", "react-day-picker": "^8.7.1", "react-dom": "18.2.0", "react-hook-form": "^7.43.1", "react-i18next": "^12.3.1", "react-lottie": "^1.2.4", "react-markdown": "^8.0.7", "react-stack-grid": "^0.7.1", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.7.4", "rehype-external-links": "^3.0.0", "rehype-katex": "^6.0.2", "rehype-raw": "^5.1.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^3.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "sanitize-html": "^2.13.0", "sass": "^1.58.3", "slick-carousel": "^1.8.1", "timezones-list": "^3.0.3", "use-context-selector": "^2.0.0", "xml2js": "^0.6.2", "zustand": "^4.3.5"}, "devDependencies": {"@svgr/webpack": "^6.5.1", "@types/crypto-js": "^4.2.2", "@types/element-resize-detector": "^1.1.6", "@types/formidable": "^2.0.5", "@types/js-cookie": "^3.0.3", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.191", "@types/node": "^20.8.5", "@types/nprogress": "^0.2.0", "@types/papaparse": "^5.3.14", "@types/qrcode": "^1.5.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-lottie": "^1.2.10", "@types/react-stack-grid": "^0.7.7", "@types/react-syntax-highlighter": "^15.5.6", "@types/request-ip": "^0.0.37", "@types/xml2js": "^0.4.14", "eslint": "8.34.0", "eslint-config-next": "13.1.6", "typescript": "5.5.3"}}