{"App": "应用", "Create New": "", "Export": "导出", "Folder": "文件夹", "Move": "移动", "Name": "名称", "Rename": "重命名", "Running": "运行中", "Select value is empty": "选择的内容为空", "UnKnow": "未知", "Warning": "提示", "Import": "导入", "app": {"AI Advanced Settings": "AI 高级配置", "AI Settings": "AI 配置", "Advance App TestTip": "当前应用可能为高级编排模式\n如需切换为【简易模式】请点击左侧保存按键", "App Detail": "应用详情", "Basic Settings": "基本信息", "Chat Debug": "调试预览", "Chat Logs Tips": "日志会记录该应用的在线、分享和 API(需填写 chatId) 对话记录", "Chat logs": "对话日志", "Confirm Del App Tip": "确认删除该应用及其所有聊天记录？", "Connection is invalid": "连接无效", "Connection type is different": "连接的类型不一致", "Copy Module Config": "复制配置", "Dataset Quote Template": "知识库问答模式", "Export Config Successful": "已复制配置，请注意检查是否有重要数据", "Export Configs": "导出配置", "Feedback Count": "用户反馈", "Import Configs": "导入配置", "Import Configs Failed": "导入配置失败，请确保配置正常！", "Input Field Settings": "输入字段编辑", "Logs Empty": "还没有日志噢~", "Logs Message Total": "消息总数", "Logs Source": "来源", "Logs Time": "时间", "Logs Title": "标题", "Mark Count": "标注答案数量", "My Apps": "我的应用", "My AI Copilot": "AI 助手", "Open AI Advanced Settings": "高级配置", "Output Field Settings": "输出字段编辑", "Paste Config": "粘贴配置", "To Chat": "前去对话", "To Settings": "查看详情", "Variable Key Repeat Tip": "变量 key 重复", "module": {"Combine Modules": "组合模块", "Custom Title Tip": "该标题名字会展示在对话过程中", "My Modules": "", "No Modules": "还没有模块~", "System Module": "系统模块", "type": "\"{{type}}\"类型\n{{description}}"}, "modules": {"Title is required": "模块名不能为空"}}, "common": {"Add": "添加", "Add New": "新增", "All": "全部", "Back": "返回", "Beta": "实验版", "Business edition features": "无权限切换团队~", "Choose": "选择", "Close": "关闭", "Collect": "收藏", "Config": "配置", "Confirm": "确认", "Cancel": "取消", "Confirm Create": "确认编辑", "Confirm Import": "确认导入", "Confirm Move": "移动到这", "Confirm Update": "确认更新", "Copy": "复制", "Copy Successful": "复制成功", "Course": "", "Create Failed": "创建异常", "Create New": "新建", "Create Success": "创建成功", "Create Time": "创建时间", "Custom Title": "自定义标题", "Delete": "删除", "Delete Failed": "删除失败", "Delete Success": "删除成功", "Delete Tip": "删除提示", "Delete Warning": "删除警告", "Done": "完成", "Edit": "编辑", "More": "更多", "Exit": "退出", "Expired Time": "过期时间", "File": "文件", "Filed is repeat": "", "Filed is repeated": "字段重复了", "Finish": "完成", "Verify": "确认", "Input": "输入", "Intro": "介绍", "Invalid Json": "无效的JSON格式，请注意检查。", "Last Step": "上一步", "Last use time": "最后使用时间", "Load Failed": "加载失败", "Loading": "加载中...", "Max credit": "最大金额", "Max credit tips": "该链接最大可消耗多少金额，超出后链接将被禁止使用。-1 代表无限制。", "More settings": "更多设置", "Name": "名称", "Name Can": "名称不能为空", "Name is empty": "名称不能为空", "New Create": "新建", "New Create AI Copilot": "新建应用", "Next Step": "下一步", "Not open": "未开启", "Number of words": "{{amount}}字", "OK": "好的", "Opened": "已开启", "Output": "输出", "Params": "参数", "Password inconsistency": "两次密码不一致", "Please Input Name": "请输入名称", "Price used": "金额消耗", "Read document": "查看文档", "Read intro": "查看说明", "Rename": "重命名", "Rename Failed": "重命名失败", "Rename Success": "重命名成功", "Request Error": "请求异常", "Require Input": "必填", "Root folder": "根目录", "Save": "保存", "Save Failed": "保存失败", "Save Success": "保存成功", "Search": "搜索", "Select File Failed": "选择文件异常", "Select One Folder": "选择一个目录", "Select template": "选择模板", "Set Avatar": "点击设置头像", "Set Name": "取个名字", "Setting": "设置", "Status": "状态", "Submit failed": "提交失败", "Submit success": "提交成功", "Team": "团队", "Test": "测试", "Time": "时间", "Un used": "未使用", "UnKnow": "未知", "UnKnow Source": "未知来源", "Unlimited": "无限制", "Update Failed": "更新异常", "Update Success": "更新成功", "Update Successful": "更新成功", "Update Time": "更新时间", "Update success": "更新成功", "Upload File Failed": "上传文件失败", "Username": "用户名", "Waiting": "等待中", "Website": "网站", "avatar": {"Select Avatar": "点击选择头像", "Select Failed": "选择头像异常"}, "change": "变更", "choosable": "可选", "confirm": {"Common Tip": "操作确认"}, "course": {"Read Course": "查看教程"}, "empty": {"Common Tip": "没有什么数据噢~"}, "error": {"Select avatar failed": "头像选择异常", "Update error": "更新失败", "unKnow": "出现了点意外~"}, "export": "", "file": {"Empty file tip": "文件内容为空，可能该文件无法读取或为纯图片文件内容。", "File Content": "文件内容", "File Name": "文件名", "File content can not be empty": "文件内容不能为空", "Filename Can not Be Empty": "文件名不能为空", "Read File Error": "解析文件失败", "File is empty or not text file": "文件为空或者不是文本文件", "Select and drag file tip": "点击或拖动文件到此处上传", "Select failed": "选择文件异常", "Select file amount limit": "最多选择 {{max}} 个文件", "Select file amount limit 100": "每次最多选择100个文件", "Some file size exceeds limit": "部分文件超出: {{maxSize}}，已被过滤", "Support file type": "支持 {{fileType}} 类型文件", "Support max count": "最多支持 {{maxCount}} 个文件。", "Support max size": "单个文件最大 {{maxSize}}。", "Upload failed": "上传异常"}, "folder": {"Drag Tip": "点我可拖动", "Move Success": "移动成功", "No Folder": "没有子目录了，就放这里吧", "Root Path": "根目录", "empty": "这个目录已经没东西可选了~"}, "input": {"Repeat Value": "有重复的值"}, "jsonEditor": {"Parse error": "Json可能有误，请仔细检查"}, "link": {"UnValid": "无效的链接"}, "month": "月", "price": {"Amount": "{{amount}}{{unit}}"}, "speech": {"error tip": "语音转文字失败"}, "system": {"Help Chatbot": "机器人助手", "Use Helper": "使用帮助"}, "time": {"Just now": "刚刚", "The day before yesterday": "前天", "Yesterday": "昨天"}, "ui": {"textarea": {"Magnifying": "放大"}}}, "core": {"Chat": "对话", "Chat test": "测试对话", "Max Token": "单条数据上限", "Start chat": "立即对话", "Total chars": "总字数: {{total}}", "Total tokens": "总 Tokens: {{total}}", "ai": {"Model": "AI 模型", "Prompt": "提示词", "model": {"Dataset Agent Model": "文件处理模型", "Vector Model": "索引模型"}}, "app": {"Ai response": "返回AI内容", "Api request": "API 访问", "Api request desc": "通过 API 接入到已有系统中，或企微、飞书等", "App intro": "应用介绍", "App params config": "应用配置", "Chat Variable": "对话框变量", "External using": "外部使用途径", "Make a brief introduction of your app": "给你的 AI 应用一个介绍", "Max tokens": "回复上限", "Name and avatar": "头像 & 名称", "Next Step Guide": "下一步指引", "Question Guide": "问题引导", "Question Guide Tip": "对话结束后，会为生成 3 个引导性问题。", "Quote prompt": "引用模板提示词", "Quote templates": "引用内容模板", "Random": "发散", "Save and preview": "保存并预览", "Select TTS": "选择语音播放模式", "Select app from template": "从模板中选择", "Select quote template": "选择引用提示模板", "Set a name for your app": "给应用设置一个名称", "Share link": "免登录窗口", "Share link desc": "分享链接给其他用户，无需登录即可直接进行使用", "Share link desc detail": "可以直接分享该模型给其他用户去进行对话，对方无需登录即可直接进行对话。注意，这个功能会消耗你账号的余额，请保管好链接！", "Simple Config Tip": "仅包含基础功能，复杂 agent 功能请使用高级编排。", "TTS": "语音播报", "TTS Tip": "开启后，每次对话后可使用语音播放功能。使用该功能可能产生额外费用。", "Temperature": "温度", "Welcome Text": "对话开场白", "create app": "创建属于你的应用", "Create AI Copilot": "创建属于你的应用", "deterministic": "严谨", "edit": {"Confirm Save App Tip": "该应用可能为高级编排模式，保存后将会覆盖高级编排配置，请确认！", "Out Ad Edit": "您即将退出高级编排页面，请确认", "Prompt Editor": "提示词编辑", "Query extension background prompt": "对话背景描述", "Query extension background tip": "描述当前对话的范围，便于AI为当前问题进行补全和扩展。填写的内容，通常为该助手", "Save and out": "保存并退出", "UnSave": "不保存"}, "error": {"App name can not be empty": "应用名不能为空", "Get app failed": "获取应用异常"}, "feedback": {"Custom feedback": "自定义反馈", "close custom feedback": "关闭反馈"}, "logs": {"Source And Time": "来源 & 时间"}, "navbar": {"External": "外部使用", "Flow mode": "高级编排", "Publish": "发布", "Publish app": "发布应用", "Simple mode": "简易配置"}, "outLink": {"Can Drag": "图标可拖拽", "Default open": "默认打开", "Iframe block title": "复制下面 Iframe 加入到你的网站中", "Link block title": "将下面链接复制到浏览器打开", "Script Close Icon": "关闭图标", "Script Icon": "图标", "Script Open Icon": "打开图标", "Script block title": "将下面代码加入到你的网站中", "Select Mode": "开始使用", "Select Using Way": "选择使用方式", "Show History": "展示历史对话", "Web Link": "网络链接"}, "setting": "应用信息设置", "Scene Type": "所属场景", "position": "首页位置", "positionType": {"sidebar": "侧边栏", "rightTop": "右上角", "none": "不显示"}, "share": {"Amount limit tip": "最多创建10组", "Create link": "创建新链接", "Create link tip": "创建成功。已复制分享地址，可直接分享使用", "Ip limit title": "IP限流（人/分钟）", "Is response quote": "返回引用", "Not share link": "没有创建分享链接", "Role check": "身份校验"}, "simple": {"mode template select": "简易模板"}, "template": {"Classify and dataset": "问题分类 + 知识库", "Classify and dataset desc": "先对用户的问题进行分类，再根据不同类型问题，执行不同的操作", "Common template": "通用模板", "Common template tip": "通用模板\n可完全自行配置AI属性和知识库", "Dataset and guide": "知识库 + 对话引导", "Dataset and guide desc": "每次提问时进行一次知识库搜索，将搜索结果注入 LLM 模型进行参考回答", "Guide and variables": "对话引导 + 变量", "Guide and variables desc": "可以在对话开始发送一段提示，或者让用户填写一些内容，作为本次对话的变量", "Simple chat": "简单的对话", "Simple chat desc": "一个极其简单的 AI 对话应用", "Simple template": "简易模板", "Simple template tip": "极简模板\n已内置参数细节"}, "tip": {"Add a intro to app": "快来给应用一个介绍~", "chatNodeSystemPromptTip": "使用自然语言描述应用的设定、回复逻辑、可完成的任务和完成任务顺序(工作流)。 \n如果关联了知识库，你还可以通过适当的描述，来引导模型何时去调用知识库搜索。 \n例如:我是一名专业的中国小学初中教学设计专家，我致力于根据2022年最新课程标准，为教师提供全面、创新且实用的教学设计方案。请搜索知识库并结合搜素结果进行回答。", "userGuideTip": "可以在对话前设置引导语，设置全局变量，设置下一步指引", "variableTip": "可以在对话开始前，要求用户填写一些内容作为本轮对话的特定变量。该模块位于开场引导之后。\n变量可以通过 {{变量key}} 的形式注入到其他模块 string 类型的输入中，例如：提示词、限定词等", "welcomeTextTip": "每次对话开始前，发送一个初始内容。支持标准 Markdown 语法，可使用的额外标记:\n[快捷按键]: 用户点击后可以直接发送该问题"}, "tts": {"Close": "不使用", "Model alloy": "女声 - <PERSON><PERSON>", "Model echo": "男声 - Echo", "Speech model": "语音模型", "Speech speed": "语速", "Test Listen": "试听", "Test Listen Text": "你好，这是语音测试，如果你能听到这句话，说明语音播放功能正常", "Web": "浏览器自带"}}, "chat": {"Admin Mark Content": "纠正后的回复", "Audio Speech Error": "语音播报异常", "Chat API is error or undefined": "对话接口报错或返回为空", "Confirm to clear history": "确认清空该应用的在线聊天记录？分享和 API 调用的记录不会被清空。", "Confirm to clear share chat history": "确认删除所有聊天记录？", "Converting to text": "正在转换为文本...", "Custom History Title": "自定义历史记录标题", "Custom History Title Description": "如果设置为空，会自动跟随聊天记录。", "Exit Chat": "退出聊天", "Failed to initialize chat": "初始化聊天失败", "Feedback Failed": "提交反馈异常", "Feedback Mark": "标注", "Feedback Modal": "结果反馈", "Feedback Modal Tip": "输入你觉得回答不满意的地方", "Feedback Submit": "提交反馈", "Feedback Success": "反馈成功!", "Feedback Update Failed": "更新反馈状态失败", "History": "记录", "History Amount": "{{amount}}条记录", "Mark": "标注预期回答", "Mark Description": "当前标注功能为测试版。\n\n点击添加标注后，需要选择一个知识库，以便存储标注数据。你可以通过该功能快速的标注问题和预期回答，以便引导模型下次的回答。\n\n目前，标注功能同知识库其他数据一样，受模型的影响，不代表标注后 100% 符合预期。\n\n标注数据仅单向与知识库同步，如果知识库修改了该标注数据，日志展示的标注数据无法同步", "Mark Description Title": "标注功能介绍", "New Chat": "新对话", "Pin": "置顶", "Question Guide Tips": "猜你想问", "Quote": "引用", "Quote Amount": "知识库引用({{amount}}条)", "Read Mark Description": "查看标注功能介绍", "Record": "语音输入", "Restart": "重开对话", "Select File": "选择文件", "Select Image": "选择图片", "Select dataset": "选择知识库", "Select dataset Desc": "选择一个知识库存储预期答案", "Send Message": "发送", "Speaking": "我在听，请说...", "Start Chat": "开始对话", "Stop Speak": "停止录音", "Type a message": "输入问题", "Unpin": "取消置顶", "You need to a chat app": "你需要创建一个应用", "error": {"Chat error": "对话出现异常", "Messages empty": "接口内容为空，可能文本超长了~", "Select dataset empty": "你没有选择知识库", "User input empty": "传入的用户问题为空"}, "feedback": {"Close User Good Feedback": "", "Close User Like": "用户表示赞同\n点击关闭该标记", "Feedback Close": "关闭反馈", "No Content": "用户没有填写具体反馈内容", "Read User dislike": "用户表示反对\n点击查看内容"}, "logs": {"api": "API 调用", "online": "在线使用", "share": "外部链接调用", "test": "测试"}, "markdown": {"Edit Question": "编辑问题", "Quick Question": "点我立即提问", "Send Question": "发送问题"}, "quote": {"Quote Tip": "此处仅显示实际引用内容，若数据有更新，此处不会实时更新", "Read Quote": "查看引用", "Read Source": "查看来源"}, "response": {"Complete Response": "完整响应", "Extension model": "问题补全模型", "Plugin Resonse Detail": "插件详情", "Read complete response": "查看详情", "Read complete response tips": "点击查看详细流程", "context total length": "上下文总长度", "module cq": "问题分类列表", "module cq result": "分类结果", "module extract description": "提取要求描述", "module extract result": "提取结果", "module historyPreview": "完整记录", "module http body": "请求体", "module http result": "响应体", "module http url": "请求地址", "module limit": "单次搜索上限", "module maxToken": "最大响应 Tokens", "module model": "模型", "module name": "模型名", "module price": "计费", "module query": "问题/检索词", "module question": "问题", "module quoteList": "引用内容", "module runningTime": "运行时长", "module search query": "检索词", "module search response": "搜索结果", "module similarity": "相似度", "module temperature": "温度", "module time": "运行时长", "module tokens": "Tokens", "plugin output": "插件输出值", "search using reRank": "结果重排", "text output": "文本输出"}, "retry": "重新生成", "tts": {"Stop Speech": "停止"}}, "common": {"tip": {"leave page": "内容已修改，确认离开页面吗？"}}, "dataset": {"All Dataset": "全部知识库", "Avatar": "知识库徽标", "Select Avatar": "选择知识库徽标", "Choose Dataset": "关联知识库", "Chunk amount": "分段数", "Collection": "数据集", "Common Dataset": "通用知识库", "Common Dataset Desc": "可通过导入文件、网页链接或手动录入形式构建知识库", "Create dataset": "新建知识库", "Dataset": "知识库", "Dataset ID": "知识库 ID", "Dataset Type": "知识库类型", "Delete Confirm": "确认删除该知识库？删除后数据无法恢复，请确认！", "Delete Website Tips": "确认删除该站点？", "Empty Dataset": "", "Empty Dataset Tips": "还没有知识库，快去创建一个吧！", "File collection": "文件数据集", "Folder Dataset": "文件夹", "Folder placeholder": "这是一个目录", "Go Dataset": "前往知识库", "Intro Placeholder": "这个知识库还没有介绍~", "Manual collection": "手动数据集", "My Dataset": "我的知识库", "Name": "知识库名称", "Quote Length": "引用内容长度", "Read Dataset": "查看知识库详情", "Search score tip": "{{scoreText}}下面是详细排名和得分情况:\n----\n{{detailScore}}", "Select dataset": "选择知识库", "Set Empty Result Tip": ",未搜索到内容时回复指定内容", "Set Website Config": "开始配置网站信息", "Similarity": "相关度", "Sync Time": "最后更新时间", "Table collection": "表格数据集", "Text collection": "文本数据集", "Total chunks": "总分段: {{total}}", "Website Dataset": "Web 站点同步", "Website Dataset Desc": "Web 站点同步允许你直接使用一个网页链接构建知识库", "collection": {"Click top config website": "点击配置网站", "Collection name": "数据集名称", "Collection raw text": "数据集内容", "Empty Tip": "数据集空空如也", "QA Prompt": "QA 拆分引导词", "Start Sync Tip": "确认开始同步数据？将会删除旧数据后重新获取，请确认！", "Sync": "同步数据", "Sync Collection": "数据同步", "Website Create Success": "创建成功，正在同步数据", "Website Empty Tip": "还没有关联网站", "Website Link": "Web 站点地址", "Website Sync": "Web 站点同步", "id": "集合ID", "metadata": {"Chunk Size": "分割大小", "Createtime": "创建时间", "Raw text length": "原文长度", "Read Metadata": "查看元数据", "Training Type": "训练模式", "Updatetime": "更新时间", "Web page selector": "网站选择器", "metadata": "元数据", "read source": "查看原始内容", "source": "数据来源", "source name": "来源名", "source size": "来源大小"}, "status": {"active": "已就绪", "syncing": "同步中"}, "sync": {"result": {"sameRaw": "内容未变动，无需更新", "success": "开始同步"}}, "training": {}}, "data": {"Auxiliary Data": "辅助数据", "Auxiliary Data Placeholder": "该部分为可选填项, 通常是为了与前面的【数据内容】配合，构建结构化提示词，用于特殊场景，最多 {{maxToken}} 字。", "Auxiliary Data Tip": "该部分为可选填项\n该内容通常是为了与前面的数据内容配合，构建结构化提示词，用于特殊场景", "Data Content": "相关数据内容", "Data Content Placeholder": "该输入框是必填项，该内容通常是对于知识点的描述，也可以是用户的问题，最多 {{maxToken}} 字。", "Data Content Tip": "该输入框是必填项\n该内容通常是对于知识点的描述，也可以是用户的问题。", "Default Index Tip": "无法编辑，默认索引会使用【相关数据内容】与【辅助数据】的文本直接生成索引，如不需要默认索引，可删除。 每条数据必须保证有一个以上索引，所有索引被删除后，会自动生成默认索引。", "Edit": "编辑数据", "Empty Tip": "这个集合还没有数据~", "Main Content": "主要内容", "Search data placeholder": "搜索相关数据", "Too Long": "总长度超长了", "Total Amount": "{{total}} 组", "data is deleted": "该数据已被删除", "get data error": "获取数据异常", "id": "数据ID", "unit": "条"}, "embedding model tip": "索引模型可以将自然语言转成向量，用于进行语义检索。\n注意，不同索引模型无法一起使用，选择完索引模型后将无法修改。", "error": {"Data not found": "数据不存在或已被删除", "Start Sync Failed": "开始同步失败", "Template does not exist": "模板不存在", "unAuthDataset": "无权操作该知识库", "unAuthDatasetCollection": "无权操作该数据集", "unAuthDatasetData": "无权操作该数据", "unAuthDatasetFile": "无权操作该文件", "unCreateCollection": "无权操作该数据", "unLinkCollection": "不是网络链接集合"}, "file": "文件", "folder": "目录", "import": {"Auto process": "自动", "Auto process desc": "自动设置分割和预处理规则", "CSV Import": "CSV 导入", "CSV Import Tip": "通过批量导入问答对，要求提前整理好数据", "Chunk Range": "范围: {{min}}~{{max}}", "Chunk Split": "直接分段", "Chunk Split Tip": "将文本按一定的规则进行分段处理后，转成可进行语义搜索的格式，适合绝大多数场景。", "Chunk length": "分块总量", "Csv format error": "csv 文件格式有误,请确保 index 和 content 两列", "Custom file": "自定义文本", "Custom process": "自定义规则", "Custom process desc": "自定义设置分制和预处理规则", "Custom prompt": "自定义提示词", "Custom split char": "自定义分隔符", "Custom split char Tips": "允许你根据自定义的分隔符进行分块。通常用于已处理好的数据，使用特定的分隔符来精确分块。", "Custom text": "自定义文本", "Custom text desc": "手动输入一段文本作为数据集", "Data Preprocessing": "数据处理", "Data file progress": "数据上传进度", "Data process params": "数据处理参数", "Down load csv template": "点击下载 CSV 模板", "Embedding Estimated Price Tips": "索引计费: {{price}}/1k字符", "Estimated Price": "预估价格: {{amount}}{{unit}}", "Estimated Price Tips": "QA计费为\n输入: {{inputPrice}}/1k tokens\n输出: {{outputPrice}}/1k tokens", "Fetch Error": "获取链接失败", "Fetch Url": "网络链接", "Fetch url placeholder": "最多10个链接，每行一个。", "Fetch url tip": "仅支持读取静态链接，请注意检查结果", "File chunk amount": "分段: {{amount}}", "File list": "文件列表", "Ideal chunk length": "理想分块长度", "Ideal chunk length Tips": "按结束符号进行分段。我们建议您的文档应合理的使用标点符号，以确保每个完整的句子长度不要超过该值\n中文文档建议400~1000\n英文文档建议600~1200", "Import Failed": "导入文件失败", "Import Success Tip": "共成功导入 {{num}} 组数据，请耐心等待训练.", "Import Tip": "该任务无法终止，需要一定时间生成索引，请确认导入。如果余额不足，未完成的任务会被暂停，充值后可继续进行。", "Link name": "网络链接", "Link name placeholder": "仅支持静态链接，如果上传后数据为空，可能该链接无法被读取\n每行一个，每次最多 10 个链接", "Local file": "本地文件", "Local file desc": "上传 PDF, TXT, DOCX 等格式的文件", "Only Show First 50 Chunk": "仅展示部分", "Preview chunks": "分段预览", "Preview raw text": "预览源文本（最多展示10000字）", "Process way": "处理方式", "QA Estimated Price Tips": "QA计费为: {{price}}元/1k 字符(包含输入和输出)", "QA Import": "QA拆分", "QA Import Tip": "根据一定规则，将文本拆成一段较大的段落，调用 AI 为该段落生成问答对。", "Re Preview": "重新生成预览", "Select file": "选择文件", "Select source": "选择来源", "Set Chunk Error": "文本分段异常", "Source name": "来源名", "Sources list": "来源列表", "Start upload": "开始上传", "Total Chunk Preview": "分段预览({{totalChunks}}组)", "Total files": "共 {{total}} 个文件", "Total tokens": "总Tokens", "Training mode": "训练模式", "Upload data": "上传数据", "Upload file progress": "文件上传进度", "Upload status": "上传状态", "Upload success": "上传成功", "Web link": "网页链接", "Web link desc": "读取静态网页内容作为数据集"}, "link": "链接", "search": {"Basic params": "基础参数", "Dataset Search Params": "知识库搜索配置", "Embedding score": "语意检索得分", "Empty result response": "空搜索回复", "Empty result response Tips": "若填写该内容，没有搜索到合适内容时，将直接回复填写的内容。", "Filter": "搜索过滤", "Limit": "", "Max Tokens": "引用上限", "Max Tokens Tips": "单次搜索最大的 Tokens 数量，中文约1字=1.7Tokens，英文约1字=1Tokens", "Min Similarity": "最低相关度", "Min Similarity Tips": "不同索引模型的相关度有区别，请通过搜索测试来选择合适的数值，使用 ReRank 时，相关度可能会很低。", "Nonsupport": "不支持", "Not similarity": "", "Params Setting": "搜索参数设置", "Quote index": "第几个引用", "Rank": "排名", "Rank Tip": "在所有数据中的排名", "ReRank": "结果重排", "ReRank desc": "使用重排模型来进行二次排序，可增强综合排名。", "Read score": "查看得分", "Rerank score": "结果重排得分", "Score": "得分", "Search type": "类型", "Source id": "来源ID", "Source name": "引用来源名", "Top K": "单次搜索上限", "Using cfr": "", "Using query extension": "使用问题补全", "mode": {"embedding": "语义检索", "embedding desc": "使用向量进行文本相关性查询", "fullTextRecall": "全文检索", "fullTextRecall desc": "使用传统的全文检索，适合查找一些关键词和主谓语特殊的数据", "mixedRecall": "混合检索", "mixedRecall desc": "使用向量检索与全文检索的综合结果返回，使用RRF算法进行排序。"}, "score": {"embedding": "语义检索", "embedding desc": "通过计算向量之间的距离获取得分，范围为 0~1。", "fullText": "全文检索", "fullText desc": "计算相同关键词的得分，范围为 0~无穷。", "fullTextRecall": "", "fullTextRecall desc": "", "mixedRecall": "", "mixedRecall desc": "", "reRank": "结果重排", "reRank desc": "通过 ReRank 模型计算句子之间的关联度，范围为 0~1。", "rrf": "综合排名", "rrf desc": "通过倒排计算的方式，合并多个检索结果。"}, "search mode": "搜索模式"}, "settings": {"Search basic params": "检索参数"}, "status": {"active": "已就绪", "syncing": "同步中"}, "test": {"Batch test": "批量测试", "Batch test Placeholder": "选择一个 Csv 文件", "Search Test": "搜索测试", "Test": "测试", "Test File": "批量测试", "Test Result": "测试结果", "Test Text": "单个文本测试", "Test Text Placeholder": "输入需要测试的文本", "Test params": "测试参数", "delete test history": "删除该测试结果", "test history": "测试历史", "test result placeholder": "测试结果将在这里展示", "test result tip": "根据知识库内容与测试文本的相似度进行排序，你可以根据测试结果调整对应的文本。\n注意：测试记录中的数据可能已经被修改过，点击某条测试数据后将展示最新的数据。"}, "training": {"Agent queue": "QA训练排队", "Chunk mode": "直接分段", "Full": "预计5分钟以上", "Leisure": "空闲", "Manual": "", "Manual mode": "手动导入", "QA mode": "问答拆分", "Vector queue": "索引排队", "Waiting": "预计5分钟", "Website Sync": "Web 站点同步"}, "website": {"Base Url": "根地址", "Config": "Web站点配置", "Config Description": "Web 站点同步功能允许你填写一个网站的根地址，系统会自动深度抓取相关的网页进行知识库训练。仅会抓取静态的网站，以项目文档、博客为主。", "Confirm Create Tips": "确认同步该站点，同步任务将随后开启，请确认！", "Confirm Update Tips": "确认更新站点配置？会立即按新的配置开始同步，请确认！", "Selector": "选择器", "Selector Course": "使用教程", "Start Sync": "开始同步", "UnValid Website Tip": "您的站点可能非静态站点，无法同步"}}, "module": {"Add question type": "添加问题类型", "Can not connect self": "不能连接自身", "Data Type": "数据类型", "Dataset quote": {"Add quote": "添加引用", "Concat result": "合并结果", "Input description": "可接收知识库搜索的结果。", "label": "知识库引用"}, "Field Description": "字段描述", "Field Name": "字段名", "Field Type": "字段类型", "Field key": "字段 Key", "Http request props": "请求参数", "Http request settings": "请求配置", "Http request timeout": "超时设置", "Input Type": "输入类型", "Plugin output must connect": "自定义输出必须全部连接", "QueryExtension": {"placeholder": "例如:\n关于 python 的介绍和使用等问题。\n当前对话与游戏《GTA5》有关。", "tip": "描述当前对话的范围，便于AI补全首次问题或模糊的问题，从而增强知识库连续对话的能力。建议开启该功能后，都简单的描述在对话的背景，否则容易造成补全对象不准确。"}, "Unlink tip": "【{{name}}】存在未填或未连接参数", "Variable": "全局变量", "Variable Setting": "变量设置", "edit": {"Field Already Exist": "key 重复", "Field Edit": "字段编辑"}, "extract": {"Enum Description": "列举出该字段可能的值，每行一个", "Enum Value": "枚举值", "Field Description Placeholder": "姓名/年龄/sql语句……", "Field Setting Title": "提取字段配置"}, "http": {"Add props": "添加参数", "AppId": "应用的ID", "ChatId": "当前对话ID", "Current time": "当前时间", "Histories": "历史纪录,最多取10条", "Key already exists": "Key 已经存在", "Key cannot be empty": "参数名不能为空", "Props name": "参数名", "Props tip": "可以设置 Http 请求的相关参数\n可通过 {{key}} 来调用全局变了或外部参数输入，当前可使用变量:\n{{variable}}", "Props value": "参数值", "ResponseChatItemId": "AI回复的ID", "Url and params have been split": "路径参数已被自动加入 Params 中", "Variables": "全局变量", "params": "Params"}, "input": {"Add Input": "添加入参", "Input Number": "入参: {{length}}", "description": {"Background": "你可以添加一些特定内容的介绍，从而更好的识别用户的问题类型。这个内容通常是给模型介绍一个它不知道的内容。", "Http Request Header": "自定义请求头，请严格填入JSON字符串。\n1. 确保最后一个属性没有逗号\n2. 确保 key 包含双引号\n例如: {\"Authorization\":\"Bearer xxx\"}", "Http Request Url": "新的HTTP请求地址。如果出现两个“请求地址”，可以删除该模块重新加入，会拉取最新的模块配置。", "Quote": "对象数组格式，结构：\n [{q:'问题',a:'回答'}]", "Response content": "可以使用 \\n 来实现连续换行。\n可以通过外部模块输入实现回复，外部模块输入时会覆盖当前填写的内容。\n如传入非字符串类型数据将会自动转成字符串", "TFSwitch textarea": "允许定义一些字符串来实现 false 匹配，每行一个，支持正则表达式。", "Trigger": "大部分时候，你不需要连接该属性。\n当你需要延迟执行，或精确控制执行时机时，可以连接该属性。", "dynamic input": "接收用户动态添加的参数，会在运行时将这些参数平铺传入", "textEditor textarea": "可以通过 {{key}} 的方式引用传入的变量。变量仅支持字符串或数字。"}, "label": {"Background": "背景知识", "Classify model": "分类模型", "Http Request Header": "请求头", "Http Request Method": "请求方式", "Http Request Url": "请求地址", "Http Request Timeout": "请求超时", "LLM": "AI 模型", "Quote": "引用内容", "Response content": "回复的内容", "Select dataset": "选择知识库", "TFSwitch input tip": "任意内容输入", "TFSwitch textarea": "自定义 False 匹配规则", "aiModel": "AI 模型", "anyInput": "", "chat history": "聊天记录", "switch": "触发器", "textEditor textarea": "文本编辑", "user question": "用户问题", "user question text": "问题文本", "user question image": "问题图片"}, "placeholder": {"Classify background": "例如: \n1. AIGC（人工智能生成内容）是指使用人工智能技术自动或半自动地生成数字内容，如文本、图像、音乐、视频等。\n2. AIGC技术包括但不限于自然语言处理、计算机视觉、机器学习和深度学习。这些技术可以创建新内容或修改现有内容，以满足特定的创意、教育、娱乐或信息需求。"}}, "inputType": {"chat history": "历史记录", "dynamicTargetInput": "动态外部数据", "input": "输入框", "selectApp": "应用选择", "selectChatModel": "对话模型选择", "selectDataset": "知识库选择", "switch": "开关", "target": "外部数据", "textarea": "段落输入"}, "output": {"Add Output": "添加出参", "Output Number": "出参: {{length}}", "description": {"Ai response content": "将在 stream 回复完毕后触发", "New context": "将本次回复内容拼接上历史记录，作为新的上下文返回", "Quote": "始终返回数组，如果希望搜索结果为空时执行额外操作，需要用到上面的两个输入以及目标模块的触发器", "running done": "模块调用结束时触发"}, "label": {"Ai response content": "AI回复内容", "New context": "新的上下文", "Quote": "引用内容", "Search result empty": "搜索结果为空", "Search result not empty": "搜索结果不为空", "cfr result": "补全结果", "result false": "False", "result true": "True", "running done": "模块调用结束", "text": "文本输出"}}, "template": {"Ai chat": "AI 对话", "Ai chat intro": "AI 大模型对话", "Assigned reply": "指定回复", "Assigned reply intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。", "Chat entrance": "对话入口", "Chat entrance intro": "当用户发送一个内容后，流程将会从这个模块开始执行。", "Classify question": "问题分类", "Classify question intro": "根据用户的历史记录和当前问题判断该次提问的类型。可以添加多组问题类型，下面是一个模板例子：\n类型1: 打招呼\n类型2: 关于商品“使用”问题\n类型3: 关于商品“购买”问题\n类型4: 其他问题", "Dataset search": "知识库搜索", "Dataset search intro": "调用知识库搜索能力，查找有可能与问题相关的内容", "Dataset search result concat intro": "可以将多个知识库搜索结果进行合并输出。使用 RRF 的合并方式进行最终排序输出。", "External module": "外部调用", "Extract field": "文本内容提取", "Extract field intro": "可从文本中提取指定的数据，例如：sql语句、搜索关键词、代码等", "Function module": "功能调用", "Guide module": "引导模块", "Http request": "HTTP 请求", "Http request intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "My plugin module": "个人插件", "Query extension": "问题补全", "Query extension intro": "开启问题补全功能，可以提高提高连续对话时，知识库搜索的精度。开启该功能后，在进行知识库搜索时，会根据对话记录，利用 AI 补全问题缺失的信息。", "Response module": "文本输出", "Running app": "应用调用", "Running app intro": "可以选择一个其他应用进行调用", "System input module": "系统输入", "TFSwitch": "判断器", "TFSwitch intro": "根据传入的内容进行 True False 输出。默认情况下，当传入的内容为 false, undefined, null, 0, none 时，会输出 false。你也可以增加一些自定义的字符串来补充输出 false 的内容。非字符、非数字、非布尔类型，直接输出 True。", "Tool module": "工具", "UnKnow Module": "未知模块", "User guide": "用户引导", "textEditor": "文本加工", "textEditor intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。"}, "textEditor": {"Text Edit": "文本加工"}, "valueType": {"any": "任意", "boolean": "布尔", "chatHistory": "聊天记录", "datasetQuote": "引用内容", "dynamicTargetInput": "动态字段输入", "number": "数字", "selectApp": "应用选择", "selectDataset": "知识库选择", "string": "字符串"}, "variable": {"Custom type": "自定义变量", "add option": "添加选项", "input type": "文本", "key": "变量 key", "key is required": "变量key是必须的", "select type": "下拉单选", "text max length": "最大长度", "textarea type": "段落", "variable key is required": "变量 key 不能为空", "variable name": "变量名", "variable name is required": "变量名不能为空", "variable option is required": "选项不能全空", "variable option is value is required": "选项内容不能为空", "variable options": "选项"}, "variable add option": "添加选项"}, "plugin": {"Get Plugin Module Detail Failed": "加载插件异常"}, "shareChat": {"Init Error": "初始化对话框失败", "Init History Error": "初始化聊天记录失败"}, "workflow": {"Can not delete node": "该节点不允许删除", "Change input type tip": "修改输入类型会清空已填写的值，请确认！", "Check Failed": "工作流校验失败，请检查节点是否正确填值，以及连线是否正常", "Confirm stop debug": "确认终止调试？调试信息将会不保留。", "Copy node": "已复制节点", "Custom inputs": "自定义输入", "Custom outputs": "自定义输出", "Dataset quote": "知识库引用", "Debug": "调试", "Debug Node": "Debug 模式", "Failed": "运行失败", "Not intro": "这个节点没有介绍~", "Running": "运行中", "Skipped": "跳过运行", "Stop debug": "停止调试", "Success": "运行成功", "Value type": "数据类型", "Variable": {"Variable type": "变量类型"}, "debug": {"Done": "完成调试", "Hide result": "隐藏结果", "Not result": "无运行结果", "Run result": "运行结果", "Show result": "展示结果"}, "inputType": {"JSON Editor": "JSON 输入框", "Manual input": "手动输入", "Manual select": "手动选择", "Reference": "变量引用", "dynamicTargetInput": "动态外部数据", "input": "单行输入框", "number input": "数字输入框", "selectApp": "应用选择", "selectDataset": "知识库选择", "selectLLMModel": "对话模型选择", "switch": "开关", "textarea": "多行输入框"}, "publish": {"OnRevert version": "点击回退到该版本", "OnRevert version confirm": "确认回退至该版本？会为您保存编辑中版本的配置，并为回退版本创建一个新的发布版本。", "histories": "发布记录"}, "template": {"Multimodal": "多模态", "Search": "搜索"}, "tool": {"Handle": "工具连接器", "Select Tool": "选择工具"}, "value": "值", "variable": "变量"}}, "dataset": {"Confirm move the folder": "确认移动到该目录", "Confirm to delete the data": "确认删除该数据？", "Confirm to delete the file": "确认删除该文件及其所有数据？", "Create Folder": "创建文件夹", "Create manual collection": "创建手动数据集", "Delete Dataset Error": "删除知识库异常", "Edit Folder": "编辑文件夹", "Export": "导出", "Export Dataset Limit Error": "导出数据失败", "File Input": "文件导入", "File Size": "文件大小", "Filename": "文件名", "Files": "文件: {{total}}个", "Folder Name": "输入文件夹名称", "Insert Data": "插入", "Manual Data": "手动录入", "Manual Input": "手动录入", "Manual Mark": "手动标注", "Manual collection Tip": "手动数据集允许创建一个空的容器装入数据", "Mark Data": "标注数据", "Move Failed": "移动出现错误~", "Queue Desc": "该数据是指整个系统当前待训练的数量。{{title}} 采用排队训练的方式，如果待训练的数据过多，可能需要等待一段时间", "Select Dataset": "选择该知识库", "Select Dataset Tips": "仅能选择同一个索引模型的知识库", "Select Folder": "进入文件夹", "System Data Queue": "排队长度", "Training Name": "数据训练", "Upload Time": "上传时间", "collections": {"Click to view file": "点击查看文件详情", "Click to view folder": "进入目录", "Collection Embedding": "{{total}}组索引中", "Confirm to delete the folder": "确认删除该文件夹及里面所有内容？", "Create And Import": "新建/导入", "Add File": "添加文件", "File Total": "文件总数", "Show File Only": "仅显示文件", "Folder": "文件夹", "File": "文件", "Create Training Data": "文件训练-{{filename}}", "Create manual collection Success": "创建手动数据集成功", "Data Amount": "数据总量", "Select Collection": "选择文件", "Select One Collection To Store": "选择一个文件进行存储", "Default Folder Name": "新建文件夹", "Upload File": "文件上传", "Supported File Type": "已支持文件类型", "Other File Type Expect": "其他文件类型敬请期待", "Choose File": "选择文件", "Choose Folder": "选择目录", "Drag File To Here To Upload": "可将文件拖拽至此框内上传", "File Table Label Order": "序号", "File Table Label Update Time": "修改日期", "File Table Label Name": "文件名", "File Table Label Size": "大小", "File Table Label Status": "状态", "File Table Label Operation": "操作", "File Status Processing": "正在处理", "File Status Waiting": "等待处理", "File Status Upload Failed": "上传失败", "File Status Upload Success": "上传成功", "File Status Error": "文件错误", "File Status Reupload": "重新上传", "File Tab All": "总计", "File Tab Success": "成功", "File Tab Failed": "失败", "Skip Import": "跳过导入", "Confirm Import": "确定导入", "Delete All": "全部删除", "Only Current Folder": "仅本目录", "File Name Search Placeholder": "输入文件名查找", "Delete Folder Confirm Prompt": "删除 {{name}}，将删除这个文件夹下面的所有文件，确定吗？", "Delete File Confirm Prompt": "确定删除 {{name}} 文件吗？", "Delete All Confirm Prompt": "确定删除所有文件吗？"}, "data": {"Add Index": "新增自定义索引", "Can not delete tip": "无修改权限", "Can not edit": "无编辑权限", "Custom Index Number": "自定义索引{{number}}", "Default Index": "默认索引", "Delete Success Tip": "删除成功", "Delete Tip": "确认删除该条数据？", "File import": "文件导入", "Index Edit": "数据索引", "Index Placeholder": "输入索引文本内容", "Input Data": "导入新数据", "Input Success Tip": "导入数据成功", "Update Data": "更新数据", "Update Success Tip": "更新数据成功", "edit": {"Content": "数据内容", "Course": "说明文档", "Delete": "删除数据", "Index": "数据索引({{amount}})"}, "input is empty": "数据内容不能为空 "}, "deleteFolderTips": "确认删除该文件夹及其包含的所有知识库？删除后数据无法恢复，请确认！", "import csv tip": "请确保CSV为UTF-8格式，否则会乱码", "test": {"noResult": "搜索结果为空"}}, "error": {"fileNotFound": "文件找不到了~", "team": {"overSize": "团队成员超出上限"}}, "file": {"Click to download file template": "点击下载模板：{{name}}", "Click to view file": "点击查看原始文件", "Create File": "创建新文件", "Create file": "创建文件", "Drag and drop": "拖拽文件至此", "Fetch Url": "链接读取", "If the imported file is garbled, please convert CSV to UTF-8 encoding format": "如果导入文件乱码，请将 CSV 转成 UTF-8 编码格式", "Parse": "{{name}} 解析中...", "Release the mouse to upload the file": "松开鼠标上传文件", "Select a maximum of 10 files": "最多选择10个文件", "Uploading": "正在上传 {{name}}，进度: {{percent}}%", "max 10": "最多选择 10 个文件", "max 100": "最多选择 100 个文件", "select a document": "选择文件", "support": "支持 {{fileExtension}} 文件", "upload error description": "单次只支持上传多个文件或者一个文件夹"}, "home": {"AI Assistant": "AI 客服", "AI Assistant Desc": "无论对内还是对外，AI 将 24 小时为您的用户提供服务", "Advanced Settings": "高级编排", "Advanced Settings Desc": "基于 Flow 的流程编排模式，让你的 AI 轻松实现数据库查询、IO 操作、联网通信等扩展能力", "Choice Debug": "调试便捷", "Choice Debug Desc": "拥有搜索测试、引用修改、完整对话预览等多种调试途径", "Choice Desc": "", "Choice Extension": "无限扩展", "Choice Extension Desc": "基于 HTTP 实现扩展，轻松实现定制功能", "Choice Fast": "开箱即用", "Choice Fast Desc": "{{title}} 提供开箱即用的可视化操作，点点点即可构建 AI 应用", "Choice Models": "支持多种模型", "Choice Models Desc": "支持 GPT、Claude、Spark、ChatGLM等多模型", "Choice Open": "更开放", "Choice Open Desc": "{{title}} 遵循 Apache License 2.0 开源协议", "Choice QA": "独特的 QA 结构", "Choice QA Desc": "采用 QA 对的结构构建索引，适应问答、阅读等多种场景", "Choice Visual": "可视化工作流", "Choice Visual Desc": "可视化模块操作，轻松实现复杂工作流，让你的 AI 不再单一", "Commercial": "商务咨询", "Community": "社区", "Dateset": "自动数据预处理", "Dateset Desc": "提供手动输入、直接分段、LLM 自动处理和 CSV 等多种数据导入途径", "Docs": "文档", "FastGPT Ability": "{{title}} 能力", "FastGPT Desc": "{{title}} 是一个基于 LLM 大语言模型的知识库问答系统，提供开箱即用的数据处理、模型调用等能力。同时可以通过 Flow 可视化进行工作流编排，从而实现复杂的问答场景！", "Features": "特点", "Footer Developer": "开发者", "Footer Docs": "文档", "Footer FastGPT Cloud": "{{title}} 线上服务", "Footer Feedback": "反馈", "Footer Git": "源码", "Footer Product": "产品", "Footer Support": "支持", "Login": "登录", "Open": "", "OpenAPI": "OpenAPI", "OpenAPI Desc": "与 GPT API 一致的对外接口，助你轻松接入已有应用", "Quickly build AI question and answer library": "快速搭建 AI 问答系统", "Start Now": "立即开始", "Visual AI orchestration": "可视化 AI 编排", "Why FastGPT": "为什么选择 {{title}}", "desc": "基于 LLM 大模型的 AI 知识库问答平台", "navbar": {"Use guidance": "使用引导", "chatbot": "问答机器人"}, "slogan": "让 AI 更懂你的知识"}, "module": {"Confirm Delete Module": "确认删除该自定义模块？", "Confirm Sync Plugin": "确认同步插件最新信息？插件的连线和输入的内容将会被清空，请确认！", "Create Your Module": "创建自定义模块", "Intro": "模块介绍", "Load Module Failed": "加载模块失败", "Plugin input is not value": "自定义输入的参数不能为空", "Plugin input is required": "插件编排必须包含一个输入模块", "Plugin input must connect": "自定义输入模块必须全部连接", "Preview Plugin": "预览插件", "Save Config": "保存配置", "Update Your Module": "更新模块信息"}, "navbar": {"Account": "账号", "Apps": "应用", "AI Copilot": "AI 助手", "Chat": "聊天", "Datasets": "知识库", "Module": "模块", "Plugin": "插件", "Store": "应用市场", "Tools": "工具", "Workbench": "工作台"}, "openapi": {"app key tips": "这些 key 已有当前应用标识，具体使用可参考文档", "key alias": "key 的别名，仅用于展示", "key tips": "你可以使用 API 秘钥访问一些特定的接口(无法访问应用，访问应用需使用应用内的API Key)"}, "outlink": {"Copy IFrame": "嵌入网页", "Copy Link": "复制", "Create API Key": "创建新 Key", "Create Link": "创建链接", "Delete Link": "删除链接", "Edit API Key": "编辑 Key 信息", "Edit IFrame Link": "更新嵌入链接", "Edit Link": "编辑", "Edit Share Window": "更新分享窗口", "Link Name": "分享链接的名字", "Link is empty": "", "QPM": "", "QPM Tips": "每个 IP 每分钟最多提问多少次", "QPM is empty": "QPM 不能为空", "token auth": "身份验证", "token auth Tips": "身份校验服务器地址，如填写该值，每次对话前都会向指定服务器发送一个请求，进行身份校验", "token auth use cases": "查看身份验证使用说明"}, "permission": {"Private": "私有", "Private Tip": "仅自己可用", "Public": "共享", "Public Tip": "授权成员可使用", "Share": "共享", "Share Tip": "授权成员可使用", "Set Private": "设为私有", "Set Public": "设为共享", "Set Share": "设为共享"}, "plugin": {"Confirm Delete": "确认删除该插件？", "Create Your Plugin": "创建你的插件", "Get Plugin Module Detail Failed": "获取插件信息异常", "Intro": "插件介绍", "Load Plugin Failed": "加载插件异常", "My Plugins": "我的插件", "No Intro": "这个插件没有介绍~", "Plugin Module": "插件模块", "Set Name": "给插件取个名字", "Synchronous version": "同步版本", "To Edit Plugin": "去编辑", "Update Your Plugin": "更新插件"}, "support": {"openapi": {"Api baseurl": "API根地址", "Api manager": "API 秘钥管理", "Copy success": "已复制 API 地址", "Max usage": "最大额度(￥)", "New api key": "新的 API 秘钥", "New api key tip": "请保管好你的秘钥，秘钥不会再次展示~", "Usage": "已用额度(￥)"}, "outlink": {"share": {"Response Quote": "返回引用", "Response Quote tips": "在分享链接中返回引用内容，但不会允许用户下载原文档"}}, "subscription": {"Cancel subscription": "取消订阅"}, "user": {"Need to login": "请先登录", "Price": "计费标准", "auth": {"Sending Code": "正在发送"}, "login": {"Github": "G<PERSON><PERSON> 登录", "Google": "Google 登录", "Provider error": "登录异常，请重试"}, "team": {"Dataset usage": "知识库容量", "member": "成员"}}, "wallet": {"Balance not enough tip": "余额不足，请先到账号页充值", "Buy more": "扩容", "Confirm pay": "支付确认", "Pay error": "支付失败", "Pay success": "支付成功", "bill": {"AI Model": "AI 模型", "AI Type": "AI 类型", "Price": "价格（￥）"}, "subscription": {"Ai points": "AI 积分计算标准", "Buy now": "开始使用", "Change will take effect after the current subscription expires": "更新成功。将会再下个订阅周期生效。", "Current dataset store": "当前额外容量", "Current plan": "当前计划", "Dataset store": "知识库容量", "Dataset store price tip": "每月1号从账号余额里扣除", "Expand size": "扩大容量", "Extra dataset size": "额外知识库容量", "Extra plan": "额外套餐", "Extra plan tip": "标准套餐不够时，您可以购买额外套餐继续使用", "FAQ": "常见问题", "Next plan": "未来计划", "Next sub dataset size": "下次订阅额外容量", "Nonsupport": "无法切换", "Refund plan and pay confirm": "切换该套餐您无需支付额外费用，并将退换 {{amount}} 元至余额中。", "Standard plan pay confirm": "切换该套餐，您本次需要补充支付 {{payPrice}} 元。", "Standard update fail": "修改订阅套餐异常", "Standard update success": "变更订阅套餐成功！", "Sub plan": "订阅套餐", "Sub plan tip": "免费使用 FastGPT 或升级更高的套餐", "Training weight": "训练优先级: {{weight}}", "Update extra dataset size": "调整知识库额外容量", "function": {"History store": "{{amount}} 天对话记录保留", "Max app": "{{amount}} 个应用与插件", "Max dataset": "{{amount}} 个知识库", "Max dataset size": "{{amount}} 组知识库数据", "Max members": "{{amount}} 个团队成员", "Points": "{{amount}}万AI积分"}, "mode": {"Month": "按月", "Year": "按年", "Year sale": "赠送两个月"}, "standardSubLevel": {"enterprise": "企业版", "experience": "体验版", "experience desc": "可使用 {{title}} 的完整功能", "free": "免费版", "free desc": "每月均可免费使用 {{title}} 的基础功能", "team": "团队版"}, "type": {"extraDatasetSize": "知识库扩容", "standard": "套餐订阅"}}}}, "system": {"Help Document": "帮助文档"}, "template": {"Quote Content Tip": "该配置只有传入引用内容（知识库搜索）时生效。\n可以自定义引用内容的结构，以更好的适配不同场景。可以使用一些变量来进行模板配置:\n{{q}} - 检索内容, {{a}} - 预期内容, {{source}} - 来源，{{sourceId}} - 来源文件名，{{index}} - 第n个引用，{{score}} - 该引用的得分(0-1)，他们都是可选的，下面是默认值：\n{{default}}", "Quote Prompt Tip": "该配置只在知识库搜索时生效。\n可以用 {{quote}} 来插入引用内容模板，使用 {{question}} 来插入问题。下面是默认值：\n{{default}}"}, "user": {"Account": "账号", "Amount of earnings": "收益（￥）", "Amount of inviter": "累计邀请人数", "Application Name": "项目名", "Avatar": "头像", "Balance": "余额", "Bill Detail": "账单详情", "Change": "变更", "Copy invite url": "复制邀请链接", "Edit name": "点击修改昵称", "Invite Url": "邀请链接", "Invite url tip": "通过该链接注册的好友将永久与你绑定，其充值时你会获得一定余额奖励。\n此外，好友使用手机号注册时，你将立即获得 5 元奖励。\n奖励会发送到您的默认团队中。", "Language": "语言", "Member Name": "昵称", "Notice": "通知", "Old password is error": "旧密码错误", "OpenAI Account Setting": "OpenAI 账号配置", "Password": "密码", "Pay": "充值", "Permission": "使用权限", "Personal Information": "个人中心", "Promotion": "", "Promotion Rate": "返现比例", "Promotion Record": "推广记录", "Promotion rate tip": "好友充值时你将获得一定比例的余额奖励", "Recharge Record": "支付记录", "Replace": "更换", "Set OpenAI Account Failed": "设置 OpenAI 账号异常", "Sign Out": "退出", "Source": "来源", "Team": "团队", "Tenant": "租户", "Time": "时间", "Timezone": "时区", "Total Amount": "总金额", "Update Password": "修改密码", "Update password failed": "修改密码异常", "Update password successful": "修改密码成功", "Usage Record": "使用记录", "apikey": {"key": "API 秘钥"}, "promotion": {"pay": "好友充值", "register": "好友注册"}, "team": {"Balance": "团队余额", "Check Team": "切换", "Confirm Invite": "确认邀请", "Create Team": "创建新团队", "Invite Member": "邀请成员", "Invite Member Failed Tip": "邀请成员出现异常", "Invite Member Result Tip": "邀请结果提示", "Invite Member Success Tip": "邀请成员完成\n成功: {{success}}人\n用户名无效: {{inValid}}\n已在团队中:{{inTeam}}", "Invite Member Tips": "对方可查阅或使用团队内的其他资源", "Invite Role Admin Alias": "邀请管理员加入", "Invite Role Admin Tip": "管理员\n可创建、编辑和使用团队资源", "Invite Role Visitor Alias": "邀请访客加入", "Invite Role Visitor Tip": "访客\n仅可使用资源，无创建编辑权限", "Leave Team": "离开团队", "Leave Team Failed": "离开团队异常", "Manage": "团队管理", "Member": "成员", "Member Name": "成员名", "Over Max Member Tip": "团队最多{{max}}人", "Personal Team": "个人团队", "Processing invitations": "处理邀请", "Processing invitations Tips": "你有{{amount}}个需要处理的团队邀请", "Reinvite": "重新邀请", "Remove Member Confirm Tip": "确认将 {{username}} 移出团队？其所有资源将转让到团队创建者的账户内。", "Remove Member Failed": "移除团队成员异常", "Remove Member Success": "移除团队成员成功", "Remove Member Tip": "移出团队", "Role": "身份", "Select Team": "团队选择", "Set Name": "给团队取个名字", "Switch Team Failed": "切换团队异常", "Team Name": "团队名", "Update Team": "更新团队信息", "invite": {"Accept Confirm": "确认加入该团队？", "Accepted": "已加入团队", "Deal Width Footer Tip": "处理完会自动关闭噢~", "Reject": "已拒绝邀请", "Reject Confirm": "确认拒绝该邀请？", "accept": "接受", "reject": "拒绝"}, "member": {"Confirm Leave": "确认离开该团队？", "active": "已加入", "reject": "拒绝", "waiting": "待接受"}, "role": {"Admin": "管理员", "Member": "成员", "Owner": "创建者", "Update to Visitor": "修改成访客", "Visitor": "访客"}}}, "wallet": {"bill": {"Ai model": "AI模型", "App name": "应用名", "Audio Speech": "语音播报", "Bill Module": "扣费模块", "Chars length": "文本长度", "Data Length": "数据长度", "Dataset store": "知识库存储", "Duration": "时长（秒）", "Extension Input Token Length": "问题补全输入Tokens", "Extension Output Token Length": "问题补全输出Tokens", "Extension result": "问题补全结果", "Input Token Length": "输入 Tokens", "Module name": "模块名", "Next Step Guide": "下一步指引", "Number": "订单号", "Output Token Length": "输出 Tokens", "ReRank": "结果重排", "Source": "来源", "Text Length": "文本长度", "Time": "生成时间", "Token Length": "Token长度", "Total": "总金额", "Whisper": "语音输入", "bill username": "用户"}, "moduleName": {"index": "索引生成", "qa": "QA 拆分"}}, "layout": {"Qbank": "题库"}}