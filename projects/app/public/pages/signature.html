<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>H5签名页面</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        background-color: #f0f0f0;
      }
      .signature-container {
        background-color: #fff;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
      canvas {
        border: 1px solid #000;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      button {
        padding: 10px 20px;
        font-size: 16px;
        border: none;
        border-radius: 5px;
        background-color: #007bff;
        color: #fff;
        cursor: pointer;
      }
      button:hover {
        background-color: #0056b3;
      }
      .loading {
        display: none;
        margin-top: 10px;
        font-size: 16px;
        color: #007bff;
      }
    </style>
  </head>
  <body>
    <div class="signature-container">
      <div style="display: none" class="signature-success">签名已保存,请返回pc端</div>
      <div class="signature-wrapper">
        <h2>请在此签名</h2>
        <canvas id="signature-pad" width="350" height="200"></canvas>
        <div style="display: flex; justify-content: space-between">
          <div></div>
          <button id="save">完成</button>
        </div>
        <div class="loading" id="loading">保存中...</div>
      </div>
    </div>

    <script src="./../js/signature_pad.js"></script>
    <script>
      // 解析URL参数
      function getQueryParams() {
        const params = new URLSearchParams(window.location.search);
        return {
          signatureId: params.get('signatureId'),
          token: params.get('token')
        };
      }

      const { signatureId, token } = getQueryParams();

      var canvas = document.getElementById('signature-pad');
      var signaturePad = new SignaturePad(canvas);

      function dataURLtoFile(dataurl, filename) {
        var arr = dataurl.split(','),
          mime = arr[0].match(/:(.*?);/)[1],
          bstr = atob(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, { type: mime });
      }

      document.getElementById('save').addEventListener('click', function () {
        if (signaturePad.isEmpty()) {
          alert('请先签名');
        } else {
          var data = signaturePad.toDataURL('image/png');
          var file = dataURLtoFile(data, 'signature.png');

          var formData = new FormData();
          formData.append('file', file);

          // 显示“保存中...”提示
          document.getElementById('loading').style.display = 'block';

          fetch(`${window.location.origin}/huayun-ai/system/file/upload`, {
            method: 'POST',
            headers: {
              Authorization: token
            },
            body: formData
          })
            .then((response) => response.json())
            .then((result) => {
              if (result.code == 200 && result.data) {
                const fileKey = result.data.fileKey;
                alert('签名已保存' + fileKey);
                console.log(result);

                // 调用新增接口
                fetch(`${window.location.origin}/huayun-evaluation/signFile/add`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: token
                  },
                  body: JSON.stringify({
                    fileKey: fileKey,
                    frontGenerateId: signatureId
                  })
                })
                  .then((response) => response.json())
                  .then((addResult) => {
                    if (addResult.code == 200) {
                      console.log('新增记录成功', addResult);
                      document.querySelector('.signature-wrapper').style.display = 'none';
                      document.querySelector('.signature-success').style.display = 'block';
                    } else {
                      console.error('新增记录失败', addResult);
                    }
                  })
                  .catch((error) => {
                    console.error('Error:', error);
                  });
              }
              // 隐藏“保存中...”提示
              document.getElementById('loading').style.display = 'none';
            })
            .catch((error) => {
              console.error('Error:', error);
              // 隐藏“保存中...”提示
              document.getElementById('loading').style.display = 'none';
            });
        }
      });
    </script>
  </body>
</html>
