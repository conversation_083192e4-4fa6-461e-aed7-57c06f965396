import React from 'react';
import { BoxProps, ModalContentProps } from '@chakra-ui/react';
import { useSystemStore } from '@/store/useSystemStore';
import CustomModal from './CustomModal';

export interface MyModalProps extends ModalContentProps {
  iconSrc?: string;
  title?: any;
  isCentered?: boolean;
  isOpen: boolean;
  headerStyle?: BoxProps;
  isLoading?: boolean;

  returnFocusOnClose?: boolean;
  closeOnOverlayClick?: boolean;
  hideCloseButton?: boolean;
  onClose?: () => void;
}

const MyModal = ({
  isOpen,
  onClose,
  iconSrc,
  title,
  children,
  isCentered,
  closeOnOverlayClick,
  headerStyle,
  isLoading,
  hideCloseButton,

  w = 'auto',
  maxW = ['90vw', '600px'],
  ...props
}: MyModalProps) => {
  const { isPc } = useSystemStore();
  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      iconSrc={iconSrc}
      title={title}
      isLoading={isLoading}
      headerStyle={headerStyle}
      closeOnOverlayClick={closeOnOverlayClick}
      hideCloseButton={hideCloseButton}
      isCentered={isPc ? isCentered : true}
      w={w}
      maxW={maxW}
      {...props}
    >
      {children}
    </CustomModal>
  );
};

export default MyModal;
