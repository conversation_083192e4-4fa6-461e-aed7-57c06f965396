import React from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalContentProps,
  Box,
  Image,
  BoxProps
} from '@chakra-ui/react';
import MyBox from '../common/MyBox';
import SvgIcon from '../SvgIcon';

export interface MyModalProps extends ModalContentProps {
  iconSrc?: string;
  title?: any;
  isCentered?: boolean;
  isOpen: boolean;
  onClose?: () => void;
  headerStyle?: BoxProps;
  isLoading?: boolean;
  isPc?: boolean;
  returnFocusOnClose?: boolean;
  closeOnOverlayClick?: boolean;
  hideCloseButton?: boolean;
}

const CustomModal = ({
  isOpen,
  onClose,
  iconSrc,
  title,
  isLoading,
  children,
  isCentered = true,
  headerStyle,
  w = 'auto',
  maxW = ['90vw', '600px'],
  returnFocusOnClose = true,
  closeOnOverlayClick = true,
  hideCloseButton,
  ...props
}: MyModalProps) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={() => onClose && onClose()}
      autoFocus={false}
      isCentered={isCentered}
      returnFocusOnClose={returnFocusOnClose}
      closeOnOverlayClick={closeOnOverlayClick}
    >
      <ModalOverlay />
      <ModalContent
        w={w}
        minW={['90vw', '400px']}
        maxW={maxW}
        position={'relative'}
        maxH={'85vh'}
        boxShadow={'7'}
        {...props}
      >
        {!title && onClose && !hideCloseButton && <ModalCloseButton zIndex={1} />}
        {!!title && (
          <ModalHeader
            display={'flex'}
            alignItems={'center'}
            fontWeight={500}
            background={'#FBFBFC'}
            borderBottom={'1px solid #F4F6F8'}
            roundedTop={'lg'}
            py={'10px'}
            {...headerStyle}
          >
            {iconSrc && (
              <>
                {iconSrc.startsWith('/') ? (
                  <Image mr={3} objectFit={'contain'} alt="" src={iconSrc} w={'20px'} />
                ) : (
                  <SvgIcon mr={3} name={iconSrc as any} w={'20px'} />
                )}
              </>
            )}
            {title}
            <Box flex={1} />
            {onClose && !hideCloseButton && (
              <ModalCloseButton position={'relative'} fontSize={'sm'} top={0} right={0} ml="27px" />
            )}
          </ModalHeader>
        )}

        <MyBox
          overflow={props.overflow || 'overlay'}
          h={'100%'}
          display={'flex'}
          flexDirection={'column'}
          isLoading={isLoading}
        >
          {children}
        </MyBox>
      </ModalContent>
    </Modal>
  );
};

export default CustomModal;
