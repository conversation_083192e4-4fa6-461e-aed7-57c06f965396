import { Box, BoxProps } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import React<PERSON><PERSON>ie, { LottieProps, Options } from 'react-lottie';

const datas = {
  chating: () => import('./data/chating.json'),
  click: () => import('./data/click.json'),
  waveform: () => import('./data/waveform.json'),
  waveform2: () => import('./data/waveform2.json'),
  processing: () => import('./data/processing.json'),
  Loading: () => import('./data/loading.json'),
  dialogue: () => import('./data/dialogue.json'),
  basedTeaching: () => import('./data/basedTeaching.json'),
  planning: () => import('./data/planning.json'),
  documents: () => import('./data/documents.json'),
  plan: () => import('./data/plan.json')
};

export type LottieNameType = keyof typeof datas;

const Lottie = ({
  name,
  options,
  isStopped,
  isPaused,
  eventListeners,
  segments,
  speed,
  direction,
  ariaRole,
  ariaLabel,
  isClickToPauseDisabled = true,
  preserveAspectRatio = 'none',
  ...props
}: {
  name: LottieNameType;
  options?: Omit<Options, 'animationData'>;
  preserveAspectRatio?: string;
} & Omit<LottieProps, 'options' | 'width' | 'height'> &
  BoxProps) => {
  const [data, setData] = useState<any>();

  const usedOptions = options ? { ...options, animationData: data } : { animationData: data };

  if (preserveAspectRatio && !usedOptions.rendererSettings?.preserveAspectRatio) {
    usedOptions.rendererSettings = {
      ...usedOptions.rendererSettings,
      preserveAspectRatio
    };
  }

  useEffect(() => {
    datas[name]().then((res) => {
      setData(res);
    });
  }, [name]);

  return (
    data && (
      <Box {...props}>
        <ReactLottie
          options={usedOptions}
          width="100%"
          height="100%"
          isStopped={isStopped}
          isPaused={isPaused}
          eventListeners={eventListeners}
          segments={segments}
          speed={speed}
          direction={direction}
          ariaRole={ariaRole}
          ariaLabel={ariaLabel}
          isClickToPauseDisabled={isClickToPauseDisabled}
        />
      </Box>
    )
  );
};

export default Lottie;
