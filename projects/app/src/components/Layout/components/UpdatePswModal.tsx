import React, { useEffect, useState } from 'react';
import {
  ModalBody,
  Box,
  Flex,
  Input,
  InputGroup,
  InputRightElement,
  ModalFooter,
  Button,
  FormControl,
  FormErrorMessage,
  Center
} from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { getClientUserSmsCode, getClientUserValid, updateUser } from '@/api/user';
import { Toast } from '@/utils/ui/toast';
import SvgIcon from '@/components/SvgIcon';
import { useUserStore } from '@/store/useUserStore';

type FormType = {
  phone: string;
  code: string;
  oldPsw: string;
  newPsw: string;
  confirmPsw: string;
};

const UpdatePswModal = ({ onClose, onSuccess }: { onClose: () => void; onSuccess: () => void }) => {
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState(0);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [step, setStep] = useState(1);
  const { userInfo, initUserInfo } = useUserStore();

  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    watch,
    formState: { errors }
  } = useForm<FormType>({
    defaultValues: {
      phone: '',
      code: '',
      oldPsw: '',
      newPsw: '',
      confirmPsw: ''
    }
  });

  const onSendCode = async () => {
    const isPhoneValid = await trigger('phone');
    if (!isPhoneValid) {
      return;
    }
    const phoneNumber = getValues('phone');

    try {
      const data = await getClientUserSmsCode(3, phoneNumber);
      if (data && data.code === 200) {
        setIsCodeSent(true);
        setCountdown(60);
      } else {
        Toast.error('发送验证码失败，请重试。');
      }
    } catch (error) {}
  };

  const { mutate: onVerifyPhone, isLoading: isVerifying } = useRequest({
    mutationFn: (data: { phone: string; code: string }) => {
      return getClientUserValid(3, data.phone, Number(data.code));
    },
    onSuccess() {
      setStep(2);
    }
  });

  const { mutate: onSubmit, isLoading: isUpdating } = useRequest({
    mutationFn: (data: FormType) => {
      if (data.newPsw !== data.confirmPsw) {
        Toast.error({
          title: '新密码和确认密码不一致'
        });
        return Promise.reject();
      }
      return updateUser({
        oldPassword: data.oldPsw,
        password: data.newPsw
      });
    },
    onSuccess() {
      onSuccess();
      onClose();
    },
    successToast: t('操作成功')
  });

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    } else if (countdown === 0 && isCodeSent) {
      setIsCodeSent(false);
    }
    return () => clearInterval(timer!);
  }, [countdown, isCodeSent]);

  return (
    <MyModal isOpen onClose={onClose} iconSrc="/imgs/modal/password.svg" title="更换密码">
      <ModalBody>
        {step === 1 && (
          <>
            <FormControl isInvalid={!!errors.phone}>
              <Flex flexDir="column" position="relative">
                <Center>
                  <Box
                    flex={'0 0 40px'}
                    fontSize="14px"
                    color="#1D2129"
                    fontWeight="400"
                    position="absolute"
                    left="16px"
                    top="11px"
                    zIndex="999"
                  >
                    +86
                  </Box>
                  <Input
                    placeholder="请输入手机号"
                    flex={1}
                    pl="52px"
                    _placeholder={{
                      color: '#86909C'
                    }}
                    type={'text'}
                    disabled={isCodeSent}
                    {...register('phone', {
                      required: '请输入手机号',
                      pattern: {
                        value: /^1[3-9]\d{9}$/,
                        message: '请输入正确的手机号'
                      },
                      validate: (value) =>
                        value === userInfo?.account || '当前手机号与登录手机号不一致'
                    })}
                  />
                </Center>
                <Box ml="40px">
                  <FormErrorMessage>{errors.phone && errors.phone.message}</FormErrorMessage>
                </Box>
              </Flex>
            </FormControl>

            <FormControl isInvalid={!!errors.code} mt={5}>
              <Flex flexDir="column" pos="relative">
                <Center>
                  <Input
                    flex={1}
                    type={'text'}
                    placeholder="请输入验证码"
                    _placeholder={{
                      color: '#86909C'
                    }}
                    {...register('code', {
                      required: '请输入验证码',
                      maxLength: {
                        value: 20,
                        message: '验证码最多 20 位'
                      }
                    })}
                  ></Input>
                  <Button
                    pos="absolute"
                    zIndex="999"
                    variant="link"
                    right="16px"
                    color="#0052D9"
                    onClick={() => {
                      countdown <= 0 && onSendCode();
                    }}
                    disabled={countdown > 0}
                  >
                    {countdown > 0 ? `${countdown}s` : '发送验证码'}
                  </Button>
                </Center>
                <Box>
                  <FormErrorMessage>{errors.code && errors.code.message}</FormErrorMessage>
                </Box>
              </Flex>
            </FormControl>
          </>
        )}

        {step === 2 && (
          <>
            <FormControl isInvalid={!!errors.oldPsw}>
              <Flex flexDir="column">
                <Center>
                  <Box flex={'0 0 70px'}>旧密码:</Box>
                  <InputGroup>
                    <Input
                      placeholder="请输入旧密码"
                      flex={1}
                      type={showPassword ? 'text' : 'password'}
                      {...register('oldPsw', { required: '请输入旧密码' })}
                    />
                    <InputRightElement>
                      <SvgIcon
                        name={showPassword ? 'eye' : 'eyeOff'}
                        w="18px"
                        h="18px"
                        onClick={() => setShowPassword((state) => !state)}
                      />
                    </InputRightElement>
                  </InputGroup>
                </Center>
                <Box ml="70px">
                  <FormErrorMessage>{errors.oldPsw && errors.oldPsw.message}</FormErrorMessage>
                </Box>
              </Flex>
            </FormControl>

            <FormControl isInvalid={!!errors.newPsw} mt={5}>
              <Flex flexDir="column">
                <Center>
                  <Box flex={'0 0 70px'}>新密码:</Box>
                  <InputGroup>
                    <Input
                      flex={1}
                      type={showNewPassword ? 'text' : 'password'}
                      placeholder="请输入新密码"
                      {...register('newPsw', {
                        required: '请输入新密码',
                        maxLength: {
                          value: 16,
                          message: '密码最多 16 位'
                        },
                        minLength: {
                          value: 8,
                          message: '密码最少 8 位'
                        },
                        pattern: {
                          value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                          message: '密码需包含大小写字母和数字的组合，可以使用特殊字符，长度8-16'
                        }
                      })}
                    />
                    <InputRightElement>
                      <SvgIcon
                        name={showNewPassword ? 'eye' : 'eyeOff'}
                        w="18px"
                        h="18px"
                        onClick={() => setShowNewPassword((state) => !state)}
                      />
                    </InputRightElement>
                  </InputGroup>
                </Center>
                <Box ml="70px">
                  <FormErrorMessage>{errors.newPsw && errors.newPsw.message}</FormErrorMessage>
                </Box>
              </Flex>
            </FormControl>

            <FormControl isInvalid={!!errors.confirmPsw} mt={5}>
              <Flex flexDir="column">
                <Center>
                  <Box flex={'0 0 70px'}>确认密码:</Box>
                  <InputGroup>
                    <Input
                      flex={1}
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="请输入确认密码"
                      {...register('confirmPsw', {
                        required: '请输入确认密码',
                        maxLength: {
                          value: 16,
                          message: '密码最多 16 位'
                        },
                        minLength: {
                          value: 8,
                          message: '密码最少 8 位'
                        },
                        validate: (value) => value === watch('newPsw') || '两次输入的密码不一致'
                      })}
                    />
                    <InputRightElement>
                      <SvgIcon
                        name={showConfirmPassword ? 'eye' : 'eyeOff'}
                        w="18px"
                        h="18px"
                        onClick={() => setShowConfirmPassword((state) => !state)}
                      />
                    </InputRightElement>
                  </InputGroup>
                </Center>
                <Box ml="70px">
                  <FormErrorMessage>
                    {errors.confirmPsw && errors.confirmPsw.message}
                  </FormErrorMessage>
                </Box>
              </Flex>
            </FormControl>
          </>
        )}
      </ModalBody>
      <ModalFooter>
        {step === 1 ? (
          <>
            <Button mr={3} variant={'grayBase'} onClick={onClose}>
              取消
            </Button>
            <Button isLoading={isVerifying} onClick={handleSubmit((data) => onVerifyPhone(data))}>
              确认
            </Button>
          </>
        ) : (
          <>
            <Button mr={3} variant={'grayBase'} onClick={() => setStep(1)}>
              返回
            </Button>
            <Button isLoading={isUpdating} onClick={handleSubmit((data) => onSubmit(data))}>
              完成
            </Button>
          </>
        )}
      </ModalFooter>
    </MyModal>
  );
};

export default UpdatePswModal;
