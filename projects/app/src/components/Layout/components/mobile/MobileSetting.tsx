import { rpxDim } from '@/utils/chakra';
import {
  Box,
  Button,
  Center,
  Flex,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalOverlay,
  useDisclosure
} from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import React, { useCallback } from 'react';
import { useSelectFile } from '@/hooks/useSelectFile';
import { getErrText } from '@/utils/string';
import { useToast } from '@/hooks/useToast';
import { useTranslation } from 'next-i18next';
import MyModal from '@/components/MyModal';
import MobileChangePass from './MobileChangePass';
import { useSystemStore } from '@/store/useSystemStore';
import { clearToken } from '@/utils/auth';
import { uploadImage } from '@/utils/file';

const MobileSetting = ({ onClose }: { onClose: () => void }) => {
  const { userInfo, updateUserInfo, setUserInfo } = useUserStore();
  const { t } = useTranslation();
  const { toast } = useToast();
  const { isPc } = useSystemStore();
  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });
  const router = useRouter();
  const OverlayOne = () => (
    <ModalOverlay bg="blackAlpha.300" backdropFilter="blur(10px) hue-rotate(90deg)" />
  );
  const [overlay, setOverlay] = React.useState(<OverlayOne />);
  const { isOpen: isOpenLogout, onOpen: onOpenLogout, onClose: onCloseLogout } = useDisclosure();
  const {
    isOpen: isOpenMobileChangePass,
    onOpen: onOpenMobileChangePass,
    onClose: onCloseMobileChangePass
  } = useDisclosure();

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300
        });
        await updateUserInfo({
          avatar: data.fileKey,
          avatarUrl: data.fileUrl
        });
        toast({
          title: '更新数据成功',
          status: 'success'
        });
        onClose();
      } catch (err: any) {
        toast({
          title: getErrText(err, t('common.error.Select avatar failed')),
          status: 'warning'
        });
      }
    },
    [onClose, t, toast, updateUserInfo]
  );

  return (
    <MyModal
      minH="100%"
      minW="100%"
      {...(!isPc && { borderRadius: '0' })}
      title={
        <>
          <Flex flex="1">
            <SvgIcon
              name="chevronLeft"
              w={rpxDim(48)}
              h={rpxDim(48)}
              cursor="pointer"
              onClick={onClose}
              alignSelf="start"
            />
          </Flex>
          <Center color="rgba(0,0,0,0.9)" fontSize={rpxDim(36)} fontWeight="600">
            设置
          </Center>
        </>
      }
      bgColor="#F9F9F9"
      isOpen
      overflow="auto"
    >
      <ModalBody>
        <Box mt={rpxDim(30)} mr={rpxDim(20)} ml={rpxDim(20)} backgroundColor="#F9F9F9">
          <Flex
            flexDir="column"
            backgroundColor="#fff"
            mb={rpxDim(32)}
            p={rpxDim(32)}
            borderRadius={rpxDim(12)}
          >
            <Flex alignItems="center" onClick={onOpenMobileChangePass}>
              <SvgIcon name="settingPassword" w={rpxDim(48)} h={rpxDim(48)} />
              <Box flex="1" fontSize={rpxDim(32)} color="rgba(0,0,0,0.9)" ml={rpxDim(24)}>
                修改密码
              </Box>

              <SvgIcon name="chevronRight" w={rpxDim(48)} h={rpxDim(48)} color="#909297" />
            </Flex>
            <Box key="divider" w="100%" h="1px" bgColor="#E7E7E7" mt={rpxDim(32)} mb={rpxDim(32)} />

            <Flex alignItems="center" onClick={onOpenSelectFile}>
              <SvgIcon name="settingAvatar" w={rpxDim(48)} h={rpxDim(48)} />
              <Box flex="1" fontSize={rpxDim(32)} color="rgba(0,0,0,0.9)" ml={rpxDim(24)}>
                修改头像
              </Box>

              <SvgIcon name="chevronRight" w={rpxDim(48)} h={rpxDim(48)} color="#909297" />
            </Flex>
          </Flex>

          <Flex
            flexDir="column"
            backgroundColor="#fff"
            pt={rpxDim(26)}
            pr={rpxDim(32)}
            pb={rpxDim(26)}
            pl={rpxDim(32)}
            borderRadius={rpxDim(12)}
          >
            <Center fontSize={rpxDim(32)} color="#1D2129" fontWeight="400" onClick={onOpenLogout}>
              退出账号
            </Center>
          </Flex>

          {isOpenLogout && (
            <Modal isCentered isOpen={true} onClose={onClose} size={'xl'}>
              {overlay}
              <ModalContent>
                <Box m={rpxDim(48)}>
                  <ModalBody>
                    <Center mb={rpxDim(48)} fontSize={rpxDim(32)} color="rgba(0,0,0,0.9)">
                      退出登录
                    </Center>
                  </ModalBody>
                  <ModalFooter>
                    <Button
                      fontSize={rpxDim(32)}
                      color="#3D7FFF"
                      mr={rpxDim(26)}
                      backgroundColor="#EDF3FF"
                      h={rpxDim(80)}
                      w={rpxDim(251)}
                      onClick={onCloseLogout}
                    >
                      取消
                    </Button>
                    <Button
                      fontSize={rpxDim(32)}
                      color="#fff"
                      w={rpxDim(251)}
                      h={rpxDim(80)}
                      onClick={() => {
                        clearToken().finally(() => {
                          router.replace('/login').finally(() => {
                            setUserInfo(null);
                          });
                        });
                      }}
                    >
                      确认
                    </Button>
                  </ModalFooter>
                </Box>
              </ModalContent>
            </Modal>
          )}
          <File onSelect={onSelectFile} />
        </Box>
      </ModalBody>

      {isOpenMobileChangePass && (
        <>
          <MobileChangePass onClose={onCloseMobileChangePass} />
        </>
      )}
    </MyModal>
  );
};
export default MobileSetting;
