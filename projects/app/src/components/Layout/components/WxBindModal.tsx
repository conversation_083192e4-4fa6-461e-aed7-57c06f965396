import React, { useEffect, useState } from 'react';
import { Box, Flex, Image } from '@chakra-ui/react';
import MyModal from '../../MyModal';
import { useSystemStore } from '@/store/useSystemStore';
import { respDims } from '@/utils/chakra';
import { getBindQRCode, getQRCodeResult, wxBind } from '@/api/auth';
import { Toast } from '@/utils/ui/toast';
import { WxTicketType } from '@/types/api/auth';

const WxBindModal = ({ onClose, onSuccess }: { onClose: () => void; onSuccess: () => void }) => {
  const { isPc } = useSystemStore();
  const [timer, setTimer] = useState(null as any);
  const [ticket, setTicket] = useState('');
  const [qrCode, setQRCode] = useState('');
  const [unionId, setUnionId] = useState('');

  const handleClose = () => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    onClose();
  };

  const wxLogin = () => {
    getBindQRCode()
      .then((res: any) => {
        if (res.ticket) {
          setQRCode(res.ticket);
          setTicket(res.ticket);
          loginQRCodeResult(res.ticket);
        }
      })
      .catch((err) => {
        Toast.error(err.message);
      });
  };

  const loginQRCodeResult = (ticket: string) => {
    getQRCodeResult(ticket).then((res) => {
      if (res) {
        handleResultData(res);
      } else {
        const newTimer = setTimeout(() => loginQRCodeResult(ticket), 2000);
        setTimer(newTimer);
      }
    });
  };

  const handleResultData = (data: WxTicketType) => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    setUnionId(data.unionid);
    wxBind(data.unionid)
      .then((res) => {
        if (res.code === 200) {
          Toast.success('操作成功');
          onSuccess();
        }
      })
      .catch((err) => {
        Toast.error(err.message);
        wxLogin();
      });
  };

  useEffect(() => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    wxLogin();
  }, []);

  return (
    <MyModal isOpen={true} onClose={handleClose} isCentered={!isPc} w="100%" minH="600px">
      <Box
        px={['20px', '5vw']}
        py={['60px', '60px']}
        backgroundSize="cover"
        backgroundRepeat="no-repeat"
        backgroundImage="/imgs/home/<USER>"
        pos="relative"
        overflowY="auto"
        overflowX="hidden"
        borderTopLeftRadius="8px"
        borderTopRightRadius="8px"
      >
        <Box
          fontSize={respDims(22, 18)}
          color="#000A3E"
          lineHeight="32px"
          fontWeight="500"
          pos="absolute"
          top="70%"
          left="50%"
          transform="translate(-50%, -50%)"
        >
          请使用微信扫码授权
        </Box>
      </Box>

      <Flex alignItems="center" justifyContent="center" mb="60px" mt="24px" flexDir="column">
        <Box
          boxShadow="0px 0px 10px 0px rgba(48,55,88,0.05), 0px 0px 28px 0px rgba(44,54,66,0.05);"
          borderRadius="15px"
          backgroundColor="#fff"
          textAlign="center"
          p="6px 10px"
        >
          {qrCode && (
            <Box>
              <Image src={`https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${qrCode}`} alt="" />
            </Box>
          )}
        </Box>
      </Flex>
    </MyModal>
  );
};

export default WxBindModal;
