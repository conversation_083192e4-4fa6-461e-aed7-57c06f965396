import React, { useCallback, useEffect, useState } from 'react';
import {
  Box,
  Flex,
  Input,
  Button,
  Image,
  Center,
  useDisclosure,
  FormControl,
  FormErrorMessage,
  Tooltip
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { clientUserGetDept, updateUser } from '@/api/user';
import { Toast } from '@/utils/ui/toast';
import { getErrText, maskPhoneNumber } from '@/utils/string';
import { respDims } from '@/utils/chakra';
import AvatarUpload from './AvatarUpload';
import { LOGO_ICON } from '@/constants/common';
import { useSelectFile } from '@/hooks/useSelectFile';
import { uploadImage } from '@/utils/file';
import dynamic from 'next/dynamic';
import OldPhoneModal from '../../OldPhoneModal';
import WxBindModal from '../../WxBindModal';
import { MessageBox } from '@/utils/ui/messageBox';
import { useUserStore } from '@/store/useUserStore';
import { useRouter } from 'next/router';
import { wxIsBindWx, wxUnBind } from '@/api/auth';
import SvgIcon from '@/components/SvgIcon';
import Loading from '@/components/Loading';

const UpdatePswModal = dynamic(() => import('../../UpdatePswModal'), {
  loading: () => <Loading fixed={false} />,
  ssr: false
});

type FormType = {
  dept: string;
  username: string;
  avatarUrl: string;
};

const PersonalCenter = () => {
  const { t } = useTranslation();
  const [refresh, setRefresh] = useState(false);
  const [activeTab, setActiveTab] = useState('personalInfo');
  const [isHovered, setIsHovered] = useState(false);
  const [isBindWx, setIsBindWx] = useState(false);
  const { userInfo, initUserInfo } = useUserStore();
  const [fileKey, setFileKey] = useState('');
  const router = useRouter();
  const tabs = [
    {
      id: 'personalInfo',
      icon: 'personal',
      label: '个人信息'
    },
    {
      id: 'accountManagement',
      icon: 'settings',
      label: '账号管理'
    }
  ];
  const {
    isOpen: isOpenUpdatePsw,
    onClose: onCloseUpdatePsw,
    onOpen: onOpenUpdatePsw
  } = useDisclosure();

  const {
    isOpen: isOpenOldPhoneModal,
    onClose: onCloseOldPhoneModal,
    onOpen: onOpenOldPhoneModal
  } = useDisclosure();

  const {
    isOpen: isOpenWxBindModal,
    onClose: onCloseWxBindModal,
    onOpen: onOpenWxBindModal
  } = useDisclosure();

  const {
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors }
  } = useForm<FormType>({
    defaultValues: {
      dept: '',
      username: userInfo?.username || '',
      avatarUrl: userInfo?.avatar || LOGO_ICON
    }
  });

  const onBindOrUnbind = () => {
    if (isBindWx) {
      MessageBox.confirm({
        title: '解绑微信',
        content: (
          <>
            <Box>您确认要解绑微信吗？</Box>
            <Box>解绑后，您可以重新绑定微信账号</Box>
          </>
        ),
        okText: '确定解绑',
        onOk: () => {
          wxUnBind().then((res) => {
            if (res && res.code === 200) {
              setIsBindWx(false);
              Toast.success('操作成功');
            }
          });
        }
      });
    } else {
      onOpenWxBindModal();
    }
  };

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300
        });
        setValue('avatarUrl', data.fileUrl);
        setFileKey(data.fileUrl);
        setRefresh((state) => !state);
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      }
    },
    [setValue, t, Toast]
  );

  const { mutate: onSubmit, isLoading } = useRequest({
    mutationFn: (data: FormType) => {
      return updateUser({
        avatar: fileKey,
        username: data.username,
        account: userInfo?.account!
      });
    },
    onSuccess() {
      initUserInfo();
    },
    successToast: '操作成功'
  });
  const getClientUserGetDept = () => {
    clientUserGetDept().then((res) => {
      setValue('dept', res.deptName);
    });
  };

  const getWxIsBindWx = () => {
    wxIsBindWx().then((res) => {
      setIsBindWx(res!);
    });
  };

  const onUpdateSuccess = () => {
    onCloseUpdatePsw();
    router.replace('/login');
  };

  const onWxBindSuccess = () => {
    onCloseWxBindModal();
    getWxIsBindWx();
  };

  useEffect(() => {
    getClientUserGetDept();
    getWxIsBindWx();
  }, []);

  return (
    <Box>
      <Box textAlign="center">
        <Tooltip label={t('common.Set Avatar')} placement="bottom">
          <Center pb="20px">
            <AvatarUpload getValues={getValues} onOpenSelectFile={onOpenSelectFile} />
          </Center>
        </Tooltip>
        <Box color="#303133" fontSize="18px" fontWeight="bold" pb="14px">
          {userInfo?.username}
        </Box>
        <Box color="#606266" fontSize="16px" pb="16px">
          账号：{userInfo?.account}
        </Box>
      </Box>
      <Flex h="380px" w="100%">
        <Flex>
          <Flex
            w="200px"
            flexDir="column"
            bg="#fff"
            p="20px 24px"
            borderRadius="20px"
            mr="20px"
            boxShadow="0px 0px 20.4px 0px rgba(0, 0, 0, 0.06)"
          >
            {tabs.map((item) => (
              <Flex
                align="center"
                cursor="pointer"
                onClick={() => setActiveTab(item.id)}
                key={item.id}
                w="160px"
                p="10px"
                borderRadius="8px"
                fontWeight="bold"
                fontSize="15px"
                {...(item.id === activeTab
                  ? {
                      color: 'primary.500',
                      bg: 'primary.50'
                    }
                  : {
                      color: '#4E5969',
                      _hover: {
                        color: '#4080FF',
                        borderColor: 'primary.50'
                      }
                    })}
              >
                {item.icon && (
                  <SvgIcon
                    name={item.icon === 'personal' ? 'personal' : 'settings'}
                    w={respDims(23)}
                    h={respDims(23)}
                    mr={respDims(10)}
                  />
                )}
                <Box>{item.label}</Box>
              </Flex>
            ))}
          </Flex>
        </Flex>

        {activeTab !== 'personalInfo' ? (
          <>
            <Flex
              flexDir="column"
              bg="#FFF"
              w="500px"
              p="32px"
              borderRadius="20px"
              lineHeight="30px"
            >
              <Flex alignItems="center" justifyContent="space-between" pb="20px">
                <Flex flexDir="column" flex="1">
                  <Box fontSize={respDims(14, 12)} color="#4E5969" fontWeight="400">
                    密码管理
                  </Box>
                  <Flex
                    w="100%"
                    alignItems="center"
                    justifyContent="space-between"
                    bg="#F2F3F5"
                    borderRadius="8px"
                    p="7px 14px 7px 12px"
                  >
                    <Box fontSize={respDims(14, 12)} color="#303133" fontWeight="400">
                      已设置
                    </Box>
                    <Button color="primary.500" variant="link" onClick={() => onOpenUpdatePsw()}>
                      更换
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
              <Flex alignItems="center" justifyContent="space-between" pb="20px">
                <Flex flexDir="column" flex="1">
                  <Box fontSize={respDims(14, 12)} color="#4E5969" fontWeight="500">
                    绑定手机号
                  </Box>
                  <Flex
                    w="100%"
                    alignItems="center"
                    bg="#F2F3F5"
                    justifyContent="space-between"
                    borderRadius="8px"
                    p="7px 14px 7px 12px"
                  >
                    <Box fontSize={respDims(14, 12)} color="#303133" fontWeight="400">
                      {maskPhoneNumber(userInfo?.phone!)}
                    </Box>
                    <Button color="#0052D9" variant="link" onClick={() => onOpenOldPhoneModal()}>
                      更换
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
              <Flex alignItems="center" justifyContent="space-between" pb="20px">
                <Flex flexDir="column" flex="1">
                  <Box fontSize={respDims(14, 12)} color="#4E5969" fontWeight="400">
                    微信绑定
                  </Box>
                  <Flex
                    w="100%"
                    alignItems="center"
                    justifyContent="space-between"
                    bg="#F2F3F5"
                    borderRadius="8px"
                    p="12px 14px 12px 12px"
                  >
                    <Center>
                      <Image
                        w={respDims(32)}
                        h={respDims(32)}
                        borderRadius="50%"
                        src={userInfo?.avatar || LOGO_ICON}
                        alt=""
                        mr="14px"
                        objectFit="cover"
                      />
                      <Box>{userInfo?.username}</Box>
                    </Center>
                    <Button color="#0052D9" variant="link" onClick={() => onBindOrUnbind()}>
                      {isBindWx ? '解绑' : '绑定'}
                    </Button>
                  </Flex>
                </Flex>
              </Flex>
            </Flex>
          </>
        ) : (
          <>
            <Flex
              flexDir="column"
              bg="#FFF"
              w="500px"
              p="32px"
              borderRadius="20px"
              boxShadow="0px 0px 20.4px 0px rgba(0, 0, 0, 0.06)"
            >
              <FormControl isInvalid={!!errors.username} pb="20px">
                <Flex flexDir="column">
                  <Box fontSize={respDims(14, 12)} color="#4E5969" fontWeight="400" pb="4px">
                    姓名
                  </Box>
                  <Center>
                    <Input
                      placeholder="请输入姓名"
                      bg="#F2F3F5"
                      borderRadius="8px"
                      flex={1}
                      {...register('username', { required: '请输入姓名' })}
                    />
                  </Center>
                  <Box ml="70px">
                    <FormErrorMessage>
                      {errors.username && errors.username.message}
                    </FormErrorMessage>
                  </Box>
                </Flex>
              </FormControl>

              <FormControl pb="40px">
                <Flex flexDir="column">
                  <Box fontSize={respDims(14, 12)} color="#4E5969" fontWeight="400" pb="4px">
                    所属部门
                  </Box>
                  <Center>
                    <Input
                      placeholder="请输入所属部门"
                      bg="#F2F3F5"
                      borderRadius="8px"
                      flex={1}
                      disabled={true}
                      {...register('dept')}
                    />
                  </Center>
                </Flex>
              </FormControl>

              <Box display="flex" justifyContent="flex-end">
                <Button
                  w="80px"
                  isLoading={isLoading}
                  onClick={handleSubmit((data) => onSubmit(data))}
                >
                  保存
                </Button>
              </Box>
            </Flex>
          </>
        )}
      </Flex>

      {isOpenUpdatePsw && (
        <UpdatePswModal onSuccess={() => onUpdateSuccess()} onClose={onCloseUpdatePsw} />
      )}
      {isOpenOldPhoneModal && <OldPhoneModal onClose={onCloseOldPhoneModal} />}
      {isOpenWxBindModal && (
        <WxBindModal onClose={onCloseWxBindModal} onSuccess={() => onWxBindSuccess()} />
      )}

      <File onSelect={onSelectFile} />
    </Box>
  );
};

export default PersonalCenter;
