import React, { useEffect, useState } from 'react';
import {
  ModalBody,
  Box,
  Flex,
  Input,
  ModalFooter,
  Button,
  FormControl,
  FormErrorMessage,
  Center
} from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import {
  getClientUserSmsCode,
  getClientUserValid,
  getUpdateUserPhoneNum,
  getWxBindUserLogin
} from '@/api/user';
import { Toast } from '@/utils/ui/toast';
import { respDims } from '@/utils/chakra';
import { useRouter } from 'next/router';
import { setToken } from '@/utils/auth';
import { getTokenLogin } from '@/api/auth';
import { useUserStore } from '@/store/useUserStore';

type FormType = {
  phone: string;
  code: string;
};

const ReplacePhoneModal = ({
  onClose,
  unionId,
  title,
  isBindPhone
}: {
  onClose: () => void;
  unionId?: string;
  title?: string;
  isBindPhone?: boolean;
}) => {
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState(0);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const router = useRouter();
  const { lastRoute = '' } = router.query as { lastRoute: string };
  const { userInfo, setUserInfo } = useUserStore();
  const {
    register,
    handleSubmit,
    trigger,
    getValues,
    formState: { errors }
  } = useForm<FormType>({
    defaultValues: {
      phone: '',
      code: ''
    }
  });

  const onSendCode = async () => {
    const isPhoneValid = await trigger('phone');
    if (!isPhoneValid) {
      return;
    }
    const phoneNumber = getValues('phone');

    try {
      const data = await getClientUserSmsCode(
        title ? 2 : 6,
        title ? phoneNumber : userInfo?.phone!,
        title ? undefined : phoneNumber
      );
      if (data && data.code === 200) {
        setIsCodeSent(true);
        setCountdown(60);
      } else {
        Toast.error('发送验证码失败，请重试。');
      }
    } catch (error) {}
  };

  const { mutate: onSubmit, isLoading } = useRequest({
    mutationFn: (data: FormType) => {
      return getClientUserValid(title ? 2 : 6, data.phone, Number(data.code));
    },
    onSuccess: async () => {
      try {
        if (title) {
          const res = await getWxBindUserLogin({ phone: getValues('phone'), unionId: unionId! });
          if (res) {
            setToken('Bearer ' + res);
            const data = await getTokenLogin();
            if (data) {
              setUserInfo(data);
              setTimeout(() => {
                router.push(lastRoute ? decodeURIComponent(lastRoute) : '/');
              }, 300);
              Toast.success('登录成功');
              onClose();
            }
          }
        } else {
          const data = await getUpdateUserPhoneNum({ bizType: 6, mobile: getValues('phone') });
          if (data) {
            Toast.success('手机号修改成功');
            setTimeout(() => {
              router.replace('/login');
            }, 1000);
          }
        }
      } catch (error) {
        // 处理错误
        console.error('Error:', error);
      }
    }
  });

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    } else if (countdown === 0 && isCodeSent) {
      setIsCodeSent(false);
    }
    return () => clearInterval(timer!);
  }, [countdown, isCodeSent]);

  return (
    <MyModal
      closeOnOverlayClick={false}
      isOpen
      onClose={onClose}
      title={title ? title : '更换手机号'}
    >
      <ModalBody>
        {isBindPhone && (
          <Box m="12px 0 20px 0" color="#7f7f7f" fontSize={respDims(14, 12)}>
            当前微信未绑定手机号，请绑定登录账号
          </Box>
        )}
        <FormControl isInvalid={!!errors.phone}>
          <Flex flexDir="column" position="relative">
            <Center>
              <Box
                flex={'0 0 40px'}
                fontSize={respDims(14, 12)}
                color="#1D2129"
                fontWeight="400"
                position="absolute"
                left="16px"
                top="11px"
                zIndex="999"
              >
                +86
              </Box>
              <Input
                placeholder="请输手机号"
                flex={1}
                pl="52px"
                _placeholder={{
                  color: '#86909C'
                }}
                type={'text'}
                disabled={isCodeSent}
                {...register('phone', {
                  required: '请输手机号',
                  pattern: {
                    value: /^1[3-9]\d{9}$/,
                    message: '请输入正确的手机号'
                  }
                })}
              />
            </Center>
            <Box ml="40px">
              <FormErrorMessage>{errors.phone && errors.phone.message}</FormErrorMessage>
            </Box>
          </Flex>
        </FormControl>

        <FormControl isInvalid={!!errors.code}>
          <Flex flexDir="column" mt={5} pos="relative">
            <Center>
              <Input
                flex={1}
                type={'text'}
                placeholder="请输入验证码"
                _placeholder={{
                  color: '#86909C'
                }}
                {...register('code', {
                  required: '请输入验证码',
                  maxLength: {
                    value: 20,
                    message: '密码最少 4 位最多 20 位'
                  }
                })}
              ></Input>
              <Button
                pos="absolute"
                zIndex="999"
                variant="link"
                right="16px"
                color="#0052D9"
                onClick={() => {
                  countdown <= 0 && onSendCode();
                }}
                disabled={countdown > 0}
              >
                {countdown > 0 ? `${countdown}s` : '发送验证码'}
              </Button>
            </Center>
            <Box>
              <FormErrorMessage>{errors.code && errors.code.message}</FormErrorMessage>
            </Box>
          </Flex>
        </FormControl>
      </ModalBody>
      <ModalFooter>
        <Button mr={3} variant={'grayBase'} onClick={onClose}>
          取消
        </Button>
        <Button isLoading={isLoading} onClick={handleSubmit((data) => onSubmit(data))}>
          确认
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default ReplacePhoneModal;
