import SvgIcon from '@/components/SvgIcon';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useRoutes } from '@/hooks/useRoutes';
import { traverseRoutes } from '@/routes';
import { useUserStore } from '@/store/useUserStore';
import { clearToken } from '@/utils/auth';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex, Image } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import UserInfo from '../UserInfo';
import { Toast } from '@/utils/ui/toast';
import MyMenu from '@/components/MyMenu';
import { ReactNode } from 'react';
import { DimsMinScale } from '../../Sidebar/constants';
import { CaretDownOutlined } from '@ant-design/icons';

const UserMenu = ({ isCollapsed, ...props }: { isCollapsed?: boolean } & ChakraProps) => {
  const router = useRouter();

  const { userInfo, setUserInfo } = useUserStore();

  const { routeGroup, routeGroupMap } = useRoutes();

  const adminRouteGroup = routeGroupMap[RouteGroupTypeEnum.Admin];

  const { openOverlay } = useOverlayManager();

  const menus: { label: string; icon: ReactNode; onClick: () => void }[] = [
    ...(isCollapsed && routeGroup?.type === RouteGroupTypeEnum.Admin
      ? [
          {
            label: 'AI平台',
            icon: <SvgIcon name="navHomeLine" />,
            onClick: () => {
              router.push('/home');
            }
          }
        ]
      : []),
    ...(isCollapsed &&
    routeGroup?.type === RouteGroupTypeEnum.Chat &&
    adminRouteGroup.navRoutes.length
      ? [
          {
            label: `${'组织'}管理`,
            icon: <SvgIcon name="navSchoolLine" />,
            onClick: () => {
              const path = traverseRoutes(adminRouteGroup?.navRoutes, (it) => it.path);
              path && router.push(path);
            }
          }
        ]
      : []),
    {
      label: '个人中心',
      icon: <SvgIcon name="user" />,
      onClick: () => {
        openOverlay({
          name: 'userInfo',
          Overlay: UserInfo,
          props: {}
        });
      }
    },
    {
      label: '退出登录',
      icon: <SvgIcon name="power" />,
      onClick: () => {
        clearToken().finally(() => {
          router.replace('/login').finally(() => {
            setUserInfo(null);
            Toast.success('退出成功');
          });
        });
      }
    }
  ];

  return (
    <Flex align="center" justify="space-between" {...props}>
      <Box flex="1" overflow="hidden">
        <MyMenu
          offset={[10, 10]}
          menuList={menus}
          Button={
            <Flex align="center" display="flex" cursor="pointer">
              <Image
                w={respDims('32fpx')}
                h={respDims('32fpx')}
                borderRadius="50%"
                src={userInfo?.avatar || '/imgs/layout/avatar.svg'}
                alt=""
                objectFit="cover"
              />

              {!isCollapsed && (
                <Box
                  ml={respDims(10)}
                  mr={respDims(4)}
                  maxWidth="7ch"
                  whiteSpace="nowrap"
                  overflow="hidden"
                  color="#3B4143"
                  fontSize="14px"
                  textOverflow="ellipsis"
                >
                  {userInfo?.username}
                </Box>
              )}
              <CaretDownOutlined style={{ color: '#ACB0B0' }} />
            </Flex>
          }
        />
      </Box>
    </Flex>
  );
};

export default UserMenu;
