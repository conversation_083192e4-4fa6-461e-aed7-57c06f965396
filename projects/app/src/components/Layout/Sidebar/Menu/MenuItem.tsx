import React, { useMemo, useState } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { MenuItemProps, MenuItemType } from './type';
import AdvIcon from '@/components/AdvIcon';

interface ExtendedMenuItemProps extends MenuItemProps {
  level?: number;
}

const MenuItem: React.FC<ExtendedMenuItemProps> = ({
  level = 0,
  item,
  activeKey,
  activePathKeys,
  expandedKeys,
  onExpand,
  onClickItem
}) => {
  const isActive = item.key === activeKey;

  const hasActiveChild = useMemo(
    () => !!activePathKeys?.includes(item.key),
    [activePathKeys, item.key]
  );

  const isExpanded = useMemo(() => expandedKeys.includes(item.key), [expandedKeys, item.key]);

  const hasChildren = !!item.children?.length;

  const [childClicked, setChildClicked] = useState(false);
  const [activeChildKey, setActiveChildKey] = useState<string | null>(null);

  const icon =
    level === 0
      ? hasActiveChild
        ? item.activeIcon
        : item.inactiveIcon
      : isActive
        ? item.activeIcon
        : item.inactiveIcon;

  // 根据层级设置颜色和字体大小
  const textColor =
    level === 0
      ? hasActiveChild
        ? '#36F'
        : '#000000'
      : isActive
        ? '#36F'
        : hasActiveChild
          ? '#1D2129'
          : '#7D7B7B';
  const fontSize = level === 0 ? respDims(18, 14) : respDims(16, 14);

  const parentColor =
    childClicked || (level == 0 && !item.children && isActive) ? '#36F' : textColor;

  const handleItemClick = (clickedItem: MenuItemType) => {
    if (level === 0) {
      onClickItem?.(item);
    }
    if (hasChildren) {
      onExpand(
        isExpanded ? expandedKeys.filter((key) => key !== item.key) : [...expandedKeys, item.key]
      );
      setChildClicked(false);
    } else {
      onClickItem?.(clickedItem);
      setChildClicked(false);
      setActiveChildKey(item.key);
    }
  };

  return (
    <>
      <Flex
        pl="10px"
        py={respDims(12)}
        mt={respDims(4, 2)}
        alignItems="center"
        fontSize={fontSize}
        w="100%"
        borderRadius={respDims(8)}
        cursor="pointer"
        bgColor={isActive || (activeChildKey == activeKey && level !== 0) ? '#FFF' : 'transparent'}
        fontWeight={isActive || (activeChildKey == activeKey && level !== 0) ? '500' : '400'}
        color={parentColor}
        _hover={{
          bgColor: '#FFF'
        }}
        onClick={() => handleItemClick(item)}
      >
        {!!icon && <AdvIcon name={icon} w="28px" h="28px" />}

        <Box ml={respDims(12)} whiteSpace="nowrap">
          {item.name}
        </Box>

        {hasChildren && (
          <SvgIcon
            name="chevronDown"
            w={respDims(16, 14)}
            h={respDims(16, 14)}
            ml="auto"
            mr="10px"
            style={{
              transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease'
            }}
          />
        )}
      </Flex>

      {isExpanded && hasChildren && (
        <Box>
          {item.children!.map((childItem: MenuItemType) => (
            <MenuItem
              key={childItem.key}
              level={level + 1} // 递增层级
              item={childItem}
              activeKey={activeKey}
              activePathKeys={activePathKeys}
              expandedKeys={expandedKeys}
              onExpand={onExpand}
              onClickItem={(item) => {
                onClickItem?.(item);
                console.log(activeKey, item.key, 'activeKey');

                if (activeKey === item.key) {
                  setChildClicked(true);
                  setActiveChildKey(item.key);
                } else {
                  setChildClicked(false);
                }
              }}
            />
          ))}
        </Box>
      )}
    </>
  );
};

export default MenuItem;
