import React, { useContext, useMemo } from 'react';
import { useRoutes } from '@/hooks/useRoutes';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { useSystemStore } from '@/store/useSystemStore';
import { Drawer } from 'antd';
import { rpxDim } from '@/utils/chakra';
import styles from '@/pages/index.module.scss';
import { LayoutContext } from '../../LayoutProvider';
import MenuSidebar from './MenuSidebar';

const Sidebar = () => {
  const { routeGroup } = useRoutes();

  const { isPc } = useSystemStore();

  const { isOpenSidebar, closeSidebar } = useContext(LayoutContext);

  const RouteSidebar = useMemo(() => {
    if (routeGroup?.type) {
      return MenuSidebar;
    }

    return undefined;
  }, [routeGroup?.type]);

  if (!RouteSidebar) {
    return null;
  }

  return isPc ? (
    <RouteSidebar />
  ) : (
    <Drawer
      placement="left"
      open={isOpenSidebar}
      closable={false}
      onClose={closeSidebar}
      width={rpxDim(576)}
      rootClassName={styles['layout-sidebar-drawer']}
      style={{
        width: 'auto',
        borderTopRightRadius: rpxDim(40),
        borderBottomRightRadius: rpxDim(40)
      }}
      styles={{
        body: {
          padding: 0
        }
      }}
    >
      <RouteSidebar />
    </Drawer>
  );
};

export default Sidebar;
