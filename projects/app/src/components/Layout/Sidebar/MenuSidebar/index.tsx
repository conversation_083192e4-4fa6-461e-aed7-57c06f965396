import { Box, Flex } from '@chakra-ui/react';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import { useSystemStore } from '@/store/useSystemStore';
import { LayoutContext } from '../../../LayoutProvider';
import { respDims } from '@/utils/chakra';
import { useRoutes } from '@/hooks/useRoutes';
import { traverseRouteTop } from '@/routes';
import { MenuItemType } from '../Menu/type';
import Menu from '../Menu';
import { DimsMinScale } from '../constants';
const MenuSidebar = () => {
  const router = useRouter();

  const { isPc } = useSystemStore();

  const { closeSidebar } = useContext(LayoutContext);

  const { routeGroup } = useRoutes();

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const items: MenuItemType[] = routeGroup?.navRoutes || [];

  const { activeKey, activePathKeys } = useMemo(() => {
    const activeKey = routeGroup?.navActiveRoute?.key;
    if (!activeKey) {
      return {};
    }

    const activePathKeys: string[] = [];
    traverseRouteTop(routeGroup.navActiveRoute!, (it) => {
      activePathKeys.unshift(it.key);
    });

    return {
      activeKey,
      activePathKeys
    };
  }, [routeGroup?.navActiveRoute]);
  const onClickMenuItem = (nav: MenuItemType) => {
    nav.path && router.push(nav.path);
    !isPc && closeSidebar();
  };

  useEffect(() => {
    activePathKeys &&
      setExpandedKeys((state) => {
        const keys = activePathKeys.filter((it) => it !== activeKey && !state.includes(it));
        return keys.length ? [...state, ...keys] : state;
      });
  }, [activeKey, activePathKeys]);

  return (
    <Flex flexDir="column" w={respDims('576rpx', 180, DimsMinScale)} h="100%" tabIndex={0}>
      <Box flex="1 0 0" overflowX="hidden" overflowY="scroll" pt={respDims(10, DimsMinScale)}>
        <Menu
          groups={[{ name: '', items }]}
          activeKey={activeKey}
          activePathKeys={activePathKeys}
          expandedKeys={expandedKeys}
          onExpand={(keys) => setExpandedKeys(keys)}
          onClickItem={onClickMenuItem}
        />
      </Box>
    </Flex>
  );
};

export default React.memo(MenuSidebar);
