import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import { useQuery } from '@tanstack/react-query';
import { useRoutes } from '@/hooks/useRoutes';
import { useContext, useEffect } from 'react';
import { Toast } from '@/utils/ui/toast';
import { Box, BoxProps } from '@chakra-ui/react';
import { LayoutContext } from '../../LayoutProvider';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { respDims } from '@/utils/chakra';
import { thirdLogin } from '@/api/auth';
import { UserInfoType } from '@/types/store/useUserStore';
import { LoginRes } from '@/types/api/auth';
import { clearToken, setToken } from '@/utils/auth';

const Content = ({
  unauth,
  children,
  ...props
}: { unauth: boolean; children: JSX.Element } & BoxProps) => {
  const router = useRouter();

  const { userInfo, initUserInfo, setUserInfo } = useUserStore();

  const { routeGroup, isAccessDenied } = useRoutes();

  const { setContentSize } = useContext(LayoutContext);

  const { setElement, offsetWidth, offsetHeight } = useResizeObserver();

  const { code, orgCode } = router.query; // 获取 URL 参数

  useQuery(
    [router.pathname, code, orgCode], // 添加 code 和 orgCode 到依赖数组
    async () => {
      console.log(unauth, userInfo);

      if (code && orgCode && typeof code === 'string' && typeof orgCode === 'string') {
        try {
          const res = await thirdLogin({ code, orgCode });
          setUserInfo(res);
          setToken(res.accessToken);

          // 移除 URL 中的 code 和 orgCode 参数
          const { pathname, query } = router;
          delete query.code;
          delete query.orgCode;
          await router.replace({ pathname, query }, undefined, { shallow: true });

          return res;
        } catch (error) {
          console.error('Third-party login failed:', error);
          Toast.error('第三方登录失败');
          clearToken().finally(() => {
            router.replace('/login').finally(() => {
              setUserInfo(null);
            });
          });
          router.replace('/login');
          return Promise.reject('第三方登录失败');
        }
      } else {
        return initUserInfo();
      }
    },
    {
      enabled: !unauth && !userInfo,
      onSuccess(res: LoginRes | UserInfoType) {},
      onError() {
        if (!code || !orgCode) {
          router.replace(
            `/login?lastRoute=${encodeURIComponent(location.pathname + location.search)}`
          );
          Toast.warning('请先登录');
        }
      }
    }
  );
  useEffect(() => {
    setContentSize(
      offsetWidth && offsetHeight
        ? {
            width: offsetWidth,
            height: offsetHeight
          }
        : undefined
    );
  }, [offsetWidth, offsetHeight, setContentSize]);

  useEffect(() => {
    if (isAccessDenied) {
      Toast.warning('当前页面没有权限');
      router.push('/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAccessDenied]);

  return (
    <Box
      ref={setElement}
      h="100%"
      overflow="hidden"
      {...(routeGroup?.type === RouteGroupTypeEnum.Admin && { pl: respDims(19) })}
      {...props}
    >
      {unauth || (!!userInfo && !isAccessDenied) ? children : null}
    </Box>
  );
};

export default Content;
