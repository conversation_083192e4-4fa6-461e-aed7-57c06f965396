import React from 'react';
import { Box, Flex, Image } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useLoading } from '@/hooks/useLoading';
import { useSystemStore } from '@/store/useSystemStore';

import Content from './Content';

import Sidebar from './Sidebar';
import MobileNavbar from '../MobileNavbar';
import { respDims } from '@/utils/chakra';
import { useTenantStore } from '@/store/useTenantStore';
import Footer from './Sidebar/Footer';

type LayoutConfigType = {
  fullpage?: boolean;
  hideSidebar?: boolean;
  unauth?: boolean;

  // 移动端配置
  mobile?: {
    autoback?: boolean;
    sidebarButton?: boolean;
    title?: string;
  };
};

const layoutConfigs: Record<string, LayoutConfigType> = {
  '/login': { fullpage: true, unauth: true },
  '/thirdlogin/qywx': { fullpage: true, unauth: true },
  '/thirdlogin/ding': { fullpage: true, unauth: true },
  '/thirdlogin/qywxauth': { fullpage: true, unauth: true }
};

const Layout = ({ children }: { children: JSX.Element }) => {
  const router = useRouter();

  const { Loading } = useLoading();

  const { loading, isPc } = useSystemStore();

  const { tenant } = useTenantStore();

  const layoutConfig = layoutConfigs[router.pathname];

  if (isPc === undefined) {
    return <></>;
  }

  return (
    <>
      <Flex flexDir="column" h="100%" bgColor="#f8fafa">
        {!isPc && layoutConfig?.mobile && (
          <MobileNavbar
            autoback={layoutConfig.mobile?.autoback}
            sidebarButton={layoutConfig.mobile?.sidebarButton}
            title={layoutConfig.mobile?.title}
          />
        )}

        <Flex
          flex="1"
          overflow="hidden"
          bgImage="/evaluate/imgs/evaluation_bg.png"
          bgSize="100% 100%"
          flexDir="column"
          bgRepeat="no-repeat"
          p={!(layoutConfig?.fullpage || layoutConfig?.hideSidebar) ? '6px 0 0 24px' : '0'}
          h="75px"
        >
          {!(layoutConfig?.fullpage || layoutConfig?.hideSidebar) && (
            <Flex alignItems="center">
              <Flex flex="1" alignItems="center" h="100%">
                <Image
                  src={tenant?.avatarUrl}
                  h={respDims(48)}
                  w="auto"
                  maxW="100%"
                  objectFit="contain"
                  alt=""
                />
                <Box w="2px" m="0 12px" borderRight="1px solid #E1E1E1" h="60%" />
                <Box
                  color="#303133"
                  fontSize="24px"
                  fontWeight="500"
                  lineHeight={respDims('20fpx')}
                  textAlign="center"
                  whiteSpace="pre-wrap"
                >
                  {tenant?.fullName || tenant?.name} · AI评价系统
                </Box>
              </Flex>

              <Footer flexShrink={0} />
            </Flex>
          )}
          <Flex>
            {!(layoutConfig?.fullpage || layoutConfig?.hideSidebar) && <Sidebar />}

            <Content
              flex="1"
              h="calc(100vh - 56px)"
              overflow="auto"
              unauth={layoutConfig?.unauth === true}
            >
              {children}
            </Content>
          </Flex>
        </Flex>
      </Flex>

      <Loading loading={loading} zIndex={999999} />
    </>
  );
};

export default Layout;
