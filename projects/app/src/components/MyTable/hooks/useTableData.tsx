import { useQueryList } from '@/hooks/useQueryList';
import { useQueryPage } from '@/hooks/useQueryPage';
import { RequestPageParams, PagingData } from '@/types';

type TPartQuery<T> = Omit<T, keyof RequestPageParams>;

interface UseTableDataProps<TData, TError, TQuery> {
  key?: string;
  defaultQuery?: TQuery;
  enabled?: boolean;
  api: (params: TQuery) => Promise<PagingData<TData> | TData[]>;
  onSuccess?: (data: TData[] | PagingData<TData>) => void;
  onError?: (err: TError) => void;
  showPaginate?: boolean;
  defaultCurrent?: number | (() => number);
  defaultSize?: number | (() => number);
}

interface UseTableDataResult<TData, TQuery> {
  query?: TQuery | TPartQuery<TQuery>;
  data: TData[];
  isFetching: boolean;
  isFetched: boolean;
  isError: boolean;
  setQuery: (query?: TQuery) => void;
  refetch: () => void;
  current?: number;
  size?: number;
  total?: number;
  setCurrent?: (current: number) => void;
  setSize?: (size: number) => void;
}

export function useTableData<
  TData = any,
  TError = unknown,
  TQuery extends RequestPageParams = RequestPageParams
>({
  key,
  defaultQuery,
  enabled = true,
  api,
  onSuccess,
  onError,
  showPaginate = false,
  defaultCurrent = 1,
  defaultSize = 10
}: UseTableDataProps<TData, TError, TQuery>): UseTableDataResult<TData, TQuery> {
  if (showPaginate) {
    let funApi = api as (params: TQuery) => Promise<PagingData<TData>>;
    const {
      current,
      size,
      query,
      total,
      data,
      isFetching,
      isFetched,
      isError,
      setCurrent,
      setSize,
      setQuery,
      refetch
    } = useQueryPage<TData, TError, TQuery>({
      key,
      defaultCurrent,
      defaultSize,
      defaultQuery,
      enabled,
      api: funApi,
      onSuccess,
      onError
    });

    return {
      current,
      size,
      query,
      total,
      data,
      isFetching,
      isFetched,
      isError,
      setCurrent,
      setSize,
      setQuery,
      refetch
    };
  } else {
    let funApi = api as (params: TQuery) => Promise<TData[]>;

    const { query, data, isFetching, isFetched, isError, setQuery, refetch } = useQueryList<
      TData,
      TError,
      TQuery
    >({
      key,
      defaultQuery,
      enabled,
      api: funApi,
      onSuccess,
      onError
    });

    return {
      query,
      data,
      isFetching,
      isFetched,
      isError,
      setQuery,
      refetch
    };
  }
}
