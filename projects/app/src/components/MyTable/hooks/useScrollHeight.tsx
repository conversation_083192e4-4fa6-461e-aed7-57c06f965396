import { useState, useEffect } from 'react';
import { useResizeObserver } from '@/hooks/useResizeObserver';

export const useScrollHeight = (dataSource: any[]) => {
  const [headerHeight, setHeaderHeight] = useState(0);
  const { setElement, offsetHeight: containerHeight, element } = useResizeObserver();
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const headerElement = document.querySelector('.ant-table-thead');
    if (headerElement) {
      setHeaderHeight(headerElement.clientHeight);
    }
  }, [dataSource, containerHeight, element]);

  useEffect(() => {
    setScrollY((containerHeight || 500) - headerHeight);
  }, [dataSource, containerHeight, headerHeight]);

  return { scrollY, setElement };
};
