import { PagingData, RequestPageParams } from '@/types';
import { BoxProps } from '@chakra-ui/react';
import { DragEndEvent } from '@dnd-kit/core';
import type { TableProps } from 'antd';

export interface PageConfig {
  showPaginate?: boolean;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  defaultCurrent?: number;
  defaultSize?: number;
}

export interface SearchBarProps<TQuery = any, TData = any> {
  onSearch?: (query: TQuery) => void;
  query: TQuery | undefined | TPartQuery<TQuery>;
  defaultQuery?: TQuery;
  tableInstance: MyTableRef<TQuery, TData>; // Add tableInstance prop
}

export type HeaderComponentProps<TQuery = any, TData = any> = {} & SearchBarProps<TQuery, TData>;
export interface FooterComponentProps<TQuery = any, TData = any> {
  PageRender: () => JSX.Element;
  total: number | undefined;
  tableInstance: MyTableRef<TQuery, TData>; // Add tableInstance prop
}

export interface DragConfig<T = any> {
  enabled: boolean;
  onDragEnd?: (
    event: DragEndEvent,
    newDataSource: T[],
    updateLists: { parent?: T; addedData?: T; removedData?: T; list: T[] }[]
  ) => void;
  rowKey: PropertyKey;
  allowCrossLevelDrag?: boolean;
  columnTitle?: string;
  columnWidth?: number;
}

export interface MyTableProps<TQuery = any, TData = any, TApiReturn = PagingData<TData> | TData[]>
  extends TableProps<TData> {
  api?: (params: TQuery) => Promise<any>;
  columns: TableProps<TData>['columns'];
  defaultQuery?: TQuery;
  headerConfig?: {
    HeaderComponent?: React.ComponentType<HeaderComponentProps<TQuery, TData>>;
    showHeader?: boolean;
    showIfEmpty?: boolean;
    SearchComponent?: React.ComponentType<SearchBarProps<TQuery, TData>>;
    ButtonsComponent?: React.ComponentType<any>;
    headerStyle?: BoxProps;
  };
  FooterComponent?: React.ComponentType<FooterComponentProps<TQuery, TData>>;
  tableTitle?: string;
  cacheKey?: string;
  pageConfig?: PageConfig;
  boxStyle?: BoxProps;
  tableWrapperStyle?: BoxProps;
  footerStyle?: BoxProps;
  dragConfig?: DragConfig<TData>;
  emptyConfig?: {
    EmptyComponent?: React.ComponentType<any>;
    emptyText?: string;
    EmptyPicComponent?: React.ComponentType<any>;
  };
  queryConfig?: {
    enabled?: boolean;
    onSuccess?: (data: TApiReturn) => void;
  };
  scrollMode?: 'calc' | 'sticky';
}

type TPartQuery<T> = Omit<T, keyof RequestPageParams>;

export interface MyTableRef<TQuery = any, TData = any> {
  reload: () => void;
  setQuery: (newQuery: TQuery) => void;
  current: number | undefined;
  size: number | undefined;
  query: TQuery | undefined | TPartQuery<TQuery>;
  total: number | undefined;
  data: TData[];
  isFetching: boolean;
  setCurrent: ((page: number) => void) | undefined;
  setSize: ((size: number) => void) | undefined;
  scrollToBottom: () => void;
}
