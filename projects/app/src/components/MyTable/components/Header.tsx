// Header.tsx
import React from 'react';
import { Box, BoxProps, Flex } from '@chakra-ui/react';
import { HeaderComponentProps, MyTableRef } from '../types';
import { AnyObject } from 'antd/lib/_util/type';
import { RequestPageParams } from '@/types';
type TPartQuery<T> = Omit<T, keyof RequestPageParams>;

interface HeaderProps<TQuery = any, TData = any> {
  title?: string;
  tableInstance: MyTableRef<TQuery, TData>;
  headerStyle?: BoxProps;
  onSearch?: (params: TQuery) => void;
  query: TQuery | undefined | TPartQuery<TQuery>;
  HeaderComponent?: React.ComponentType<HeaderComponentProps<TQuery, TData>>;
  defaultQuery?: TQuery;
  SearchComponent?: React.ComponentType<any>;
  ButtonsComponent?: React.ComponentType<any>;
}

const Header = <TQuery extends AnyObject = any, TData = any>({
  title,
  onSearch,
  query,
  HeaderComponent,
  SearchComponent,
  ButtonsComponent,
  defaultQuery,
  tableInstance,
  headerStyle
}: HeaderProps<TQuery, TData>) => {
  return (
    <Flex alignItems="center" w="100%" mb="16px" whiteSpace="nowrap" {...headerStyle}>
      {HeaderComponent ? (
        <HeaderComponent
          onSearch={onSearch}
          query={query}
          defaultQuery={defaultQuery}
          tableInstance={tableInstance}
        />
      ) : (
        <>
          <Box mr="auto" color="primary.500" fontSize="16px" fontWeight="bold">
            {title}
          </Box>
          {SearchComponent && (
            <SearchComponent
              onSearch={onSearch}
              query={query}
              defaultQuery={defaultQuery}
              tableInstance={tableInstance}
            />
          )}
          {ButtonsComponent && <ButtonsComponent />}
        </>
      )}
    </Flex>
  );
};

export default Header;
