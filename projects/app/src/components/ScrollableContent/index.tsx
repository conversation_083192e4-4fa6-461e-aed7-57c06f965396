import React, { useRef } from 'react';
import BoardPane from '@/components/BoardPane';
import { useHorizontalScroll } from '@/hooks/useHorizontalScroll';
import { Box, Flex, Text } from '@chakra-ui/react';

interface Item {
  header?: string;
  content?: string;
  slot?: string;
  padding?: string;
  [key: string]: any;
}

interface ScrollableContentProps {
  items: Item[];
  itemWidth?: string;
  children?: React.ReactNode;
  renderSlot?: (item: Item) => React.ReactNode;
}

const ScrollableContent: React.FC<ScrollableContentProps> = ({
  items,
  itemWidth = '100%',
  children,
  renderSlot
}) => {
  const scrollbarRef = useRef<HTMLDivElement>(null);
  useHorizontalScroll(scrollbarRef);

  return (
    <Box ref={scrollbarRef} height="100%" overflowX="auto">
      <Flex height="100%">
        {items?.map((item, index) => (
          <Box
            key={index}
            flexShrink={0}
            width={itemWidth}
            boxShadow="none"
            borderRadius="8px"
            textAlign="left"
            ml={index !== 0 ? '12px' : '0'}
          >
            <BoardPane
              height="100%"
              titleFontSize="14px"
              title={item.header}
              backgroundColor="#f8f8f8"
              padding={item.padding || '10px'}
            >
              {item.slot && renderSlot ? (
                renderSlot(item)
              ) : children ? (
                children
              ) : (
                <Text
                  fontFamily="PingFang SC"
                  fontWeight={400}
                  fontSize="14px"
                  color="#4e5969"
                  lineHeight="22px"
                >
                  {item.content}
                </Text>
              )}
            </BoardPane>
          </Box>
        ))}
      </Flex>
    </Box>
  );
};

export default ScrollableContent;
