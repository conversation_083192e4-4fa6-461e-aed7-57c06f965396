import { isSvgIconName } from '../SvgIcon/utils';
import { Image, ImageProps } from '@chakra-ui/react';
import SvgIcon, { SvgIconProps } from '../SvgIcon';
import { SvgIconNameType } from '../SvgIcon/data';

type CommonKeys<T, U> = keyof T & keyof U;

type CommonProperties<T, U> = {
  [K in CommonKeys<T, U>]?: T[K] & U[K];
};

export type AdvIconProps = CommonProperties<SvgIconProps, ImageProps>;

const AdvIcon = ({
  name,
  w = '16px',
  h = '16px',
  alt,
  ...props
}: { name: SvgIconNameType | string; alt?: string } & AdvIconProps) => {
  return isSvgIconName(name) ? (
    <SvgIcon name={name} w={w} h={h} {...props} />
  ) : (
    <Image src={name} w={w} h={h} alt={alt} {...props} />
  );
};

export default AdvIcon;
