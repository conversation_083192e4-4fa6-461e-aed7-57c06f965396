import React from 'react';
import { useRouter } from 'next/router';
import { Box, type BoxProps } from '@chakra-ui/react';
import MyBox from '../common/MyBox';

const TenantPageContainer = ({
  children,
  pageBgColor,
  ...props
}: BoxProps & { pageBgColor?: string }) => {
  return (
    <Box w="100%" h="100%" bgColor={pageBgColor || '#ffffff'} borderRadius="20px" {...props}>
      {children}
    </Box>
  );
};

const DefaultPageContainer = ({
  children,
  isLoading,
  insertProps = {},
  ...props
}: BoxProps & { isLoading?: boolean; insertProps?: BoxProps; pageBgColor?: string }) => {
  return (
    <MyBox h={'100%'} pt={[0, '72px']} pr={[0, '16px']} pb={[0, '16px']} {...props}>
      <MyBox
        isLoading={isLoading}
        h={'100%'}
        borderColor={'borderColor.base'}
        borderWidth={[0, 1]}
        boxShadow={'1.5'}
        overflow={'overlay'}
        bg={'myGray.25'}
        borderRadius={[0, '16px']}
        {...insertProps}
      >
        {children}
      </MyBox>
    </MyBox>
  );
};

const PageContainer = (
  props: BoxProps & { isLoading?: boolean; insertProps?: BoxProps; pageBgColor?: string }
) => {
  const router = useRouter();

  if (router.pathname.startsWith('/tenant')) {
    return <TenantPageContainer {...props} />;
  } else {
    return <DefaultPageContainer {...props} />;
  }
};

export default PageContainer;
