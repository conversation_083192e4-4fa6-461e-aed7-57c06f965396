import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Tooltip } from 'antd';

const CustomTooltip = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [content, setContent] = useState('');
  const [position, setPosition] = useState({ left: '0px', top: '0px' });

  useImperativeHandle(ref, () => ({
    setPosition: (left: any, top: any) => {
      setPosition({ left: `${left}px`, top: `${top}px` });
    },
    setContent: (newContent: any) => {
      setContent(newContent);
    },
    show: () => {
      setVisible(true);
    },
    hide: () => {
      setVisible(false);
    }
  }));

  return (
    <div
      style={{
        position: 'fixed',
        left: position.left,
        top: position.top,
        visibility: visible ? 'visible' : 'hidden',
        backgroundColor: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '4px',
        padding: '8px',
        boxShadow: '0 1px 6px rgba(0, 0, 0, 0.2)',
        zIndex: 1000,
        textAlign: 'left'
      }}
    >
      {content}
    </div>
  );
});

CustomTooltip.displayName = 'CustomTooltip';

export default CustomTooltip;
