import { rpxDim } from '@/utils/chakra';
import { Box, Center } from '@chakra-ui/react';
import { ReactNode } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import { useLayout } from '@/hooks/useLayout';

export type MobileNavbarProps = {
  autoback?: boolean;
  sidebarButton?: boolean;
  left?: ReactNode;
  title?: ReactNode;
  right?: ReactNode;
  bgColor?: string;
  onBack?: () => void;
};

const MobileNavbar = ({
  autoback = true,
  sidebarButton,
  left,
  title,
  right,
  bgColor = '#FFFFFF',
  onBack
}: MobileNavbarProps) => {
  const router = useRouter();

  const { openSidebar } = useLayout();

  return (
    <Center w="100%" h={rpxDim(96)} flexShrink="0" pos="relative" bgColor={bgColor}>
      <Center pos="absolute" left="0" top="0" bottom="0" color="#303133">
        {(onBack || autoback) && (
          <SvgIcon
            ml={rpxDim(24)}
            name="chevronLeft"
            w={rpxDim(48)}
            h={rpxDim(48)}
            cursor="pointer"
            onClick={onBack || (() => router.back())}
          />
        )}

        {sidebarButton && (
          <SvgIcon
            ml={autoback ? 0 : rpxDim(32)}
            name="menu"
            w={rpxDim(48)}
            h={rpxDim(48)}
            cursor="pointer"
            onClick={openSidebar}
          />
        )}

        {left}
      </Center>

      <Box color="rgba(0,0,0,0.9)" fontSize={rpxDim(36)} lineHeight={rpxDim(52)} fontWeight="bold">
        {title}
      </Box>

      {right && (
        <Center pos="absolute" right="0" top="0" bottom="0">
          {right}
        </Center>
      )}
    </Center>
  );
};

export default MobileNavbar;
