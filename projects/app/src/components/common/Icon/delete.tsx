import React from 'react';
import { IconProps } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';

const DeleteIcon = (props: IconProps) => {
  return (
    <SvgIcon
      className="delete"
      name={'trash' as any}
      w={'14px'}
      _hover={{ color: 'red.600' }}
      display={['block', 'none']}
      cursor={'pointer'}
      {...props}
    />
  );
};

export default DeleteIcon;

export const hoverDeleteStyles = {
  '& .delete': {
    display: 'block'
  }
};
