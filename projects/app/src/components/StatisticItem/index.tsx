import React from 'react';
import { Box, Text, VStack, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';

interface StatisticItemProps {
  content: string;
  num: string | number;
  itemWidth?: string;
}

const StatisticItem: React.FC<StatisticItemProps> = ({ content, num, itemWidth = '192px' }) => {
  return (
    <Box
      p="12px 11px 12px 14px"
      bg="white"
      borderRadius="12px"
      display="flex"
      alignItems="baseline"
      mb="12px"
    >
      <VStack align="flex-start" spacing="4px">
        <Flex alignItems="center">
          <Box w="7px" h="14px" bg="#3366ff" borderRadius="8px" mr="10px" />
          <Text
            fontWeight={500}
            fontStyle="normal"
            fontSize="16px"
            color="#1d2129"
            lineHeight="24px"
            whiteSpace="nowrap"
            maxWidth="150px"
            overflow="hidden"
            textOverflow="ellipsis"
            maxW={respDims(158)}
          >
            {content}
          </Text>
        </Flex>
        <Text
          ml="18px"
          fontFamily="Helvetica Neue"
          fontWeight={400}
          fontSize="18px"
          color="#2e2e3a"
          lineHeight="32px"
          mt="4px"
        >
          {num}
        </Text>
      </VStack>
    </Box>
  );
};

export default StatisticItem;
