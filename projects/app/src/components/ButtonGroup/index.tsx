import React from 'react';
import { Box, Center, Flex, MenuButton } from '@chakra-ui/react';
import { Button } from 'antd';
import MyMenu from '@/components/MyMenu';

interface ButtonProps {
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
}

interface ButtonGroupProps {
  buttons: ButtonProps[];
  visibleCount: number;
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({ buttons, visibleCount }) => {
  const visibleButtons = buttons.slice(0, visibleCount);
  const hiddenButtons = buttons.slice(visibleCount);

  return (
    <Flex alignItems="center">
      {visibleButtons.map((button, index) => (
        <Button key={index} type="link" onClick={button.onClick}>
          {button.label}
        </Button>
      ))}
      {hiddenButtons.length > 0 && (
        <MyMenu
          trigger="click"
          offset={[20, 0]}
          width={20}
          Button={
            <MenuButton
              className="app-menu"
              display="flex"
              alignItems="center"
              w="auto"
              h="auto"
              _hover={{
                bg: 'myWhite.600'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <Center>
                <Button type="link">更多</Button>
              </Center>
            </MenuButton>
          }
          menuList={buttons.map((button, index) => ({
            key: index,
            label: button.label,
            icon: button.icon,
            onClick: button.onClick
          }))}
        />
      )}
    </Flex>
  );
};

export default ButtonGroup;
