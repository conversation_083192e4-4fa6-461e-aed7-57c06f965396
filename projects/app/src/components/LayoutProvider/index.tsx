import { useSystemStore } from '@/store/useSystemStore';
import { SizeType } from '@/types';
import { updateRootFontSize } from '@/utils/chakra';
import { isMobile } from '@/utils/mobile';
import { useDisclosure } from '@chakra-ui/react';
import { throttle } from 'lodash';
import { createContext, useEffect, useState } from 'react';

export type LayoutContextType = {
  isPc?: boolean;
  isOpenSidebar: boolean;
  openSidebar: () => void;
  closeSidebar: () => void;
  contentSize?: SizeType;
  setContentSize: (size?: SizeType) => void;
  overlayId?: string;
  setOverlayId: (id: string) => void;
};

export const LayoutContext = createContext<LayoutContextType>({
  isOpenSidebar: false,
  openSidebar: () => {},
  closeSidebar: () => {},
  setContentSize: () => {},
  setOverlayId: () => {}
});

export const LayoutProvider = ({ children }: { children: JSX.Element }) => {
  const {
    isOpen: isOpenSidebar,
    onOpen: openSidebar,
    onClose: closeSidebar
  } = useDisclosure({ defaultIsOpen: !isMobile });

  const [contentSize, setContentSize] = useState<SizeType>();

  const [overlayId, setOverlayId] = useState<string>();

  const { setScreenWidth, isPc } = useSystemStore();

  useEffect(() => {
    const onResize = throttle(() => {
      updateRootFontSize();
      setScreenWidth(document.documentElement.clientWidth);
    }, 50);

    window.addEventListener('resize', onResize);
    onResize();
    return () => window.removeEventListener('resize', onResize);
  }, [setScreenWidth]);

  return (
    <LayoutContext.Provider
      value={{
        isPc,
        isOpenSidebar,
        openSidebar,
        closeSidebar,
        contentSize,
        setContentSize,
        overlayId,
        setOverlayId
      }}
    >
      {children}
    </LayoutContext.Provider>
  );
};
