export const iconDatas = {
  more: () => import('./icons/common/more.svg'),
  doubleRight: () => import('./icons/common/double_right.svg'),
  circleClose: () => import('./icons/common/circle_close.svg'),
  ai: () => import('./icons/common/ai.svg'),
  copy: () => import('./icons/common/copy.svg'),
  edit: () => import('./icons/common/edit.svg'),
  editLine: () => import('./icons/common/edit_line.svg'),
  upvote: () => import('./icons/common/upvote.svg'),
  upvoteFill: () => import('./icons/common/upvote_fill.svg'),
  downvote: () => import('./icons/common/downvote.svg'),
  downvoteFill: () => import('./icons/common/downvote_fill.svg'),
  voice: () => import('./icons/common/voice.svg'),
  repeat: () => import('./icons/common/repeat.svg'),
  share: () => import('./icons/common/share.svg'),
  circlePlus: () => import('./icons/common/circle_plus.svg'),
  circleMinus: () => import('./icons/common/circle_minus.svg'),
  backBoundray: () => import('./icons/common/back_boundary.svg'),
  trash: () => import('./icons/common/trash.svg'),
  brush: () => import('./icons/common/brush.svg'),
  export: () => import('./icons/common/export.svg'),
  chat: () => import('./icons/common/chat.svg'),
  newChat: () => import('./icons/common/new_chat.svg'),
  chatShare: () => import('./icons/common/chat_share.svg'),
  settings: () => import('./icons/common/settings.svg'),
  group: () => import('./icons/common/group.svg'),
  chevronLeft: () => import('./icons/common/chevron_left.svg'),
  chevronRight: () => import('./icons/common/chevron_right.svg'),
  chevronUp: () => import('./icons/common/chevron_up.svg'),
  chevronDown: () => import('./icons/common/chevron_down.svg'),
  search: () => import('./icons/common/search.svg'),
  slash: () => import('./icons/common/slash.svg'),
  microphone: () => import('./icons/common/microphone.svg'),
  check: () => import('./icons/common/check.svg'),
  close: () => import('./icons/common/close.svg'),
  stop: () => import('./icons/common/stop.svg'),
  app: () => import('./icons/common/app.svg'),
  inspiration: () => import('./icons/common/inspiration.svg'),
  plus: () => import('./icons/common/plus.svg'),
  transferUpDown: () => import('./icons/common/transfer_up_down.svg'),
  redo: () => import('./icons/common/redo.svg'),
  alignTop: () => import('./icons/common/align_top.svg'),
  alignBottom: () => import('./icons/common/align_bottom.svg'),
  fold: () => import('./icons/common/fold.svg'),
  menu: () => import('./icons/common/menu.svg'),
  menu2: () => import('./icons/common/menu2.svg'),
  fullscreen: () => import('./icons/common/fullscreen.svg'),
  exitFullscreen: () => import('./icons/common/exit_fullscreen.svg'),
  eye: () => import('./icons/common/eye.svg'),
  eyeOff: () => import('./icons/common/eye_off.svg'),
  swap: () => import('./icons/common/swap.svg'),
  download: () => import('./icons/common/download.svg'),
  share2: () => import('./icons/common/share2.svg'),
  file: () => import('./icons/common/file.svg'),
  folder: () => import('./icons/common/folder.svg'),
  book: () => import('./icons/common/book.svg'),
  drag: () => import('./icons/common/drag.svg'),
  box: () => import('./icons/common/box.svg'),
  boxFill: () => import('./icons/common/box_fill.svg'),
  user: () => import('./icons/common/user.svg'),
  userFill: () => import('./icons/common/user_fill.svg'),
  package: () => import('./icons/common/package.svg'),
  packageFill: () => import('./icons/common/package_fill.svg'),
  power: () => import('./icons/common/power.svg'),
  avatarBg: () => import('./icons/common/avatar_bg.svg'),
  chatEmpty: () => import('./icons/common/chat_empty.svg'),
  chatPower: () => import('./icons/common/chat_power.svg'),
  stayTuned: () => import('./icons/common/stay_tuned.svg'),

  arrowUp: () => import('./icons/common/arrow_up.svg'),
  arrowDown: () => import('./icons/common/arrow_down.svg'),
  appSetting: () => import('./icons/common/app_setting.svg'),
  chevronUpCircle: () => import('./icons/common/chevronUpCircle.svg'),

  fileTxt: () => import('./icons/file/txt.svg'),
  filePdf: () => import('./icons/file/pdf.svg'),
  fileDoc: () => import('./icons/file/doc.svg'),
  fileXlsx: () => import('./icons/file/xlsx.svg'),
  filePpt: () => import('./icons/file/ppt.svg'),
  fileMd: () => import('./icons/file/md.svg'),
  fileBlank: () => import('./icons/file/blank.svg'),
  fileError: () => import('./icons/file/error.svg'),

  file2Txt: () => import('./icons/file2/txt.svg'),
  file2Pdf: () => import('./icons/file2/pdf.svg'),
  file2Doc: () => import('./icons/file2/doc.svg'),
  file2Xlsx: () => import('./icons/file2/xlsx.svg'),
  file2Ppt: () => import('./icons/file2/ppt.svg'),
  file2Mp3: () => import('./icons/file2/mp3.svg'),
  file2Mp4: () => import('./icons/file2/mp4.svg'),
  file2Zip: () => import('./icons/file2/zip.svg'),
  file2Avi: () => import('./icons/file2/avi.svg'),
  file2Csv: () => import('./icons/file2/csv.svg'),
  file2Md: () => import('./icons/file2/md.svg'),
  file2Rar: () => import('./icons/file2/rar.svg'),
  file2Wav: () => import('./icons/file2/wav.svg'),
  file2Folder: () => import('./icons/file2/folder.svg'),
  file2Unknown: () => import('./icons/file2/unknown.svg'),

  file2Eye: () => import('./icons/file2/file_eye.svg'),
  file2Preview: () => import('./icons/file2/file_preview.svg'),
  file2Label: () => import('./icons/file2/label.svg'),
  file2Dynamic: () => import('./icons/file2/file_dynamic.svg'),
  file2Info: () => import('./icons/file2/file_info.svg'),
  file2Query: () => import('./icons/file2/query.svg'),
  file2Look: () => import('./icons/file2/look.svg'),
  file2Examine: () => import('./icons/file2/examine.svg'),
  order: () => import('./icons/common/order.svg'),
  workflow: () => import('./icons/common/workflow.svg'),

  transferWaiting: () => import('./icons/transfer/waiting.svg'),
  transferUpload: () => import('./icons/transfer/upload.svg'),
  transferDownload: () => import('./icons/transfer/download.svg'),
  transferSuccess: () => import('./icons/transfer/success.svg'),
  transferError: () => import('./icons/transfer/error.svg'),

  chatMagicWand: () => import('./icons/chat/magic_wand.svg'),
  chatMagicWand2: () => import('./icons/chat/magic_wand2.svg'),
  chatNetwork: () => import('./icons/chat/network.svg'),
  chatNetwork2: () => import('./icons/chat/network2.svg'),
  chatUpload: () => import('./icons/chat/upload.svg'),
  chatSend: () => import('./icons/chat/send.svg'),
  chatDrop: () => import('./icons/chat/drop.svg'),
  chatFillPlus: () => import('./icons/chat/fill_plus.svg'),
  chatFillCamera: () => import('./icons/chat/fill_camera.svg'),
  chatFillAlbum: () => import('./icons/chat/fill_album.svg'),
  chatFillFile: () => import('./icons/chat/fill_file.svg'),
  chatHistoryConversation: () => import('./icons/chat/historyConversation.svg'),
  chatDelete: () => import('./icons/chat/delete.svg'),
  chatEdit: () => import('./icons/chat/edit.svg'),
  chatApp: () => import('./icons/chat/app.svg'),
  chatCopyText: () => import('./icons/chat/copy_text.svg'),
  chatExportMD: () => import('./icons/chat/export_md.svg'),
  chatExportPDF: () => import('./icons/chat/export_pdf.svg'),
  optimizePrompt: () => import('./icons/chat/optimizePrompt.svg'),

  navSchoolLine: () => import('./icons/nav/school_line.svg'),

  navBoard: () => import('./icons/nav/board.svg'),

  // 创建应用
  navCreateApp: () => import('./icons/nav/create_app.svg'),
  // 通用对话
  navGeneralChat: () => import('./icons/nav/navGeneralChat.svg'),
  navGeneralChat2: () => import('./icons/nav/navGeneralChat2.svg'),
  // 首页
  home: () => import('./icons/nav/home.svg'),
  home2: () => import('./icons/nav/home2.svg'),
  // 应用中心
  navAppCenter: () => import('./icons/nav/navAppCenter.svg'),
  navAppCenter2: () => import('./icons/nav/navAppCenter2.svg'),
  // 数据空间
  navCloud: () => import('./icons/nav/navCloud.svg'),
  navCloud2: () => import('./icons/nav/navCloud2.svg'),
  // 知识库
  navDataset: () => import('./icons/nav/navDataset.svg'),
  navDataset2: () => import('./icons/nav/navDataset2.svg'),
  navPPT: () => import('./icons/nav/navPPT.svg'),
  navPPT2: () => import('./icons/nav/navPPT2.svg'),

  // AI 教学
  navAiTeaching: () => import('./icons/nav/ai_teaching.svg'),
  // AI 评价
  navAiEvaluation: () => import('./icons/nav/ai_evaluation.svg'),
  // AI 行政
  navAiAdmin: () => import('./icons/nav/ai_admin.svg'),
  // AI 教育资源
  navAiResource: () => import('./icons/nav/ai_resource.svg'),
  // AI 学校治理
  navAiGovern: () => import('./icons/nav/ai_govern.svg'),
  // AI 自适应学习
  navAiLearning: () => import('./icons/nav/ai_learning.svg'),
  // 项目化教学设计
  navAiProject: () => import('./icons/nav/ai_project.svg'),
  // 学科融合
  navAiSubject: () => import('./icons/nav/ai_subject.svg'),
  // 个人快捷指令
  navFolderOpenLine: () => import('./icons/nav/tenant/folder_open_line.svg'),
  navRole: () => import('./icons/nav/tenant/role.svg'),
  navStorageLine: () => import('./icons/nav/tenant/storage_line.svg'),
  navGroup: () => import('./icons/nav/tenant/group.svg'),
  navSchoolInfo: () => import('./icons/nav/tenant/schoolInfo.svg'),
  navNotice: () => import('./icons/nav/notification_line.svg'),
  navHomeLine: () => import('./icons/nav/tenant/home_line.svg'),
  navSchoolManange: () => import('./icons/nav/tenant/school_manange.svg'),
  navTenantManange: () => import('./icons/nav/tenant/tenant_manange.svg'),
  navProfileLine: () => import('./icons/nav/tenant/profile_line.svg'),

  promptPersonage: () => import('./icons/prompt/personage.svg'),
  promptCenter: () => import('./icons/prompt/promptCenter.svg'),
  promptUser: () => import('./icons/prompt/user.svg'),
  promptCopy: () => import('./icons/prompt/copy.svg'),
  advanced: () => import('./icons/common/advanced.svg'),
  easy: () => import('./icons/common/easy.svg'),
  closed_lock: () => import('./icons/common/closed_lock.svg'),
  open_lock: () => import('./icons/common/open_lock.svg'),
  empty: () => import('./icons/common/empty.svg'),
  doubt: () => import('./icons/common/doubt.svg'),
  editIcon: () => import('./icons/common/edit_icon.svg'),
  generate: () => import('./icons/common/generate.svg'),

  // 修改头像
  settingAvatar: () => import('./icons/setting/avatar.svg'),
  // 修改密码
  settingPassword: () => import('./icons/setting/password.svg'),

  // 专属
  appExclusive: () => import('./icons/app/exclusive.svg'),
  // 官方
  appAuthority: () => import('./icons/app/authority.svg'),
  // 应用中心搜索
  appAgentSearch: () => import('./icons/app/agentSearch.svg'),
  appNav: () => import('./icons/app/appNav.svg'),
  // 高阶应用
  appAdvancedAgent: () => import('./icons/app/advancedAgent.svg'),
  // 背景知识
  appBgKnowledge: () => import('./icons/app/bgKnowledge.svg'),
  // 简易应用
  appSimpleAgent: () => import('./icons/app/simpleAgent.svg'),
  // 简易应用工具
  appTool: () => import('./icons/app/tool.svg'),
  // 简易应用知识库
  appKnowledgeBase: () => import('./icons/app/knowledgeBase.svg'),
  // 高阶编排
  appArrange: () => import('./icons/app/arrange.svg'),
  // 去高阶编排配置
  appGoAgentConfig: () => import('./icons/app/goAgentConfig.svg'),
  promptSwap: () => import('./icons/app/promptSwap.svg'),
  star: () => import('./icons/app/star.svg'),
  vector: () => import('./icons/app/vector.svg'),
  vector2: () => import('./icons/app/vector2.svg'),
  uploadFile: () => import('./icons/app/upload_file.svg'),
  star1: () => import('./icons/app/star1.svg'),
  appCenterIcon: () => import('./icons/app/app_center_icon.svg'),

  administrative: () => import('./icons/app/administrative.svg'),
  PPTGeneration: () => import('./icons/app/PPTGeneration.svg'),
  textToImage: () => import('./icons/app/text_toImage.svg'),
  teaching: () => import('./icons/app/teaching.svg'),
  writing: () => import('./icons/app/writing.svg'),
  preparation: () => import('./icons/app/preparation.svg'),
  activity: () => import('./icons/app/activity.svg'),
  planning: () => import('./icons/app/planning.svg'),
  basedTeaching: () => import('./icons/app/based_teaching.svg'),

  // 个人信息
  personageCenterUserInfo: () => import('./icons/personageCenter/user_info.svg'),
  //个人信息
  personal: () => import('./icons/personageCenter/personal.svg'),
  // 微信
  personageCenterWeixin: () => import('./icons/personageCenter/weixin.svg'),

  // 知识库
  datasetFolderStar: () => import('./icons/dataset/folder_star_line.svg'),
  datasetWebLine: () => import('./icons/dataset/web_line.svg'),
  datasetGroup: () => import('./icons/dataset/group.svg'),

  emojiSmile: () => import('./icons/emoji/smile.svg'),
  emojiAngry: () => import('./icons/emoji/angry.svg'),
  emojiClassPerformance: () => import('./icons/emoji/class_performance.svg'),
  emojiStudentPerformance: () => import('./icons/emoji/student_performance.svg'),
  emojiEvaluationIndex: () => import('./icons/emoji/evaluation_index.svg'),

  evaluateSayLabel: () => import('./icons/evaluate/sayLabel.svg'),
  evaluateSayLabel2: () => import('./icons/evaluate/sayLabel2.svg'),
  evaluationMutiple: () => import('./icons/evaluate/list.svg'),
  evaluationLoop: () => import('./icons/evaluate/loop.svg'),
  evaluate: () => import('./icons/evaluate/evaluate.svg'),
  evaluateJobEvaluate: () => import('./icons/evaluate/job_evaluate.svg'),
  evaluateDataInactive: () => import('./icons/evaluate/evaluation_data_inactive.svg'),
  evaluateDataActive: () => import('./icons/evaluate/evaluation_data_active.svg'),
  evaluateManageInactive: () => import('./icons/evaluate/evaluation_manage_inactive.svg'),
  evaluateManageActive: () => import('./icons/evaluate/evaluation_manage_active.svg'),
  evaluateKanbanInactive: () => import('./icons/evaluate/kanban_inactive.svg'),
  evaluateKanbanActive: () => import('./icons/evaluate/kanban_active.svg'),
  evaluateTeacherInactive: () => import('./icons/evaluate/teacher_evaluation_inactive.svg'),
  evaluateTeacherActive: () => import('./icons/evaluate/teacher_evaluation_active.svg'),
  evaluateBasicInformationInactive: () => import('./icons/evaluate/basic_information_inactive.svg'),
  evaluateBasicInformationActive: () => import('./icons/evaluate/basic_information_active.svg')
};

export type SvgIconNameType = keyof typeof iconDatas;
