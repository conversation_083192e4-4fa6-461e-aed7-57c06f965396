<svg width="1024" height="1024" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#228;&#190;&#167;&#232;&#190;&#185;&#230;&#160;&#143;&#229;&#155;&#190;&#230;&#160;&#135;">
<g id="Group 427319054">
<rect id="Rectangle 34624194" width="32" height="32" rx="10" fill="url(#paint0_linear_73333_6251)"/>
</g>
<g id="Frame" clip-path="url(#clip0_73333_6251)">
<g id="Group 427319054_2" filter="url(#filter0_d_73333_6251)">
<path id="Vector" d="M19.7202 7.40625C21.0044 7.40625 22.0454 8.4473 22.0454 9.73152C22.0454 10.4591 21.7112 11.1087 21.188 11.5351L22.6439 14.0571C22.8924 13.9664 23.1607 13.917 23.4405 13.917C24.7246 13.917 25.7656 14.9581 25.7656 16.2423C25.7656 17.5265 24.7246 18.5676 23.4405 18.5676C23.1607 18.5676 22.8924 18.5181 22.6439 18.4275L21.3644 20.6438C21.7852 21.0646 22.0454 21.6459 22.0454 22.288C22.0454 23.5722 21.0044 24.6133 19.7202 24.6133C18.7176 24.6133 17.8631 23.9786 17.5367 23.0891H14.4633C14.1369 23.9786 13.2824 24.6133 12.2798 24.6133C10.9956 24.6133 9.95461 23.5722 9.95461 22.288C9.95461 21.6459 10.2148 21.0646 10.6356 20.6438L9.12801 18.0325C8.94212 18.0791 8.75118 18.1026 8.55953 18.1025C7.27537 18.1025 6.23438 17.0614 6.23438 15.7772C6.23438 14.493 7.27539 13.452 8.55953 13.452C8.92236 13.452 9.2658 13.5351 9.57184 13.6833L10.8121 11.5351C10.2888 11.1087 9.95463 10.4591 9.95463 9.73152C9.95461 8.4473 10.9956 7.40625 12.2797 7.40625C13.4498 7.40625 14.418 8.27053 14.5808 9.39551H17.4192C17.582 8.27055 18.5502 7.40625 19.7202 7.40625ZM16.0084 14.8086C15.8149 14.8086 15.6307 14.848 15.4632 14.9192L15.444 14.9275L15.4484 14.9255C15.441 14.9288 15.4335 14.9321 15.4261 14.9355L15.444 14.9275L15.4344 14.9318L15.4149 14.9408L15.4081 14.944L15.3878 14.9539L15.3709 14.9624L15.3653 14.9654L15.347 14.9751L15.3324 14.9831L15.313 14.994L15.2949 15.0046C15.288 15.0087 15.2812 15.0129 15.2744 15.0171L15.2907 15.0071L15.2812 15.0129L15.2744 15.0171C15.2685 15.0207 15.2627 15.0244 15.257 15.028L15.2716 15.0188L15.2631 15.0241L15.257 15.028C15.2509 15.0319 15.2449 15.0358 15.2389 15.0398L15.2546 15.0296L15.2442 15.0363L15.2338 15.0432L15.2181 15.0539L15.2035 15.064C15.1956 15.0696 15.1878 15.0753 15.1801 15.081L15.1685 15.0896L15.1517 15.1025L15.1351 15.1157C15.1276 15.1217 15.1201 15.1278 15.1127 15.134L15.0982 15.1464L15.0857 15.1573L15.0663 15.1747L15.0592 15.1813L15.052 15.188L15.0397 15.1997C15.0325 15.2067 15.0254 15.2137 15.0183 15.2208L15.0078 15.2315L15.001 15.2386L15.0058 15.2336C14.9991 15.2405 14.9924 15.2476 14.9858 15.2547L14.9795 15.2615C14.9729 15.2687 14.9664 15.276 14.9599 15.2833L14.9503 15.2944L14.9477 15.2975C14.9428 15.3032 14.9379 15.309 14.9331 15.3148L14.9234 15.3266C14.9173 15.3342 14.9112 15.3419 14.9052 15.3496L14.8943 15.3639C14.8897 15.37 14.8851 15.3762 14.8806 15.3824L14.8717 15.3947C14.709 15.6229 14.6133 15.9021 14.6133 16.2038C14.6133 16.9743 15.2379 17.5989 16.0084 17.5989C16.4737 17.5989 16.8858 17.3711 17.1392 17.021L17.1507 17.0048L17.1605 16.9907L17.1662 16.9823L17.1704 16.976C17.1744 16.97 17.1784 16.964 17.1823 16.9579L17.1719 16.9738L17.1783 16.9641L17.1845 16.9544L17.1944 16.9387L17.2043 16.9226L17.2079 16.9166L17.2183 16.8987L17.226 16.8852C17.2307 16.8769 17.2353 16.8685 17.2398 16.8601L17.2465 16.8474C17.2566 16.828 17.2662 16.8084 17.2754 16.7885L17.2857 16.7657L17.2905 16.7546L17.2952 16.7435C17.2989 16.7347 17.3025 16.7259 17.306 16.717L17.3092 16.7089C17.3128 16.6996 17.3163 16.6902 17.3198 16.6808L17.3249 16.6666C17.328 16.6576 17.3311 16.6486 17.3341 16.6395L17.3385 16.6258C17.3414 16.6166 17.3442 16.6074 17.347 16.5981L17.3523 16.5796L17.351 16.5841C17.3537 16.5747 17.3562 16.5652 17.3587 16.5557L17.3623 16.5414C17.3641 16.5342 17.3658 16.527 17.3675 16.5198L17.3632 16.5378L17.366 16.5261L17.3675 16.5198C17.3697 16.5103 17.3718 16.5007 17.3738 16.4911L17.3782 16.4696C17.3795 16.4626 17.3808 16.4556 17.3821 16.4485L17.3838 16.4385L17.3855 16.4285C17.3868 16.4204 17.388 16.4123 17.3892 16.4042L17.3914 16.3876C17.3928 16.3777 17.394 16.3677 17.3951 16.3578L17.3969 16.3395L17.3966 16.3428C17.3974 16.3348 17.3981 16.3267 17.3988 16.3187L17.3969 16.3395L17.398 16.3279L17.3988 16.3187C17.3996 16.3088 17.4003 16.299 17.4009 16.2892L17.4019 16.2697C17.4024 16.2599 17.4027 16.25 17.403 16.2401L17.4032 16.2283C17.4034 16.2202 17.4035 16.212 17.4035 16.2038L17.4033 16.1807C17.391 15.4208 16.7712 14.8086 16.0084 14.8086ZM17.1523 18.2316C16.8082 18.4327 16.4078 18.548 15.9805 18.548C15.8384 18.548 15.6992 18.5353 15.5642 18.5109L14.1292 20.8075C14.1204 20.8216 14.111 20.835 14.101 20.8478C14.4039 21.2395 14.5844 21.7307 14.5854 22.264H17.3756L17.3759 22.23C17.389 21.4223 17.8139 20.7147 18.4498 20.3081L17.1523 18.2316H17.1523ZM9.84699 17.6811L11.2782 20.1602C11.5765 20.021 11.9093 19.9432 12.2603 19.9432C12.6702 19.9432 13.0554 20.0493 13.3898 20.2356L14.6888 18.1565C14.1757 17.8131 13.8069 17.2708 13.6926 16.6398L10.7126 16.5879C10.5437 17.0297 10.2439 17.4069 9.86055 17.6718L9.84699 17.6811ZM18.2525 16.7195C18.1831 17.0376 18.0475 17.3376 17.8543 17.5997L19.3042 19.9203C19.3147 19.937 19.3241 19.9544 19.3323 19.9723C19.4542 19.9529 19.5773 19.9431 19.7007 19.9432C20.0517 19.9432 20.3845 20.021 20.6828 20.1602L21.9271 18.0047C21.5529 17.6906 21.279 17.2607 21.1606 16.7702L18.2525 16.7194L18.2525 16.7195ZM19.7007 12.0373C19.6658 12.0373 19.6309 12.0365 19.5963 12.035C19.5848 12.0686 19.5695 12.1008 19.5506 12.1309L17.8543 14.8457C18.0565 15.1204 18.1997 15.4412 18.2653 15.7894L21.1273 15.8394C21.2199 15.2808 21.5117 14.7895 21.9271 14.4408L20.4653 11.9086C20.2258 11.992 19.9686 12.0373 19.7007 12.0373ZM12.2603 12.0373C11.9924 12.0373 11.7352 11.992 11.4957 11.9086L10.2085 14.1382C10.5938 14.5352 10.8388 15.0692 10.8631 15.6602L13.7121 15.7099C13.8449 15.12 14.2019 14.6149 14.6889 14.289L13.1672 11.8537C12.8885 11.9719 12.582 12.0373 12.2602 12.0373H12.2603ZM17.423 10.1815H14.5379C14.4479 10.6209 14.2339 11.015 13.9341 11.3259L15.5642 13.9346C15.7015 13.9099 15.8409 13.8974 15.9805 13.8975C16.4078 13.8975 16.8082 14.0128 17.1523 14.214L18.6643 11.7941C18.0351 11.4802 17.5682 10.8896 17.423 10.1815Z" fill="url(#paint1_linear_73333_6251)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_73333_6251" x="5.63437" y="7.30625" width="21.7313" height="19.407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.5" dy="1"/>
<feGaussianBlur stdDeviation="0.55"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.122593 0 0 0 0 0.10822 0 0 0 0 0.826888 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_73333_6251"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_73333_6251" result="shape"/>
</filter>
<linearGradient id="paint0_linear_73333_6251" x1="3.5" y1="4" x2="32" y2="32" gradientUnits="userSpaceOnUse">
<stop stop-color="#9192FC"/>
<stop offset="1" stop-color="#5563E2"/>
</linearGradient>
<linearGradient id="paint1_linear_73333_6251" x1="9.82509" y1="9.62848" x2="24.1675" y2="28.0657" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E1E2FB"/>
</linearGradient>
<clipPath id="clip0_73333_6251">
<rect width="20" height="20" fill="white" transform="translate(6 6)"/>
</clipPath>
</defs>
</svg>
