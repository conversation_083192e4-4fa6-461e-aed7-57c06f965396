<svg width="1024" height="1024" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427319053">
<rect id="Rectangle 34624192" width="32" height="32" rx="10" fill="url(#paint0_linear_73333_6269)"/>
<g id="Frame">
<path id="Vector" d="M8.5 19.75V22.25C8.5 22.9404 9.05965 23.5 9.75 23.5H12.25" stroke="url(#paint1_linear_73333_6269)" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_2" d="M19.75 23.5H22.25C22.9404 23.5 23.5 22.9404 23.5 22.25V19.75" stroke="url(#paint2_linear_73333_6269)" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_3" d="M23.5 12.25V9.75C23.5 9.05965 22.9404 8.5 22.25 8.5H19.75" stroke="url(#paint3_linear_73333_6269)" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_4" d="M8.5 12.25V9.75C8.5 9.05965 9.05965 8.5 9.75 8.5H12.25" stroke="url(#paint4_linear_73333_6269)" stroke-opacity="0.7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<g id="Frame_2" clip-path="url(#clip0_73333_6269)">
<g id="Vector_5" filter="url(#filter0_d_73333_6269)">
<path d="M20.5511 14.645V16.2936L20.8988 16.6413L20.1738 17.3941L19.4405 16.663L19.9425 16.2068V14.9048C17.8126 15.7828 17.1305 16.0555 16.6576 16.2691C16.1846 16.4828 15.8451 16.4811 15.3768 16.3047C14.9084 16.1283 12.6783 15.3143 11.5707 14.7841C10.8318 14.4302 10.7839 14.2066 11.5829 13.9061C12.6261 13.51 14.3493 12.8757 15.2627 12.5291C15.8035 12.3098 16.0896 12.1908 16.5859 12.44C17.4727 12.8061 19.4981 13.5667 20.505 13.9823C21.3842 14.364 20.7945 14.4898 20.5506 14.6433M16.7609 16.9212C17.2763 16.7087 17.9711 16.357 18.7272 16.0332V18.6371C18.7272 18.6371 17.7481 19.6787 16.0265 19.6787C14.1727 19.6787 13.1712 18.6371 13.1712 18.6371V16.2068C13.7555 16.4449 14.412 16.6497 15.2065 16.9212C15.6961 17.0959 16.3171 17.156 16.7609 16.9212Z" fill="url(#paint5_linear_73333_6269)"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_73333_6269" x="10.9" y="11.7005" width="12.1707" height="9.57817" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="0.5"/>
<feGaussianBlur stdDeviation="0.55"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.00864258 0 0 0 0 0.00864258 0 0 0 0.26 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_73333_6269"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_73333_6269" result="shape"/>
</filter>
<linearGradient id="paint0_linear_73333_6269" x1="4.5" y1="-7.44592e-07" x2="32" y2="29.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9567"/>
<stop offset="1" stop-color="#F98B3C"/>
</linearGradient>
<linearGradient id="paint1_linear_73333_6269" x1="10.375" y1="19.75" x2="10.375" y2="23.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_73333_6269" x1="21.625" y1="19.75" x2="21.625" y2="23.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_73333_6269" x1="21.625" y1="8.5" x2="21.625" y2="12.25" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint4_linear_73333_6269" x1="10.375" y1="8.5" x2="10.375" y2="12.25" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_73333_6269" x1="14.9689" y1="14.7477" x2="19.4837" y2="21.1203" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFE5D3"/>
</linearGradient>
<clipPath id="clip0_73333_6269">
<rect width="10" height="10" fill="white" transform="translate(11 11)"/>
</clipPath>
</defs>
</svg>
