<svg width="1040" height="1040" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427319209">
<g id="_&#229;&#155;&#190;&#229;&#177;&#130;_1" clip-path="url(#clip0_76172_149168)">
<path id="Vector" d="M29.8802 11.3359H18.8922V10.468C18.8922 8.55435 17.363 7.00171 15.4765 7.00171H5.95442C4.06795 6.99993 2.53696 8.55257 2.53696 10.4662V12.2003L2.5 15.6702H2.55103V29.5336C2.55103 31.4473 4.08027 32.9999 5.96674 32.9999H29.8784C31.7649 32.9999 33.2942 31.4473 33.2942 29.5336V14.8022C33.2942 12.8886 31.7649 11.3359 29.8784 11.3359H29.8802Z" fill="url(#paint0_linear_76172_149168)"/>
<g id="Vector_2" filter="url(#filter0_b_76172_149168)">
<path d="M31.4731 33.0001H5.70837C4.1017 33.0001 2.94201 31.4457 3.38372 29.8841L6.9666 17.2212C7.23936 16.2572 8.11221 15.592 9.10472 15.592H35.276C36.7524 15.592 37.8188 17.022 37.4141 18.4572L33.7995 31.2287C33.5021 32.278 32.5536 33.0001 31.4749 33.0001H31.4731Z" fill="#F2F2FF" fill-opacity="0.27"/>
<path d="M5.70837 32.5001H31.4731H31.4749C32.3268 32.5001 33.0811 31.9296 33.3184 31.0925C33.3184 31.0925 33.3185 31.0924 33.3185 31.0923L36.9328 18.3215C37.2497 17.1979 36.4133 16.092 35.276 16.092H9.10472C8.33893 16.092 7.66039 16.6057 7.44771 17.3573L5.70837 32.5001ZM5.70837 32.5001C4.44062 32.5001 3.51143 31.2697 3.86483 30.0203C3.86483 30.0202 3.86483 30.0202 3.86484 30.0202L7.44771 17.3573L5.70837 32.5001Z" stroke="url(#paint1_linear_76172_149168)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_76172_149168" x="-0.710938" y="11.592" width="42.2119" height="25.408" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_76172_149168"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_76172_149168" result="shape"/>
</filter>
<linearGradient id="paint0_linear_76172_149168" x1="17.8971" y1="7.0513" x2="17.8971" y2="33" gradientUnits="userSpaceOnUse">
<stop stop-color="#3366FF"/>
<stop offset="1" stop-color="#AADBFF"/>
</linearGradient>
<linearGradient id="paint1_linear_76172_149168" x1="28.2474" y1="15.5313" x2="20.2667" y2="32.9414" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.36"/>
</linearGradient>
<clipPath id="clip0_76172_149168">
<rect width="35" height="26" fill="white" transform="translate(2.5 7)"/>
</clipPath>
</defs>
</svg>
