<svg width="1024" height="1024" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="emoji_fill" clip-path="url(#clip0_78404_56460)">
<g id="Group">
<g id="Vector" filter="url(#filter0_ii_78404_56460)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2ZM14.8 13.857C14.0532 14.5912 13.0473 15.0018 12 15C10.9527 15.0018 9.94682 14.5912 9.2 13.857C9.1067 13.7629 8.99563 13.6882 8.87323 13.6374C8.75084 13.5866 8.61957 13.5606 8.48704 13.561C8.35452 13.5614 8.22339 13.5881 8.10128 13.6396C7.97916 13.6911 7.8685 13.7663 7.77573 13.8609C7.68295 13.9556 7.60991 14.0677 7.56085 14.1908C7.51179 14.3139 7.48769 14.4455 7.48995 14.578C7.4922 14.7105 7.52078 14.8413 7.57401 14.9626C7.62723 15.084 7.70405 15.1936 7.8 15.285C8.92064 16.3858 10.4292 17.0018 12 17C13.5708 17.0018 15.0794 16.3858 16.2 15.285C16.385 15.0984 16.4891 14.8465 16.4898 14.5838C16.4905 14.321 16.3878 14.0685 16.2039 13.8809C16.0199 13.6933 15.7695 13.5856 15.5068 13.5811C15.2441 13.5767 14.9902 13.6758 14.8 13.857ZM8.5 8C8.10218 8 7.72064 8.15804 7.43934 8.43934C7.15804 8.72064 7 9.10218 7 9.5C7 9.89782 7.15804 10.2794 7.43934 10.5607C7.72064 10.842 8.10218 11 8.5 11C8.89782 11 9.27936 10.842 9.56066 10.5607C9.84196 10.2794 10 9.89782 10 9.5C10 9.10218 9.84196 8.72064 9.56066 8.43934C9.27936 8.15804 8.89782 8 8.5 8ZM15.5 8C15.1022 8 14.7206 8.15804 14.4393 8.43934C14.158 8.72064 14 9.10218 14 9.5C14 9.89782 14.158 10.2794 14.4393 10.5607C14.7206 10.842 15.1022 11 15.5 11C15.8978 11 16.2794 10.842 16.5607 10.5607C16.842 10.2794 17 9.89782 17 9.5C17 9.10218 16.842 8.72064 16.5607 8.43934C16.2794 8.15804 15.8978 8 15.5 8Z" fill="url(#paint0_linear_78404_56460)"/>
<path d="M15.5 8C15.1022 8 14.7206 8.15804 14.4393 8.43934C14.158 8.72064 14 9.10218 14 9.5C14 9.89782 14.158 10.2794 14.4393 10.5607C14.7206 10.842 15.1022 11 15.5 11C15.8978 11 16.2794 10.842 16.5607 10.5607C16.842 10.2794 17 9.89782 17 9.5C17 9.10218 16.842 8.72064 16.5607 8.43934C16.2794 8.15804 15.8978 8 15.5 8Z" fill="url(#paint1_linear_78404_56460)"/>
<path d="M8.5 8C8.10218 8 7.72064 8.15804 7.43934 8.43934C7.15804 8.72064 7 9.10218 7 9.5C7 9.89782 7.15804 10.2794 7.43934 10.5607C7.72064 10.842 8.10218 11 8.5 11C8.89782 11 9.27936 10.842 9.56066 10.5607C9.84196 10.2794 10 9.89782 10 9.5C10 9.10218 9.84196 8.72064 9.56066 8.43934C9.27936 8.15804 8.89782 8 8.5 8Z" fill="url(#paint2_linear_78404_56460)"/>
<path d="M14.8 13.857C14.0532 14.5912 13.0473 15.0018 12 15C10.9527 15.0018 9.94682 14.5912 9.2 13.857C9.1067 13.7629 8.99563 13.6882 8.87323 13.6374C8.75084 13.5866 8.61957 13.5606 8.48704 13.561C8.35452 13.5614 8.22339 13.5881 8.10128 13.6396C7.97916 13.6911 7.8685 13.7663 7.77573 13.8609C7.68295 13.9556 7.60991 14.0677 7.56085 14.1908C7.51179 14.3139 7.48769 14.4455 7.48995 14.578C7.4922 14.7105 7.52078 14.8413 7.57401 14.9626C7.62723 15.084 7.70405 15.1936 7.8 15.285C8.92064 16.3858 10.4292 17.0018 12 17C13.5708 17.0018 15.0794 16.3858 16.2 15.285C16.385 15.0984 16.4891 14.8465 16.4898 14.5838C16.4905 14.321 16.3878 14.0685 16.2039 13.8809C16.0199 13.6933 15.7695 13.5856 15.5068 13.5811C15.2441 13.5767 14.9902 13.6758 14.8 13.857Z" fill="url(#paint3_linear_78404_56460)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_ii_78404_56460" x="2" y="0.7" width="20" height="21.3" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="0.65"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.99079 0 0 0 0 0.769743 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_78404_56460"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.857491 0 0 0 0 0.35223 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_78404_56460" result="effect2_innerShadow_78404_56460"/>
</filter>
<linearGradient id="paint0_linear_78404_56460" x1="12" y1="2" x2="12" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF0A3"/>
<stop offset="1" stop-color="#FFC657"/>
</linearGradient>
<linearGradient id="paint1_linear_78404_56460" x1="12" y1="2" x2="12" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#AC5329"/>
<stop offset="1" stop-color="#462211"/>
</linearGradient>
<linearGradient id="paint2_linear_78404_56460" x1="12" y1="2" x2="12" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#AC5329"/>
<stop offset="1" stop-color="#462211"/>
</linearGradient>
<linearGradient id="paint3_linear_78404_56460" x1="12" y1="2" x2="12" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#AC5329"/>
<stop offset="1" stop-color="#462211"/>
</linearGradient>
<clipPath id="clip0_78404_56460">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
