import { SvgIconNameType, iconDatas } from './data';

const fileIconMap: Record<string, SvgIconNameType> = {
  doc: 'fileDoc',
  docx: 'fileDoc',
  xls: 'fileXlsx',
  xlsx: 'fileXlsx',
  ppt: 'filePpt',
  pptx: 'filePpt',
  txt: 'fileTxt',
  md: 'fileMd',
  pdf: 'filePdf'
};

export function getFileSvgIcon(name: string) {
  const type = name.substring(name.lastIndexOf('.') + 1).toLocaleLowerCase();
  return fileIconMap[type] || 'fileBlank';
}

const file2IconMap: Record<string, SvgIconNameType> = {
  txt: 'file2Txt',
  md: 'file2Md',
  csv: 'file2Csv',
  pdf: 'file2Pdf',

  doc: 'file2Doc',
  docx: 'file2Doc',

  xls: 'file2Xlsx',
  xlsx: 'file2Xlsx',

  ppt: 'file2Ppt',
  pptx: 'file2Ppt',

  mp3: 'file2Mp3',
  wav: 'file2Wav',
  mp4: 'file2Mp4',
  avi: 'file2Avi',

  zip: 'file2Zip',
  rar: 'file2Rar'
};

export function getFile2SvgIcon(name: string) {
  const type = name.substring(name.lastIndexOf('.') + 1).toLocaleLowerCase();
  return file2IconMap[type] || 'file2Unknown';
}

export function isSvgIconName(name: string): name is SvgIconNameType {
  return !!name && name in iconDatas;
}
