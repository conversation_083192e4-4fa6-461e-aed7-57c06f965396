import React, { useEffect, useState } from 'react';
import type { IconProps } from '@chakra-ui/react';
import { Icon } from '@chakra-ui/react';
import { SvgIconNameType, iconDatas } from './data';

let nextId = 0;

export type SvgIconProps = { name: SvgIconNameType } & IconProps;

const SvgIcon = ({ name, w = '16px', h = '16px', ...props }: SvgIconProps) => {
  const [iconId] = useState(() => `SvgIc-${(nextId++).toString(36)}`);

  const [iconProps, setIconProps] = useState<any>(null);

  useEffect(() => {
    const iconData = iconDatas[name];
    if (!iconData) {
      return;
    }
    iconData().then((res) => {
      const iconProps: any = {
        as: res.default
      };
      setIconProps(iconProps);
    });
  }, [name]);

  useEffect(() => {
    const iconElem = document.getElementById(iconId);
    if (!iconElem) {
      return;
    }

    const ids: string[] = [];
    const attrs: { name: string; value: string }[] = [];
    const re = /\s([\w-]+)="url\(#([^)]+)\)"/g;
    let m: RegExpExecArray | null;

    while ((m = re.exec(iconElem.innerHTML))) {
      attrs.push({
        name: m[1],
        value: m[2]
      });
      if (!ids.includes(m[2])) {
        ids.push(m[2]);
      }
    }

    ids.forEach((id) => {
      const idElem = iconElem.querySelector(`#${id}`);
      if (!idElem) {
        return;
      }
      const newId = `${iconId}-${id}`;
      idElem.setAttribute('id', newId);
      attrs.forEach((attr) => {
        if (attr.value !== id) {
          return;
        }
        const attrElems = iconElem.querySelectorAll(`[${attr.name}="url(\\#${id})"]`);
        for (let attrElem of attrElems) {
          attrElem.setAttribute(attr.name, `url(#${newId})`);
        }
      });
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [iconId, iconProps]);

  return !!iconDatas[name] ? <Icon id={iconId} {...iconProps} w={w} h={h} {...props} /> : null;
};

export default React.memo(SvgIcon);
