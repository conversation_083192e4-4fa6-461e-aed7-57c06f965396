import React, { useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Progress,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure
} from '@chakra-ui/react';
import { Upload } from 'antd';
import { downloadFile } from '@/utils/file';
import SvgIcon from '@/components/SvgIcon';
import type { RcFile } from 'antd/es/upload';
import { Toast } from '@/utils/ui/toast';
import { respDims } from '@/utils/chakra';
import { getToken } from '@/utils/auth';

const { Dragger } = Upload;

interface FileType {
  uid: string;
  lastModified: number;
  lastModifiedDate: Date;
  name: string;
  size: number;
  type: string;
  percent: number;
  originFileObj: RcFile;
  status: string;
}

interface ImportPanelProps {
  tip?: string;
  downloadTip?: string;
  downloadButtonText?: string;
  chooseTip?: string;
  chooseButtonText?: string;
  showCancelButton?: boolean;
  cancelButtonText?: string;
  showConfirmButton?: boolean;
  confirmButtonText?: string;
  importTip?: string;
  successMessage?: string;
  typeName?: string;
  templateUrl?: string;
  importUrl?: string;
  useResultMessage?: boolean;
  onUploadSuccess?: () => void;
}

const ImportPanel: React.FC<ImportPanelProps> = ({
  tip = '',
  downloadTip = '',
  downloadButtonText = '下载模板文件',
  chooseTip = '',
  chooseButtonText = '选择文件',
  showCancelButton = true,
  cancelButtonText = '取消',
  showConfirmButton = true,
  confirmButtonText = '确认导入',
  importTip = '导入数据时，请勿关闭或刷新页面',
  successMessage = '',
  typeName = '',
  templateUrl = '',
  importUrl = '',
  useResultMessage = false,
  onUploadSuccess
}) => {
  const [selectedFile, setSelectedFile] = useState<FileType | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [uploadPercentage, setUploadPercentage] = useState(0);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { isOpen, onOpen, onClose: closeModal } = useDisclosure();

  const handleClose = () => {
    setSelectedFile(null);
    setErrorMessage(null);
    setUploadPercentage(0);
    closeModal();
  };

  const validateFile = (file: RcFile) => {
    if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
      return true;
    } else {
      Toast.error('上传格式错误，请上传excel文件');
      return false;
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] as RcFile;
    if (file && validateFile(file)) {
      setSelectedFile({
        uid: file.uid,
        lastModified: file.lastModified,
        lastModifiedDate: new Date(file.lastModified),
        name: file.name,
        size: file.size,
        type: file.type,
        percent: 0,
        originFileObj: file,
        status: 'done'
      });
      setIsImporting(false);
      setUploadPercentage(0);
      setErrorMessage(null);
    }
  };

  const handleDeleteFile = () => {
    if (isImporting) return;
    setSelectedFile(null);
    setErrorMessage(null);
    const fileInput = document.getElementById('fileInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      if (!templateUrl) {
        throw new Error('模板URL未定义');
      }
      await downloadFile(templateUrl, {});
    } catch (error) {
      const err = error as Error;
      Toast.error(err.message);
    }
  };

  const handleConfirmUpload = async () => {
    if (!selectedFile) return;

    setIsImporting(true);
    setUploadPercentage(0);

    try {
      if (!importUrl) {
        throw new Error('导入URL未定义');
      }

      const formData = new FormData();
      formData.append('file', selectedFile.originFileObj);
      const response = await fetch(importUrl, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: getToken()
        }
      });

      const result = await response.json();

      if (
        result.code !== 200 ||
        result.data !== null ||
        (result.data && result.data.valid !== 1) ||
        (result.data && result.data.errMsgs && result.data.errMsgs.length > 0)
      ) {
        throw new Error(result.data?.errMsgs?.join('&nbsp;') || result.msg || '上传失败');
      }

      setUploadPercentage(100);
      Toast.success(successMessage || '上传成功');

      if (useResultMessage) {
        // 处理返回结果
      }

      setSelectedFile(null);
      setIsImporting(false);
      handleClose();
      if (onUploadSuccess) {
        onUploadSuccess();
      }
    } catch (error) {
      const err = error as Error;
      setErrorMessage(err.message || '未知错误');
      setIsImporting(false);

      if (error instanceof Response) {
        const result = await error.json();
        if (result.data && result.data.errMsgs && result.data.errMsgs.length > 0) {
          setErrorMessage(result.data.errMsgs.join('&nbsp;'));
        }
      }
    }
  };

  return (
    <>
      <Button onClick={onOpen}>批量上传</Button>

      <Modal isOpen={isOpen} onClose={handleClose} size="xl" isCentered>
        <ModalOverlay />
        <ModalContent maxW="600px">
          <ModalHeader borderBottom="1px solid #E7E7E7">上传文件</ModalHeader>
          <ModalCloseButton />
          <ModalBody p={respDims(24, 32)}>
            <Box className="import-panel" p={respDims(4)}>
              <Box className="content">
                <Flex className="operate-item" alignItems="center" mb="14px" bg="#F9F9F9" p="20px">
                  <Box
                    className="order"
                    bg="#3366ff"
                    color="#fff"
                    borderRadius="full"
                    w={respDims(32)}
                    h={respDims(32)}
                    mr={respDims(8)}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    1
                  </Box>
                  <Text className="text" flex={1} p={respDims(4)}>
                    {downloadTip || `请下载文件模板，录入${typeName}信息`}
                  </Text>
                  <Button
                    colorScheme="primary.5"
                    onClick={handleDownloadTemplate}
                    borderRadius="2px"
                    width="138px"
                    height="32px"
                  >
                    <SvgIcon name="download" w={respDims(18)} h={respDims(18)} mr={respDims(8)} />
                    {downloadButtonText}
                  </Button>
                </Flex>

                <Flex
                  className="operate-item"
                  alignItems="center"
                  mb={respDims(14)}
                  bg="#F9F9F9"
                  p={respDims(20)}
                >
                  <Box
                    className="order"
                    bg="#3366ff"
                    color="#fff"
                    borderRadius="full"
                    w={respDims(32)}
                    h={respDims(32)}
                    mr={respDims(8)}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    2
                  </Box>
                  <Text className="text" flex={1} p={respDims(4)}>
                    {chooseTip || `上传已录入${typeName}信息的表格文件`}
                  </Text>
                  <Button
                    colorScheme="primary.5"
                    onClick={() => document.getElementById('fileInput')?.click()}
                    borderRadius="2px"
                    width="138px"
                    height="32px"
                    isDisabled={isImporting}
                  >
                    {chooseButtonText}
                  </Button>
                  <input
                    id="fileInput"
                    type="file"
                    accept=".xlsx,.xls"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                  />
                </Flex>
                {selectedFile && (
                  <>
                    <Box
                      display="flex"
                      border="1px solid #E5E7EB"
                      borderRadius={respDims(8)}
                      p={respDims(8, 32)}
                      position="relative"
                    >
                      <SvgIcon
                        name="file2Xlsx"
                        w={respDims(40)}
                        h={respDims(50)}
                        mr={respDims(12)}
                      />
                      <Box flex="1">
                        <Flex className="file" alignItems="center" mb={respDims(4)} h="100%">
                          <Box as="i" className="icon el-icon-document" mr={respDims(2)} />
                          <Text className="name" flex={1} display="flex" mb={respDims(2)}>
                            {selectedFile.name}
                          </Text>
                        </Flex>
                        {/* <Progress
                          className="progress"
                          value={uploadPercentage}
                          size="sm"
                          mb={respDims(4)}
                          sx={{
                            borderRadius: 'full',
                            '& > div': {
                              borderRadius: 'full'
                            }
                          }}
                        /> */}
                      </Box>
                      <SvgIcon
                        name="trash"
                        w={respDims(22)}
                        h={respDims(22)}
                        mr={respDims(12)}
                        color="#3366ff"
                        cursor="pointer"
                        onClick={handleDeleteFile}
                        position="absolute"
                        right={respDims(0)}
                        top={respDims(4)}
                      />
                    </Box>
                  </>
                )}
                {errorMessage && (
                  <Box mt={respDims(12)} color="red.500" maxH="20vh" overflowY="auto">
                    {errorMessage.split('&nbsp;').map((errMsg, index) => (
                      <Text key={index}>
                        {index + 1}. {errMsg}
                      </Text>
                    ))}
                  </Box>
                )}
              </Box>

              <Flex className="footer" alignItems="center" mt={respDims(54)}>
                <Text className="tip" flex={1} color="orange.500">
                  {importTip}
                </Text>
                {showCancelButton && (
                  <Button variant="outline" onClick={handleClose} mr="8px">
                    {cancelButtonText}
                  </Button>
                )}
                {showConfirmButton && (
                  <Button
                    colorScheme="primary.5"
                    ml={respDims(4)}
                    onClick={handleConfirmUpload}
                    isDisabled={!selectedFile}
                    isLoading={isImporting}
                    borderRadius="8px"
                  >
                    {confirmButtonText}
                  </Button>
                )}
              </Flex>
            </Box>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default ImportPanel;
