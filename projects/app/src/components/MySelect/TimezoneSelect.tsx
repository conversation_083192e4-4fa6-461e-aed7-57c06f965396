import React, { useRef } from 'react';
import { Select } from '@chakra-ui/react';
import { timezoneList } from '@/utils/timezone';

const TimezoneSelect = ({ value, onChange }: { value?: string; onChange: (e: string) => void }) => {
  const timezones = useRef(timezoneList());

  return (
    <Select
      value={value}
      onChange={(e) => {
        onChange(e.target.value);
      }}
    >
      {timezones.current.map((item) => (
        <option key={item.value} value={item.value}>
          {item.name}
        </option>
      ))}
    </Select>
  );
};

export default React.memo(TimezoneSelect);
