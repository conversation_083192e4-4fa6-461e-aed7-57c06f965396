import React from 'react';
import MyTooltip from '.';
import { IconProps, QuestionOutlineIcon } from '@chakra-ui/icons';
import { TooltipProps } from '@chakra-ui/react';

type Props = IconProps & {
  label?: string | React.ReactNode;
  placement?: TooltipProps['placement'];
};

const QuestionTip = ({ label, maxW, placement, ...props }: Props) => {
  return (
    <MyTooltip label={label} maxW={maxW} placement={placement}>
      <QuestionOutlineIcon w={'0.9rem'} {...props} />
    </MyTooltip>
  );
};

export default QuestionTip;
