import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  Box,
  Image,
  CircularProgress,
  CircularProgressLabel,
  Text,
  Flex,
  BoxProps
} from '@chakra-ui/react';
import { useRequest } from '@/hooks/useRequest';
import { useSelectFile } from '@/hooks/useSelectFile';
import { uploadImage } from '@/utils/file';
import SvgIcon from '@/components/SvgIcon';

interface UploadImageProps extends Omit<BoxProps, 'placeholder'> {
  imageUrl: string | undefined;
  onImageSelect: (fileKey: string, fileUrl: string) => void;
  maxWidthOrHeight?: number;
  maxSizeMB?: number;
  placeholder?: React.ReactNode;
  showPlaceholderAsBox?: boolean; // 新增参数
  imageWidth?: string | number; // 新增参数
  imageHeight?: string | number; // 新增参数
  isCircular?: boolean; // 新增参数
}

const UploadImage = (props: UploadImageProps, ref: React.Ref<any>) => {
  const {
    imageUrl,
    onImageSelect,
    maxWidthOrHeight,
    maxSizeMB,
    placeholder = '上传',
    showPlaceholderAsBox = false, // 默认显示方格
    imageWidth = '80px', // 默认图片宽度
    imageHeight = '80px', // 默认图片高度
    isCircular = false, // 默认不为圆形
    ...boxProps
  } = props;

  const [process, setProcess] = useState<number>(0);
  const [localImageUrl, setLocalImageUrl] = useState<string | undefined>(imageUrl); // 本地状态

  const { File: ImageSelect, onOpen: onOpenImageSelect } = useSelectFile({
    fileType: '.jpg,.jpeg,.png',
    multiple: false
  });

  const { mutate: onSelectImage, isLoading: isUploadLoading } = useRequest({
    mutationFn: ([file]: File[]) => {
      if (!file) return Promise.resolve(null);
      return uploadImage(file, {
        maxWidthOrHeight,
        maxSizeMB,
        onProgress(number) {
          setProcess(number);
        }
      });
    },
    onSuccess(res) {
      if (res) {
        onImageSelect(res.fileKey, res.fileUrl);
        setLocalImageUrl(res.fileUrl); // 更新本地状态
      }
    }
  });

  useEffect(() => {
    if (!isUploadLoading) {
      setProcess(0);
    }
  }, [isUploadLoading]);

  useEffect(() => {
    setLocalImageUrl(imageUrl); // 当外部 imageUrl 变化时，同步更新本地状态
  }, [imageUrl]);

  const handleDeleteImage = () => {
    setLocalImageUrl(undefined);
    onImageSelect('', ''); // 清空父组件中的图片信息
  };
  useImperativeHandle(ref, () => {});

  const borderRadius = isCircular ? '50%' : '4px'; // 根据 isCircular 参数设置 borderRadius

  return (
    <Box position="relative" {...boxProps}>
      {isUploadLoading ? (
        <CircularProgress value={process} size="60px" color="blue.400">
          <CircularProgressLabel>{process}%</CircularProgressLabel>
        </CircularProgress>
      ) : localImageUrl ? (
        <Box
          position="relative"
          w={imageWidth}
          h={imageHeight}
          borderRadius={borderRadius}
          overflow="hidden"
          _hover={{ '.delete-button': { display: 'block' } }}
        >
          <Image
            w="auto"
            width={imageWidth}
            h={imageHeight}
            src={localImageUrl}
            alt=""
            cursor="pointer"
            onClick={onOpenImageSelect}
            objectFit="cover"
            borderRadius={borderRadius}
          />
          <Box
            position="absolute"
            top="0"
            right="0"
            display="none"
            className="delete-button"
            borderRadius="4px"
            bg="rgba(0,0,0,0.666)"
            w="24px"
            h="24px"
            textAlign="center"
            padding="3px"
            cursor="pointer"
          >
            <SvgIcon color="#fff" name="trash" w="18px" h="18px" onClick={handleDeleteImage} />
          </Box>
        </Box>
      ) : showPlaceholderAsBox ? (
        <Flex
          direction="column"
          align="center"
          justify="center"
          w={imageWidth}
          h={imageHeight}
          border={isCircular ? 'none' : '1px dashed #E5E6EB'} // 根据 isCircular 参数设置边框
          borderRadius={borderRadius}
          cursor="pointer"
          onClick={onOpenImageSelect}
          bg={isCircular ? '#F2F3F5' : 'transparent'} // 设置圆形背景色为 #F2F3F5
        >
          <Text fontSize="24px" color="#86909C">
            +
          </Text>
          {!isCircular && ( // 只有当 isCircular 为 false 时才显示上传文本
            <Text mt="6px" color="#86909C" fontSize="14px">
              {placeholder}
            </Text>
          )}
        </Flex>
      ) : (
        !isCircular && ( // 只有当 isCircular 为 false 时才显示上传文本
          <Text
            mt="6px"
            color="#86909C"
            fontSize="14px"
            cursor="pointer"
            onClick={onOpenImageSelect}
          >
            {placeholder}
          </Text>
        )
      )}
      <ImageSelect onSelect={onSelectImage} />
    </Box>
  );
};

export default forwardRef(UploadImage);
