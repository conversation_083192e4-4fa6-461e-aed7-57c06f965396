import { getMenuTreeList } from '@/api/tenant';
import {
  createRouteMap,
  getAuthRoutes,
  findActiveRoute,
  routeGroups,
  getNavRoutes
} from '@/routes';
import { useUserStore } from '@/store/useUserStore';
import {
  RouteGroupMapType,
  RouteGroupType,
  RouteType,
  RouteUpdateFieldsType
} from '@/types/routes';
import { treeForEach } from '@/utils/tree';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { ReactNode, createContext, useMemo } from 'react';

export interface RoutesContextType {
  // 当前页面路由组
  routeGroup?: RouteGroupType;
  // 所有页面路由组
  routeGroups: RouteGroupType[];
  // 所有页面路由组
  routeGroupMap: RouteGroupMapType;
  // 当前页面是否禁止访问
  isAccessDenied?: boolean;
}

export const RoutesContext = createContext<RoutesContextType>({
  routeGroups: [],
  routeGroupMap: {} as RoutesContextType['routeGroupMap']
});

const RoutesProvider = ({ children }: { children: ReactNode }) => {
  const router = useRouter();

  const { userInfo } = useUserStore();

  const { data: menuTree } = useQuery(['menu'], () => getMenuTreeList(), {
    enabled: !!userInfo
  });

  const { groups, groupMap } = useMemo(() => {
    if (!userInfo) {
      return { groups: [], groupMap: {} as RouteGroupMapType };
    }

    const fieldsMap: Record<string, RouteUpdateFieldsType> = {};
    menuTree?.length && treeForEach(menuTree, (it) => it.code && (fieldsMap[it.code] = it));

    const groups: RouteGroupType[] = routeGroups
      .map((it) => {
        const authRoutes = getAuthRoutes(it.routes, userInfo.menuCodes, fieldsMap);

        const navRoutes = getNavRoutes(authRoutes);
        const group: RouteGroupType = {
          type: it.type,
          routes: it.routes,
          routeMap: createRouteMap(it.routes),
          authRoutes,
          authRouteMap: createRouteMap(authRoutes),
          navRoutes,
          navRouteMap: createRouteMap(navRoutes)
        };
        return group;
      })
      .filter((it) => it.routes.length);

    const groupMap = {} as RouteGroupMapType;
    groups.forEach((it) => {
      groupMap[it.type] = it;
    });

    return {
      groups,
      groupMap
    };
  }, [userInfo, menuTree]);

  const { activeGroup, isAccessDenied } = useMemo(() => {
    let activeGroup: RouteGroupType | undefined;
    let isAccessDenied = false;

    // 在所有路由中匹配，再到授权的路由中判断
    for (let group of groups) {
      group.activeRoute = undefined;
      group.authActiveRoute = undefined;
      group.navActiveRoute = undefined;

      if (activeGroup) {
        continue;
      }

      const activeRoute = findActiveRoute(group.routes, router.pathname, router.asPath);
      if (!activeRoute) {
        continue;
      }

      group.activeRoute = activeRoute;

      if (group.authRouteMap[activeRoute.key]) {
        group.authActiveRoute = group.authRouteMap[activeRoute.key];
        let route: RouteType | undefined = activeRoute;
        do {
          group.navActiveRoute = group.navRouteMap[route.key];
          if (group.navActiveRoute) {
            break;
          }
          route = route.parent;
        } while (route);
      } else {
        group.authActiveRoute = undefined;
        group.navActiveRoute = undefined;
        isAccessDenied = true;
      }

      activeGroup = { ...group };
      break;
    }
    return { activeGroup, isAccessDenied };
  }, [groups, router.pathname, router.asPath]);

  return (
    <RoutesContext.Provider
      value={{
        routeGroup: activeGroup,
        routeGroups: groups,
        routeGroupMap: groupMap,
        isAccessDenied
      }}
    >
      {children}
    </RoutesContext.Provider>
  );
};

export default RoutesProvider;
