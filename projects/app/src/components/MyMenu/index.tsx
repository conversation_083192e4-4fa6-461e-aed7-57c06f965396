import React, { useCallback, useRef, useState } from 'react';
import {
  <PERSON>u,
  <PERSON>uList,
  MenuItem,
  Box,
  useOutsideClick,
  MenuButton,
  MenuDivider,
  Portal,
  MenuProps
} from '@chakra-ui/react';
import SvgIcon from '../SvgIcon';

interface Props {
  width?: number | string;
  offset?: [number, number];
  Button: React.ReactNode;
  trigger?: 'hover' | 'click';
  menuList: {
    isActive?: boolean;
    label: string | React.ReactNode;
    icon?: string | React.ReactNode;
    isDisabled?: boolean;
    preDivider?: boolean;
    onClick: () => any;
  }[];
  isOpen?: boolean;
  outsideClosable?: boolean;
  zIndex?: number;
  isUsePortal?: boolean;
  placement?: MenuProps['placement'];
  onClose?: () => void;
}

const MyMenu = ({
  width = 'auto',
  trigger = 'hover',
  offset = [0, 5],
  But<PERSON>,
  menuList,
  isOpen,
  outsideClosable = true,
  zIndex = 999,
  isUsePortal,
  placement,
  onClose
}: Props) => {
  const menuItemStyles = {
    borderRadius: 'sm',
    py: 3,
    display: 'flex',
    alignItems: 'center',
    _hover: {
      backgroundColor: 'myGray.05',
      color: 'primary.600'
    }
  };
  const ref = useRef<HTMLDivElement>(null);
  const closeTimer = useRef<any>();
  const [isOpenInner, setIsOpenInner] = useState(false);

  const Container = useCallback(
    ({ children }: { children: React.ReactNode }) => {
      return isUsePortal ? <Portal>{children}</Portal> : <>{children}</>;
    },
    [isUsePortal]
  );

  useOutsideClick({
    ref: ref,
    enabled: isOpen || isOpenInner,
    handler: () => {
      if (outsideClosable) {
        setIsOpenInner(false);
        onClose?.();
      }
    }
  });

  return (
    <Menu
      offset={offset}
      isOpen={isOpen || isOpenInner}
      autoSelect={false}
      direction={'ltr'}
      isLazy
      placement={placement}
    >
      <Box
        ref={ref}
        onMouseEnter={() => {
          if (trigger === 'hover') {
            setIsOpenInner(true);
          }
          clearTimeout(closeTimer.current);
        }}
        onMouseLeave={() => {
          if (trigger === 'hover') {
            closeTimer.current = setTimeout(() => {
              setIsOpenInner(false);
              onClose?.();
            }, 100);
          }
        }}
      >
        <Box
          position={'relative'}
          onClickCapture={() => {
            if (trigger === 'click') {
              setIsOpenInner(!isOpenInner);
              isOpenInner && onClose?.();
            }
          }}
        >
          <MenuButton
            w={'100%'}
            h={'100%'}
            position={'absolute'}
            top={0}
            right={0}
            bottom={0}
            left={0}
          />
          <Box position={'relative'}>{Button}</Box>
        </Box>
        <Container>
          <MenuList
            minW={isOpenInner ? `${width === 'auto' ? 'auto' : width + 'px'} !important` : 0}
            p={'6px'}
            border={'1px solid #fff'}
            boxShadow={
              '0px 2px 4px rgba(161, 167, 179, 0.25), 0px 0px 1px rgba(121, 141, 159, 0.25);'
            }
            zIndex={zIndex}
          >
            {menuList.map((item, i) => (
              <React.Fragment key={i}>
                {!!item.preDivider && <MenuDivider borderColor="#E5E7EB" />}
                <MenuItem
                  {...menuItemStyles}
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsOpenInner(false);
                    onClose?.();
                    item.onClick && item.onClick();
                  }}
                  color={item.isActive ? 'primary.700' : 'myGray.600'}
                  whiteSpace={'pre-wrap'}
                  isDisabled={item.isDisabled}
                >
                  {!!item.icon &&
                    (typeof item.icon === 'string' ? (
                      <SvgIcon name={item.icon as any} w={'16px'} />
                    ) : (
                      item.icon
                    ))}
                  <Box ml={item.icon ? '12px' : 0}>{item.label}</Box>
                </MenuItem>
              </React.Fragment>
            ))}
          </MenuList>
        </Container>
      </Box>
    </Menu>
  );
};

export default MyMenu;
