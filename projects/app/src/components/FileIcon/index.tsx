import { getFileMeta } from '@/api/file';
import SvgIcon from '@/components/SvgIcon';
import { SvgIconNameType } from '@/components/SvgIcon/data';
import { getFile2SvgIcon } from '@/components/SvgIcon/utils';
import { respDims } from '@/utils/chakra';
import { isImageFile } from '@/utils/file/format';
import { ChakraProps, Image } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';

const FileIcon = ({
  isFolder,
  fileName,
  fileKey,
  fileUrl,
  localFile,
  w,
  h,
  ...props
}: {
  isFolder?: boolean;
  fileName?: string;
  fileKey?: string;
  fileUrl?: string;
  localFile?: File;
  w?: ChakraProps['w'];
  h?: ChakraProps['h'];
} & ChakraProps) => {
  const [name, setName] = useState(fileName || localFile?.name || (!fileKey && fileUrl) || '');

  const [url, setUrl] = useState<string>(fileUrl || '');

  const { svgIcon, imgIcon }: { svgIcon?: SvgIconNameType; imgIcon?: string } = useMemo(() => {
    if (isFolder) {
      return { svgIcon: 'file2Folder' };
    }
    if (url && isImageFile(name)) {
      return { imgIcon: url };
    }
    return { svgIcon: getFile2SvgIcon(name) };
  }, [isFolder, name, url]);

  w = w ?? respDims(40);
  h = h ?? respDims(40);

  useEffect(() => {
    if (!localFile || !isImageFile(localFile)) {
      return;
    }
    const reader = new FileReader();
    reader.onload = function (e) {
      setUrl(e.target?.result as string);
    };
    reader.readAsDataURL(localFile);
  }, [localFile]);

  useEffect(() => {
    if (!fileKey || (name && (url || !isImageFile(name)))) {
      return;
    }
    getFileMeta(fileKey).then((res) => {
      setName(res.fileName);
      setUrl(res.fileUrl);
    });
  }, [fileKey, name, url]);

  return svgIcon ? (
    <SvgIcon name={svgIcon} w={w} h={h} {...props} />
  ) : (
    <Image
      src={imgIcon}
      w={w}
      h={h}
      alt=""
      objectFit="fill"
      px={respDims(5)}
      py={respDims(7)}
      {...props}
    />
  );
};

export default FileIcon;
