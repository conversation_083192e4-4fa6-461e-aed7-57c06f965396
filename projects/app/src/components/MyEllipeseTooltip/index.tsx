import React, { useEffect, useRef, useState } from 'react';
import { Box } from '@chakra-ui/react';
import MyTooltip from '../MyTooltip';
import { BoxProps } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';

type MyEllipeseTooltipProps = {
  content: React.ReactNode;
  maxLines?: number;
} & BoxProps;

const MyEllipeseTooltip: React.FC<MyEllipeseTooltipProps> = ({
  content,
  maxLines = 2,
  ...rest
}) => {
  const [isOverflowing, setIsOverflowing] = useState(false);
  const boxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (boxRef.current) {
      setIsOverflowing(boxRef.current.scrollHeight > boxRef.current.clientHeight);
    }
  }, [content, maxLines]);

  return (
    <Box position="relative" {...rest}>
      {/* 用于检测溢出的隐藏 Box */}
      <Box
        ref={boxRef}
        maxH={respDims(28, 24)}
        mt={respDims(10, 8)}
        color="#606266"
        fontSize={respDims(14, 12)}
        wordBreak="break-all"
        overflow="hidden"
        sx={{
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          WebkitLineClamp: maxLines,
          whiteSpace: 'normal',
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none'
        }}
        {...rest}
      >
        {content || '暂无介绍'}
      </Box>
      {/* 实际显示内容的 Box */}
      {isOverflowing ? (
        <MyTooltip label={content || '暂无介绍'}>
          <Box
            maxH={respDims(28, 24)}
            mt={respDims(10, 8)}
            color="#606266"
            fontSize={respDims(14, 12)}
            wordBreak="break-all"
            overflow="hidden"
            sx={{
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: maxLines,
              whiteSpace: 'normal'
            }}
            {...rest}
          >
            {content || '暂无介绍'}
          </Box>
        </MyTooltip>
      ) : (
        <Box
          maxH={respDims(28, 24)}
          mt={respDims(10, 8)}
          color="#606266"
          fontSize={respDims(14, 12)}
          wordBreak="break-all"
          overflow="hidden"
          sx={{
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: maxLines,
            whiteSpace: 'normal'
          }}
          {...rest}
        >
          {content || '暂无介绍'}
        </Box>
      )}
    </Box>
  );
};

export default MyEllipeseTooltip;
