import React, { useState, useEffect, CSSProperties } from 'react';
import { Box, Flex, Image, Input, Text, forwardRef } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';
import { getChatMessages } from '@/utils/chat';
import { streamFetch } from '@/utils/fetch';
import { chats2GPTMessages } from '@/fastgpt/global/core/chat/adapt';
import { respDims, rpxDim } from '@/utils/chakra';
import { useUserStore } from '@/store/useUserStore';
import { getConclusion, saveOrUpdateConclusion, getTeacherTypeBySubject } from '@/api/kanban';
import { Toast } from '@/utils/ui/toast';
import Lottie from '@/components/Lottie';
import { Input as AntdInput } from 'antd';
import styled from '@emotion/styled';

const GradientText = styled.span`
  background: linear-gradient(318deg, #3974ff -6.75%, #3876ff -6.74%, #21caff 81.92%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
`;
interface ChartBoxProps {
  title?: string;
  showButtons?: boolean;
  editable?: boolean;
  height?: string;
  backgroundColor?: string;
  titleFontSize?: string;
  padding?: string;
  showFullScreenButtons?: boolean;
  fullScreenHeight?: string;
  headerMarginBottom?: string;
  fullScreenoffsetX?: string;
  createLoading?: boolean;
  children?: React.ReactNode;
  header?: React.ReactNode;
  buttons?: React.ReactNode;
  hiddenContent?: React.ReactNode;
  onContentUpdate?: (content: string) => void;
  onFullScreenToggle?: (isFullScreen: boolean) => void;
  params?: any;
  examId?: string;
  selectedGradesId?: string;
  selectedGradesName?: string;
  selectedTestData?: string[];
  selectedSubjects?: string[];
  location?: number;
  option?: any;
  /**  控制显示按钮是否在编辑框的外面 默认值 false =>鼠标 houve 时显示;true 显示在边框外面*/
  buttonVisibility?: boolean;
  onClckAiGenerate?: (callback: any) => void;
  selectedYear?: any;
  selectedTerm?: any;
}
export interface BoardPaneRef {
  refresh: () => void;
}

const buttonStyles = {
  bg: '#fff',
  borderRadius: '20px',
  fontSize: '12px',
  color: '#303133',
  padding: '0px 8px',
  border: '1px solid #f5f5f5',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  h: '28px',
  w: '52px'
};
function BoardPaneAll(
  {
    title,
    showButtons = false,
    editable = false,
    height = '320px',
    backgroundColor = '#fff',
    titleFontSize = '18px',
    padding = '16px',
    showFullScreenButtons = false,
    fullScreenHeight = '770px',
    headerMarginBottom = '4px',
    fullScreenoffsetX = '12',
    createLoading = false,
    children,
    header,
    buttons,
    hiddenContent,
    onContentUpdate,
    onFullScreenToggle,
    examId,
    selectedGradesId,
    selectedGradesName,
    selectedTestData,
    selectedSubjects,
    location, //1 数据汇总表格结论 2 数据汇总图表结论
    params,
    buttonVisibility = false,
    selectedYear,
    selectedTerm
  }: ChartBoxProps,
  ref: React.ForwardedRef<BoardPaneRef>
) {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [position, setPosition] = useState<'static' | 'absolute'>('static');
  const [isEdit, setIsEdit] = useState(false);
  const [editInput, setEditInput] = useState('');
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { teacherType } = useUserStore();
  const [conclusionId, setConclusionId] = useState('');
  // 需要单独处理的 localtion 序号
  const specialLocation = [9, 10, 11, 12, 13, 14, 15, 16];

  const [hasEditAuthority, setHasEditAuthority] = useState<number>(0);

  useEffect(() => {
    if (
      selectedSubjects &&
      selectedSubjects.length > 0 &&
      typeof selectedSubjects[0] === 'string'
    ) {
      getTeacherTypeBySubject({
        semesterId: selectedTerm,
        gradeId: parseInt(selectedGradesId || '0'),
        subjectNames: selectedSubjects
      }).then((res) => {
        setHasEditAuthority(res);
      });
    }
  }, [selectedSubjects, selectedTerm, selectedGradesId]);

  useEffect(() => {
    setEditInput(content);
  }, [content]);

  useEffect(() => {
    if (
      !examId ||
      !selectedGradesId ||
      !selectedGradesName ||
      !selectedSubjects ||
      typeof selectedSubjects[0] !== 'string'
    ) {
      return;
    }
    setIsLoading(true);
    getConclusion({
      examId: examId || '',
      gradeId: selectedGradesId || '',
      gradeName: selectedGradesName || '',
      subjectNames: selectedSubjects || []
    })
      .then((res) => {
        const resData = res.find((item: any) => {
          return item.location == location;
        });
        if (resData) {
          setContent(resData.conclusion);
          setConclusionId(resData.id);
        } else {
          setContent('');
          setConclusionId('');
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [examId, selectedGradesId, selectedGradesName, selectedSubjects, location]);

  // 保存全部结论数据兼容
  async function saveAllConclusionData(location: number, conclusion: any) {
    await saveOrUpdateConclusion({
      id: conclusionId,
      examId: examId || '',
      teacherType: teacherType,
      gradeId: selectedGradesId || '',
      gradeName: selectedGradesName || '',
      subjectNames: selectedSubjects || [],
      location: location,
      conclusion: conclusion
    });
  }

  const refresh = () => {
    // 重新获取结论数据
    if (examId && selectedGradesId && selectedGradesName && selectedSubjects) {
      getConclusion({
        examId: examId || '',
        gradeId: selectedGradesId || '',
        gradeName: selectedGradesName || '',
        subjectNames: selectedSubjects || []
      }).then((res) => {
        const resData = res.find((item: any) => item.location == location);
        if (resData) {
          setContent(resData.conclusion);
          setConclusionId(resData.id);
        } else {
          setContent('');
          setConclusionId('');
        }
      });
    }
  };

  // 将 refresh 方法暴露给外部
  React.useImperativeHandle(ref, () => ({
    refresh
  }));

  const toggleFullScreen = () => {
    const newIsFullScreen = !isFullScreen;
    setIsFullScreen(newIsFullScreen);
    setPosition(newIsFullScreen ? 'absolute' : 'static');
    onFullScreenToggle?.(newIsFullScreen);
  };

  const handleEdit = () => {
    setIsEdit(true);
    setEditInput(content);
    setContent(content);
  };

  const handleFinishEdit = async () => {
    setContent(editInput);
    onContentUpdate?.(editInput);
    setIsEdit(false);

    try {
      const response = await saveOrUpdateConclusion({
        id: conclusionId,
        examId: examId || '',
        teacherType: teacherType,
        gradeId: selectedGradesId || '',
        gradeName: selectedGradesName || '',
        subjectNames: selectedSubjects || [],
        location: location || 0,
        conclusion: editInput
      });

      if (response) {
        Toast.success('保存结论成功');

        // 重新调用 getConclusion 更新内容
        const updatedConclusions = await getConclusion({
          examId: examId || '',
          gradeId: selectedGradesId || '',
          gradeName: selectedGradesName || '',
          subjectNames: selectedSubjects || []
        });

        const updatedResData = updatedConclusions.find((item: any) => item.location == location);
        if (updatedResData) {
          setContent(updatedResData.conclusion);
          setConclusionId(updatedResData.id);
        } else {
          setContent('');
          setConclusionId('');
        }
      }
    } catch (error) {
      console.error('Error saving conclusion:', error);
      Toast.error('保存结论时出错');
    }
  };
  // 临时存储AI生成的结论,在点击完成的时候才保存
  const [aiGeneratedConclusion, setAiGeneratedConclusion] = useState<any>('');
  const handleAIGenerate = async (data?: any) => {
    setIsLoading(true);
    try {
      const { newChatList } = getChatMessages({
        text: JSON.stringify(data ?? params),
        files: []
      });

      const abortSignal = new AbortController();

      const messages = chats2GPTMessages({
        messages: newChatList,
        reserveId: true
      });

      const { responseText } = await streamFetch({
        url: '/huayun-ai/client/chat/once',
        onMessage: () => {},
        abortCtrl: abortSignal,
        data: {
          messages: messages.slice(0, -1),
          type: 6
        }
      });

      if (!responseText) {
        Toast.error('生成内容为空');
        return;
      }
      setAiGeneratedConclusion(responseText);
      //#region 临时处理
      function setSpecialLocationContent(text: string) {
        setEditInput(text);
        setIsEdit(true);
      }
      // TODO: 存在问题和改进措施 调用一次生成所有tab的结论,每个结论单独可以编辑
      if (location && specialLocation.includes(location)) {
        const data = JSON.parse(responseText);
        switch (location) {
          // 以下为存在问题
          case 9: // 教学常规
            setSpecialLocationContent(data.data[0]?.problem_description);
            break;
          case 10: // 集体备课
            setSpecialLocationContent(data.data[1]?.problem_description);
            break;
          case 11: // 分层教学
            setSpecialLocationContent(data.data[2]?.problem_description);
            break;
          case 12: //其他
            setSpecialLocationContent(data.data[3]?.problem_description);
            break;
          // 以下为改进措施
          case 13: // 科任
            console.log({ data });
            setSpecialLocationContent(data.data[0]?.solution_description);
            break;
          case 14: //备课组
            setSpecialLocationContent(data.data[1]?.solution_description);
            break;
          case 15: //班主任
            setSpecialLocationContent(data.data[2]?.solution_description);
            break;
          case 16: //其他
            setSpecialLocationContent(data.data[3]?.solution_description);
            break;
          default:
            setSpecialLocationContent(responseText || '');
            break;
        }
      } else {
        setSpecialLocationContent(responseText || '');
      }

      //#endregion
    } catch (error) {
      console.error('Error generating AI content:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const canEditAndView = () => {
    return hasEditAuthority === 1;
  };

  const showHeader = title || showButtons || header;

  function buttonVisibilityStyle() {
    if (buttonVisibility) {
      return {
        _hover: {},
        _button: {
          position: 'absolute',
          top: position === 'absolute' ? '-40px' : '0px',
          right: 0
        }
      };
    } else {
      return {
        _hover: {
          boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.07)',
          transition: 'box-shadow 0.3s ease',
          '.buttons': {
            display: 'flex !important'
          }
        },
        _button: {
          display: 'none'
        }
      };
    }
  }

  async function handleAIGeneration() {
    // 从接口拿数据
    if (location && specialLocation.includes(location)) {
      // 特殊情况处理
      //
      /**
       * 存在问题模块获取 从接口拿到结论
       * 数据汇总location=2 班级成绩对比=3 近三次测试标准分对比=4 成绩分段对比统计图=5 各名次段人数对比=7 学业等级分布=8
       * 结论
       */
      const result = await fetchGetConclusion();
      // 存在问题模块 需要取出的localtion 序号
      const locationIndex = [2, 3, 4, 5, 7, 8];
      // 存在问题模块的四个结论 localtion 序号
      const problemsLocation = [9, 10, 11, 12];
      // 改进措施模块的四个结论 localtion 序号
      const measureLocation = [13, 14, 15, 16];

      if (problemsLocation.includes(location)) {
        const problemsData =
          result &&
          result
            .filter((item: any) => {
              return locationIndex.includes(item.location);
            })
            .map((item: any) => {
              if (item.location == 2) {
                return { section_name: '数据汇总', conclusion: item.conclusion };
              }
              if (item.location == 3) {
                return { section_name: '班级成绩对比', conclusion: item.conclusion };
              }
              if (item.location == 4) {
                return { section_name: '近三次测试标准分对比', conclusion: item.conclusion };
              }
              if (item.location == 5) {
                return { section_name: '成绩分段对比统计图', conclusion: item.conclusion };
              }
              if (item.location == 7) {
                return { section_name: '各名次段人数对比', conclusion: item.conclusion };
              }
              if (item.location == 8) {
                return { section_name: '学业等级分布', conclusion: item.conclusion };
              }
            });

        // 空结论判断
        const problemsFlag =
          problemsData &&
          problemsData.every((item: any) => {
            return item.conclusion && item.conclusion !== '';
          });
        if (!problemsFlag) {
          Toast.error('请完善所有板块的结论后，再进行AI生成');
          return;
        }

        // 请求参数
        const problemsDataParams = {
          academic_year: selectedYear,
          semester: selectedTerm === 1 ? '第一学期' : '第二学期',
          grade: selectedGradesName,
          test_type: selectedTestData?.[0] || '',
          test_name: '期末质量分析',
          subject: selectedSubjects,
          data_block_name: '存在问题',
          data_render_type: '文字',
          data: problemsData, // 从接口拿数据
          analysis_requirements:
            '请你综合对本次测试的所有数据的分析结论进行分析，分析该年级在本次测试中存在的问题'
        };

        setIsLoading(true);
        await handleAIGenerate(problemsDataParams);
        setIsLoading(false);
      }

      /**
       * 改进措施是拿 存在问题模块的 4个tag的结论
       */
      if (measureLocation.includes(location)) {
        const measureData =
          result &&
          result
            .filter((item: any) => {
              return problemsLocation.includes(item.location);
            })
            .map((item: any) => {
              if (item.location == 9) {
                return { problem_dimension: '教学常规', problem_description: item.conclusion };
              }
              if (item.location == 10) {
                return { problem_dimension: '集体备课', problem_description: item.conclusion };
              }
              if (item.location == 11) {
                return { problem_dimension: '分层教学', problem_description: item.conclusion };
              }
              if (item.location == 12) {
                return { problem_dimension: '其他', problem_description: item.conclusion };
              }
            });
        // 空结论判断
        const measureFlag =
          measureData &&
          measureData.every((item: any) => {
            return item.problem_description && item.problem_description !== '';
          });
        if (!measureFlag) {
          Toast.error('请完善所有板块的结论后，再进行AI生成');
        }
        const measuresAIParams = {
          academic_year: selectedYear,
          semester: selectedTerm === 1 ? '第一学期' : '第二学期',
          grade: selectedGradesName,
          test_type: selectedTestData?.[0] || '',
          test_name: '期末质量分析',
          subject: selectedSubjects,
          data_block_name: '改进措施',
          data_render_type: '文字',
          data: measureData,
          analysis_requirements:
            '请你综合对本次测试的所有数据的分析结论进行分析，分析该年级在本次测试中存在的问题'
        };

        setIsLoading(true);
        await handleAIGenerate(measuresAIParams);
        setIsLoading(false);
      }
    } else {
      setIsLoading(true);
      await handleAIGenerate();
      setIsLoading(false);
    }
  }

  async function fetchGetConclusion() {
    try {
      const result = await getConclusion({
        examId: examId || '',
        gradeId: selectedGradesId || '',
        gradeName: selectedGradesName || '',
        subjectNames: selectedSubjects || []
      });
      return result;
    } catch (error) {
      console.log(error);
    }
  }

  async function handleEditDone() {
    // 存在问题模块的四个结论 localtion 序号
    const problemsLocation = [9, 10, 11, 12];
    // 改进措施模块的四个结论 localtion 序号
    const measureLocation = [13, 14, 15, 16];
    if (location) {
      if (problemsLocation.includes(location) && aiGeneratedConclusion) {
        const data = JSON.parse(aiGeneratedConclusion).data.map(
          (item: any) => item.problem_description
        );
        console.log(data);
        // 点击完成才保存全部结论数据
        setIsLoading(true);
        Promise.all([
          saveAllConclusionData(9, data[0]),
          saveAllConclusionData(10, data[1]),
          saveAllConclusionData(11, data[2]),
          saveAllConclusionData(12, data[3])
        ]).finally(() => {
          setIsLoading(false);
        });
      }
      if (measureLocation.includes(location) && aiGeneratedConclusion) {
        const data = JSON.parse(aiGeneratedConclusion).data.map(
          (item: any) => item.solution_description
        );
        console.log(data);
        // 点击完成才保存全部结论数据
        setIsLoading(true);
        Promise.all([
          saveAllConclusionData(13, data[0]),
          saveAllConclusionData(14, data[1]),
          saveAllConclusionData(15, data[2]),
          saveAllConclusionData(16, data[3])
        ]).finally(() => {
          setIsLoading(false);
        });
      }
    }

    setIsLoading(true);
    handleFinishEdit();
    setIsLoading(false);
  }

  function handleEditCancel() {
    setIsEdit(false);
    setEditInput(content);
  }

  return (
    <Box>
      <Box
        className="board_chart_box"
        borderRadius="8px"
        height={isFullScreen ? fullScreenHeight : height}
        backgroundColor={backgroundColor}
        padding={padding}
        zIndex={isFullScreen ? 1 : 0}
        width={`calc(100% - ${isFullScreen ? 0 : 0}px)`}
        boxSizing="border-box"
        display="flex"
        flexDirection="column"
        _hover={buttonVisibilityStyle()._hover}
        position={position}
      >
        {showHeader && (
          <Flex
            className="box_header"
            justifyContent="space-between"
            alignItems="center"
            marginBottom={headerMarginBottom}
          >
            {header || (
              <>
                {title && (
                  <Text
                    className="box_title"
                    fontWeight="500"
                    color="#1d2129"
                    lineHeight="28px"
                    fontSize={titleFontSize}
                  >
                    {title}
                  </Text>
                )}
                <Flex
                  className="buttons"
                  style={buttonVisibilityStyle()._button as CSSProperties}
                  gap="8px"
                >
                  {editable && canEditAndView() && (
                    <>
                      {!isEdit ? (
                        <Flex>
                          <Box
                            className="button"
                            onClick={() => {
                              setIsLoading(true);
                              handleEdit();
                              setIsLoading(false);
                            }}
                            sx={buttonStyles}
                            mr="12px"
                            _hover={{
                              color: '#4080FF'
                            }}
                          >
                            <span style={{ color: '#4E5969' }}>编辑</span>
                          </Box>
                          <GradientText></GradientText>
                          <Box
                            className="button"
                            onClick={handleAIGeneration}
                            bg="linear-gradient(90deg, rgba(235, 253, 255, 0.9) 0%, rgba(236, 240, 255, 0.95) 100%)"
                            borderRadius="20px"
                            fontSize="12px"
                            color="linear-gradient(318.23deg, #3974FF -6.75%, #3876FF -6.74%, #21CAFF 81.92%)"
                            padding="3px 10px"
                            border="1px solid #DDF2FF"
                            cursor="pointer"
                            display="flex"
                            alignItems="center"
                            h="28px"
                          >
                            <SvgIcon name="generate" pt="2px" />

                            <Box
                              style={{
                                background:
                                  'linear-gradient(318.23deg, #3974FF -6.75%, #3876FF -6.74%, #21CAFF 81.92%)',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                                backgroundClip: 'text',
                                color: 'transparent'
                              }}
                            >
                              <GradientText>AI生成</GradientText>
                            </Box>
                          </Box>
                        </Flex>
                      ) : (
                        <Flex color="#4E5969">
                          <Box
                            className="button"
                            onClick={handleEditDone}
                            sx={buttonStyles}
                            mr="8px"
                          >
                            完成
                          </Box>
                          <Box className="button" onClick={handleEditCancel} sx={buttonStyles}>
                            取消
                          </Box>
                        </Flex>
                      )}
                    </>
                  )}
                  {showFullScreenButtons && (
                    <>
                      {!isFullScreen ? (
                        <SvgIcon
                          name="fullscreen"
                          onClick={toggleFullScreen}
                          cursor="pointer"
                          _hover={{ color: '#175DFF' }}
                          mt="3px"
                          w="20px"
                          h="20px"
                        />
                      ) : (
                        <SvgIcon
                          name="exitFullscreen"
                          onClick={toggleFullScreen}
                          cursor="pointer"
                          _hover={{ color: '#175DFF' }}
                          mt="3px"
                          w="20px"
                          h="20px"
                        />
                      )}
                    </>
                  )}
                </Flex>
              </>
            )}
          </Flex>
        )}

        {isLoading ? (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bg="rgba(255, 255, 255, 0.7)"
            display="flex"
            alignItems="center"
            justifyContent="center"
            zIndex="1"
          >
            <Lottie
              name="chating"
              w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
              h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
            />
          </Box>
        ) : (
          <Box
            className="box_content"
            height={showHeader ? 'calc(100% - 35px)' : '100%'}
            flex="1"
            position="relative"
          >
            {createLoading && (
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                bottom="0"
                bg="rgba(255,255,255,0.7)"
                zIndex="1"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                Loading...
              </Box>
            )}
            {children || (
              <Box
                className="board_content"
                fontWeight="400"
                fontSize="14px"
                color="#4e5969"
                lineHeight="22px"
                height="100%"
                whiteSpace="pre-wrap"
              >
                {!isEdit ? (
                  <Box overflowY="auto" height="100%">
                    {content ? (
                      <Text className="class_item" maxHeight={'200px'}>
                        {content}
                      </Text>
                    ) : (
                      <Text
                        className="class_item"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        pb="6px"
                        height="100%"
                      >
                        当前面板未添加结论，请联系年级主任或学科组长添加结论。
                      </Text>
                    )}
                  </Box>
                ) : (
                  <AntdInput.TextArea
                    value={editInput}
                    onChange={(e) => setEditInput(e.target.value)}
                    placeholder="请输入"
                    // autoSize={{ maxRows: isFullScreen ? 12 : 2 }}
                    style={{
                      height: isFullScreen ? fullScreenHeight : 'auto'
                    }}
                  />
                )}
              </Box>
            )}
            {isFullScreen && hiddenContent}
          </Box>
        )}
      </Box>
    </Box>
  );
}

export default forwardRef(BoardPaneAll);
