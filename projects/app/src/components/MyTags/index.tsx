import React, { useEffect, useRef, useState } from 'react';
import { Box, Flex, BoxProps, FlexProps } from '@chakra-ui/react';
import MyTooltip from '../MyTooltip';
import { respDims } from '@/utils/chakra';

type LabelItemType = {
  id: string | number;
  content: string;
};

type MyTagsProps = {
  labelList: LabelItemType[];
  maxLines?: number;
  labelStyle?: FlexProps;
  containerStyle?: FlexProps;
  overflowLabelStyle?: BoxProps;
} & BoxProps;

const MyTags: React.FC<MyTagsProps> = ({
  labelList,
  maxLines = 1,
  labelStyle,
  containerStyle,
  overflowLabelStyle,
  ...rest
}) => {
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [visibleLabels, setVisibleLabels] = useState<LabelItemType[]>([]);
  const [hiddenCount, setHiddenCount] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.clientWidth;
      let totalWidth = 0;
      const visible: LabelItemType[] = [];

      const fontSize = 12; // 使用默认字体大小

      for (const item of labelList) {
        const itemWidth = item.content.length * fontSize + 50; // 估算标签宽度，50是左右padding的总和

        if (totalWidth + itemWidth <= containerWidth) {
          visible.push(item);
          totalWidth += itemWidth;
        } else {
          break;
        }
      }

      setVisibleLabels(visible);
      setHiddenCount(labelList.length - visible.length);
      setIsOverflowing(visible.length < labelList.length);
    }
  }, [labelList]);

  const defaultLabelStyle: FlexProps = {
    mr: respDims(10),
    justifyContent: 'center',
    alignItems: 'center',
    bg: 'rgba(243,244,246,0.6)',
    border: '1px solid #ECECEC',
    borderRadius: '229px 229px 229px 229px',
    color: '#606266',
    mb: respDims(4),
    fontSize: respDims(13, 10),
    px: respDims(16),
    py: respDims(2)
  };

  const defaultOverflowLabelStyle: BoxProps = {
    mr: respDims(10),
    justifyContent: 'center',
    alignItems: 'center',
    bg: 'rgba(243,244,246,0.6)',
    flexWrap: 'nowrap',
    h: respDims(30),
    border: '1px solid #ECECEC',
    borderRadius: '229px 229px 229px 229px',
    color: '#606266',
    mb: respDims(4),
    fontSize: respDims(13, 10),
    px: respDims(16),
    py: respDims(2)
  };

  return (
    <Box position="relative" {...rest}>
      <Flex
        ref={containerRef}
        flex="1"
        justifyContent="flex-start"
        flexWrap="wrap"
        color="#909399"
        fontSize={respDims(14, 12)}
        my={respDims(10)}
        {...containerStyle}
      >
        {visibleLabels.map((item) => (
          <Flex key={item.id} id={`label-${item.id}`} {...defaultLabelStyle} {...labelStyle}>
            {item.content}
          </Flex>
        ))}
        {isOverflowing && hiddenCount > 0 && (
          <MyTooltip
            label={
              <Flex flexDirection="column">
                {labelList.map((item) => (
                  <Flex
                    key={item.id}
                    id={`label-${item.id}`}
                    {...defaultLabelStyle}
                    {...labelStyle}
                  >
                    {item.content}
                  </Flex>
                ))}
              </Flex>
            }
            bg="#fff"
          >
            <Flex
              {...defaultOverflowLabelStyle}
              {...overflowLabelStyle}
              alignItems="center"
              justifyContent="center"
            >
              +{hiddenCount}
            </Flex>
          </MyTooltip>
        )}
      </Flex>
    </Box>
  );
};

export default MyTags;
