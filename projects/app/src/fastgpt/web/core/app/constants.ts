import { AppTypeEnum } from '@/fastgpt/global/core/app/constants';
import { AppDetailType } from '@/fastgpt/global/core/app/type';
import { i18nT } from '@/utils/tools';

export enum TTSTypeEnum {
  none = 'none',
  web = 'web',
  model = 'model'
}

export const defaultApp: AppDetailType = {
  _id: '',
  name: i18nT('加载中...'),
  type: AppTypeEnum.simple,
  avatar: '/icon/logo.svg',
  intro: '',
  updateTime: new Date(),
  modules: [],
  chatConfig: {},
  teamId: '',
  tmbId: '',
  teamTags: [],
  edges: [],
  version: 'v2',
  defaultPermission: '',
  permission: ''
};
