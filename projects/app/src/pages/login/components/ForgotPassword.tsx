import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message } from 'antd';
import { Box, ModalBody } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { getClientUserSmsCode, setClientAuthResetPwd } from '@/api/user';
import styles from '@/pages/index.module.scss';
import { respDims } from '@/utils/chakra';
import { Toast } from '@/utils/ui/toast';
import SliderCaptchaModal from './SliderCaptchaModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';

const ForgotPassword: React.FC = () => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mobileValid, setMobileValid] = useState(false); // 新增：手机号验证状态
  const { openOverlay, OverlayContainer } = useOverlayManager(); // 使用 Overlay 管理钩子

  // 监听手机号变化
  useEffect(() => {
    const mobile = form.getFieldValue('mobile');
    const isMobileValid = mobile && /^1[3-9]\d{9}$/.test(mobile);
    setMobileValid(isMobileValid);

    // 监听表单字段变化
    const unsubscribe = form.getFieldInstance('mobile')?.props?.onChange?.subscribe(() => {
      const currentMobile = form.getFieldValue('mobile');
      const isValid = currentMobile && /^1[3-9]\d{9}$/.test(currentMobile);
      setMobileValid(isValid);
    });

    return () => {
      unsubscribe?.();
    };
  }, [form]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    form.resetFields();
    setCountdown(0);
    setIsCodeSent(false);
    setMobileValid(false);
  };

  const onSendCode = () => {
    form
      .validateFields(['mobile'])
      .then((values) => {
        const mobile = values.mobile;

        // 打开滑动验证码弹窗
        openOverlay({
          Overlay: SliderCaptchaModal,
          props: {
            mobile,
            onClose: () => {
              setIsSubmitting(false);
            },
            requesting: isSubmitting,
            setRequesting: setIsSubmitting,
            startCountdown: () => {
              setIsCodeSent(true);
              setCountdown(60);
              Toast.success('验证码已发送');
            }
          }
        });
      })
      .catch((error) => {
        // 表单验证失败
      });
  };

  const onSubmit = (values: any) => {
    if (values.password !== values.password1) {
      form.setFields([
        {
          name: 'password1',
          errors: ['两次输入的密码不一致']
        }
      ]);
      return;
    }

    setIsSubmitting(true);
    setClientAuthResetPwd({
      mobile: values.mobile,
      code: values.code,
      password: values.password,
      password1: values.password1
    })
      .then((res) => {
        Toast.success('密码重置成功');
        handleCloseModal();
      })
      .catch((error) => {
        // 处理错误
        Toast.error('重置密码失败，请重试');
      })
      .finally(() => {
        setIsSubmitting(false); // 确保在 finally 中重置 isSubmitting
      });
  };

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0 && isCodeSent) {
      setIsCodeSent(false);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown, isCodeSent]);

  // 获取验证码按钮的颜色
  const getCodeButtonColor = () => {
    if (countdown > 0) return '#CBB8FF';
    return mobileValid ? '#7D4DFF' : '#CBB8FF';
  };

  // 获取验证码按钮是否可点击
  const isCodeButtonClickable = () => {
    return mobileValid && countdown === 0;
  };

  // 修改 handleKeyPress 并添加新的处理函数
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  };

  // 手机号输入处理
  const handleInput = (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    input.value = input.value.replace(/\D/g, ''); // 移除所有非数字字符
    // 触发表单值更新
    form.setFieldsValue({ mobile: input.value });
    // 更新手机号验证状态
    setMobileValid(!!input.value.match(/^1[3-9]\d{9}$/));
  };

  // 验证码输入处理
  const handleInput1 = (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    input.value = input.value.replace(/\D/g, ''); // 移除所有非数字字符
    // 触发表单值更新 - 修改为更新 code 字段
    form.setFieldsValue({ code: input.value });
  };

  return (
    <>
      <Box
        onClick={handleOpenModal}
        color="#7D4DFF"
        textAlign="right"
        mt={respDims(24)}
        cursor="pointer"
        w={respDims(100)}
      >
        忘记密码
      </Box>
      <MyModal isOpen={isModalOpen} title="重置密码" w={respDims(480, 480)}>
        <ModalBody style={{ marginTop: 30, paddingBottom: 0 }}>
          <Form form={form} onFinish={onSubmit} layout="vertical" className={styles['my-form']}>
            <Form.Item
              name="mobile"
              rules={[
                { required: true, message: '请输入手机号' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
              ]}
            >
              <Input
                placeholder="请输入手机号"
                disabled={isCodeSent}
                size="large"
                inputMode="numeric"
                pattern="[0-9]*"
                onKeyPress={handleKeyPress}
                onInput={handleInput}
                maxLength={11}
              />
            </Form.Item>

            <Form.Item name="code" rules={[{ required: true, message: '请输入验证码' }]}>
              <Input
                placeholder="请输入验证码"
                size="large"
                inputMode="numeric"
                pattern="[0-9]*"
                onKeyPress={handleKeyPress}
                onInput={handleInput1}
                suffix={
                  <Box
                    cursor={isCodeButtonClickable() ? 'pointer' : 'not-allowed'}
                    onClick={() => {
                      if (isCodeButtonClickable()) {
                        onSendCode();
                      }
                    }}
                    color={getCodeButtonColor()}
                    fontSize="14px"
                  >
                    {countdown > 0 ? `${countdown}s` : '获取验证码'}
                  </Box>
                }
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入新密码' },
                {
                  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                  message: '密码需8至16位，包含大小写字母和数字的组合，可以输入特殊符号'
                }
              ]}
            >
              <Input.Password placeholder="请输入新密码" size="large" />
            </Form.Item>

            <Form.Item
              name="password1"
              rules={[
                { required: true, message: '请再次输入新密码' },
                {
                  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                  message: '密码需8至16位，包含大小写字母和数字的组合，可以输入特殊符号'
                },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  }
                })
              ]}
            >
              <Input.Password placeholder="请再次输入新密码" size="large" />
            </Form.Item>

            <Form.Item style={{ marginTop: 35, textAlign: 'right' }}>
              <Button onClick={handleCloseModal} style={{ marginRight: 20 }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认修改
              </Button>
            </Form.Item>
          </Form>
        </ModalBody>
      </MyModal>
      <OverlayContainer></OverlayContainer>
    </>
  );
};

export default ForgotPassword;
