import React, { useState, Dispatch, useCallback, useEffect } from 'react';
import {
  FormControl,
  Flex,
  Input,
  Button,
  Divider,
  AbsoluteCenter,
  Box,
  InputRightElement,
  InputGroup,
  Image,
  Spinner,
  Center,
  useDisclosure,
  FormErrorMessage
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { LoginQRCodeParams, TocWxQRCodeResultType, WxTicketType } from '@/types/api/auth';
import { PageTypeEnum } from '@/constants/user';
import {
  getTocWxLoginQRCode,
  getTocWxQRCodeResult,
  postLogin,
  tocWxIsTocTenant,
  TocWxLoginByOpenId
} from '@/api/auth';
import { useTranslation } from 'next-i18next';
import { respDims, rpxDim } from '@/utils/chakra';
import { getDingConfig, getQywxConfig } from '@/api/auth';
import { getLoginQRCode, getQRCodeResult, loginByUnionId } from '@/api/auth';
import { useUserStore } from '@/store/useUserStore';
import { setToken } from '@/utils/auth';
import { useRouter } from 'next/router';
import { APP_ICON } from '@/constants/common';
import { useSystemStore } from '@/store/useSystemStore';
import { TenantType } from '@/types/api/tenant';
import { DingConfig, LoginRes } from '@/types/api/auth';
import { Toast } from '@/utils/ui/toast';
import ReplacePhoneModal from '@/components/Layout/components/ReplacePhoneModal';
import ForgotPassword from './ForgotPassword';
import SliderCaptchaModal from './SliderCaptchaModal';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import CheckAccountActivationModal from './CheckAccountActivationModal';

interface Props {
  setPageType: Dispatch<`${PageTypeEnum}`>;
  loginSuccess: (e: LoginRes) => void;
  tenantData?: TenantType;
}

interface LoginFormType {
  account: string;
  password: string;
}
interface IWwLoginConfig {
  id?: string;
  appid?: string;
  agentid?: string;
  redirectUri: string;
  state?: string;
  href?: string;
}

declare global {
  interface Window {
    DTFrameLogin: (
      containerConfig: { id: string; width: string; height: string },
      loginConfig: {
        redirect_uri: string;
        client_id: string;
        scope: string;
        response_type: string;
        state: string;
        prompt: string;
      },
      onSuccess: (loginResult: { redirectUri: string; authCode: string; state: string }) => void,
      onError: (errorMsg: string) => void
    ) => void;
    WwLogin: (config: IWwLoginConfig) => void;
  }
}

const LoginForm = ({ setPageType, loginSuccess, tenantData }: Props) => {
  const { t } = useTranslation();
  const { isPc } = useSystemStore();
  const { userInfo, setUserInfo } = useUserStore();
  const [showPassword, setShowPassword] = useState(false);
  const [loginMethod, setLoginMethod] = useState('account');
  const [isLoading, setIsLoading] = useState(false);
  const [talkLoading, setTalkLoading] = useState(false);
  const [timer, setTimer] = useState(null as any);
  const [ticket, setTicket] = useState('');
  const router = useRouter();
  const { lastRoute = '' } = router.query as { lastRoute: string };
  const [qrCode, setQRCode] = useState('');
  const [unionId, setUnionId] = useState('');
  const [isTocTenant, setIsTocTenant] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<LoginFormType>();

  const {
    isOpen: isOpenReplacePhone,
    onClose: onCloseReplacePhone,
    onOpen: onOpenReplacePhone
  } = useDisclosure();

  const { openOverlay, OverlayContainer } = useOverlayManager();

  const {
    openOverlay: openOverlayCheckAccount,
    OverlayContainer: OverlayCheckAccountActivationModal,
    closeOverlay: closeOverlayCheckAccount
  } = useOverlayManager();
  const handleClick = () => setShowPassword(!showPassword);

  const onclickLogin = useCallback(
    async ({ account, password }: LoginFormType) => {
      setIsLoading(true);
      try {
        // 打开滑动验证码弹窗
        openOverlay({
          Overlay: SliderCaptchaModal,
          props: {
            mobile: account,
            password,
            onClose: () => {
              setIsLoading(false);
            },
            requesting: isLoading,
            setRequesting: setIsLoading,
            onSuccess: onAfterSliderCaptcha
          }
        });
      } catch (error) {
        setIsLoading(false);
      }
    },
    [loginSuccess, Toast]
  );

  const onAfterSliderCaptcha = async (params: LoginRes) => {
    if (params.status === '0') {
      setToken(params.accessToken);
      // 调用首次登录修改密码弹窗
      openOverlayCheckAccount({
        Overlay: CheckAccountActivationModal,
        props: {
          onSuccess: (data: any) => {
            // 关闭登录Loading
            setIsLoading(false);
            // 成功之后关闭弹窗
            closeOverlayCheckAccount('checkAccountActivationModal');
            setValue('password', '');
            Toast.success('密码修改成功，请使用新密码重新登录。');
          }
        },
        name: 'checkAccountActivationModal'
      });
      return;
    }
    loginSuccess(params);
    Toast.success('登录成功');
    setIsLoading(false);
  };

  const fetchLoginConfig = (type: string) => {
    setTalkLoading(true);
    if (type == 'ddLogin') {
      getDingConfig()
        .then((res) => {
          const script = document.createElement('script');
          script.src = 'https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js';
          setTalkLoading(false);
          script.onload = () => {
            dingTalkLogin(res);
          };
          document.body.appendChild(script);
        })
        .catch((err) => {
          Toast.error(err.message);
          setTalkLoading(false);
        });
    }
    if (type == 'qywxLogin') {
      getQywxConfig()
        .then((res) => {
          const script = document.createElement('script');
          script.src = 'http://rescdn.qqmail.com/node/ww/wwopenmng/js/sso/wwLogin-1.0.0.js';
          script.async = true;
          setTalkLoading(false);
          script.onload = () => {
            if (window.WwLogin) {
              const params = {
                id: 'wx_reg',
                appid: res.appid,
                agentid: res.agentid,
                redirectUri: res.redirectUri,
                state: 'state',
                href: 'href'
              };
              window.WwLogin(params);
            }
          };
          document.body.appendChild(script);
        })
        .catch((err) => {
          Toast.error(err.message);
          setTalkLoading(false);
        });
    }
  };
  const handleLogin = (loginType: string) => {
    setLoginMethod(loginType);
    fetchLoginConfig(loginType);
  };

  const ddLogin = () => handleLogin('ddLogin');
  const qywxLogin = () => handleLogin('qywxLogin');

  const dingTalkLogin = (res: DingConfig) => {
    window.DTFrameLogin(
      {
        id: 'dingtalk_login_container',
        width: '100%',
        height: '100%'
      },
      {
        redirect_uri: res.redirectUri,
        client_id: res.clientId,
        scope: 'openid',
        response_type: 'code',
        state: 'state',
        prompt: 'consent'
      },
      (loginResult) => {
        const { authCode, state } = loginResult;
        try {
          Toast.success('登录成功');
          setTimeout(() => {
            window.location.href = `/huayun-ai/ding/thirdlogin/dinglogin?authCode=${encodeURIComponent(authCode)}&state=${encodeURIComponent(state)}`;
          }, 600);
        } catch (error) {
          throw error;
        }
      },
      (errorMsg) => {
        // 处理登录失败
        console.error('Login Error:', errorMsg);
      }
    );
  };

  const wxLogin = async () => {
    try {
      setLoginMethod('weixin');
      setTalkLoading(true);
      const data = await tocWxIsTocTenant();
      setIsTocTenant(data.data);
      const getQRCode = data.data ? getTocWxLoginQRCode : getLoginQRCode;
      const res = await getQRCode();

      if (res.ticket) {
        setQRCode(res.ticket);
        setTicket(res.ticket);
      }
    } catch (err) {
    } finally {
      setTalkLoading(false);
    }
  };

  const onCloseModal = () => {
    onCloseReplacePhone();
    if (!userInfo) {
      wxLogin();
    }
  };

  const loginQRCodeResult = async () => {
    try {
      const res = isTocTenant ? await getTocWxQRCodeResult(ticket) : await getQRCodeResult(ticket);
      if (res) {
        isTocTenant
          ? await handleTocResultData(res as TocWxQRCodeResultType)
          : await handleResultData(res as WxTicketType);
      } else {
        const newTimer = setTimeout(() => loginQRCodeResult(), 2000);
        setTimer(newTimer);
      }
    } catch (error) {
      console.error(error);
      const newTimer = setTimeout(() => loginQRCodeResult(), 2000);
      setTimer(newTimer);
    }
  };

  const handleTocResultData = async (data: TocWxQRCodeResultType) => {
    try {
      if (timer) {
        clearTimeout(timer);
        setTimer(null);
      }
      const res = await TocWxLoginByOpenId(data.openid);
      if (res) {
        handleLoginSuccess(res);
      } else {
        if (timer) {
          clearTimeout(timer);
          setTimer(null);
        }
      }
    } catch (error) {
      wxLogin();
    }
  };
  const handleResultData = async (data: WxTicketType) => {
    try {
      if (timer) {
        clearTimeout(timer);
        setTimer(null);
      }
      setUnionId(data.unionid);
      const res = await loginByUnionId(data.unionid);
      if (res) {
        handleLoginSuccess(res);
        Toast.success('登录成功');
      } else {
        onOpenReplacePhone();
        if (timer) {
          clearTimeout(timer);
          setTimer(null);
        }
      }
    } catch (error) {
      wxLogin();
    }
  };

  const handleLoginSuccess = async (res: LoginRes) => {
    setUserInfo(res);
    setToken(res.accessToken);
    setTimeout(() => {
      router.push(lastRoute ? decodeURIComponent(lastRoute) : '/');
    }, 300);
    Toast.success('登录成功');
  };

  useEffect(() => {
    if (timer) {
      clearTimeout(timer);
      setTimer(null);
    }
    if (loginMethod === 'weixin' && ticket) {
      loginQRCodeResult();
    }
  }, [loginMethod, ticket]);

  return (
    <Flex
      flexDirection="column"
      h="100%"
      ml={respDims('32rpx', 45)}
      mr={respDims('32rpx', 45)}
      alignItems="center"
      w={['100%', '80%']}
      justifyContent="center"
    >
      <Flex flexDirection="column" w={['100%', '100%']} mb={respDims('0rpx', 4)}>
        {!isPc && (
          <Center>
            <Image
              src={tenantData?.fullNameImgUrl}
              alt=""
              fallbackSrc={APP_ICON}
              objectFit={'contain'}
              h={rpxDim(130)}
              mb={rpxDim(80)}
            />
          </Center>
        )}

        {isPc ? (
          <Box w="70%" alignSelf="center">
            <Box mb="8px" fontSize={respDims(30, 16)} fontWeight={'bold'}>
              欢迎使用
            </Box>
            {tenantData && tenantData.fullName ? (
              <Box fontSize={respDims(30, 16)} fontWeight={'bold'} whiteSpace="pre-wrap">
                {tenantData.fullName}
              </Box>
            ) : (
              <></>
            )}
          </Box>
        ) : (
          <>
            {tenantData && tenantData.fullName ? (
              <Center
                fontSize={rpxDim(32)}
                color="#2771DF"
                fontWeight="500"
                borderTopLeftRadius={rpxDim(30)}
                borderTopRightRadius={rpxDim(30)}
                borderLeft="2px solid #fff"
                borderRight="2px solid #fff"
                borderTop="2px solid #fff"
                pt={rpxDim(16)}
                pb={rpxDim(16)}
                whiteSpace="pre-wrap"
              >
                欢迎使用,{tenantData.fullName}
              </Center>
            ) : (
              <></>
            )}
          </>
        )}
      </Flex>
      {loginMethod == 'account' && (
        <Box
          {...(isPc
            ? {
                w: '70%'
              }
            : {
                w: '100%'
              })}
          mt={respDims('0rpx', 42)}
          alignSelf="center"
          {...(!isPc && {
            bg: '#fff'
          })}
          onKeyDown={(e) => {
            if (e.keyCode === 13 && !e.shiftKey && !isLoading) {
              handleSubmit(onclickLogin)();
            }
          }}
        >
          <FormControl
            isInvalid={!!errors.account}
            mt={respDims('24rpx', 0)}
            ml={respDims('40rpx', 0)}
            mr={respDims('38rpx', 0)}
            {...(!isPc && { w: 'auto' })}
          >
            <Input
              borderRadius="50px"
              placeholder="请输入账号"
              {...register('account', {
                required: '账号不能为空'
              })}
              {...(isPc
                ? {
                    bg: '#F3F4F6',
                    h: '56px',
                    minW: '200px',
                    fontSize: '16px'
                  }
                : {
                    fontSize: '32rpx',
                    bg: '#F3F4F6',
                    ml: '48rpx',
                    h: rpxDim(92),
                    _placeholder: {
                      color: '#A8ABB2'
                    }
                  })}
            ></Input>
            {errors.account && (
              <FormErrorMessage ml="20px">{errors.account.message}</FormErrorMessage>
            )}
          </FormControl>
          <FormControl
            {...(!isPc && { w: 'auto' })}
            isInvalid={!!errors.password}
            mt={respDims('40rpx', 24)}
            ml={respDims('40rpx', 0)}
            mr={respDims('38rpx', 0)}
          >
            <InputGroup size="md">
              <Input
                borderRadius="50px"
                {...(isPc
                  ? {
                      bg: '#F3F4F6',
                      h: '56px',
                      minW: '200px',
                      fontSize: '16px'
                    }
                  : {
                      fontSize: '32rpx',
                      bg: '#F3F4F6',
                      ml: '48rpx',
                      h: rpxDim(92),
                      _placeholder: {
                        color: '#A8ABB2'
                      }
                    })}
                type={showPassword ? 'text' : 'password'}
                placeholder="请输入登录密码"
                {...register('password', {
                  required: '密码不能为空'
                  // minLength: { value: 8, message: '密码最少8位' }
                  // pattern: {
                  //   value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,16}$/,
                  //   message: '密码需包含大小写字母和数字的组合，可以使用特殊字符'
                  // }
                })}
              ></Input>

              <InputRightElement width="4.5rem" {...(!isPc ? { mt: rpxDim(26) } : { mt: '8px' })}>
                {!showPassword ? (
                  <Image onClick={handleClick} src={'/icon/login-browse-off.svg'} alt="" />
                ) : (
                  <Image onClick={handleClick} src={'/icon/login-browse.svg'} alt="" />
                )}
              </InputRightElement>
            </InputGroup>
            {errors.password && (
              <FormErrorMessage ml="20px">{errors.password.message}</FormErrorMessage>
            )}
            <Box display="flex" justifyContent="flex-end">
              <ForgotPassword />
            </Box>
          </FormControl>

          <Button
            type="submit"
            my={respDims('40rpx', 32)}
            {...(!isPc ? { w: '88%', h: rpxDim(92) } : { w: '100%', minW: '200px', h: '56px' })}
            size={'lg'}
            ml={respDims('42rpx', 0)}
            mr={respDims('36rpx', 0)}
            colorScheme="blue"
            borderRadius={respDims('100rpx', 50)}
            isLoading={isLoading}
            onClick={handleSubmit(onclickLogin)}
          >
            {t('home.Login')}
          </Button>
        </Box>
      )}
      {loginMethod == 'qywxLogin' && (
        <Flex
          mt="42px"
          flexDir="column"
          p="0 20px 0 20px"
          w="296px"
          h="309px"
          alignItems="center"
          justifyContent="center"
          boxShadow="0px 0px 10px 0px rgba(48,55,88,0.05), 0px 0px 28px 0px rgba(44,54,66,0.05)"
          borderRadius="15px"
          minH="309px"
          overflow="hidden"
        >
          {talkLoading ? (
            <Spinner size="xl" />
          ) : (
            <>
              <Box id="wx_reg" mt="-41px" overflow="hidden"></Box>
              <Flex alignItems="center" justifyContent="center" mt="6px">
                <Image src={'/icon/login-qywx.svg'} alt="" h="26px" mr="7px" />
                <Box fontSize={respDims(14, 12)} color="#303133">
                  企业微信扫码
                </Box>
              </Flex>
            </>
          )}
        </Flex>
      )}
      {loginMethod == 'ddLogin' && (
        <Flex
          mt="42px"
          flexDir="column"
          p="12px 24px 24px 24px"
          w="296px"
          h="309px"
          alignItems="center"
          justifyContent="center"
          boxShadow="0px 0px 10px 0px rgba(48,55,88,0.05), 0px 0px 28px 0px rgba(44,54,66,0.05)"
          borderRadius="15px"
        >
          {talkLoading ? (
            <Spinner size="xl" />
          ) : (
            <>
              <Box
                id="dingtalk_login_container"
                display="flex"
                alignItems="center"
                justifyContent="center"
                w="100%"
                h="309px"
              />
              <Flex alignItems="center" justifyContent="center" mb="-16px">
                <Image src={'/icon/login-dd.svg'} alt="" h="26px" mr="7px" />
                <Box fontSize={respDims(14, 12)} color="#303133">
                  钉钉扫码
                </Box>
              </Flex>
            </>
          )}
        </Flex>
      )}
      {loginMethod == 'weixin' && (
        <Flex
          mt="42px"
          flexDir="column"
          p="0 24px 0 24px"
          w="296px"
          h="309px"
          alignItems="center"
          justifyContent="center"
          boxShadow="0px 0px 10px 0px rgba(48,55,88,0.05), 0px 0px 28px 0px rgba(44,54,66,0.05)"
          borderRadius="15px"
        >
          {talkLoading ? (
            <Spinner size="xl" />
          ) : (
            <>
              {qrCode && (
                <Box>
                  <Image
                    src={`https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${qrCode}`}
                    alt=""
                  />
                </Box>
              )}

              <Flex alignItems="center" justifyContent="center" mt="6px">
                <Image src={'/icon/login_weixin.png'} alt="" h="26px" mr="7px" />
                <Box fontSize={respDims(14, 12)} color="#303133">
                  微信扫码
                </Box>
              </Flex>
            </>
          )}
        </Flex>
      )}

      {isPc && (
        <>
          <Box position={'relative'} {...(isPc && { w: '100%', py: '35px' })}>
            <Divider />
            <AbsoluteCenter {...(isPc && { bg: 'white', px: '4', color: 'myGray.500' })}>
              更多方式
            </AbsoluteCenter>
          </Box>
        </>
      )}

      {isPc && (
        <Flex alignItems="center" justifyContent="center">
          {(loginMethod == 'ddLogin' || loginMethod == 'qywxLogin' || loginMethod == 'weixin') && (
            <Flex
              alignItems="center"
              borderRadius="8px"
              backgroundColor="#fff"
              border="1px solid #E5E7EB"
              p="10px 12px 10px 12px"
              mr="16px"
              cursor="pointer"
              onClick={() => {
                setLoginMethod('account');
              }}
            >
              <Image src={'/icon/login-phone.svg'} alt="" h="24px" mr="6px" />
              <Box fontSize={respDims(14, 12)} color="#303133">
                账号密码登录
              </Box>
            </Flex>
          )}

          {(loginMethod == 'account' || loginMethod == 'ddLogin' || loginMethod == 'weixin') &&
            tenantData?.qywxAppId && (
              <Box
                borderRadius="8px"
                backgroundColor="#fff"
                border="1px solid #E5E7EB"
                p="7px 6px 7px 8px"
                mr="16px"
                cursor="pointer"
                onClick={() => {
                  qywxLogin();
                }}
              >
                <Image src={'/icon/login-qywx.svg'} alt="" h="26px" />
              </Box>
            )}

          {(loginMethod == 'account' || loginMethod == 'qywxLogin' || loginMethod == 'weixin') &&
            tenantData?.dingAgentId && (
              <Box
                borderRadius="8px"
                backgroundColor="#fff"
                border="1px solid #E5E7EB"
                p="7px 6px 7px 8px"
                cursor="pointer"
                mr="16px"
                onClick={() => {
                  ddLogin();
                }}
              >
                <Image src={'/icon/login-dd.svg'} alt="" h="26px" />
              </Box>
            )}

          {(loginMethod == 'account' || loginMethod == 'qywxLogin' || loginMethod == 'ddLogin') && (
            <Box
              borderRadius="8px"
              backgroundColor="#fff"
              border="1px solid #E5E7EB"
              p="7px 6px 7px 8px"
              mr="16px"
              cursor="pointer"
              onClick={() => {
                wxLogin();
              }}
            >
              <Image src={'/icon/login_weixin.png'} alt="" h="26px" />
            </Box>
          )}
        </Flex>
      )}
      {isOpenReplacePhone && (
        <ReplacePhoneModal
          unionId={unionId}
          title={'绑定手机号'}
          isBindPhone={true}
          onClose={() => {
            onCloseModal();
          }}
        />
      )}
      <OverlayContainer></OverlayContainer>
    </Flex>
  );
};

export default LoginForm;
