import React, { useState, useCallback } from 'react';
import { Box, Flex, Image } from '@chakra-ui/react';
import { PageTypeEnum } from '@/constants/user';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import LoginForm from './components/LoginForm';
import { serviceSideProps } from '@/utils/i18n';
import { setToken } from '@/utils/auth';
import { respDims, rpxDim } from '@/utils/chakra';
import { useSystemStore } from '@/store/useSystemStore';
import { useTenantStore } from '@/store/useTenantStore';
import { LoginRes } from '@/types/api/auth';

const Login = () => {
  const LOGO_ICON = `/imgs/common/logoAvatar.png`;
  const { isPc } = useSystemStore();
  const router = useRouter();
  const { lastRoute = '' } = router.query as { lastRoute: string };
  const [pageType, setPageType] = useState<`${PageTypeEnum}`>(PageTypeEnum.Login);
  const { tenant } = useTenantStore();
  const { setUserInfo } = useUserStore();

  const loginSuccess = useCallback(
    (res: LoginRes) => {
      setUserInfo(res);
      setToken(res.accessToken);
      setTimeout(() => {
        router.push(lastRoute ? decodeURIComponent(lastRoute) : '/');
      }, 300);
    },
    [setUserInfo, router, lastRoute]
  );

  function DynamicComponent({ type }: { type: `${PageTypeEnum}` }) {
    const TypeMap = {
      [PageTypeEnum.Login]: LoginForm
    };

    const Component = TypeMap[type];

    return <Component setPageType={setPageType} loginSuccess={loginSuccess} tenantData={tenant} />;
  }

  return (
    <>
      <Flex
        flexDir="column"
        bg={`url(${tenant?.backgroundImgUrl || '/icon/login-bg.png'})`}
        backgroundSize="cover"
        userSelect="none"
        backgroundRepeat="no-repeat"
        w={['100%', 'auto']}
        h="100%"
        justifyContent="space-between"
        overflow="hidden"
      >
        <Box>
          <Image
            src={tenant?.fullNameImgUrl}
            alt=""
            fallbackSrc={LOGO_ICON}
            h={respDims('0rpx', 50)}
            mt={respDims('0rpx', 27)}
            mb={respDims('0rpx', 31)}
            ml={respDims('0rpx', 44)}
          />
        </Box>
        <Flex
          flex="1"
          alignItems="center"
          justifyContent="center"
          w={['100%', 'auto']}
          h={['100%', 'auto']}
          overflow="hidden"
          ml={respDims('0rpx', 150)}
          mr={respDims('0rpx', 150)}
          mb={respDims('0rpx', 108)}
        >
          <Image
            {...(isPc
              ? {
                  w: '100%',
                  h: '100%',
                  minW: '400px'
                }
              : {
                  w: rpxDim(0),
                  h: rpxDim(0)
                })}
            src={tenant?.sidebarImgUrl}
            alt=""
            fallbackSrc={'/icon/common_school_bg.png'}
            objectFit="cover"
            objectPosition="center"
          />

          <Flex
            {...(isPc
              ? {
                  bg: 'white',
                  w: '100%',
                  h: '100%',
                  minW: '400px'
                }
              : {
                  bg: `url(${tenant?.backgroundImgUrl || '/icon/login-bg.png'})`,
                  backgroundRepeat: 'no-repeat',
                  bgSize: 'cover',
                  bgPosition: 'center',
                  w: '100%',
                  h: '100%',
                  maxH: '100%'
                })}
            justifyContent="center"
          >
            <DynamicComponent type={pageType} />
          </Flex>
        </Flex>
      </Flex>
    </>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: { ...(await serviceSideProps(context)) }
  };
}

export default Login;
