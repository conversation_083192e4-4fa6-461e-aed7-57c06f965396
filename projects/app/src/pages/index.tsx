import { RouteGroupTypeEnum } from '@/constants/routes';
import { useRoutes } from '@/hooks/useRoutes';
import { traverseRoutes } from '@/routes';
import { serviceSideProps } from '@/utils/i18n';
import { Toast } from '@/utils/ui/toast';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

const Index = () => {
  const { routeGroup, routeGroupMap } = useRoutes();
  const adminRouteGroup = routeGroupMap[RouteGroupTypeEnum.Admin];
  const router = useRouter();
  useEffect(() => {
    if (adminRouteGroup) {
      if (adminRouteGroup.navRoutes.length > 0) {
        const path = traverseRoutes(adminRouteGroup?.navRoutes, (it) => it.path);
        path && router.push(path);
      } else {
        Toast.warning('当前页面没有权限');
      }
    }
  }, [adminRouteGroup]);
  return <></>;
};

export async function getServerSideProps(context: any) {
  const { query } = context;
  const queryString = new URLSearchParams(query).toString();
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Index;
