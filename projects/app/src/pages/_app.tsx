import { useEffect, useState } from 'react';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { ChakraProvider, ColorModeScript } from '@chakra-ui/react';
import Layout from '@/components/Layout';
import { theme } from '@/styles/theme';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import NProgress from 'nprogress'; //nprogress module
import Router from 'next/router';
import { initSystemData } from '@/utils/system';
import { appWithTranslation, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { change2DefaultLng, setLngStore } from '@/utils/i18n';

import 'nprogress/nprogress.css';
import '@/styles/reset.scss';
import { useUserStore } from '@/store/useUserStore';
import { getToken, setToken } from '@/utils/auth';

import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { useTenantStore } from '@/store/useTenantStore';
import PortalProvider from '@/components/PortalProvider';
import RoutesProvider from '@/components/RoutesProvider';
import { LayoutProvider } from '@/components/LayoutProvider';
import { ClickToComponent } from 'click-to-react-component';

//Binding events.
Router.events.on('routeChangeStart', () => NProgress.start());
Router.events.on('routeChangeComplete', () => NProgress.done());
Router.events.on('routeChangeError', () => NProgress.done());

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      keepPreviousData: true,
      refetchOnWindowFocus: false,
      retry: false,
      cacheTime: 10
    }
  }
});

function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const { token, pathname } = router.query as { hiId?: string; token?: string; pathname?: string };
  const { i18n } = useTranslation();
  const [title, setTitle] = useState('AI平台');
  const { userInfo, setUserInfo } = useUserStore();
  const { tenant, loadTenant } = useTenantStore();

  useEffect(() => {
    // add window error track
    window.onerror = function (msg, url) {
      window.umami?.track('windowError', {
        device: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          appName: navigator.appName
        },
        msg,
        url
      });
    };

    return () => {
      window.onerror = null;
    };
  }, []);

  useEffect(() => {
    // get default language
    const targetLng = change2DefaultLng(i18n.language);
    if (targetLng) {
      setLngStore(targetLng);
      router.replace(router.asPath, undefined, { locale: targetLng });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setTitle(tenant?.name ? `${tenant.name}AI平台` : 'AI平台');
  }, [tenant?.name]);

  useEffect(() => {
    console.log(pathname);

    if (token) {
      setUserInfo(null);
      setToken(token);
      const query = { ...router.query };
      delete query.token;
      delete query.pathname;

      if (pathname) {
        router.replace({
          pathname,
          query
        });
      } else {
        router.replace({
          pathname: router.pathname,
          query
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, pathname, setUserInfo]);

  useEffect(() => {
    loadTenant();
  }, [loadTenant]);

  useEffect(() => {
    userInfo?.userId && initSystemData();
  }, [userInfo?.userId]);
  return (
    <>
      <ClickToComponent editor="cursor" />
      <Head>
        <title>{title}</title>
        <meta
          name="description"
          content={`华云校园专属AI平台，让每所学校成为AI时代的领航者，每位教师成为AI时代的先行者，每个学生成为AI时代的受益者。`}
        />
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no, viewport-fit=cover"
        />
        {tenant?.avatarUrl && <link rel="icon" href={tenant?.avatarUrl} />}

        <script
          dangerouslySetInnerHTML={{
            __html: `
            console.log('Clarity Analytics Script Loaded');
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "ofcg6mom4x");
          `
          }}
        />
      </Head>

      <QueryClientProvider client={queryClient}>
        <ChakraProvider theme={theme}>
          <ColorModeScript initialColorMode={theme.config.initialColorMode} />
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#3366FF'
              }
            }}
          >
            <LayoutProvider>
              <RoutesProvider>
                <PortalProvider>
                  <Layout>
                    <Component {...pageProps} />
                  </Layout>
                </PortalProvider>
              </RoutesProvider>
            </LayoutProvider>
          </ConfigProvider>
        </ChakraProvider>
      </QueryClientProvider>
    </>
  );
}

// @ts-ignore
export default appWithTranslation(App);
