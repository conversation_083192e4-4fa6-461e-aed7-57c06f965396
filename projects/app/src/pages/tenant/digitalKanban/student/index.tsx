import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  Image,
  Text,
  VStack,
  HStack,
  Divider,
  SimpleGrid,
  Input
} from '@chakra-ui/react';
import EChartsReact from 'echarts-for-react';
import {
  individualCommentOption,
  individualSuitableChartOptions,
  cultureKnowChartOptions,
  homeCommentOptions,
  communityCommentOptions,
  studentInfoDefault
} from './chartOptions';
import { useRouter } from 'next/router';
import { nanoid } from 'nanoid';
import dayjs from 'dayjs';
import StatisticItem from '@/components/StatisticItem';
import BoardPane from '@/components/BoardPane';
import ScrollableContent from '@/components/ScrollableContent';
import { Select, Table } from 'antd';
import { serviceSideProps } from '@/utils/i18n';
// import { streamFetch } from "@/utils/ai/index";

const BoardIndex = () => {
  const router = useRouter();
  const tooltipRef = useRef(null);
  const dialogRef = useRef(null);
  const TeacherCommentsDialogRef = useRef(null);
  const [createLoading, setCreateLoading] = useState(false);
  const [activeName, setActiveName] = useState('comprehensiveKanBan');
  const [yearValue, setYearValue] = useState('2023-2024学年');
  const [searchInput, setSearchInput] = useState('');
  const [gradeValue, setGradeValue] = useState([]);
  const [classValue, setClassValue] = useState('全部');
  const [schoolValue, setSchoolValue] = useState('林圳南 0123323232');
  const [evaluateValue, setEvaluateValue] = useState('教师评价');
  const [testNamValue, setTestNamValue] = useState('期末质量分析');
  const [subjectValue, setSubjectValue] = useState('全部');
  const [evaluationDimensionValue, setEvaluationDimensionValue] = useState('学科核心素养');

  const statistics = [
    { content: '学生学号', num: '0123323232' },
    { content: '学生姓名', num: '林圳南' },
    { content: '班级', num: '七(四)班' },
    { content: '本校培养目标总评', num: 'A' },
    { content: '学习适应性总评', num: 'B' },
    { content: '平均过程表现综合评价', num: 'B' },
    { content: '平均展示性综合评价', num: 'B' },
    { content: '平均发展综合评价', num: 'B' }
  ];

  const yearOptions = [
    {
      value: 1,
      label: '2019-2020学年'
    },
    {
      value: 2,
      label: '2020-2021学年'
    },
    {
      value: 3,
      label: '2021-2022学年'
    },
    {
      value: 4,
      label: '2022-2023学年'
    },
    {
      value: 5,
      label: '2023-2024学年'
    }
  ];

  const gradeOptions = [
    {
      value: 1,
      label: '一年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 2,
      label: '二年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 3,
      label: '三年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 4,
      label: '四年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 5,
      label: '五年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 6,
      label: '六年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 7,
      label: '七年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 8,
      label: '八年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 9,
      label: '九年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    }
  ];

  const schoolOptions = [
    {
      value: 1,
      label: '王艺皓 4647377440'
    },
    {
      value: 2,
      label: '周浩辰 4286537201'
    },
    {
      value: 3,
      label: '徐弘峰 1471184131'
    },
    {
      value: 4,
      label: '刘羽天 9503571697'
    },
    {
      value: 5,
      label: '李宇强 8321337517'
    },
    {
      value: 6,
      label: '赵嘉辉 2158487605'
    },
    {
      value: 7,
      label: '陈昊城 7091690875'
    },
    {
      value: 8,
      label: '孙钧辰 7861367383'
    },
    {
      value: 9,
      label: '杨睿洋 8707340826'
    },
    {
      value: 10,
      label: '胡瑞天 6241742913'
    },
    {
      value: 11,
      label: '高正南 6293698314'
    },
    {
      value: 12,
      label: '马嘉德 5428781342'
    },
    {
      value: 13,
      label: '张浩东 1678333193'
    },
    {
      value: 14,
      label: '何子辰 1578946250'
    },
    {
      value: 15,
      label: '罗皓威 4152728486'
    },
    {
      value: 16,
      label: '林嘉辉 7741689615'
    },
    {
      value: 17,
      label: '吴艺航 3442923450'
    },
    {
      value: 18,
      label: '郭泽正 2894731504'
    },
    {
      value: 19,
      label: '王泽博 8167234539'
    },
    {
      value: 20,
      label: '周鹏皓 4867983642'
    },
    {
      value: 21,
      label: '徐天羽 9582867611'
    },
    {
      value: 22,
      label: '刘弘泽 3731504962'
    },
    {
      value: 23,
      label: '李强嘉 6492813752'
    },
    {
      value: 24,
      label: '赵瑞辰 5274389203'
    },
    {
      value: 25,
      label: '陈艺南 4602734857'
    },
    {
      value: 26,
      label: '孙羽峰 7810563928'
    },
    {
      value: 27,
      label: '杨子辉 2461739850'
    },
    {
      value: 28,
      label: '胡弘天 1597326408'
    },
    {
      value: 29,
      label: '高瑞皓 4385761234'
    },
    {
      value: 30,
      label: '马强南 3984172506'
    },
    {
      value: 31,
      label: '张泽洋 6928431072'
    },
    {
      value: 32,
      label: '何子博 1573286498'
    },
    {
      value: 33,
      label: '罗天鹏 4827605193'
    },
    {
      value: 34,
      label: '林嘉南 6428931750'
    },
    {
      value: 35,
      label: '吴艺正 3758264091'
    },
    {
      value: 36,
      label: '郭浩洋 1265387402'
    },
    {
      value: 37,
      label: '王浩泽 3941725061'
    },
    {
      value: 38,
      label: '周子辰 8612735096'
    },
    {
      value: 39,
      label: '徐天博 2194857360'
    },
    {
      value: 40,
      label: '刘强羽 5762819340'
    },
    {
      value: 41,
      label: '李泽辉 6729403185'
    },
    {
      value: 42,
      label: '赵睿航 5872301469'
    },
    {
      value: 43,
      label: '陈正辉 4382160753'
    },
    {
      value: 44,
      label: '孙钧洋 7428956130'
    }
  ];

  const evaluateOptions = [
    {
      value: 1,
      label: '自我评定'
    },
    {
      value: 2,
      label: '团队评议'
    },
    {
      value: 3,
      label: '教师评价'
    }
  ];

  const teacherSayInfo = {
    teacherMessage:
      '你是一个聪明，善于开动脑筋，勇于探索，富有进取心的学生，老师欣赏你;你学习自觉勤奋，有良好的学习习惯，成绩优秀稳定。课堂上，你聪明、具有创造力的提问，令人暗暗称赞。你严于律己，乐于助人，热爱集体。书写方面只要你能再认真些，你的字会更漂亮!加油!',
    achievementRecord: `语文：在期中考试中取得了班级第一名的好成绩,作文《我的家乡》被评为校级优秀作文.
    数学：在数学竞赛中获得了二等奖,展示了出色的逻辑思维能力和解题技巧.
    英语：在英语口语比赛中表现优异,获得了三等奖,口语表达能力显著提升`
  };

  const studentInfo = studentInfoDefault;

  const handleAiCreate = async () => {
    // try {
    //   const aiCreateInfo = await TeacherCommentsDialogRef.current?.openDialog();
    //   if (aiCreateInfo) {
    //     // 调用生成ai接口，
    //     console.log(aiCreateInfo);
    //     const content = `在这个学期里，该同学在学业表现方面${aiCreateInfo?.academicPerformance}，在品德修养方面${aiCreateInfo?.moralDevelopment}，在行为习惯方面${aiCreateInfo?.behaviorHabits}，以及在心理素质方面${aiCreateInfo?.psychologicalQuality}。`;
    //     const data = {
    //       messages: [
    //         {
    //           dataId: nanoid(),
    //           role: 'user',
    //           content,
    //           fileRefs: []
    //         },
    //         {
    //           dataId: nanoid(),
    //           role: 'assistant',
    //           content: ''
    //         }
    //       ],
    //       variables: {
    //         cTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    //       },
    //       appId: process.env.VUE_APP_AI_CREATESAY_APP_ID, // 使用环境变量
    //       chatId: '',
    //       detail: true,
    //       stream: true
    //     };
    //     // 定义 onMessage 函数
    //     const onMessage = (message: any) => {
    //       // console.log("Received message:", message.text);
    //     };
    //     const abortCtrl = new AbortController();
    //     setCreateLoading(true);
    //     try {
    //       // const response = await streamFetch({
    //       //   url: '/api/v1/chat/completions',
    //       //   data,
    //       //   onMessage,
    //       //   abortCtrl
    //       // });
    //       // console.log(response);
    //       // teacherSayInfo.teacherMessage = response.responseText;
    //     } catch (error) {
    //       console.error(error);
    //     }
    //     setCreateLoading(false);
    //   }
    //   console.log('Dialog confirmed');
    // } catch (error) {
    //   console.error('Dialog cancelled or closed', error);
    // }
  };

  const handleChartClick = () => {
    router.push({
      pathname: '/personalDevelopRadar'
    });
  };

  return (
    <Box className="student_kanban" padding="0 24px">
      <Flex mb="12px">
        <Flex bgColor="#fff" alignItems="center" maxW="63%" borderRadius="12px" flex="1">
          <Image src={'/icon/school_logo.png'} w="140px" objectFit="contain" ml="24px" />
          <Box w="1px" h="26px" borderRight="1px solid #e5e7eb" margin="0 24px"></Box>
          <Flex
            background="url(/icon/head_bg.png) no-repeat"
            backgroundSize="100% 100%"
            borderRadius="12px"
            w="100%"
            h="100%"
            alignItems="center"
          >
            <Box fontSize="24px" color="#1d2129" fontWeight="500">
              学生个人发展综合评价
            </Box>
          </Flex>
        </Flex>
        <Flex alignItems="center" maxW="37%" gap="12px" flex="1" ml="12px">
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              学年
            </Box>
            <Select
              options={yearOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择学年"
              defaultValue={yearOptions?.[0]?.value}
            />
          </Box>
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              年级-学期
            </Box>
            <Select
              options={gradeOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择年级-学期"
              defaultValue={gradeOptions?.[0]?.value}
            />
          </Box>
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              学生
            </Box>
            <Select
              options={schoolOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择学生"
              defaultValue={schoolOptions?.[0]?.value}
            />
          </Box>
          {/* <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              评价维度
            </Box>
            <Select
              options={evaluateOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择评价维度"
              defaultValue={evaluateOptions?.[0]?.value}
            />
          </Box> */}
        </Flex>
      </Flex>

      <SimpleGrid columns={8} spacing={3} className="statistic-box">
        {statistics.map((item, index) => (
          <StatisticItem key={index} content={item.content} num={item.num} />
        ))}
      </SimpleGrid>

      <Grid templateColumns="repeat(4, 1fr)" gap={3} className="first_pane">
        <BoardPane
          title="个人资料"
          showFullScreenButtons
          hiddenContent={
            <ScrollableContent
              items={[
                { header: '基本信息', content: '', slot: 'basicInfo' },
                { header: '家庭情况', content: '', slot: 'familyInfo' }
              ]}
              renderSlot={(item) => {
                if (item.slot === 'basicInfo') {
                  return (
                    <Box>
                      {[
                        { label: '户口所在地', value: studentInfo.residence },
                        { label: '家庭住址', value: studentInfo.address }
                      ].map((info, index) => (
                        <Box key={index}>
                          <Text color="gray.500" fontSize="sm">
                            {info.label}
                          </Text>
                          <Text>{info.value}</Text>
                        </Box>
                      ))}
                    </Box>
                  );
                }

                if (item.slot === 'familyInfo') {
                  return (
                    <>
                      <ScrollableContent
                        items={[
                          {
                            name: '王伟',
                            company: '中华电信有限公司',
                            email: '<EMAIL>',
                            phone: '13800001234',
                            relation: '父亲'
                          },
                          {
                            name: '李丽',
                            company: '中央美术学院',
                            email: '<EMAIL>',
                            phone: '13900005678',
                            relation: '母亲'
                          }
                        ]}
                        itemWidth="54%"
                        renderSlot={(familyMember) => (
                          <Box className="family_info" p={4} bg="gray.50" borderRadius="md">
                            <Flex className="relation_tag" align="center" mb={3}>
                              <Image
                                src="/icon/image/StudentKanban/prefixIcon.svg"
                                w="14px"
                                mr={2}
                              />
                              <Text fontWeight="medium">{familyMember.relation}</Text>
                            </Flex>
                            <VStack align="stretch" spacing={2}>
                              {[
                                { label: '姓名', value: familyMember.name },
                                { label: '工作单位', value: familyMember.company },
                                { label: '邮箱', value: familyMember.email },
                                { label: '联系电话', value: familyMember.phone }
                              ].map((item, idx) => (
                                <Flex key={idx} justify="space-between">
                                  <Text color="gray.500" fontSize="sm">
                                    {item.label}
                                  </Text>
                                  <Text>{item.value}</Text>
                                </Flex>
                              ))}
                            </VStack>
                          </Box>
                        )}
                      />

                      {[
                        { label: '监护人', value: studentInfo.guardians },
                        { label: '变动情况', value: studentInfo.change }
                      ].map((item, index) => (
                        <Box key={index}>
                          <Text color="gray.500" fontSize="sm">
                            {item.label}
                          </Text>
                          <Text>{item.value}</Text>
                        </Box>
                      ))}
                    </>
                  );
                }
              }}
            />
          }
        >
          <Box className="box_content">
            <Flex className="main_content" justify="space-between" align="center">
              <Box className="left_avatar">
                <Image
                  width="110px"
                  height="147px"
                  objectFit="cover"
                  src="/icon/image/StudentKanban/user_avatar.png"
                  alt="User Avatar"
                />
              </Box>
              <Divider orientation="vertical" />
              <Box className="right_info" flex="1">
                <Text fontSize="lg" fontWeight="bold" mb={2}>
                  {studentInfo.name}
                </Text>
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontWeight="bold">班级</Text>
                    <Text>{studentInfo.class}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">学号</Text>
                    <Text>{studentInfo.id}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">出生年月</Text>
                    <Text>{studentInfo.birthDate}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">入学时间</Text>
                    <Text>{studentInfo.enrollmentDate}</Text>
                  </Box>
                </SimpleGrid>
              </Box>
            </Flex>
            <Divider my={4} />
            <Box className="other_content">
              <Text fontWeight="bold">学习经历</Text>
              <Text>{studentInfo.studyExperience}</Text>
            </Box>
          </Box>
        </BoardPane>

        <BoardPane>
          <EChartsReact option={individualCommentOption} style={{ height: '100%' }} />
        </BoardPane>

        <BoardPane>
          <EChartsReact option={individualSuitableChartOptions} style={{ height: '100%' }} />
        </BoardPane>

        <BoardPane>
          <EChartsReact
            option={cultureKnowChartOptions}
            style={{ height: '100%' }}
            onEvents={{ click: handleChartClick }}
          />
        </BoardPane>
      </Grid>

      <Grid templateColumns="repeat(4, 1fr)" gap={3} className="second_pane">
        <BoardPane title="老师的话" height="477px">
          <Box height="196px" marginBottom="10px">
            <BoardPane title="突出成绩记载" height="196px" backgroundColor="#f9f9f9">
              <Text>{teacherSayInfo.achievementRecord}</Text>
            </BoardPane>
          </Box>
          <Box height="196px">
            <BoardPane title="班主任寄语" height="196px" backgroundColor="#f9f9f9">
              <Text>{teacherSayInfo.teacherMessage}</Text>
              <Button onClick={handleAiCreate} isLoading={createLoading}>
                AI生成
              </Button>
            </BoardPane>
          </Box>
        </BoardPane>

        <BoardPane height="477px">
          <EChartsReact option={homeCommentOptions} style={{ height: '100%' }} />
          <Box className="inner_footer">
            <Text>
              家长的话: 虽然有进步,但还是需是自律、自觉的加强体育锻炼，为了自己的理想努力奋斗
            </Text>
          </Box>
        </BoardPane>

        <BoardPane height="477px">
          <EChartsReact option={communityCommentOptions} style={{ height: '100%' }} />
          <Box className="inner_footer">
            <Text>
              通过在南山图书馆参加义工活动，我学会了如何更好地管理时间和提高组织能力，同时增强了与人沟通的技巧，积累了宝贵的社会服务经验
            </Text>
          </Box>
        </BoardPane>

        <Box>
          <BoardPane title="自己的话" height="233px">
            <Text>本校培养目标达成度: 目标没有全部达成，但也要继续努力，加油！</Text>
            <Text>学习适应性: 收获了许多知识，与很多老师持续友好交流</Text>
            <Text>文化知识: 收货了许多知识，如答题技巧，检查方法等</Text>
          </BoardPane>

          <BoardPane title="回顾与反思" height="233px">
            <Text>
              这学期我在学习上进步了不少，特别是数学和语文，成绩都有提高。上课时我认真听讲，积极回答问题，作业也按时完成。不过，有时候我还是会有点粗心，做题时会犯小错误。下学期我要更加细心，争取取得更好的成绩！
            </Text>
          </BoardPane>
        </Box>
      </Grid>
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default BoardIndex;
