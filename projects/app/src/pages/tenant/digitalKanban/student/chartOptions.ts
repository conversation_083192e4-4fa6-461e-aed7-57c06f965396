// src/chartOptions.ts
import { EChartsOption } from 'echarts';
let labelIndex = 0;
let labelIndex1 = 0;
let labelIndex2 = 0;
let labelIndex3 = 0;
export const labelsMap = ['', 'D', 'C', 'B', 'A'];

export const individualCommentIndicator = [
  { name: '创新之魂', max: 4, color: '#86909C' },
  { name: '健康之本', max: 4, color: '#86909C' },
  { name: '中华之情', max: 4, color: '#86909C' }
];
export const individualCommentOption: EChartsOption = {
  title: {
    text: '本校培养目标达成度'
  },
  axisName: {
    formatter: (name: string) => {
      return name;
    }
  },
  // tooltip: {
  //   trigger: 'item',
  //   formatter: (params: any) => {
  //     console.log(params);

  //     return '12312';
  //   }
  // },
  legend: {
    data: ['自我评价', '团队评议', '教师评价'],
    bottom: 0, // 将 legend 放置在底部
    icon: 'circle'
  },
  radar: {
    shape: 'circle', // 设置雷达图为圆形
    radius: '60%', // 雷达图的半径
    indicator: individualCommentIndicator,
    splitNumber: 4, // 将雷达图分成4个等级
    axisLine: {
      lineStyle: {
        color: '#ccc'
      }
    },
    splitLine: {
      lineStyle: {
        color: ['#E5E6EB']
      }
    },
    splitArea: {
      areaStyle: {
        color: ['#f4f4f4', '#f9f9f9']
      }
    },
    axisLabel: {
      show: true,
      align: 'right',
      formatter: function (value: string, index: number) {
        const flag = labelIndex % 15 < 5;
        labelIndex++;
        const labels = ['', 'D', 'C', 'B', 'A'];
        if (flag) {
          return labels[index];
        } else {
          return '';
        }
      },
      color: '#000',
      fontSize: 12
    }
  },
  series: [
    {
      name: '评价',
      type: 'radar',
      areaStyle: {
        opacity: 0.3
      }, // 显示面积

      data: [
        {
          value: [4, 3, 4], // 自我评价
          name: '自我评价',

          itemStyle: {
            color: '#0052f9'
          },
          areaStyle: {
            color: '#0052f9'
          }
        },
        {
          value: [3, 4, 3], // 团队评议
          name: '团队评议',
          itemStyle: {
            color: '#00c0c1'
          },
          areaStyle: {
            color: '#00c0c1'
          }
        },
        {
          value: [4, 4, 3], // 教师评价
          name: '教师评价',
          itemStyle: {
            color: '#009cf5'
          },
          areaStyle: {
            color: '#009cf5'
          }
        }
      ]
    }
  ]
};

const individualSuitableindicator = [
  { name: '听课方法', max: 4, color: '#86909C' },
  { name: '学习环境', max: 4, color: '#86909C' },
  { name: '独立性和毅力', max: 4, color: '#86909C' },
  { name: '学习态度', max: 4, color: '#86909C' }
];

export const individualSuitableChartOptions: EChartsOption = {
  title: {
    text: '学习适应性'
  },
  // tooltip: {
  //   formatter: function (params: any) {
  //     let result = `${params.name}<br/>`;
  //     params.value.forEach((value: number, index: number) => {

  //       result += ` ${individualSuitableindicator[index].name}: ${labelsMap[value]}<br/>`;
  //     });
  //     return result;
  //   },
  // },
  legend: {
    data: ['自我评定', '团队评议', '教师评价'],
    bottom: 0,
    icon: 'circle'
  },
  radar: {
    radius: '60%',
    indicator: individualSuitableindicator,
    splitNumber: 4,
    axisLine: {
      lineStyle: {
        color: '#ccc'
      }
    },
    splitLine: {
      lineStyle: {
        color: ['#E5E6EB']
      }
    },
    splitArea: {
      areaStyle: {
        color: ['#f4f4f4', '#f9f9f9']
      }
    },
    axisLabel: {
      show: true,
      align: 'right',
      formatter: function (value: string, index: number) {
        const flag = labelIndex1 % 20 < 5;
        labelIndex1++;
        const labels = ['', 'D', 'C', 'B', 'A'];
        if (flag) {
          return labels[index];
        } else {
          return '';
        }
      },
      color: '#000',
      fontSize: 12
    }
  },
  series: [
    {
      name: '评价',
      type: 'radar',
      areaStyle: {
        opacity: 0.3
      },
      data: [
        {
          value: [4, 3, 4, 2], // 自我评定
          name: '自我评定',
          itemStyle: {
            color: '#0052f9'
          },
          areaStyle: {
            color: '#0052f9'
          }
        },
        {
          value: [3, 2, 3, 3], // 团队评议
          name: '团队评议',
          itemStyle: {
            color: '#00c0c1'
          },
          areaStyle: {
            color: '#00c0c1'
          }
        },
        {
          value: [4, 4, 4, 3], // 教师评价
          name: '教师评价',
          itemStyle: {
            color: '#009cf5'
          },
          areaStyle: {
            color: '#009cf5'
          }
        }
      ]
    }
  ]
};
export const cultureKnowChartOptions: EChartsOption = {
  title: {
    text: '文化知识',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params: any) {
      let result = '';
      console.log(params);

      params.forEach((item: any, index: number) => {
        console.log(item.seriesName);

        result += `${item.seriesName}(${index == 0 ? '80' : '20'}%): ${item.data}<br/>`;
      });
      return result;
    }
  },
  legend: {
    data: ['过程表现综合评价', '学业水平综合评价'],
    bottom: 0,
    icon: 'circle'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [
      '道德与法治',
      '语文',
      '数学',
      '英语',
      '历史',
      '地理',
      '生物',
      '信息科技',
      '体育与健康',
      '音乐',
      '美术',
      '劳动',
      '综合实践'
    ],
    axisLabel: {
      interval: 0,
      rotate: 30
    }
  },
  yAxis: {
    type: 'value',
    max: 100
  },

  series: [
    {
      name: '学业水平综合评价',
      type: 'bar',
      data: [78, 71, 62, 64, 78, 66, 68, 66, 77, 63, 73, 77, 68],
      itemStyle: {
        color: '#175DFF'
      },
      stack: '总量'
    },
    {
      name: '过程表现综合评价',
      type: 'bar',
      data: [17, 17, 17, 15, 17, 18, 14, 13, 17, 16, 20, 17, 13],
      itemStyle: {
        color: '#34A7FA'
      },
      stack: '总量'
    }
  ]
};

const homeCommentindicator = [
  { name: '文明礼仪', max: 4, color: '#86909C' },
  { name: '尽责守本', max: 4, color: '#86909C' },
  { name: '守法守则', max: 4, color: '#86909C' },
  { name: '听教守规', max: 4, color: '#86909C' },
  { name: '勤学自创', max: 4, color: '#86909C' },
  { name: '自理自立', max: 4, color: '#86909C' },
  { name: '健康生活', max: 4, color: '#86909C' },
  { name: '安全自护', max: 4, color: '#86909C' },
  { name: '理财规划', max: 4, color: '#86909C' }
];

export const homeCommentOptions: EChartsOption = {
  title: {
    text: '家庭评价'
  },
  // tooltip: {
  //   formatter: function (params: any) {
  //     let result = `${params.name}<br/>`;
  //     params.value.forEach((value: number, index: number) => {

  //       result += ` ${homeCommentindicator[index].name}: ${labelsMap[value]}<br/>`;
  //     });
  //     return result;
  //   },
  // },
  legend: {
    data: ['自我评定', '家长评价'],
    bottom: 0, // 将 legend 放置在底部
    icon: 'circle'
  },
  radar: {
    radius: '60%', // 雷达图的半径
    indicator: homeCommentindicator,
    splitNumber: 4, // 将雷达图分成4个等级
    axisLine: {
      lineStyle: {
        color: '#ccc'
      }
    },
    splitLine: {
      lineStyle: {
        color: ['#E5E6EB']
      }
    },
    splitArea: {
      areaStyle: {
        color: ['#f4f4f4', '#f9f9f9']
      }
    },
    axisLabel: {
      show: true,
      align: 'right',
      formatter: function (value: string, index: number) {
        const flag = labelIndex2 % 45 < 5;
        labelIndex2++;
        const labels = ['', 'D', 'C', 'B', 'A'];
        if (flag) {
          return labels[index];
        } else {
          return '';
        }
      },
      color: '#000',
      fontSize: 12
    }
  },
  series: [
    {
      name: '评价',
      type: 'radar',
      areaStyle: {
        opacity: 0.3
      }, // 显示面积

      data: [
        {
          value: [4, 3, 4, 3, 3, 2, 3, 4, 2], // 自我评定
          name: '自我评定',
          itemStyle: {
            color: '#0052f9'
          },
          areaStyle: {
            color: '#0052f9'
          }
        },
        {
          value: [3, 4, 3, 4, 4, 3, 4, 3, 3], // 家长评价
          name: '家长评价',
          itemStyle: {
            color: '#00c0c1'
          },
          areaStyle: {
            color: '#00c0c1'
          }
        }
      ]
    }
  ]
};

const communityCommentindicator = [
  { name: '守法遵规', max: 4, color: '#86909C' },
  { name: '诚实守信', max: 4, color: '#86909C' },
  { name: '社区参与', max: 4, color: '#86909C' },
  { name: '环境爱护', max: 4, color: '#86909C' },
  { name: '社区关怀', max: 4, color: '#86909C' },
  { name: '文明礼貌', max: 4, color: '#86909C' },
  { name: '彬彬有礼', max: 4, color: '#86909C' }
];
export const communityCommentOptions: EChartsOption = {
  title: {
    text: '社区评价'
  },
  // tooltip: {
  //   formatter: function (params: any) {
  //     let result = `${params.name}<br/>`;
  //     params.value.forEach((value: number, index: number) => {

  //       result += ` ${communityCommentindicator[index].name}: ${labelsMap[value]}<br/>`;
  //     });
  //     return result;
  //   },
  // },
  legend: {
    data: ['自我评定', '队长评价', '义工评价'],
    bottom: 0, // 将 legend 放置在底部
    icon: 'circle'
  },
  radar: {
    radius: '60%', // 雷达图的半径
    indicator: communityCommentindicator,
    splitNumber: 4, // 将雷达图分成4个等级
    axisLine: {
      lineStyle: {
        color: '#ccc'
      }
    },
    splitLine: {
      lineStyle: {
        color: ['#E5E6EB']
      }
    },
    splitArea: {
      areaStyle: {
        color: ['#f4f4f4', '#f9f9f9']
      }
    },
    axisLabel: {
      show: true,
      align: 'right',
      formatter: function (value: string, index: number) {
        const flag = labelIndex3 % 35 < 5;
        labelIndex3++;
        const labels = ['', 'D', 'C', 'B', 'A'];
        if (flag) {
          return labels[index];
        } else {
          return '';
        }
      },
      color: '#000',
      fontSize: 12
    }
  },
  series: [
    {
      name: '评价',
      type: 'radar',
      areaStyle: {
        opacity: 0.3
      }, // 显示面积

      data: [
        {
          value: [4, 3, 4, 3, 4, 4, 3], // 自我评定
          name: '自我评定',
          itemStyle: {
            color: '#0052f9'
          },
          areaStyle: {
            color: '#0052f9'
          }
        },
        {
          value: [3, 4, 3, 4, 3, 3, 4], // 队长评价
          name: '队长评价',
          itemStyle: {
            color: '#00c0c1'
          },
          areaStyle: {
            color: '#00c0c1'
          }
        },
        {
          value: [2, 3, 4, 2, 4, 3, 3], // 义工评价
          name: '义工评价',
          itemStyle: {
            color: '#009cf5'
          },
          areaStyle: {
            color: '#009cf5'
          }
        }
      ]
    }
  ]
};

export const studentInfoDefault = {
  class: '七(四)班',
  id: '202401',
  name: '林圳南',
  gender: '男',
  birthDate: '2010年5月12日',
  enrollmentDate: '2023年9月1日',
  studyExperience: '2017年9月-2023年7月就读于南山外国语小学',
  residence: '深圳市南山区华侨城中旅广场H3',
  address: '深圳市南山区华侨城中旅广场H3',
  father: {
    name: '王伟',
    workplace: '中华电信有限公司',
    email: '<EMAIL>',
    phone: '13800001234'
  },
  mother: {
    name: '李丽',
    workplace: '无',
    email: '<EMAIL>',
    phone: '13900005678'
  },
  guardians: '王伟,李丽',
  change: '2022年9月1日，从南山实验小学转入本校'
};

const Dom = () => {
  return null;
};

export default Dom;
