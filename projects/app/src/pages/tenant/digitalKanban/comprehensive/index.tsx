import { respDims, rpxDim } from '@/utils/chakra';
import { Box, Image, Flex } from '@chakra-ui/react';
import { useState, useMemo } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import { Select, Table } from 'antd';
import EChartsReact from 'echarts-for-react';

const EvaluateData = () => {
  const yearOptions = [
    {
      value: 1,
      label: '2019-2020学年'
    },
    {
      value: 2,
      label: '2020-2021学年'
    },
    {
      value: 3,
      label: '2021-2022学年'
    },
    {
      value: 4,
      label: '2022-2023学年'
    },
    {
      value: 5,
      label: '2023-2024学年'
    }
  ];

  const gradeOptions = [
    {
      value: 1,
      label: '一年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 2,
      label: '二年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 3,
      label: '三年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 4,
      label: '四年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 5,
      label: '五年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 6,
      label: '六年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 7,
      label: '七年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 8,
      label: '八年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    },
    {
      value: 9,
      label: '九年级',
      children: [
        {
          value: 1,
          label: '第一学期'
        },
        {
          value: 2,
          label: '第二学期'
        }
      ]
    }
  ];

  const classOptions = [
    {
      value: 0,
      label: '全部'
    },
    {
      value: 1,
      label: '七1班'
    },
    {
      value: 2,
      label: '七2班'
    },
    {
      value: 3,
      label: '七3班'
    },
    {
      value: 4,
      label: '七4班'
    },
    {
      value: 5,
      label: '七5班'
    },
    {
      value: 6,
      label: '七6班'
    },
    {
      value: 8,
      label: '七8班'
    }
  ];

  const evaluateOptions = [
    {
      value: 1,
      label: '自我评定'
    },
    {
      value: 2,
      label: '团队评议'
    },
    {
      value: 3,
      label: '教师评价'
    }
  ];

  const subjectOptions = [
    {
      value: 0,
      label: '道德与法治'
    },
    {
      value: 1,
      label: '语文'
    },
    {
      value: 2,
      label: '数学'
    },
    {
      value: 3,
      label: '英语'
    },
    {
      value: 4,
      label: '历史'
    },
    {
      value: 5,
      label: '地理'
    },
    {
      value: 6,
      label: '物理'
    },
    {
      value: 7,
      label: '化学'
    },
    {
      value: 8,
      label: '生物'
    },
    {
      value: 9,
      label: '体育与健康'
    },
    {
      value: 10,
      label: '音乐'
    },
    {
      value: 11,
      label: '美术'
    },
    {
      value: 11,
      label: '劳动'
    },
    {
      value: 12,
      label: '综合实践'
    }
  ];

  const echartsOption = useMemo(() => {
    return {
      title: {
        text: '本校培养目标达成度',
        left: 'left'
      },
      tooltip: false,
      series: [
        {
          type: 'pie',
          radius: '65%',
          center: ['50%', '62%'],
          label: {
            formatter: `{d}%`
          },

          data: [
            { value: 480, name: 'A', itemStyle: { color: '#2d5cf6' } },
            { value: 220, name: 'B', itemStyle: { color: '#5dc6c7' } },
            { value: 170, name: 'C', itemStyle: { color: '#58a5f3' } },
            { value: 130, name: 'D', itemStyle: { color: '#eebd47' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            formatter: '{b}: {d}%'
          }
        },
        {
          type: 'pie',
          radius: '65%',
          center: ['50%', '62%'],
          label: {
            show: true,
            position: 'inside',
            padding: [0, -4, 0, -4],
            formatter: '{b}',
            color: '#fff',
            fontSize: '14px',
            lineHeight: 10
          },
          data: [
            { value: 480, name: 'A', itemStyle: { color: '#2d5cf6' } },
            { value: 220, name: 'B', itemStyle: { color: '#5dc6c7' } },
            { value: 170, name: 'C', itemStyle: { color: '#58a5f3' } },
            { value: 130, name: 'D', itemStyle: { color: '#eebd47' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            formatter: '{b}: {d}%'
          }
        }
      ]
    };
  }, []);

  const echartsOption2 = useMemo(() => {
    return {
      tooltip: false,
      series: [
        {
          type: 'pie',
          radius: '80%',
          center: ['48%', '50%'],
          label: {
            formatter: `{d}%`
          },
          labelLine: {
            length: 5, // 第一段线的长度
            length2: 5 // 第二段线的长度
          },

          data: [
            { value: 490, name: 'A', itemStyle: { color: '#2d5cf6' } },
            { value: 210, name: 'B', itemStyle: { color: '#5dc6c7' } },
            { value: 180, name: 'C', itemStyle: { color: '#58a5f3' } },
            { value: 120, name: 'D', itemStyle: { color: '#eebd47' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            formatter: '{b}: {d}%'
          }
        },
        {
          type: 'pie',
          radius: '80%',
          center: ['48%', '50%'],
          label: {
            show: true,
            position: 'inside',
            padding: [0, -4, 0, -4],
            formatter: '{b}',
            color: '#fff',
            fontSize: '14px',
            lineHeight: 10
          },
          labelLine: {
            length: 5, // 第一段线的长度
            length2: 5 // 第二段线的长度
          },
          data: [
            { value: 490, name: 'A', itemStyle: { color: '#2d5cf6' } },
            { value: 210, name: 'B', itemStyle: { color: '#5dc6c7' } },
            { value: 180, name: 'C', itemStyle: { color: '#58a5f3' } },
            { value: 120, name: 'D', itemStyle: { color: '#eebd47' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            formatter: '{b}: {d}%'
          }
        }
      ]
    };
  }, []);

  const echartsOption3 = useMemo(() => {
    return {
      tooltip: false,
      series: [
        {
          type: 'pie',
          radius: '80%',
          center: ['50%', '50%'],
          label: {
            formatter: `{d}%`
          },
          labelLine: {
            length: 5, // 第一段线的长度
            length2: 5 // 第二段线的长度
          },

          data: [
            { value: 510, name: 'A', itemStyle: { color: '#2d5cf6' } },
            { value: 190, name: 'B', itemStyle: { color: '#5dc6c7' } },
            { value: 160, name: 'C', itemStyle: { color: '#58a5f3' } },
            { value: 140, name: 'D', itemStyle: { color: '#eebd47' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            formatter: '{b}: {d}%'
          }
        },
        {
          type: 'pie',
          radius: '80%',
          center: ['50%', '50%'],
          label: {
            show: true,
            position: 'inside',
            padding: [0, -4, 0, -4],
            formatter: '{b}',
            color: '#fff',
            fontSize: '14px',
            lineHeight: 10
          },
          labelLine: {
            length: 5, // 第一段线的长度
            length2: 5 // 第二段线的长度
          },
          data: [
            { value: 490, name: 'A', itemStyle: { color: '#2d5cf6' } },
            { value: 210, name: 'B', itemStyle: { color: '#5dc6c7' } },
            { value: 180, name: 'C', itemStyle: { color: '#58a5f3' } },
            { value: 120, name: 'D', itemStyle: { color: '#eebd47' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            formatter: '{b}: {d}%'
          }
        }
      ]
    };
  }, []);

  const echartsOption4 = useMemo(() => {
    return {
      title: {
        text: '学习适应性',
        left: 'left'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (p: any) => {
          let dom = `<div >

        <div style="display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;position: relative;z-index: 1;">
            <div style="width:100%;display:${p[0] ? 'flex' : 'none'};>
                <span style="font-size:14px;">${p[0] ? 'A' : ''}</span>：
                <span style="font-size:14px;">${p[0] ? p[0].data + '人' : ''}</span>
            </div>
            <div style="width:100%;height:100%;display:${p[1] ? 'flex' : 'none'};">
                <span style="font-size:14px;">${p[1] ? 'B' : ''}</span>：
                <span style="font-size:14px;">${p[1] ? p[1].data + '人' : ''}</span>
            </div>
            <div style="width:100%;height:100%;display:${p[2] ? 'flex' : 'none'};">
                <span style="font-size:14px;">${p[2] ? 'C' : ''}</span>：
                <span style="font-size:14px;">${p[2] ? p[2].data + '人' : ''}</span>
            </div>
            <div style="width:100%;height:100%;display:${p[3] ? 'flex' : 'none'};">
                <span style="font-size:14px;">${p[3] ? 'D' : ''}</span>：
                <span style="font-size:14px;">${p[3] ? p[3].data + '人' : ''}</span>
            </div>
        </div>
    </div>`;
          return dom;
        }
      },
      legend: {
        align: 'left',
        right: '26%',
        bottom: '0%',
        type: 'plain',
        textStyle: {
          fontSize: 12
        },
        icon: 'circle',
        itemGap: 25,
        itemWidth: 18,
        data: [
          {
            name: '学习态度'
          },
          {
            name: '独立性和毅力'
          },
          {
            name: '听课方法'
          },
          {
            name: '学习环境'
          }
        ]
      },
      grid: {
        top: '15%',
        left: '4%',
        right: '2%',
        bottom: '23%'
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E5E6EB',
              width: 1,
              type: 'dashed'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#86909C',
              padding: 8,
              fontSize: 12
            },
            formatter: function (data: any) {
              return data;
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E5E6EB',
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          },
          data: ['七1班', '七2班', '七3班', '七4班', '七5班', '七6班', '七7班', '七8班']
        }
      ],
      yAxis: [
        {
          min: 0,
          max: 40,
          interval: 15,
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#86909C',
              padding: 8,
              fontSize: 12
            },
            formatter: function (value: any, index: number) {
              if (value >= 15) {
                if (value == 15) {
                  value = 'C';
                } else if (value == 30) {
                  value = 'B';
                } else {
                  value = 'A';
                }
              } else if (value < 10) {
                value = 'D';
              }
              return value;
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E5E6EB',
              type: 'dashed'
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E5E6EB',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '学习态度',
          type: 'line',
          symbol: 'circle',
          showAllSymbol: true,
          symbolSize: 6,
          itemStyle: {
            color: '#5dc6c7' // 折线图颜色
          },
          smooth: true,
          lineStyle: {
            normal: {
              width: 2,
              color: '#5dc6c7'
            }
          },
          tooltip: {
            show: true
          },

          data: [38, 31, 34, 33, 35, 26, 32, 25]
        },
        {
          name: '独立性和毅力',
          type: 'line',
          symbol: 'circle',
          showAllSymbol: true,
          symbolSize: 6,
          smooth: true,
          lineStyle: {
            normal: {
              width: 2,
              color: '#2d5cf6'
            }
          },
          itemStyle: {
            color: '#2d5cf6'
          },
          tooltip: {
            show: true
          },

          data: [10, 13, 12, 13, 8, 16, 5, 13]
        },
        {
          name: '听课方法',
          type: 'line',
          symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
          showAllSymbol: true,
          symbolSize: 6,
          smooth: true,
          lineStyle: {
            normal: {
              width: 2,
              color: '#58a5f3' // 线条颜色
            }
          },
          itemStyle: {
            color: '#58a5f3'
          },
          tooltip: {
            show: true
          },

          data: [1, 3, 3, 2, 6, 5, 9, 7]
        },
        {
          name: '学习环境',
          type: 'line',
          symbol: 'circle',
          showAllSymbol: true,
          symbolSize: 6,
          lineStyle: {
            normal: {
              width: 2,
              color: '#eebd47'
            }
          },
          itemStyle: {
            color: '#eebd47'
          },
          tooltip: {
            show: true
          },

          data: [1, 3, 1, 2, 1, 3, 4, 3]
        }
      ]
    };
  }, []);

  const scoreData = {
    七1班: { A: 10, B: 22, C: 12, D: 3 },
    七2班: { A: 12, B: 25, C: 10, D: 3 },
    七3班: { A: 9, B: 23, C: 13, D: 3 },
    七4班: { A: 11, B: 20, C: 12, D: 3 },
    七5班: { A: 13, B: 22, C: 11, D: 3 },
    七6班: { A: 8, B: 25, C: 10, D: 2 },
    七7班: { A: 10, B: 21, C: 13, D: 3 },
    七8班: { A: 11, B: 24, C: 10, D: 3 }
  } as any;

  const echartsOption5 = useMemo(() => {
    return {
      title: {
        text: '文化知识',
        left: 'left'
      },
      grid: {
        top: '12%',
        left: '10%',
        right: '2%',
        bottom: '16%'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          const className = params[0].axisValue;
          const scores = scoreData[className];
          return `
                A(85-100分): ${scores.A}人<br/>
                B(70-84分): ${scores.B}人<br/>
                C(60-69分): ${scores.C}人<br/>
                D(59分以下): ${scores.D}人
              `;
        }
      },
      legend: {
        data: ['过程表现综合评价', '学业水平综合评价', '学科核心素养'],
        bottom: 0,
        left: '40px',
        icon: 'circle'
      },
      xAxis: {
        type: 'category',
        data: ['七1班', '七2班', '七3班', '七4班', '七5班', '七6班', '七7班', '七8班']
      },
      yAxis: {
        type: 'value',
        max: 100
      },
      series: [
        {
          name: '学科核心素养',
          type: 'bar',
          stack: 'processEvaluation',
          data: [76, 89, 98, 84, 80, 77, 98, 90],
          itemStyle: {
            color: '#5dc6c7'
          }
        },
        {
          name: '学业水平综合评价',
          type: 'bar',
          stack: 'total',
          data: [66, 78, 70, 70, 63, 67, 62, 61],
          itemStyle: {
            color: '#2d5cf6'
          },
          markLine: {
            symbol: 'none',
            lineStyle: {
              color: '#FFA500',
              type: 'solid',
              width: 1
            },
            data: [
              {
                name: '发展综合均值',
                yAxis: 78.91,
                label: {
                  show: true,
                  position: 'insideEndTop',
                  color: '#FFA500',
                  formatter: '发展综合均值：78.91'
                }
              }
            ],
            silent: true
          }
        },
        {
          name: '过程表现综合评价',
          type: 'bar',
          stack: 'total',
          data: [16.5, 19.5, 17.5, 17.5, 15.75, 16.75, 15.5, 15.25],
          itemStyle: {
            color: '#58a5f3'
          },
          markLine: {
            symbol: 'none',
            lineStyle: {
              color: '#FF0000',
              type: 'solid',
              width: 1
            },
            data: [
              {
                name: '核心素养均值',
                yAxis: 86.5,
                label: {
                  show: true,
                  position: 'insideEndTop',
                  color: '#FF0000',
                  formatter: '核心素养均值：86.5'
                }
              }
            ],
            silent: true
          }
        }
      ]
    };
  }, []);

  var legendData = ['年级', '班级']; //图例
  var familyEvaluationIndicator = [
    {
      text: '文明礼仪',
      max: 100,
      sub: '（0.2）',
      axisLabel: { show: true }
    },
    {
      text: '理财规划',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '安全自护',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '健康生活',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '自理自立',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '勤学自创',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '听教守规',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '守法守则',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '尽责守本',
      max: 100,
      sub: '（0.2）'
    }
  ];
  let familyEvaluationDataArr = [
    {
      value: [100, 60, 100, 80, 60, 80, 100, 100, 80],
      name: legendData[0],
      lineStyle: {
        normal: {
          color: '#2d5cf6'
        }
      },
      itemStyle: {
        color: '#0052f9'
      },
      areaStyle: {
        color: '#0052f9'
      }
    },
    {
      value: [80, 80, 80, 100, 100, 100, 80, 80, 100],
      name: legendData[1],
      lineStyle: {
        normal: {
          color: '#5dc6c7'
        }
      },
      itemStyle: {
        color: '#00c0c1'
      },
      areaStyle: {
        color: '#00c0c1'
      }
    }
  ];
  const echartsOption6 = useMemo(() => {
    return {
      title: {
        text: '家庭评价',
        left: 'left'
      },
      legend: {
        icon: 'circle',
        data: legendData,
        align: 'left',
        bottom: '4%',
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 21,
        textStyle: {
          fontSize: 14,
          color: '#1D2129'
        }
      },
      radar: {
        center: ['50%', '50%'],
        radius: '58%',
        splitNumber: 4,
        name: {
          textStyle: {
            color: '#86909C',
            fontSize: 12
          }
        },
        indicator: familyEvaluationIndicator,
        axisLabel: {
          color: '#000',
          formatter: function (value: number) {
            if (value === 100) return 'A';
            if (value === 75) return 'B';
            if (value === 50) return 'C';
            if (value === 25) return 'D';
            return '';
          }
        },
        axisLine: {
          lineStyle: {
            color: '#ccc'
          }
        },
        splitLine: {
          lineStyle: {
            color: ['#E5E6EB']
          }
        },
        splitArea: {
          areaStyle: {
            color: ['#f4f4f4', '#f9f9f9']
          }
        }
      },
      series: [
        {
          type: 'radar',
          symbolSize: 8,
          data: familyEvaluationDataArr,
          areaStyle: {
            opacity: 0.3
          }
        }
      ]
    };
  }, []);

  var communityEvaluationIndicator = [
    {
      text: '守法遵规',
      max: 100,
      sub: '（0.2）',
      axisLabel: { show: true }
    },
    {
      text: '彬彬有礼',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '文明礼貌',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '社区关怀',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '环境爱护',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '社区参与',
      max: 100,
      sub: '（0.2）'
    },
    {
      text: '诚实守信',
      max: 100,
      sub: '（0.2）'
    }
  ];
  let communityEvaluationDataArr = [
    {
      value: [100, 80, 100, 100, 80, 100, 80],
      name: legendData[0],
      itemStyle: {
        color: '#0052f9'
      },
      areaStyle: {
        color: '#0052f9'
      }
    },
    {
      value: [80, 100, 80, 80, 100, 80, 100],
      name: legendData[1],
      itemStyle: {
        color: '#00c0c1'
      },
      areaStyle: {
        color: '#00c0c1'
      }
    }
  ];
  const echartsOption7 = useMemo(() => {
    return {
      title: {
        text: '社区评价',
        left: 'left'
      },
      legend: {
        icon: 'circle',
        data: legendData,
        align: 'left',
        bottom: '4%',
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 21,
        textStyle: {
          fontSize: 14,
          color: '#1D2129'
        }
      },
      radar: {
        center: ['50%', '50%'],
        radius: '58%',
        splitNumber: 4,
        name: {
          textStyle: {
            color: '#86909C',
            fontSize: 12
          }
        },
        indicator: communityEvaluationIndicator,
        splitArea: {
          show: true,
          areaStyle: {
            color: '#fafafa'
          }
        },
        axisLabel: {
          color: '#000',
          formatter: function (value: number) {
            if (value === 100) return 'A';
            if (value === 75) return 'B';
            if (value === 50) return 'C';
            if (value === 25) return 'D';
            return '';
          }
        },
        axisLine: {
          //指向外圈文本的分隔线样式
          lineStyle: {
            color: '#ccc'
          }
        },
        splitLine: {
          lineStyle: {
            color: ['#E5E6EB'],
            width: 1 // 分隔线线宽
          }
        }
      },
      series: [
        {
          type: 'radar',
          symbolSize: 8,
          data: communityEvaluationDataArr,
          areaStyle: {
            opacity: 0.3
          }
        }
      ]
    };
  }, []);

  const tableData = [
    {
      key: '1',
      name: '胡彦斌',
      stuNum: 32,
      address: '西湖区湖底公园1号'
    },
    {
      key: '2',
      name: '胡彦祖',
      stuNum: 42,
      address: '西湖区湖底公园1号'
    }
  ];

  const columns = [
    {
      title: '学号',
      dataIndex: 'stuNum',
      key: 'stuNum',
      width: 40
    },
    {
      title: '学生姓名',
      dataIndex: 'name',
      key: 'name',
      width: 40
    },
    {
      title: '课堂表现',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '作业评价',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '期中测试',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '学科实践',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '单项测试',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '期末测试',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '等级',
      dataIndex: 'address',
      key: 'address',
      width: 40
    },
    {
      title: '发展综合评价',
      dataIndex: 'address',
      key: 'address',
      width: 40
    }
  ];

  return (
    <Box minH="100%" bgColor="#f8fafc">
      {/* row1 */}
      <Flex>
        <Flex bgColor="#fff" alignItems="center" maxW="51%" borderRadius="12px" flex="1">
          <Image src={'/icon/school_logo.png'} w="140px" objectFit="contain" ml="24px" />
          <Box w="1px" h="26px" borderRight="1px solid #e5e7eb" margin="0 24px"></Box>
          <Flex
            background="url(/icon/head_bg.png) no-repeat"
            backgroundSize="100% 100%"
            borderRadius="12px"
            w="100%"
            h="100%"
            alignItems="center"
          >
            <Box fontSize="24px" color="#1d2129" fontWeight="500">
              学生发展综合评价看板
            </Box>
          </Flex>
        </Flex>
        <Flex alignItems="center" maxW="49%" gap="12px" flex="1" ml="12px">
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              学年
            </Box>
            <Select
              options={yearOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择学年"
              defaultValue={yearOptions?.[0]?.value}
            />
          </Box>
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              年级-学期
            </Box>
            <Select
              options={gradeOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择年级-学期"
              defaultValue={gradeOptions?.[0]?.value}
            />
          </Box>
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              班级
            </Box>
            <Select
              options={classOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择班级"
              defaultValue={classOptions?.[0]?.value}
            />
          </Box>
          <Box bgColor="#fff" flex="1" height="100%" padding="16px 15px" borderRadius="12px">
            <Box mb="4px" fontSize="16px" color="#2c3e50">
              评价维度
            </Box>
            <Select
              options={evaluateOptions}
              style={{ background: '#fff', width: '100%' }}
              placeholder="请选择评价维度"
              defaultValue={evaluateOptions?.[0]?.value}
            />
          </Box>
        </Flex>
      </Flex>
      {/* row2 */}
      <Flex mt="12px">
        <Flex flexDirection="column" flex="1">
          {/* top */}
          <Flex mr="12px" mb="12px">
            {/* item */}
            <Flex
              padding="12px 11px 12px 14px"
              bgColor="#fff"
              borderRadius="12px"
              flex="1"
              alignItems="baseline"
              whiteSpace="nowrap"
            >
              <Box w="7px" h="14px" bgColor="#3366ff" borderRadius="8px" mr="10px"></Box>
              <Flex flexDirection="column">
                <Box fontSize="16px" fontWeight="500" color="#1d2129">
                  学生
                </Box>
                <Box fontSize="18px" color="#2e2e3a" mt="4px">
                  800人
                </Box>
              </Flex>
            </Flex>
            {/* item */}
            <Flex
              padding="12px 11px 12px 14px"
              bgColor="#fff"
              borderRadius="12px"
              flex="1"
              alignItems="baseline"
              whiteSpace="nowrap"
              ml="12px"
            >
              <Box w="7px" h="14px" bgColor="#3366ff" borderRadius="8px" mr="10px"></Box>
              <Flex flexDirection="column">
                <Box fontSize="16px" fontWeight="500" color="#1d2129">
                  本校培养目标A级达成率
                </Box>
                <Box fontSize="18px" color="#2e2e3a" mt="4px">
                  60%
                </Box>
              </Flex>
            </Flex>
            {/* item */}
            <Flex
              padding="12px 11px 12px 14px"
              bgColor="#fff"
              borderRadius="12px"
              flex="1"
              alignItems="baseline"
              whiteSpace="nowrap"
              ml="12px"
            >
              <Box w="7px" h="14px" bgColor="#3366ff" borderRadius="8px" mr="10px"></Box>
              <Flex flexDirection="column">
                <Box fontSize="16px" fontWeight="500" color="#1d2129">
                  学习适应性A级达成率
                </Box>
                <Box fontSize="18px" color="#2e2e3a" mt="4px">
                  70%
                </Box>
              </Flex>
            </Flex>
            {/* item */}
            <Flex
              padding="12px 11px 12px 14px"
              bgColor="#fff"
              borderRadius="12px"
              flex="1"
              alignItems="baseline"
              whiteSpace="nowrap"
              ml="12px"
            >
              <Box w="7px" h="14px" bgColor="#3366ff" borderRadius="8px" mr="10px"></Box>
              <Flex flexDirection="column">
                <Box fontSize="16px" fontWeight="500" color="#1d2129">
                  社区评价A级达成率
                </Box>
                <Box fontSize="18px" color="#2e2e3a" mt="4px">
                  60%
                </Box>
              </Flex>
            </Flex>
            {/* item */}
            <Flex
              padding="12px 11px 12px 14px"
              bgColor="#fff"
              borderRadius="12px"
              flex="1"
              alignItems="baseline"
              whiteSpace="nowrap"
              ml="12px"
            >
              <Box w="7px" h="14px" bgColor="#3366ff" borderRadius="8px" mr="10px"></Box>
              <Flex flexDirection="column">
                <Box fontSize="16px" fontWeight="500" color="#1d2129">
                  家庭评价A级达成率
                </Box>
                <Box fontSize="18px" color="#2e2e3a" mt="4px">
                  60%
                </Box>
              </Flex>
            </Flex>
            {/* item */}
            <Flex
              padding="12px 11px 12px 14px"
              bgColor="#fff"
              borderRadius="12px"
              flex="1"
              alignItems="baseline"
              whiteSpace="nowrap"
              ml="12px"
            >
              <Box w="7px" h="14px" bgColor="#3366ff" borderRadius="8px" mr="10px"></Box>
              <Flex flexDirection="column">
                <Box fontSize="16px" fontWeight="500" color="#1d2129">
                  文化知识A级达成率
                </Box>
                <Box fontSize="18px" color="#2e2e3a" mt="4px">
                  A
                </Box>
              </Flex>
            </Flex>
          </Flex>
          {/* bottom */}
          <Flex>
            {/* 本校培养目标达成度 */}
            <Flex
              flexDirection="column"
              bgColor="#fff"
              padding="16px 18px 19px 16px"
              borderRadius="12px"
              h="353px"
            >
              <Flex flexDirection="column" alignItems="center">
                <EChartsReact option={echartsOption} style={{ width: '421px', height: '160px' }} />
                <Box>创新之魂</Box>
              </Flex>
              <Flex>
                <Flex flexDirection="column" alignItems="center">
                  <EChartsReact
                    option={echartsOption2}
                    style={{ width: '210px', height: '128px' }}
                  />
                  <Box>健康之本</Box>
                </Flex>
                <Flex flexDirection="column" alignItems="center">
                  <EChartsReact
                    option={echartsOption3}
                    style={{ width: '211px', height: '128px' }}
                  />
                  <Box>中华之情</Box>
                </Flex>
              </Flex>
            </Flex>
            {/* 学习适应性 */}
            <Flex
              bgColor="#fff"
              padding="16px 18px 21px 16px"
              borderRadius="12px"
              h="353px"
              margin="0 12px"
              flex="1"
            >
              <EChartsReact option={echartsOption4} style={{ width: '100%', height: '100%' }} />
            </Flex>
          </Flex>
        </Flex>
        {/* 文化知识 */}
        <Flex
          flexShrink="0"
          bgColor="#fff"
          padding="16px 18px 16px 16px"
          borderRadius="20px"
          pos="relative"
        >
          <EChartsReact option={echartsOption5} style={{ width: '421px', height: '400px' }} />
          <Flex pos="absolute" right="20px" alignItems="center">
            <Select
              options={subjectOptions}
              style={{ background: '#fff', width: '100%', minWidth: '112px' }}
              placeholder="请选择科目"
              defaultValue={subjectOptions?.[0]?.value}
            />
            <Box
              ml="12px"
              fontSize="12px"
              padding="6px 8px"
              borderRadius="3px"
              background="#f8f8f8"
              cursor="pointer"
              whiteSpace="nowrap"
            >
              测试质量分析
            </Box>
          </Flex>
        </Flex>
      </Flex>

      <Flex>
        {/* 家庭评价 */}
        <Flex
          flexDirection="column"
          bgColor="#fff"
          padding="16px 18px 16px 16px"
          borderRadius="20px"
          mt="12px"
        >
          <EChartsReact option={echartsOption6} style={{ width: '321px', height: '400px' }} />
          <Box mt="14px">
            <Box fontSize="20px" fontWeight="500" ml="6px" color="#4c4c4c">
              家长的话
            </Box>
            <Image
              src={'/icon/parental_words.png'}
              w="100%"
              h="281px"
              objectFit="contain"
              margin="0 auto"
              mt="28px"
            />
          </Box>
        </Flex>
        {/* 社区评价 */}
        <Flex
          flexDirection="column"
          bgColor="#fff"
          padding="16px 18px 16px 16px"
          borderRadius="20px"
          mt="12px"
          ml="12px"
        >
          <EChartsReact option={echartsOption7} style={{ width: '100%', height: '400px' }} />
          <Box mt="14px">
            <Box fontSize="20px" fontWeight="500" ml="6px" color="#4c4c4c">
              社区活动参与记录
            </Box>
            <Image
              src={'/icon/community_activity.png'}
              w="100%"
              h="125px"
              objectFit="contain"
              margin="0 auto"
              mt="28px"
            />
          </Box>
          <Box>
            <Box fontSize="20px" fontWeight="500" ml="6px" color="#4c4c4c">
              自己的话
            </Box>
            <Image
              src={'/icon/own_words.png'}
              w="100%"
              h="125px"
              objectFit="contain"
              margin="0 auto"
              mt="28px"
            />
          </Box>
        </Flex>
        {/* 学生表现 */}
        <Flex
          flex="1"
          flexDirection="column"
          bgColor="#fff"
          padding="16px 18px 16px 16px"
          borderRadius="20px"
          mt="12px"
          ml="12px"
        >
          <Flex justifyContent="space-between">
            <Box fontSize="20px" fontWeight="500" ml="6px" color="#4c4c4c">
              学生表现
            </Box>
            <Flex alignItems="center">
              <Flex
                minW="62px"
                h="27px"
                alignItems="center"
                mr="14px"
                borderRadius="20px"
                border="1px solid #ececec"
                background="rgba(243,244,246,0.6)"
                padding="3px 8px"
                cursor="pointer"
              >
                <Image src={'/icon/student_sort.png'} w="14px" h="14px" objectFit="contain" />
                <Box fontSize="13px" fontWeight="400px" ml="4px" color="#2c3e50">
                  分数
                </Box>
              </Flex>
              <Select
                options={subjectOptions}
                style={{ background: '#fff', width: '100%', minWidth: '112px', color: '#606266' }}
                placeholder="请选择科目"
                defaultValue={subjectOptions?.[0]?.value}
              />
            </Flex>
          </Flex>
          <Table
            bordered
            dataSource={tableData}
            columns={columns}
            pagination={false}
            rowKey="className"
            scroll={{ x: 'max-content' }}
            style={{ borderRadius: 'none', marginTop: '12px' }}
          />
        </Flex>
      </Flex>
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default EvaluateData;
