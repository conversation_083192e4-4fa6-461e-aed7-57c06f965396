import MyModal from '@/components/MyModal';
import { Box, ModalBody } from '@chakra-ui/react';

const RemarkDetail = ({ remark, onClose }: { remark: string; onClose: () => void }) => {
  return (
    <MyModal title="查看评价内容" isOpen onClose={onClose} w="600px" minH="200px" overflow="auto">
      <ModalBody>
        <Box
          fontFamily="PingFang SC, PingFang SC"
          fontWeight="500"
          fontSize="16px"
          color="rgba(0,0,0,0.9)"
        >
          评语
        </Box>
        <Box
          fontFamily="PingFang SC, PingFang SC"
          fontWeight="400"
          fontSize="14px"
          mt="8px"
          color="#606266"
        >
          {remark}
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default RemarkDetail;
