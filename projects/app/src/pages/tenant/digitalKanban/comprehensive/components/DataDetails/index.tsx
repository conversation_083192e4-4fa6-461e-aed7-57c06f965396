import { Flex } from '@chakra-ui/react';
import { ReactNode, useEffect, useRef, useState } from 'react';
import Search from '../Search';
import Sidebar from './components/Sidebar';
import { MyTableRef } from '@/components/MyTable/types';
import { SubjectType } from '@/types/api/tenant/evaluate/rule';

interface DataDetailsProps {
  tabs: ReactNode;
}

interface SearchParams {
  year?: string;
  term?: number;
  clazzId?: string;
  evaluatorId?: string;
  evaluateeId?: string;
  subjectId?: string;
  searchKey?: string;
}

const DataDetails: React.FC<DataDetailsProps> = ({ tabs }) => {
  const tableRef = useRef<MyTableRef>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [subjectsData, setSubjectsData] = useState<SubjectType[]>([]);

  const handleSearch = (params: SearchParams & { projectId?: string }) => {
    if (tableRef.current) {
      if (Object.values(params).every(Boolean)) {
        tableRef.current.setQuery(params);
        tableRef.current.reload();
      }
    }
  };

  const handleProjectSelect = (projectId: string) => {
    setSelectedProjectId(projectId);
  };

  const handleSearchParamsChange = (params: SearchParams) => {
    setSearchParams(params);
  };

  useEffect(() => {
    if (!!selectedProjectId && Object.values(searchParams).every(Boolean)) {
      handleSearch({
        ...searchParams,
        projectId: selectedProjectId
      });
    }
  }, [selectedProjectId, searchParams]);

  return (
    <Flex w="100%" h="100%" direction="column" alignItems="baseline">
      <Flex direction="column" w="100%">
        {tabs}
        <Search
          onSearch={handleSearchParamsChange}
          placeholder="请输入评价指标"
          subjectsData={subjectsData}
        />
      </Flex>

      <Flex mt="0px" flex="1" w="100%">
        <Sidebar
          tableRef={tableRef}
          searchParams={searchParams}
          onProjectSelect={handleProjectSelect}
          onSubjectsData={(subjects) => {
            setSubjectsData(subjects);
          }}
        />
      </Flex>
    </Flex>
  );
};

export default DataDetails;
