import {
  evaluaClassEvaluateDelete,
  evaluaHomeworkEvaluateDelete,
  evaluaViewListPageNew
} from '@/api/tenant/evaluate/evaluate';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { BizType, EvaluationType } from '@/constants/api/tenant/evaluate/rule';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { PagingData } from '@/types';
import { EvaluaViewListNewList, SubjectType } from '@/types/api/tenant/evaluate/evaluate';
import { respDims } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Box, Button, Flex, FlexProps } from '@chakra-ui/react';
import { TableProps, Tree } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState } from 'react';
import RemarkDetail from './RemarkDetail';
interface TreeNodeData {
  title: string;
  key: string;
  children?: TreeNodeData[];
  isLeaf?: boolean;
  clazzId?: string;
  subjects?: SubjectType[];
}

interface SearchParams {
  year?: string;
  term?: number;
  clazzId?: string;
  evaluatorId?: string;
  evaluateeId?: string;
  subjectId?: string;
  searchKey?: string;
}
interface SidebarProps {
  tableRef: React.RefObject<MyTableRef>;
  onProjectSelect: (text: string) => void;
  searchParams: SearchParams;
  onSubjectsData?: (subjects: SubjectType[]) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  tableRef,
  onProjectSelect,
  searchParams,
  onSubjectsData
}) => {
  const [treeData, setTreeData] = useState<TreeNodeData[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [selectedClazzId, setSelectedClazzId] = useState<string | null>(null);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [firstChildKey, setFirstChildKey] = useState<string | null>(null);
  const [maxLevel, setMaxLevel] = useState(1);
  const { openOverlay } = useOverlayManager();
  const [dimensionData, setDimensionData] = useState<TreeNodeData[]>([]);

  const generateIndactorColumns = useCallback(
    (currentMaxLevel: number): ColumnsType<EvaluaViewListNewList> => {
      const columns: ColumnsType<EvaluaViewListNewList> = [];

      for (let i = 1; i <= currentMaxLevel; i++) {
        columns.push({
          title: `${i}级评价指标`,
          key: `indactor${i}`,
          dataIndex: 'indactor',
          render: (_, record: EvaluaViewListNewList) => {
            const indactorLevels: any[] = [];
            let currentIndactor: any = record.indactor;
            while (currentIndactor) {
              indactorLevels.unshift(currentIndactor);
              currentIndactor = currentIndactor.parent;
            }

            const targetIndactor = indactorLevels[i - 1];

            return targetIndactor ? (
              <Box ml={respDims(12)}>{targetIndactor.indactorName}</Box>
            ) : (
              <Box ml={respDims(12)}>-</Box>
            );
          }
        });
      }

      return columns;
    },
    []
  );
  // const fetchFirstLevelData = async () => {
  //   try {
  //     const response = await listPageDimension({ current: 1, size: 9999 });
  //     const formattedData: TreeNodeData[] = response.records.map((item) => ({
  //       title: item.indactorName,
  //       key: item.id!,
  //       children: []
  //     }));

  //     setTreeData(formattedData);
  //     if (formattedData.length > 0) {
  //       await loadSecondLevelData(formattedData[0].key);
  //       setExpandedKeys([formattedData[0].key]);
  //     }
  //   } catch (error) {
  //     console.error('Failed to fetch first level data:', error);
  //   }
  // };
  // const loadSecondLevelData = async (parentKey: string) => {
  //   try {
  //     const response = await listPageProject({ current: 1, size: 9999, parentId: parentKey });
  //     const childData: TreeNodeData[] = response.records.map((item) => ({
  //       title: item.indactorName,
  //       key: item.id!,
  //       isLeaf: true,
  //       clazzId: item.clazzId,
  //       subjects: item.subjects
  //     }));
  //     onSubjectsData!(childData[0].subjects || []);
  //     setDimensionData((prevDimensionData) => [...prevDimensionData, ...childData]);

  //     setTreeData((origin) => updateTreeData(origin, parentKey, childData));

  //     if (childData.length > 0 && !firstChildKey) {
  //       const firstChild = childData[0];
  //       setFirstChildKey(firstChild.key);
  //       setSelectedKeys([firstChild.key]);
  //       setSelectedProjectId(firstChild.key);
  //       setSelectedClazzId(firstChild.clazzId || null);
  //       onProjectSelect(firstChild.key);
  //     }
  //   } catch (error) {
  //     console.error('Failed to fetch second level data:', error);
  //   }
  // };
  const onLoadData = async (treeNode: TreeNodeData): Promise<void> => {
    const { key, children } = treeNode;
    if (children && children.length > 0) return;

    // await loadSecondLevelData(key);
  };

  const updateTreeData = (
    list: TreeNodeData[],
    key: string,
    children: TreeNodeData[]
  ): TreeNodeData[] => {
    return list.map((node) => {
      if (node.key === key) {
        return { ...node, children };
      }
      return node;
    });
  };

  const onExpand = (expandedKeysValue: any) => {
    setExpandedKeys(expandedKeysValue);
  };

  const onSelect = (selectedKeysValue: any, info: any) => {
    const selectedChildData = dimensionData.find((child) => child.key === selectedKeysValue[0]);
    if (selectedChildData) {
      onSubjectsData!(selectedChildData.subjects || []);
    }
    setSelectedKeys(selectedKeysValue);
    if (selectedKeysValue.length > 0 && info.node.isLeaf) {
      const selectedNode = info.node;
      setSelectedProjectId(selectedNode.key);
      setSelectedClazzId(selectedNode.clazzId || null);
      onProjectSelect(selectedNode.key);
      setFirstChildKey(selectedNode.key);
    }
  };

  const itemStyle: FlexProps = {
    px: respDims(16),
    py: respDims(4),
    mb: respDims(4),
    align: 'center',
    borderRadius: respDims(8),
    cursor: 'pointer'
  };

  const onDelete = (data: EvaluaViewListNewList) => {
    const deleteAction = async () => {
      const deleteFunction =
        data.bizType === BizType.ClassroomEvaluation
          ? evaluaClassEvaluateDelete
          : evaluaHomeworkEvaluateDelete;

      await deleteFunction({ id: data.id });
      tableRef.current?.reload();
      Toast.success('删除成功');
    };

    MessageBox.confirm({
      title: '操作确认',
      content: '确定删除该评价数据？',
      onOk: deleteAction
    });
  };

  const onRemarkDetail = (remark: string) => {
    openOverlay({
      Overlay: RemarkDetail,
      props: {
        remark,
        onClose: () => {}
      }
    });
  };

  const columns: TableProps['columns'] = useMemo(() => {
    const baseColumns: ColumnsType<EvaluaViewListNewList> = [
      {
        title: '评价方',
        key: 'evaluatorName',
        dataIndex: 'evaluatorName'
      },
      {
        title: '被评价方',
        key: 'studentName',
        dataIndex: 'studentName'
      }
    ];

    const indactorColumns = generateIndactorColumns(maxLevel);

    const actionColumns: ColumnsType<EvaluaViewListNewList> = [
      {
        title: '评价内容',
        key: 'evaluateType',
        dataIndex: 'evaluateType',
        render: (_, record: EvaluaViewListNewList) => {
          switch (record.evaluateType) {
            case EvaluationType.Score:
              return <Box>{record.score}</Box>;
            case EvaluationType.ScoreLevelValue:
              return <Box>{record.scoreLevelValue}</Box>;
            default:
              return (
                <Button
                  color="#0052D9"
                  variant="link"
                  onClick={() => onRemarkDetail(record.remark)}
                >
                  查看
                </Button>
              );
          }
        }
      },
      {
        title: '学科',
        dataIndex: 'subjectName',
        key: 'subjectName',
        width: 120
      },
      {
        title: '更新时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 120,
        render: (value) => <>{value ? dayjs(value).format('YYYY/MM/DD HH:mm') : ''}</>
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (_, record: EvaluaViewListNewList) => (
          <Flex>
            <Button onClick={() => onDelete(record)} variant="link" colorScheme="primary">
              删除
            </Button>
          </Flex>
        )
      }
    ];

    return [...baseColumns, ...indactorColumns, ...actionColumns];
  }, [maxLevel, generateIndactorColumns]);

  const queryConfig = (data: PagingData<EvaluaViewListNewList>) => {
    let newMaxLevel = 1;
    data.records.forEach((item) => {
      let level = 1;
      let currentIndactor = item.indactor;
      while (currentIndactor?.parent) {
        level++;
        currentIndactor = currentIndactor.parent;
      }
      newMaxLevel = Math.max(newMaxLevel, level);
    });
    setMaxLevel(newMaxLevel);
  };

  // useEffect(() => {
  //   fetchFirstLevelData();
  // }, []);

  return (
    <Flex w="100%" bg="#fff" overflow="hidden" justifyContent="space-between" alignItems="center">
      <Box borderRight="1px solid #E5E7EB" w="210px" p="8px 20px 100px 0" h="82vh" overflowY="auto">
        <Tree
          loadData={onLoadData}
          treeData={treeData}
          expandedKeys={expandedKeys}
          onExpand={onExpand}
          onSelect={onSelect}
          selectedKeys={selectedKeys}
          defaultSelectedKeys={firstChildKey ? [firstChildKey] : []}
          titleRender={(nodeData: TreeNodeData) => (
            <Flex
              {...itemStyle}
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitBoxOrient: 'vertical',
                WebkitLineClamp: '1',
                whiteSpace: 'normal'
              }}
              justify="space-between"
              minW={nodeData.isLeaf ? respDims(160) : respDims(190)}
            >
              <Box flex="1 0 0">{nodeData.title}</Box>
            </Flex>
          )}
        />
      </Box>
      <Box flex="1" h="82vh">
        <MyTable
          ref={tableRef}
          rowKey="id"
          api={evaluaViewListPageNew}
          boxStyle={{ px: 4, py: 0 }}
          defaultQuery={{
            ...searchParams,
            projectId: firstChildKey!
          }}
          headerConfig={{
            showIfEmpty: true,
            showHeader: false
          }}
          emptyConfig={{
            EmptyPicComponent: () => <></>
          }}
          queryConfig={{
            enabled: !!(
              firstChildKey &&
              searchParams.term &&
              searchParams.year &&
              searchParams.clazzId
            ),
            onSuccess: (data) => {
              queryConfig(data as PagingData<EvaluaViewListNewList>);
            }
          }}
          columns={columns}
        />
      </Box>
    </Flex>
  );
};

export default Sidebar;
