import React from 'react';
import {
  Box,
  Flex,
  Text,
  SimpleGrid,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel
} from '@chakra-ui/react';
import StatisticItem from '@/components/StatisticItem';
import BoardPane, { BoardPaneRef } from '@/components/BoardPane';
import EChartsReact from 'echarts-for-react';
import { serviceSideProps } from '@/utils/i18n';
import { Select, Button, Cascader, Dropdown, message } from 'antd';
import { useState, useMemo, useEffect, useRef } from 'react';
import { getSemesterPage } from '@/api/tenant/teamManagement/semester';
import NoDataComponent from './components/NoDataProps';
import {
  getTestQualityAnalysis,
  getSubjectStatistics,
  getSubjectSummarizationGraph,
  getGradeList,
  getQueryReportSubjects,
  getTeacherType,
  createPPT,
  getConclusion
} from '@/api/kanban';
import DataSummaryModal, { DataSummaryModalRef } from './components/DataSummaryModal/index';
import { useUserStore } from '@/store/useUserStore';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import Lottie from '@/components/Lottie';
import styles from './index.module.scss';
import LevelScoreChart, { LevelScoreChartRef } from './components/LevelScoreChart';
import ThreeTest, { ThreeTestRef } from './components/ThreeTest';
import GradeDivision, { GradeDivisionRef } from './components/GradeDivision';

import ClassScoreChart, { ClassScoreChartRef } from './components/ClassScoreChart';
import RankDivision, {
  RankDivisionRef
} from '@/pages/tenant/digitalKanban/qualityAnalysis/components/RankDivision';
import AcademicLevel, {
  AcademicLevelRef
} from '@/pages/tenant/digitalKanban/qualityAnalysis/components/AcademicLevel';
import WeakSubject, {
  WeakSubjectRef
} from '@/pages/tenant/digitalKanban/qualityAnalysis/components/WeakSubject';
import { getRankDivision, levelDivision } from '@/api/tenant/digitalKanban/qualityAnalysis';
import {
  LevelDivisionType,
  RankDivisionType
} from '@/types/api/tenant/digitalKanban/qualityAnalysis';
import ProblemsAndMeasures, { ProblemsAndMeasuresRef } from './components/ProblemsAndMeasures';
import { CreatePPTParams } from '@/types/api/kanban';
import { uploadFile } from '@/api/file';
const { Option } = Select;

// const statistics = [
//   { content: '实考人数/应考人数', num: '746人/800人' },
//   { content: '满分', num: '660分' },
//   { content: '最高分', num: '660分' },
//   { content: '最低分', num: '360分' },
//   { content: '平均分', num: '450分' },
//   { content: '优秀率', num: '30%' },
//   { content: '合格率', num: '60%' },
//   { content: '低分率', num: '4%' }
// ];

const dataHubText = `数据表明，3班和4班在所有科目上表现优异，而1班、5班、6班、7班在语文、数学、英语、道法、历史、物理和化学等大多数科目上表现低于年级平均水平，尤其是6班在数学和道法上的表现最差，需要在这些科目上进行特别改进。`;
const dataClassGapText = `数据表明，七3班和七4班在优秀率（分别为80%和80.43%）和平均分（分别为386.28和386.83）方面表现最为突出，而七1班、七6班和七7班的合格率均为0%，且平均分（分别为347.15、344.32和332.01）低于年级平均值线356.86，需重点关注和改进。`;
const threeTestStandardsText = `数据表明，大多数班级在期末成绩相比于期中和第三次质量监测成绩有所下降，只有班级5（期中到期末提升21分，第三次到期末提升40分）和班级4（第三次到期末提升15分）有明显提升。`;
const scoreSegmentComparisonText = `从这些数据可以看出，道法和化学科目得分在50分以上的学生几乎没有，特别是道法在[20,30)分数段有345人，说明这两门课程对学生有一定挑战。语文和数学成绩分布广泛，高低分学生都有，反映了学生在这两门科目上的多样化表现。历史和物理成绩集中在中高分段（30-50分），学生普遍表现较好。英语成绩主要集中在中等水平（30-50分），显示大部分学生有扎实的基础。`;
const rankSegmentComparisonText = `数据表明，七年级3班在各个名次段表现突出，尤其是前10名有4人，前100名有12人，前200名有32人；七年级4班在前50名中有11人，显示出较强的竞争力；其他班级在各名次段表现相对较弱。特别是1班、6班，仅有11人和6人进入前100名，需要重点关注。`;
const scoreData: any = {
  九1班: { A: 59.57, B: 0, C: 347.15 },
  九2班: { A: 72.34, B: 2.13, C: 372.14 },
  九3班: { A: 80, B: 11.11, C: 386.28 },
  九4班: { A: 80.43, B: 8.7, C: 386.83 },
  九5班: { A: 57.45, B: 6.38, C: 338.34 },
  九6班: { A: 54.35, B: 0, C: 344.32 },
  九7班: { A: 50, B: 0, C: 332.01 },
  九8班: { A: 70.45, B: 2.27, C: 360.09 }
};

const tableData = [
  {
    class: '1班',
    name: '云岚',
    subject: '语文',
    score: 70,
    teacher: '沈青'
  },
  {
    class: '2班',
    name: '吴少芬',
    subject: '语文',
    score: 65,
    teacher: '沈青'
  },
  {
    class: '2班',
    name: '李清清',
    subject: '语文',
    score: 68,
    teacher: '沈青'
  },
  {
    class: '3班',
    name: '林杰',
    subject: '语文',
    score: 75,
    teacher: '林涛'
  },
  {
    class: '3班',
    name: '张镇礼',
    subject: '语文',
    score: 78,
    teacher: '林涛'
  },
  {
    class: '3班',
    name: '李南海',
    subject: '语文',
    score: 72,
    teacher: '林涛'
  },
  {
    class: '3班',
    name: '陈远远',
    subject: '语文',
    score: 64,
    teacher: '林涛'
  },
  {
    class: '4班',
    name: '欧阳少华',
    subject: '语文',
    score: 76,
    teacher: '林立'
  }
];

const problems = [
  {
    label: '课堂表现',
    description:
      '部分班级学生学习状态差，课堂秩序不佳，影响整体成绩。教师作业批改和反馈不及时，学生难以找到学习中的问题。',
    color: '#175DFF',
    bgColor: '#e4eaf8'
  },
  {
    label: '集体备课',
    description:
      '科组教研不规范，缺乏有效的教学计划和方案，教师之间的教学经验交流不足，无法提升整体教学水平。',
    color: '#34A7FA',
    bgColor: '#e5f1f9'
  },
  {
    label: '分层教学',
    description:
      '针对成绩中间层学生的关注不够，缺乏有效的提升手段。优生和后进生的培优工作不到位，导致两极分化严重。',
    color: '#14C9C9',
    bgColor: '#e2f4f5'
  },
  {
    label: '其他',
    description:
      '班级间的成绩差异大，部分班级教师可能需要额外培训。学生自律未成体系，可能与学习方法不当或学习压力过大有关。',
    color: '#F7BA1E',
    bgColor: '#faf4e5'
  }
];

const problems1 = [
  {
    label: '科任',
    description: '提前候课，避免学生迟到，确保课堂有序。关注学生学习状态，及时批改并反馈作业。',
    color: '#175DFF',
    bgColor: '#e4eaf8'
  },
  {
    label: '备课组',
    description:
      '按照计划时间、地点和主题内容，规范并有效地进行科组教研。加强教师之间的教学经验交流，提升整体教学水平。',
    color: '#34A7FA',
    bgColor: '#e5f1f9'
  },
  {
    label: '班主任',
    description: '加强班风学风建设，营造良好的班级学习环境。通过多种形式激励学生，增强学习积极性。',
    color: '#14C9C9',
    bgColor: '#e2f4f5'
  },
  {
    label: '其他',
    description:
      '对成绩较差班级的教师进行培训，提高教学质量。关注学生心理健康，减轻学习压力，提升学习效果。',
    color: '#F7BA1E',
    bgColor: '#faf4e5'
  }
];

const initDataHubChart = {
  title: {
    text: '数据汇总',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params: any) {
      let result = params[0].name + '<br/>';
      params.forEach(function (item: any) {
        result += item.marker + ' ' + item.seriesName + ': ' + item.data + '%' + '<br/>';
      });
      return result;
    },
    textStyle: {
      align: 'left'
    }
  },
  legend: {
    data: ['语文', '数学', '英语', '道法', '历史', '物理', '化学'],
    bottom: '3%',
    icon: 'circle'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1班', '2班', '3班', '4班', '5班', '6班', '7班', '8班'],
    axisLabel: {
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value} %'
    }
  },
  series: [
    {
      name: '语文',
      type: 'bar',
      data: [-2, 4, 5, 5, -4, -3, -2, 4],
      itemStyle: {
        color: '#175DFF'
      },
      label: {
        show: false
      },
      // barWidth: auto,
      barMaxwidth: 30,
      barCategoryGap: '0%',
      barGap: '0%'
    },
    {
      name: '数学',
      type: 'bar',
      data: [-3, 5, 1, 4, -4, -12, -13, -6],
      itemStyle: {
        color: '#14C9C9'
      },
      label: {
        show: false
      },
      barWidth: 8,
      barCategoryGap: '0%',
      barGap: '0%'
    },
    {
      name: '英语',
      type: 'bar',
      data: [-14, 0, 6, 7, -13, -13, -10, -3],
      itemStyle: {
        color: '#34A7FA'
      },
      label: {
        show: false
      },
      barWidth: 8,
      barCategoryGap: '0%',
      barGap: '0%'
    },
    {
      name: '道法',
      type: 'bar',
      data: [-6, 2, 6, 5, -10, -11, -10, 4],
      itemStyle: {
        color: '#F7BA1E'
      },
      barWidth: 8,
      label: {
        show: false
      },
      barCategoryGap: '0%',
      barGap: '0%'
    },
    {
      name: '历史',
      type: 'bar',
      data: [-4, 0, 6, 9, -11, -3, -5, 3],
      itemStyle: {
        color: '#FF6678 '
      },
      label: {
        show: false
      },
      barWidth: 8,
      barCategoryGap: '0%',
      barGap: '0%'
    },
    {
      name: '物理',
      type: 'bar',
      data: [-5, 9, 5, 7, -6, -11, -8, 2],
      itemStyle: {
        color: '#722ED1'
      },
      label: {
        show: false
      },
      barWidth: 8,
      barCategoryGap: '0%',
      barGap: '0%'
    },
    {
      name: '化学',
      type: 'bar',
      data: [-10, -4, 6, 8, -5, -9, -17, -5],
      itemStyle: {
        color: '#A0D7FF'
      },
      label: {
        show: false
      },
      barWidth: 8,
      barCategoryGap: '0%',
      barGap: '0%'
    }
  ]
};

const initGradeSegmentsChart = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params: any) {
      let result = params[0].name + '<br/>';
      params.forEach(function (item: any) {
        result += item.marker + ' ' + item.seriesName + ': ' + item.data + '%' + '<br/>';
      });
      return result;
    },
    textStyle: {
      align: 'left'
    }
  },
  legend: {
    data: ['A+', 'A'],
    bottom: '0%',
    icon: 'circle'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['八1班', '八2班', '八3班', '八4班', '八5班', '八6班', '八7班', '八8班']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value} %'
    }
  },
  series: [
    {
      name: 'A+',
      type: 'bar',
      data: [6, 6.1, 8.3, 6.1, 4.0, 2, 6, 4.2],
      itemStyle: {
        color: '#14C9C9'
      },
      barWidth: 20
    },
    {
      name: 'A',
      type: 'bar',
      data: [24, 14.2, 29.1, 22.4, 14.2, 10.2, 14, 17],
      itemStyle: {
        color: '#175DFF'
      },
      barWidth: 20
    }
  ]
};

const scoreData1 = {
  '1班': { midTerm: 456, thirdTest: 485, finalTerm: 440 },
  '2班': { midTerm: 551, thirdTest: 587, finalTerm: 541 },
  '3班': { midTerm: 629, thirdTest: 648, finalTerm: 608 },
  '4班': { midTerm: 666, thirdTest: 635, finalTerm: 650 },
  '5班': { midTerm: 401, thirdTest: 382, finalTerm: 422 },
  '6班': { midTerm: 416, thirdTest: 392, finalTerm: 396 },
  '7班': { midTerm: 390, thirdTest: 375, finalTerm: 383 },
  '8班': { midTerm: 526, thirdTest: 541, finalTerm: 515 }
} as any;

const scoreRanges = [
  '(0,10)',
  '(10,20)',
  '(20,30)',
  '(30,40)',
  '(40,50)',
  '(50,60)',
  '(60,70)',
  '(70,80)',
  '(80,90)',
  '(90,100]'
];

const scoreData2 = {
  语文: [2, 12, 52, 126, 180, 198, 107, 52, 14, 2],
  数学: [2, 8, 49, 102, 182, 204, 116, 57, 26, 0],
  英语: [5, 12, 45, 110, 204, 180, 124, 49, 14, 3],
  道法: [2, 13, 44, 120, 189, 186, 123, 57, 7, 4],
  历史: [3, 16, 51, 132, 162, 185, 122, 55, 18, 2],
  物理: [4, 20, 66, 116, 182, 196, 113, 38, 9, 2],
  化学: [5, 16, 43, 117, 192, 190, 125, 40, 14, 4]
};

const initScoreSegmentComparison = {
  title: {
    text: '成绩分段对比统计图',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    formatter: function (params: any) {
      let tooltipContent = `${params[0].axisValue}<br/>`;
      params.forEach((param: any) => {
        tooltipContent += `${param.seriesName}: ${param.data}人<br/>`;
      });
      return tooltipContent;
    }
  },
  legend: {
    data: ['语文', '数学', '英语', '道法', '历史', '物理', '化学'],
    bottom: '3%',
    icon: 'circle'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: scoreRanges
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '语文',
      type: 'line',
      data: scoreData2.语文,
      itemStyle: {
        color: '#175DFF'
      }
    },
    {
      name: '数学',
      type: 'line',
      data: scoreData2.数学,
      itemStyle: {
        color: '#14C9C9'
      }
    },
    {
      name: '英语',
      type: 'line',
      data: scoreData2.英语,
      itemStyle: {
        color: '#58a5f3'
      }
    },
    {
      name: '道法',
      type: 'line',
      data: scoreData2.道法,
      itemStyle: {
        color: '#F7BA1E'
      }
    },
    {
      name: '历史',
      type: 'line',
      data: scoreData2.历史,
      itemStyle: {
        color: '#FF6678'
      }
    },
    {
      name: '物理',
      type: 'line',
      data: scoreData2.物理,
      itemStyle: {
        color: '#722ED1'
      }
    },
    {
      name: '化学',
      type: 'line',
      data: scoreData2.化学,
      itemStyle: {
        color: '#A0D7FF'
      }
    }
  ]
};

const classNames = Object.keys(scoreData1);
const midTermScores = classNames.map((className) => scoreData1[className].midTerm);
const thirdTestScores = classNames.map((className) => scoreData1[className].thirdTest);
const finalTermScores = classNames.map((className) => scoreData1[className].finalTerm);

const initThreeTestStandards: any = {
  title: {
    text: '近三次测试标准分对比',
    left: 'left'
  },
  grid: {
    left: '4%',
    right: '4%',
    bottom: '20%'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params: any) {
      const className = params[0].axisValue;
      const scores = scoreData1[className];

      return `  期中质量监测: ${scores.midTerm}人<br/>
                第三次质量监测: ${scores.thirdTest}人<br/>
                期末质量分析: ${scores.finalTerm}人
              `;
    },
    textStyle: {
      align: 'left'
    }
  },
  legend: {
    data: ['期中质量监测', '第三次质量监测', '期末质量分析'],
    bottom: '3%',
    icon: 'circle'
  },
  xAxis: {
    type: 'category',
    data: classNames.map((name) => `${name}`)
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '期中质量监测',
      type: 'bar',
      data: midTermScores,
      itemStyle: {
        color: '#5dc6c7'
      }
    },
    {
      name: '第三次质量监测',
      type: 'bar',
      data: thirdTestScores,
      itemStyle: {
        color: '#2d5cf6'
      }
    },
    {
      name: '期末质量分析',
      type: 'bar',
      data: finalTermScores,
      itemStyle: {
        color: '#58a5f3'
      }
    }
  ]
};

interface ClassPerformanceParams {
  scoreData: Record<string, { A: number; B: number; C: number }>;
  classNames: string[];
  excellentRates: number[];
  qualifiedRates: number[];
  avgScores: number[];
  gradeAverage?: number;
  targetScore?: number;
}

export const initClassPerformanceContrast = ({
  scoreData,
  classNames,
  excellentRates,
  qualifiedRates,
  avgScores,
  gradeAverage,
  targetScore
}: ClassPerformanceParams) => {
  return {
    grid: {
      top: '14%',
      left: '3%',
      right: '4%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        const className = params[0].axisValue;
        const scores = scoreData[className];
        let result = `
                  优秀率: ${scores?.A}%<br/>
                  合格率: ${scores?.B}%<br/>
                  平均分: ${scores?.C}
                `;
        return result;
      },
      textStyle: {
        align: 'left'
      }
    },
    legend: {
      data: ['优秀率', '合格率', '平均分'],
      bottom: '3%',
      icon: 'circle'
    },
    xAxis: {
      type: 'category',
      data: classNames,
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: [
      {
        type: 'value',
        max: 100,
        name: '百分比（%）',
        nameTextStyle: {
          fontSize: 12,
          color: '#666'
        }
      },
      {
        type: 'value',
        max: Number((Math.max(...avgScores, targetScore || 0) + 50).toFixed(2)),
        name: '平均分',
        nameTextStyle: {
          fontSize: 12,
          color: '#666'
        }
      }
    ],
    series: [
      {
        name: '优秀率',
        type: 'bar',
        data: excellentRates,
        itemStyle: {
          color: '#5dc6c7'
        }
      },
      {
        name: '合格率',
        type: 'bar',
        data: qualifiedRates,
        itemStyle: {
          color: '#2d5cf6'
        }
      },
      {
        name: '平均分',
        type: 'bar',
        yAxisIndex: 1,
        data: avgScores,
        itemStyle: {
          color: '#58a5f3'
        },
        markLine: {
          symbol: 'none',
          data: [
            gradeAverage && {
              name: '年级平均值线',
              yAxis: gradeAverage,
              label: {
                show: true,
                position: 'insideEndTop',
                color: '#FFA500',
                formatter: `年级平均值线：${gradeAverage}`
              },
              lineStyle: {
                color: '#FFA500',
                type: 'solid',
                width: 1
              }
            },
            targetScore && {
              name: '目标分数线',
              yAxis: targetScore,
              label: {
                show: true,
                position: 'insideEndTop',
                color: '#FF0000',
                formatter: `目标分数线：${targetScore}`
              },
              lineStyle: {
                color: '#FF0000',
                type: 'solid',
                width: 1
              }
            }
          ].filter(Boolean),
          silent: true
        }
      }
    ]
  };
};

const rankData: any = {
  七年级1班: { 前10名: 0, 前50名: 4, 前100名: 6, 前200名: 24 },
  七年级2班: { 前10名: 1, 前50名: 6, 前100名: 10, 前200名: 27 },
  七年级3班: { 前10名: 3, 前50名: 4, 前100名: 12, 前200名: 32 },
  七年级4班: { 前10名: 3, 前50名: 3, 前100名: 10, 前200名: 30 },
  七年级5班: { 前10名: 0, 前50名: 2, 前100名: 6, 前200名: 17 },
  七年级6班: { 前10名: 0, 前50名: 4, 前100名: 10, 前200名: 24 },
  七年级7班: { 前10名: 1, 前50名: 3, 前100名: 12, 前200名: 20 },
  七年级8班: { 前10名: 1, 前50名: 4, 前100名: 12, 前200名: 23 }
};

const classNames1: any = Object.keys(rankData);
const top10Data: number[] = classNames1.map((className: string) => rankData[className]['前10名']);
const top50Data: number[] = classNames1.map((className: string) => rankData[className]['前50名']);
const top100Data: number[] = classNames1.map((className: string) => rankData[className]['前100名']);
const top200Data: number[] = classNames1.map((className: string) => rankData[className]['前200名']);

const initRankSegmentComparison = {
  title: {
    text: '各名次段人数对比',
    left: 'left'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '22%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function (params: any) {
      const className = params[0].axisValue;
      const ranks = rankData[className];

      return `
            前10名: ${ranks['前10名']}人<br/>
            前50名: ${ranks['前50名']}人<br/>
            前100名: ${ranks['前100名']}人<br/>
            前200名: ${ranks['前200名']}人
          `;
    }
  },
  legend: {
    data: ['前10名', '前50名', '前100名', '前200名'],
    bottom: '4%',
    icon: 'circle'
  },
  xAxis: {
    type: 'category',
    data: classNames1
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '前10名',
      type: 'bar',
      data: top10Data,
      itemStyle: {
        color: '#5dc6c7'
      }
    },
    {
      name: '前50名',
      type: 'bar',
      data: top50Data,
      itemStyle: {
        color: '#2d5cf6'
      }
    },
    {
      name: '前100名',
      type: 'bar',
      data: top100Data,
      itemStyle: {
        color: '#58a5f3'
      }
    },
    {
      name: '前200名',
      type: 'bar',
      data: top200Data,
      itemStyle: {
        color: '#eebd47'
      }
    }
  ]
};

const QualityAnalysis = () => {
  const [yearOptions, setYearOptions] = useState([]);
  const [testOptions, setTestOptions] = useState<any[]>([]);

  const [selectedTestData, setselectedTestData] = useState<string[]>([]);
  const [examTime, setExamTime] = useState<string>();

  // useEffect(() => {
  //   const fetchYearOptions = async () => {
  //     try {
  //       const response = await getSystemDictTree('semester_year');
  //       const options = response[0].children.map((item) => ({
  //         label: item.dictValue,
  //         value: item.dictValue
  //       }));
  //       setYearOptions(options);
  //     } catch (error) {
  //       console.error("获取学年数据失败:", error);
  //     }
  //   };

  //   fetchYearOptions();
  // }, []);

  // const gradeOptions = [
  //   {
  //     value: 1,
  //     label: '一年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 2,
  //     label: '二年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 3,
  //     label: '三年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 4,
  //     label: '四年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 5,
  //     label: '五年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 6,
  //     label: '六年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 7,
  //     label: '七年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 8,
  //     label: '八年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   },
  //   {
  //     value: 9,
  //     label: '九年级',
  //     children: [
  //       {
  //         value: 1,
  //         label: '第一学期'
  //       },
  //       {
  //         value: 2,
  //         label: '第二学期'
  //       }
  //     ]
  //   }
  // ];

  const schoolOptions = [
    {
      value: 1,
      label: '王艺皓 4647377440'
    },
    {
      value: 2,
      label: '周浩辰 4286537201'
    },
    {
      value: 3,
      label: '徐弘峰 1471184131'
    },
    {
      value: 4,
      label: '刘羽天 9503571697'
    },
    {
      value: 5,
      label: '李宇强 8321337517'
    },
    {
      value: 6,
      label: '赵嘉辉 2158487605'
    },
    {
      value: 7,
      label: '陈昊城 7091690875'
    },
    {
      value: 8,
      label: '孙钧辰 7861367383'
    },
    {
      value: 9,
      label: '杨睿洋 8707340826'
    },
    {
      value: 10,
      label: '胡瑞天 6241742913'
    },
    {
      value: 11,
      label: '高正南 6293698314'
    },
    {
      value: 12,
      label: '马嘉德 5428781342'
    },
    {
      value: 13,
      label: '张浩东 1678333193'
    },
    {
      value: 14,
      label: '何子辰 1578946250'
    },
    {
      value: 15,
      label: '罗皓威 4152728486'
    },
    {
      value: 16,
      label: '林嘉辉 7741689615'
    },
    {
      value: 17,
      label: '吴艺航 3442923450'
    },
    {
      value: 18,
      label: '郭泽正 2894731504'
    },
    {
      value: 19,
      label: '王泽博 8167234539'
    },
    {
      value: 20,
      label: '周鹏皓 4867983642'
    },
    {
      value: 21,
      label: '徐天羽 9582867611'
    },
    {
      value: 22,
      label: '刘弘泽 3731504962'
    },
    {
      value: 23,
      label: '李强嘉 6492813752'
    },
    {
      value: 24,
      label: '赵瑞辰 5274389203'
    },
    {
      value: 25,
      label: '陈艺南 4602734857'
    },
    {
      value: 26,
      label: '孙羽峰 7810563928'
    },
    {
      value: 27,
      label: '杨子辉 2461739850'
    },
    {
      value: 28,
      label: '胡弘天 1597326408'
    },
    {
      value: 29,
      label: '高瑞皓 4385761234'
    },
    {
      value: 30,
      label: '马强南 3984172506'
    },
    {
      value: 31,
      label: '张泽洋 6928431072'
    },
    {
      value: 32,
      label: '何子博 1573286498'
    },
    {
      value: 33,
      label: '罗天鹏 4827605193'
    },
    {
      value: 34,
      label: '林嘉南 6428931750'
    },
    {
      value: 35,
      label: '吴艺正 3758264091'
    },
    {
      value: 36,
      label: '郭浩洋 1265387402'
    },
    {
      value: 37,
      label: '王浩泽 3941725061'
    },
    {
      value: 38,
      label: '周子辰 8612735096'
    },
    {
      value: 39,
      label: '徐天博 2194857360'
    },
    {
      value: 40,
      label: '刘强羽 5762819340'
    },
    {
      value: 41,
      label: '李泽辉 6729403185'
    },
    {
      value: 42,
      label: '赵睿航 5872301469'
    },
    {
      value: 43,
      label: '陈正辉 4382160753'
    },
    {
      value: 44,
      label: '孙钧洋 7428956130'
    }
  ];

  const testNamOptions = [
    '期初质量检测',
    '第一次月考',
    '第二次月考',
    '期中质量监测',
    '第三次月考',
    '第四次月考',
    '期末质量分析'
  ];

  interface Semester {
    id: string;
    year: string;
    type: 1 | 2;
    isCurrent: 0 | 1;
    startDate: string;
    endDate: string;
  }

  const getSemesterLabel = (sem: Semester) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  const boardPaneRef = useRef<{ refresh: () => void }>(null);
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [currentSemester, setCurrentSemester] = useState<string>();
  const [selectedYear, setSelectedYear] = useState<string>('');
  const [selectedTerm, setSelectedTerm] = useState<number>(1);

  const [startDate, setStartDate] = useState<string>(''); //学年学期开始&结束时间
  const [endDate, setEndDate] = useState<string>(''); //学年学期开始&结束时间

  const [subjectOptions, setSubjectOptions] = useState<any[]>([]);
  const [showButtons, setShowButtons] = useState(false);
  const echartsRef = useRef<any | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const [chartOptions, setChartOptions] = useState<any>({});
  const [gradeOptions, setGradeOptions] = useState<any[]>([]);

  const [selectedGradesName, setSelectedGradesName] = useState<string>(''); //选中年级name
  const [selectedGradesId, setSelectedGradesId] = useState<string>(''); //选中年级id
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]); //选中学科
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>([]); //选中数据汇总柱状图学科
  const [chartSubjectsOptions, setChartSubjectsOptions] = useState<any[]>([]); //图表学科
  const [isSearchEnabled, setIsSearchEnabled] = useState(false); //是否禁用查询按钮
  const [isFirstLoad, setIsFirstLoad] = useState(true); // 新增状态，标记首次加载

  const [examSummary, setExamSummary] = useState<any>({});

  const { teacherType, setTeacherType } = useUserStore();

  const [isAuthority, setIsAuthority] = useState<boolean>(false); //是否有查看权限

  const [isLoading, setIsLoading] = useState(false);

  const [testQualityData, setTestQualityData] = useState<any[]>([]);
  const weakSubjectRef = useRef<WeakSubjectRef>(null);
  const gradeDivisionRef = useRef<GradeDivisionRef>(null);
  const classScoreChartRef = useRef<ClassScoreChartRef>(null);
  const threeTestRef = useRef<ThreeTestRef>(null);
  const levelScoreChartRef = useRef<LevelScoreChartRef>(null);
  const academicLevelRef = useRef<AcademicLevelRef>(null);
  const problemsAndMeasuresRef = useRef<ProblemsAndMeasuresRef>(null);
  const rankDivisionRef = useRef<RankDivisionRef>(null);
  const [rankDivisionData, setRankDivisionData] = useState<RankDivisionType>([]);
  const [levelDivisionData, setLevelDivisionData] = useState<LevelDivisionType>([]);
  const boardPaneRef1 = useRef<{ refresh: () => void }>(null);
  const [isExporting, setIsExporting] = useState(false);
  const dataSummaryModalRef = useRef<DataSummaryModalRef>(null);

  const fetchSemesters = async () => {
    try {
      const response = await getSemesterPage({ current: 1, size: 999 });
      setSemesters(response.records || []);
      const current = response.records.find((sem) => sem.isCurrent === 1);

      setCurrentSemester(current?.id);
      const selectedYear = current?.year;
      const selectedTerm = current?.type;

      // 更新状态
      setSelectedYear(selectedYear as string);
      setSelectedTerm(Number(selectedTerm));
      setStartDate(current?.startDate as string);
      setEndDate(current?.endDate as string);
    } catch (error) {
      console.error('获取学期数据失败:', error);
    }
  };

  // 获取学科
  const fetchSubjectList = async () => {
    try {
      if (selectedTestData.length > 0 && teacherType) {
        const grade = gradeOptions.find((item: any) => item.label === selectedGradesName);
        const response = await getQueryReportSubjects({
          semesterId: currentSemester as string,
          gradeId: grade?.id as any,
          gradeName: selectedGradesName,
          year: selectedYear,
          term: selectedTerm,
          teacherType: teacherType,
          examType: selectedTestData?.[0],
          examId: selectedTestData?.[1]
        }).catch((err) => {
          if (!err.data) {
            setIsAuthority(true);
          }
        });
        if (response && response.length > 0) {
          setSubjectOptions([
            ...response.map((grade: any) => ({
              value: grade === '全部' ? '全部' : grade,
              label: grade === '全部' ? '全部学科' : grade
            }))
          ]);
          if (response.includes('全部')) {
            setSelectedSubjects(['全部']); // 选择"全部"
          } else {
            setSelectedSubjects([response[0]]); // 选择第一项
          }
          setIsAuthority(false);
        }
      }
    } catch (error) {
      console.error('Failed to fetch subject list:', error);
    }
  };

  useEffect(() => {
    // 获取学年
    fetchSemesters();
  }, []);

  useEffect(() => {
    if (gradeOptions.length > 0 && selectedYear && selectedTerm) {
      // 默认选中第一个年级
      const firstGrade = gradeOptions[0];
      setSelectedGradesName(firstGrade.label);
      setSelectedGradesId(firstGrade.id);
      // 调用 getTeacherType 接口获取身份
      getTeacherType({ gradeId: firstGrade.id, semesterId: currentSemester as string })
        .then((response) => {
          setTeacherType(response as any); // 假设 setTeacherType 是用来设置教师身份的
        })
        .catch((error) => {
          console.error('获取教师身份失败:', error);
        });

      // 获取测试名称
      // fetchTestOptions();
    }
  }, [gradeOptions]);

  useEffect(() => {
    // 默认选中第一个测试名称
    if (testOptions.length > 0) {
      setselectedTestData([
        testOptions[0].value,
        testOptions[0].children[0].value,
        testOptions[0].children[0].examTime
      ]);
    }
  }, [testOptions]);

  useEffect(() => {
    fetchSubjectList();
  }, [teacherType]);

  const fetchData = async () => {
    try {
      const response = await getTestQualityAnalysis({
        year: selectedYear,
        term: selectedTerm,
        semesterId: currentSemester as string,
        startDate: startDate,
        endDate: endDate
      });
      setTestQualityData(response); // 提前储存数据

      if (response && response.length > 0) {
        // 更新年级选项
        const formattedGradeOptions = response.map((grade: any) => ({
          value: grade.gradeName,
          label: grade.gradeName,
          id: grade.gradeId
        }));
        setGradeOptions(formattedGradeOptions);

        // 默认选中第一个年级
        const firstGrade = formattedGradeOptions[0];
        setSelectedGradesName(firstGrade.label);
        setSelectedGradesId(firstGrade.id);

        // 更新测试名称选项，从第二级开始渲染
        function formattedTestOptions(response: any) {
          // 只获取第一个年级的数据
          const gradeData = response[0];

          // 创建一个Map来存储不同考试类型的数据
          const examTypeMap = new Map();

          // 遍历考试列表
          gradeData.examList.forEach((exam: any) => {
            const examTypeName = exam.examTypeName;

            // 如果Map中还没有这个考试类型，则初始化
            if (!examTypeMap.has(examTypeName)) {
              examTypeMap.set(examTypeName, {
                value: examTypeName,
                label: examTypeName,
                children: []
              });
            }

            // 获取当前考试类型的数据
            const examTypeData = examTypeMap.get(examTypeName);

            // 添加考试信息到children数组
            exam.reportList.forEach((report: any) => {
              examTypeData.children.push({
                value: report.examId,
                label: report.examName,
                examTime: report.examTime
              });
            });
          });

          // 将Map转换为数组
          return Array.from(examTypeMap.values());
        }
        const uniqueTestOptions = formattedTestOptions(response);
        console.log('uniqueTestOptions', uniqueTestOptions);
        setTestOptions(uniqueTestOptions);

        // 默认选中第一个测试名称
        const firstTest = uniqueTestOptions[0];
        setselectedTestData([
          firstTest.value,
          firstTest.children[0].value,
          firstTest.children[0].examTime
        ]);

        setIsAuthority(false);
      } else {
        setTestQualityData([]); // 提前储存数据
        setGradeOptions([]);
        setSelectedGradesName('');
        setSelectedGradesId('');
        setTestOptions([]);
        setselectedTestData([]);
      }
    } catch (error) {
      console.error('获取测试质量分析数据失败:', error);
      setIsAuthority(true);
    }
  };

  useEffect(() => {
    // 监听 gradeOptions 的变化
    if (selectedYear && selectedTerm) {
      fetchData();
    }
  }, [currentSemester]);

  useEffect(() => {
    // 初始化时设置默认选中的测试名称
    if (testOptions.length > 0 && !selectedTestData.length) {
      setselectedTestData([testOptions[0]?.value, testOptions[0]?.children?.[0]?.value]);
      if (testOptions[0]?.children?.[0]?.examTime) {
        setExamTime(testOptions[0]?.children?.[0]?.examTime);
      }
    }
  }, [selectedYear, selectedTerm]);

  useEffect(() => {
    setSelectedChartSubjects([]);

    const res = selectedSubjects?.map((subject: any) => ({
      value: subject,
      label: subject
    }));
    console.log('res44', res);
    // 检查用户是否选择了 "全部" 选项
    if (selectedSubjects.includes('全部')) {
      // 如果选择了 "全部"，则返回所有学科选项
      setChartSubjectsOptions(subjectOptions);
    } else {
      // 否则，仅返回用户选择的学科
      setChartSubjectsOptions(res);
    }
  }, [selectedSubjects]);

  useEffect(() => {
    setSelectedChartSubjects([]);

    // 检查 subjectOptions 是否包含 "全部" 选项
    const hasAllOption = subjectOptions.some((option: any) => option.label === '全部');

    const res = selectedSubjects
      ?.filter((subject: any) => subject !== '') // 过滤掉空值
      .map((subject: any) => ({
        value: subject,
        label: subject
      }));
    console.log('selectedSubjects', selectedSubjects);
  }, [selectedSubjects]);

  const handleSemesterChange = (value: string | undefined) => {
    setCurrentSemester(value);

    const currentSemesterIndex = semesters.findIndex((semester) => semester.id === value);

    // 确保索引有效
    if (currentSemesterIndex !== -1) {
      const selectedYear = semesters[currentSemesterIndex].year;
      const selectedTerm = semesters[currentSemesterIndex].type;
      setStartDate(semesters[currentSemesterIndex].startDate as string);
      setEndDate(semesters[currentSemesterIndex].endDate as string);

      // 更新状态
      setSelectedYear(selectedYear);
      setSelectedTerm(selectedTerm);

      // 清空年级和测试名称的下拉项和选中项
      setGradeOptions([]);
      setSelectedGradesName('');
      setSelectedGradesId('');
      setTestOptions([]);
      setselectedTestData([]);
    }
  };

  const handleGradeChange = (value: string) => {
    setSelectedGradesName(value);
    const selectedGrade = gradeOptions.find((grade) => grade.label === value);

    if (selectedGrade) {
      const selectedGradeData = testQualityData.find(
        (gradeData) => gradeData.gradeId === selectedGrade.id
      );

      if (selectedGradeData?.examList?.length > 0) {
        const formattedTestOptions = selectedGradeData.examList.map((exam: any) => ({
          value: exam.examTypeName,
          label: exam.examTypeName,
          examTime: exam?.examTime || '',
          children: exam.reportList.map((report: any) => ({
            value: report.examId,
            label: report.examName,
            examTime: report.examTime || ''
          }))
        }));

        setTestOptions(formattedTestOptions);

        // 直接设置第一个测试名称为默认选中
        const firstExam = selectedGradeData.examList[0];
        const firstReport = firstExam.reportList[0];
        setselectedTestData([
          firstExam.examTypeName,
          firstReport.examId,
          firstReport.examTime || ''
        ]);
      } else {
        setTestOptions([]);
        setselectedTestData([]);
      }
    } else {
      setTestOptions([]);
      setselectedTestData([]);
    }
  };

  const handleSubjectChange = (value: any) => {
    // 确保 value 是一个数组
    if (Array.isArray(value)) {
      setSelectedSubjects(value);
    } else {
      setSelectedSubjects([]);
    }
  };

  interface ExamOption {
    value: string;
    label: string;
    examTime?: string;
  }

  interface ExamTypeOption {
    value: string;
    label: string;
    children: ExamOption[];
  }

  const handleTestDataChange = (value: any, children: any) => {
    // 从 testOptions 中找到对应的考试信息
    const selectedExamType = testOptions.find(
      (option: ExamTypeOption) => option.value === value[0]
    );
    const selectedExam = selectedExamType?.children.find(
      (exam: ExamOption) => exam.value === value[1]
    );

    console.log('selectedExam', selectedExam);
    // 设置考试时间
    const examTime = selectedExam?.examTime || '';
    console.log('selectedExam', [...value, examTime]);
    setselectedTestData([...value, examTime]);
  };

  const generateExamSummary = (params: any, responseData: any[]) => {
    const result = {
      academic_year: params.year,
      semester: params.term === 1 ? '第一学期' : '第二学期',
      grade: params.gradeName,
      test_type: params.examType,
      test_name: '期末质量分析',
      subject: Array.isArray(params.subjectNames)
        ? params.subjectNames
        : (params.subjectNames || '').split(','),
      data_block_name: '数据汇总',
      data_render_type: '柱状图',
      data: [] as any[],
      analysis_requirements: '请你对本次测试的班级超均率进行分析'
    };

    responseData.forEach((item) => {
      const className = item.className.replace('七年级', '七');
      const exceedingAverageRate = parseFloat(item.overAverage) / 100;

      result.data.push({
        class_name: className,
        subject: item.subjectName,
        exceeding_average_rate: exceedingAverageRate
      });
    });

    return result;
  };
  const handleSearch = () => {
    fetchStatisticsData();
    fetchSubjectSummarizationGraph();

    fetchRankDivision();
    fetchLevelDivisionData();
    weakSubjectRef.current?.fetchWeakSubjectAnalysis();
    gradeDivisionRef.current?.getData();
    classScoreChartRef.current?.fetchGradeComparison();
    threeTestRef.current?.getData();
    levelScoreChartRef.current?.getData();
  };

  // 各名次段人数对比
  async function fetchRankDivision() {
    try {
      const res = await getRankDivision({
        year: selectedYear,
        term: selectedTerm,
        teacherType: teacherType,
        gradeId: selectedGradesId,
        gradeName: selectedGradesName,
        subjectNames: [selectedSubjects[0]],
        examType: selectedTestData?.[0] || '',
        examId: selectedTestData?.[1]
      });
      setRankDivisionData(res);
    } catch (error) {
      console.log('获取各名次段人数对比失败:', error);
    }
  }

  // 学业等级分布
  async function fetchLevelDivisionData() {
    try {
      const res = await levelDivision({
        year: selectedYear,
        term: selectedTerm,
        teacherType: teacherType,
        gradeId: Number(selectedGradesId),
        gradeName: selectedGradesName,
        subjectNames: [selectedSubjects[0]],
        examType: selectedTestData?.[0],
        examId: selectedTestData?.[1]
      });
      setLevelDivisionData(res);
    } catch (error) {
      console.log('获取学业等级分布失败:', error);
    }
  }

  // 统计数据
  const fetchStatisticsData = async () => {
    try {
      const response = await getSubjectStatistics({
        year: selectedYear,
        term: selectedTerm,
        startDate: startDate,
        endDate: endDate,
        subjectNames: selectedSubjects,
        gradeName: selectedGradesName,
        gradeId: selectedGradesId,
        examType: selectedTestData?.[0] || '',
        teacherType: teacherType,
        examId: selectedTestData?.[1] || ''
      });

      const data = response;
      const updatedStatistics = [
        {
          content: '实考人数/应考人数',
          num: `${data.submitStudentNum || 0}人/${data.classStudentNum || 0}人`
        },
        { content: '满分', num: `${data.standardScore || 0}分` },
        { content: '最高分', num: `${data.maxScore || 0} 分` },
        { content: '最低分', num: `${data.minScore || 0} 分` },
        { content: '平均分', num: `${data.avgScore || 0} 分` },
        { content: '优秀率', num: `${data.excellentRate || 0}% ` },
        { content: '合格率', num: `${data.qualifiedRate || 0}% ` },
        { content: '低分率', num: `${data.lowScoreRate || 0}% ` }
      ];

      setStatistics(updatedStatistics);
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  // 图表数据
  const fetchSubjectSummarizationGraph = async (value?: any) => {
    try {
      setIsLoading(true);
      const params = {
        year: selectedYear,
        term: selectedTerm,
        startDate: startDate,
        endDate: endDate,
        subjectNames: value?.length > 0 ? value : selectedSubjects,
        gradeName: selectedGradesName,
        gradeId: selectedGradesId,
        examType: selectedTestData?.[0] || '',
        teacherType: teacherType,
        examId: selectedTestData?.[1] || ''
      };

      const response = await getSubjectSummarizationGraph(params as any).catch((err) => {
        if (!err.data) {
          setIsAuthority(true);
        }
      });

      if (response && response.length > 0) {
        const processedData = processChartData(response);
        setChartOptions({});
        setTimeout(() => {
          setChartOptions({ ...processedData });
        }, 1000);

        const res = generateExamSummary(params, response);

        setExamSummary(res);

        setIsAuthority(false);
      } else {
        resetChartOptions();
      }
    } catch (error) {
      console.error('Error fetching subject summarization graph:', error);
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }
  };

  const resetChartOptions = () => {
    setTimeout(() => {
      setChartOptions({});
    }, 1000);
  };

  // 处理数据，生成图表配置
  const processChartData = (data: any[]) => {
    // 提取唯一的班级名称
    const classNames = Array.from(new Set(data.map((item) => item.className)));

    // 提取唯一的学科名称
    const subjectNames = Array.from(new Set(data.map((item) => item.subjectName)));

    // 按学科分组，生成 series 数据
    const series = subjectNames
      .map((subject) => {
        const subjectData = data
          .filter((item) => item.subjectName === subject)
          .map((item) => parseFloat(item.overAverage)); // 将 overAverage 转换为数值

        // 仅在 subjectData 不为空时创建 series 条目
        if (subjectData.length > 0) {
          return {
            name: subject,
            type: 'bar',
            data: subjectData,
            itemStyle: {
              color: getSubjectColor(subject) // 根据学科设置颜色
            },
            label: {
              show: false
            },
            barGap: '0%' // 减少组间的间隔
            // barCategoryGap: '0%' // 设置为0%确保柱子占满整个分类区域
          };
        }
        return null;
      })
      .filter(Boolean); // 过滤掉 null 值

    // 返回图表配置
    return {
      title: {
        text: '', //数据汇总
        left: 'left'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let result = params[0].name + '<br/>';
          params.forEach(function (item: any) {
            result += item.marker + ' ' + item.seriesName + ': ' + item.data + '%' + '<br/>';
          });
          return result;
        },
        textStyle: {
          align: 'left'
        }
      },
      legend: {
        data: subjectNames,
        bottom: '3%',
        icon: 'circle'
      },
      grid: {
        top: '12%',
        left: '0%',
        right: '0%',
        // bottom: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: classNames,
        axisLabel: {
          interval: 0,
          rotate: 0, // 增加旋转角度以减少重叠
          formatter: function (value: string) {
            return value.length > 5 ? value.slice(0, 5) + '...' : value; // 缩短标签长度
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '超均率', // 添加纵轴名称
        nameTextStyle: {
          padding: [0, 30, 0, 0], // 调整名称位置
          color: '#666'
        },
        axisLabel: {
          formatter: '{value} %'
        }
      },
      dataZoom: [
        {
          type: 'inside',
          realtime: true,
          height: 12,
          bottom: 0,
          handleSize: 0, // 调整手柄大小
          handleStyle: {
            color: '#D9D9D9', // 手柄颜色
            borderColor: 'transparent' // 手柄边框颜色设置为透明
          },
          textStyle: {
            color: '#000' // 文本颜色
          },
          borderColor: '#fff',
          showDetail: false,
          fillerColor: '#eee',
          backgroundColor: '#fff',
          showDataShadow: false,
          minSpan: 30,
          zoomLock: true
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 30,
          minSpan: 30,
          zoomLock: true
        }
      ],
      series: series
    };
  };

  const handleExport = () => {
    if (!echartsRef.current?.getEchartsInstance()) {
      return;
    }

    const chartInstance = echartsRef.current?.getEchartsInstance();

    // 显示加载指示器
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    // 设置 dataZoom 以显示完整图表
    chartInstance.setOption({
      tooltip: {
        show: false
      }
    });

    // 等待图表更新完成后再导出
    setTimeout(() => {
      // 隐藏加载指示器
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '数据汇总.png';
      link.click();

      // 还原初始配置
      chartInstance.setOption({
        tooltip: {
          show: true
        }
      });
    }, 1800); // 延迟以确保图表更新完成
  };

  // 根据学科名称返回对应的颜色
  const getSubjectColor = (subject: string) => {
    const colors = [
      '#175DFF',
      '#14C9C9',
      '#34A7FA',
      '#F7BA1E',
      '#FF6678',
      '#722ED1',
      '#A0D7FF',
      '#FF5733',
      '#33FF57',
      '#3357FF',
      '#FF33A1',
      '#A133FF',
      '#FF8C33',
      '#FF6347',
      '#4682B4' // 替换掉不喜欢的颜色
    ];

    // 使用学科名称的哈希值来选择颜色
    const hash = Array.from(subject).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const colorIndex = hash % colors.length;

    switch (subject) {
      case '语文':
        return colors[0];
      case '数学':
        return colors[1];
      case '英语':
        return colors[2];
      case '道法':
        return colors[3];
      case '历史':
        return colors[4];
      case '物理':
        return colors[5];
      case '化学':
        return colors[6];
      default:
      // 如果没有匹配的学科名称，使用 echarts 随机颜色
      // return colors[colorIndex];
    }
  };

  const [statistics, setStatistics] = useState([
    { content: '实考人数/应考人数', num: '' },
    { content: '满分', num: '' },
    { content: '最高分', num: '' },
    { content: '最低分', num: '' },
    { content: '平均分', num: '' },
    { content: '优秀率', num: '' },
    { content: '合格率', num: '' },
    { content: '低分率', num: '' }
  ]);

  useEffect(() => {
    // 检查 "全部" 是否在第一项，并且后面有新增选项
    if (selectedSubjects[0] === '全部' && selectedSubjects.length > 1) {
      setSelectedSubjects(selectedSubjects.slice(1));
    }

    // 检查 "全部" 是否在最后一项，并且前面有其他选项
    if (selectedSubjects[selectedSubjects.length - 1] === '全部' && selectedSubjects.length > 1) {
      // 只保留 "全部" 选项
      setSelectedSubjects(['全部']);
    }
  }, [selectedSubjects]);

  const handleChartSubjectChange = (value: string[]) => {
    if (value[0] === '全部') {
      setSelectedChartSubjects(['全部']);
    } else {
      setSelectedChartSubjects(value);
      setTimeout(() => {
        fetchSubjectSummarizationGraph(value);
      }, 1000);
    }
  };

  // 筛选框科目首次获取数据
  useEffect(() => {
    if (isFirstLoad) {
      if (currentSemester && gradeOptions.length && selectedTestData.length) {
        fetchSubjectSummarizationGraph();
        fetchStatisticsData();
        setTimeout(() => {
          classScoreChartRef.current?.fetchGradeComparison();
          gradeDivisionRef.current?.getData();
          threeTestRef.current?.getData();
          levelScoreChartRef.current?.getData();
        }, 1000);
      }

      setTimeout(() => {
        setIsFirstLoad(false);
      }, 2000);
    }
  }, [selectedSubjects]);

  // 筛选项选中判断是否禁用按钮
  useEffect(() => {
    if (
      selectedYear &&
      selectedTerm &&
      selectedSubjects.length > 0 &&
      selectedGradesId &&
      selectedTestData.length
    ) {
      setIsSearchEnabled(true);
    } else {
      setIsSearchEnabled(false);
    }
  }, [selectedYear, selectedTerm, selectedSubjects, selectedGradesId, selectedTestData]);

  useEffect(() => {
    if (selectedGradesId) {
      setTimeout(() => {
        getTeacherType({ gradeId: selectedGradesId, semesterId: currentSemester as string }).then(
          (res) => {
            if (selectedTestData.length && res === teacherType) {
              fetchSubjectList();
            }
          }
        );
      }, 500);
    }
  }, [selectedTestData]);

  useEffect(() => {
    // 监听 isModalVisible 的变化
    if (!isModalVisible) {
      boardPaneRef.current?.refresh();
    }
  }, [isModalVisible]);

  useEffect(() => {
    if (!isFirstLoad) {
      const updatedStatistics = [
        {
          content: '实考人数/应考人数',
          num: `0人 / 0人`
        },
        { content: '满分', num: `0分` },
        { content: '最高分', num: `0分` },
        { content: '最低分', num: `0分` },
        { content: '平均分', num: `0分` },
        { content: '优秀率', num: `0% ` },
        { content: '合格率', num: `0% ` },
        { content: '低分率', num: `0% ` }
      ];

      setselectedTestData([]);

      setSelectedSubjects([]);

      setStatistics(updatedStatistics);

      // setChartOptions([]);
    }
  }, [selectedGradesName, selectedGradesId]);

  useEffect(() => {
    if (testOptions.length > 0 && testOptions[0]?.children?.length > 0) {
      const firstOption = testOptions[0];
      const firstChild = firstOption.children[0];

      // 使用 setTimeout 确保在下一个事件循环中设置值
      setTimeout(() => {
        setselectedTestData([firstOption.value, firstChild.value, firstChild.examTime || '']);
      }, 0);
    }
  }, [testOptions]);

  // 获取父组件中BoardPane location={1}内容的函数
  const getMainBoardPaneContent = async (): Promise<string> => {
    try {
      const conclusionParams = {
        examId: selectedTestData?.[1] || '',
        gradeId: selectedGradesId || '',
        gradeName: selectedGradesName || '',
        subjectNames: selectedSubjects || []
      };

      const conclusions = await getConclusion(conclusionParams);
      const resData = conclusions.find((item: any) => item.location === 1); // location 1 是数据汇总图表结论

      return resData ? resData.conclusion : '';
    } catch (error) {
      console.error('获取主页面BoardPane内容失败:', error);
      return '';
    }
  };

  // 获取ECharts图表图片的函数
  const getEChartsImage = async (): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      if (!echartsRef.current?.getEchartsInstance()) {
        reject(new Error('ECharts实例不存在'));
        return;
      }

      const chartInstance = echartsRef.current.getEchartsInstance();

      try {
        // 设置图表选项以优化导出
        chartInstance.setOption({
          tooltip: {
            show: false
          }
        });

        // 等待图表更新完成后再导出
        setTimeout(async () => {
          try {
            const imgData = chartInstance.getDataURL({
              type: 'png',
              pixelRatio: 2, // 提高分辨率
              backgroundColor: '#fff'
            });

            // 还原初始配置
            chartInstance.setOption({
              tooltip: {
                show: true
              }
            });

            // 将Base64转换为Blob
            const base64Data = imgData.split(',')[1];
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });

            // 创建文件对象
            const file = new File([blob], `${selectedGradesName}数据汇总图表.png`, { type: 'image/png' });

            // 上传文件到服务器
            const formData = new FormData();
            formData.append('file', file);

            try {
              const response = await uploadFile(formData);
              if (response && response.fileUrl) {
                resolve(response.fileUrl);
              } else {
                reject(new Error('上传图表图片失败'));
              }
            } catch (uploadError) {
              reject(uploadError);
            }
          } catch (error) {
            reject(error);
          }
        }, 500); // 延迟以确保图表更新完成
      } catch (error) {
        reject(error);
      }
    });
  };

  // 导出报告处理函数
  const handleExportReport = async (type: 'grade' | 'subject') => {
    try {
      let tableImageUrl = '';
      let tableBoardPaneContent = '';
      let chartImageUrl = '';
      let chartBoardPaneContent = '';
      let levelScoreTableImageUrl = '';
      let classScoreChartImageUrl = '';
      let classScoreBoardPaneContent = '';
      let academicLevelChartImageUrl = '';
      let academicLevelBoardPaneContent = '';
      let threeTestChartImageUrl = '';
      let threeTestBoardPaneContent = '';
      let weakSubjectTableImageUrl = '';
      let gradeDivisionChartImageUrl = '';
      let gradeDivisionBoardPaneContent = '';
      let gradeDivisionTableImageUrl = '';
      let gradeDivisionTableBoardPaneContent = '';
      let rankDivisionChartImageUrl = '';
      let rankDivisionBoardPaneContent = '';
      let problemsAndMeasuresConclusions = {
        problems: {
          teachingRoutine: '',
          collectivePreparation: '',
          layeredTeaching: '',
          other: ''
        },
        measures: {
          subjectTeacher: '',
          preparationGroup: '',
          classTeacher: '',
          other: ''
        }
      };

      // 获取DataSummaryModal的数据，不需要打开模态框
      if (dataSummaryModalRef.current) {
        const { tableImageUrl: tableImageUrl1, boardPaneContent: boardPaneContent1 } = await dataSummaryModalRef.current.getTableCanvasWithoutModal();
        tableImageUrl = tableImageUrl1;
        tableBoardPaneContent = boardPaneContent1;
      }

      // 获取ECharts图表图片
      try {
        chartImageUrl = await getEChartsImage();
      } catch (error) {
        console.error('获取图表图片失败:', error);
        // 如果获取图表图片失败，继续执行，但chartImageUrl为空
      }

      // 获取主页面BoardPane location={1}的内容
      try {
        chartBoardPaneContent = await getMainBoardPaneContent();
      } catch (error) {
        console.error('获取主页面BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但chartBoardPaneContent为空
      }

      // 获取等级分段表格图片
      try {
        if (levelScoreChartRef.current) {
          levelScoreTableImageUrl = await levelScoreChartRef.current.getLevelScoreTableImage();
        }
      } catch (error) {
        console.error('获取等级分段表格图片失败:', error);
        // 如果获取失败，继续执行，但levelScoreTableImageUrl为空
      }

      // 获取班级成绩对比图表图片
      try {
        if (classScoreChartRef.current) {
          classScoreChartImageUrl = await classScoreChartRef.current.getClassScoreChartImage();
        }
      } catch (error) {
        console.error('获取班级成绩对比图表图片失败:', error);
        // 如果获取失败，继续执行，但classScoreChartImageUrl为空
      }

      // 获取班级成绩对比BoardPane内容
      try {
        if (classScoreChartRef.current) {
          classScoreBoardPaneContent = await classScoreChartRef.current.getClassScoreBoardPaneContent();
        }
      } catch (error) {
        console.error('获取班级成绩对比BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但classScoreBoardPaneContent为空
      }

      // 获取学业等级分布图表图片
      try {
        if (academicLevelRef.current) {
          academicLevelChartImageUrl = await academicLevelRef.current.getAcademicLevelChartImage();
        }
      } catch (error) {
        console.error('获取学业等级分布图表图片失败:', error);
        // 如果获取失败，继续执行，但academicLevelChartImageUrl为空
      }

      // 获取学业等级分布BoardPane内容
      try {
        if (academicLevelRef.current) {
          academicLevelBoardPaneContent = await academicLevelRef.current.getAcademicLevelBoardPaneContent();
        }
      } catch (error) {
        console.error('获取学业等级分布BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但academicLevelBoardPaneContent为空
      }

      // 获取近三次测试标准分对比图表图片
      try {
        if (threeTestRef.current) {
          threeTestChartImageUrl = await threeTestRef.current.getThreeTestChartImage();
        }
      } catch (error) {
        console.error('获取近三次测试标准分对比图表图片失败:', error);
        // 如果获取失败，继续执行，但threeTestChartImageUrl为空
      }

      // 获取近三次测试标准分对比BoardPane内容
      try {
        if (threeTestRef.current) {
          threeTestBoardPaneContent = await threeTestRef.current.getThreeTestBoardPaneContent();
        }
      } catch (error) {
        console.error('获取近三次测试标准分对比BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但threeTestBoardPaneContent为空
      }

      // 获取薄弱学科分析表格图片
      try {
        if (weakSubjectRef.current) {
          weakSubjectTableImageUrl = await weakSubjectRef.current.getWeakSubjectTableImage();
        }
      } catch (error) {
        console.error('获取薄弱学科分析表格图片失败:', error);
        // 如果获取失败，继续执行，但weakSubjectTableImageUrl为空
      }

      // 获取成绩分段对比图表图片
      try {
        if (gradeDivisionRef.current) {
          gradeDivisionChartImageUrl = await gradeDivisionRef.current.getGradeDivisionChartImage();
        }
      } catch (error) {
        console.error('获取成绩分段对比图表图片失败:', error);
        // 如果获取失败，继续执行，但gradeDivisionChartImageUrl为空
      }

      // 获取成绩分段对比BoardPane内容
      try {
        if (gradeDivisionRef.current) {
          gradeDivisionBoardPaneContent = await gradeDivisionRef.current.getGradeDivisionBoardPaneContent();
        }
      } catch (error) {
        console.error('获取成绩分段对比BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但gradeDivisionBoardPaneContent为空
      }

      // 获取成绩分段表格图片
      try {
        if (gradeDivisionRef.current) {
          gradeDivisionTableImageUrl = await gradeDivisionRef.current.getGradeDivisionTableImage();
        }
      } catch (error) {
        console.error('获取成绩分段表格图片失败:', error);
        // 如果获取失败，继续执行，但gradeDivisionTableImageUrl为空
      }

      // 获取成绩分段表格BoardPane内容
      try {
        if (gradeDivisionRef.current) {
          gradeDivisionTableBoardPaneContent = await gradeDivisionRef.current.getGradeDivisionTableBoardPaneContent();
        }
      } catch (error) {
        console.error('获取成绩分段表格BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但gradeDivisionTableBoardPaneContent为空
      }

      // 获取各名次段人数对比图表图片
      try {
        if (rankDivisionRef.current) {
          rankDivisionChartImageUrl = await rankDivisionRef.current.getRankDivisionChartImage();
        }
      } catch (error) {
        console.error('获取各名次段人数对比图表图片失败:', error);
        // 如果获取失败，继续执行，但rankDivisionChartImageUrl为空
      }

      // 获取各名次段人数对比BoardPane内容
      try {
        if (rankDivisionRef.current) {
          rankDivisionBoardPaneContent = await rankDivisionRef.current.getRankDivisionBoardPaneContent();
        }
      } catch (error) {
        console.error('获取各名次段人数对比BoardPane内容失败:', error);
        // 如果获取失败，继续执行，但rankDivisionBoardPaneContent为空
      }

      // 获取存在问题和改进措施的所有结论
      try {
        if (problemsAndMeasuresRef.current) {
          problemsAndMeasuresConclusions = await problemsAndMeasuresRef.current.getProblemsAndMeasuresConclusions();
        }
      } catch (error) {
        console.error('获取存在问题和改进措施结论失败:', error);
        // 如果获取失败，继续执行，但使用默认空值
      }

      console.log('数据汇总表格图片URL:', tableImageUrl);
      console.log('数据汇总表格BoardPane内容:', tableBoardPaneContent);
      console.log('数据汇总图表图片URL:', chartImageUrl);
      console.log('数据汇总图表BoardPane内容:', chartBoardPaneContent);
      console.log('等级分段表格图片URL:', levelScoreTableImageUrl);
      console.log('班级成绩对比图表图片URL:', classScoreChartImageUrl);
      console.log('班级成绩对比BoardPane内容:', classScoreBoardPaneContent);
      console.log('学业等级分布图表图片URL:', academicLevelChartImageUrl);
      console.log('学业等级分布BoardPane内容:', academicLevelBoardPaneContent);
      console.log('近三次测试标准分对比图表图片URL:', threeTestChartImageUrl);
      console.log('近三次测试标准分对比BoardPane内容:', threeTestBoardPaneContent);
      console.log('薄弱学科分析表格图片URL:', weakSubjectTableImageUrl);
      console.log('成绩分段对比图表图片URL:', gradeDivisionChartImageUrl);
      console.log('成绩分段对比BoardPane内容:', gradeDivisionBoardPaneContent);
      console.log('成绩分段表格图片URL:', gradeDivisionTableImageUrl);
      console.log('成绩分段表格BoardPane内容:', gradeDivisionTableBoardPaneContent);
      console.log('各名次段人数对比图表图片URL:', rankDivisionChartImageUrl);
      console.log('各名次段人数对比BoardPane内容:', rankDivisionBoardPaneContent);
      console.log('存在问题和改进措施结论:', problemsAndMeasuresConclusions);

      console.log('成功获取到所有图片URL和结论内容，无需打开模态框');
      let params: CreatePPTParams = {} as CreatePPTParams;

      if (type === 'grade') {
        params = {
          "year": selectedYear,
          "grade": selectedGradesName,
          "testName": selectedTestData?.[0],
          "subject": selectedSubjects.join(','),
          content: [
            {
              "title": `${selectedGradesName}各班数据汇总统计表`,
              "imageUrl": tableImageUrl,
              "conclusion": tableBoardPaneContent
            },
            {
              "title": `${selectedGradesName}数据汇总图表`,
              "imageUrl": chartImageUrl,
              "conclusion": chartBoardPaneContent
            },
            {
              "title": `${selectedGradesName}等级分段表格统计表`,
              "imageUrl": levelScoreTableImageUrl,
              "conclusion": ""
            },
            {
              "title": `${selectedGradesName}班级成绩对比`,
              "imageUrl": classScoreChartImageUrl,
              "conclusion": classScoreBoardPaneContent
            },
            {
              "title": `${selectedGradesName}学业等级分布`,
              "imageUrl": academicLevelChartImageUrl,
              "conclusion": academicLevelBoardPaneContent
            },
            {
              "title": `${selectedGradesName}近三次测试标准分对比`,
              "imageUrl": threeTestChartImageUrl,
              "conclusion": threeTestBoardPaneContent
            },
            {
              "title": `${selectedGradesName}薄弱学科分析`,
              "imageUrl": weakSubjectTableImageUrl,
              "conclusion": ""
            },
            {
              "title": `${selectedGradesName}成绩分段对比`,
              "imageUrl": gradeDivisionChartImageUrl,
              "conclusion": gradeDivisionBoardPaneContent
            },
            {
              "title": `${selectedGradesName}成绩分段表格`,
              "imageUrl": gradeDivisionTableImageUrl,
              "conclusion": gradeDivisionTableBoardPaneContent
            },
            {
              "title": `${selectedGradesName}各名次段人数对比`,
              "imageUrl": rankDivisionChartImageUrl,
              "conclusion": rankDivisionBoardPaneContent
            }

          ],
          problem: `一、教学常规\n${problemsAndMeasuresConclusions.problems.teachingRoutine}\n二、集体备课\n${problemsAndMeasuresConclusions.problems.collectivePreparation}\n三、分层教学\n${problemsAndMeasuresConclusions.problems.layeredTeaching}\n四、其他问题\n${problemsAndMeasuresConclusions.problems.other}`,
          measure: `一、科任\n${problemsAndMeasuresConclusions.measures.subjectTeacher}\n二、备课组\n${problemsAndMeasuresConclusions.measures.preparationGroup}\n三、班主任\n${problemsAndMeasuresConclusions.measures.classTeacher}\n四、其他措施\n${problemsAndMeasuresConclusions.measures.other}`
        };

      } else if (type === 'subject') {
        params = {
          "year": selectedYear,
          "grade": selectedGradesName,
          "testName": selectedTestData?.[0],
          "subject": selectedSubjects.join(','),
          content: [
            {
              "title": `数据分析1 - 各班数据汇总统计表`,
              "imageUrl": tableImageUrl,
              "conclusion": tableBoardPaneContent
            },
            {
              "title": `数据分析2 - 成绩分段对比统计表`,
              "imageUrl": gradeDivisionChartImageUrl,
              "conclusion": gradeDivisionBoardPaneContent
            },
            {
              "title": `数据分析3 - 成绩分段对比统计图`,
              "imageUrl": gradeDivisionChartImageUrl,
              "conclusion": gradeDivisionBoardPaneContent
            },
          ],
          problem: `一、教学常规\n${problemsAndMeasuresConclusions.problems.teachingRoutine}\n二、集体备课\n${problemsAndMeasuresConclusions.problems.collectivePreparation}\n三、分层教学\n${problemsAndMeasuresConclusions.problems.layeredTeaching}\n四、其他问题\n${problemsAndMeasuresConclusions.problems.other}`,
          measure: `一、科任\n${problemsAndMeasuresConclusions.measures.subjectTeacher}\n二、备课组\n${problemsAndMeasuresConclusions.measures.preparationGroup}\n三、班主任\n${problemsAndMeasuresConclusions.measures.classTeacher}\n四、其他措施\n${problemsAndMeasuresConclusions.measures.other}`

        }
      }


      const res = await createPPT(params);
      console.log(res);
      if (res) {
        const url = window.URL.createObjectURL(res.data);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${selectedGradesName}质量分析报告.pptx`;
        a.click();
        window.URL.revokeObjectURL(url);
        message.success('导出报告成功');
      } else {
        message.error('导出报告失败');
      }
    } catch (error) {
      console.error('导出报告失败:', error);
      message.error('导出报告失败');
    }
  };

  // 判断导出按钮的选项
  const exportItems = useMemo(() => {
    const items: { key: string; label: string }[] = [];

    // 如果选择了"全部"学科，提供两种导出选项
    if (selectedSubjects.includes('全部')) {
      items.push({
        key: 'grade',
        label: '年级组质量分析'
      });
      items.push({
        key: 'subject',
        label: '备课组质量分析'
      });
    } else {
      // 如果选择了多个学科，可以导出年级组质量分析
      if (selectedSubjects.length > 1) {
        items.push({
          key: 'grade',
          label: '年级组质量分析'
        });
      }

      // 如果只选择了一个学科，可以导出备课组质量分析
      if (selectedSubjects.length === 1) {
        items.push({
          key: 'subject',
          label: '备课组质量分析'
        });
      }
    }

    return items;
  }, [selectedSubjects]);

  return (
    <Box
      padding="14px 24px"
      w="100%"
      className={styles.qualityAnalysis}
      borderRadius="24px 0px 0px 0px"
      borderTop="1px solid #FFF"
      borderLeft="1px solid #FFF"
      background="#F2F4F5"
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      backdropFilter="blur(3.700000047683716px)"
    >
      <Flex
        mb="12px"
        padding="0 16px"
        alignItems="center"
        justifyContent="space-between"
        bg="#fff"
        borderRadius="8px"
        flexDirection={{ base: 'column', md: 'row' }}
        height={'57px'}
      >
        <Flex w="260px" alignItems="center" padding="12px" mb={{ base: '12px', md: '0' }}>
          <Box>数智看版</Box>
          <SvgIcon name="chevronRight" m="0 0px 0 4px" w="20px" h="20px" />
          <Box color="#175DFF" fontWeight="700" ml="8px">
            测试质量分析
          </Box>
        </Flex>

        <Box display="flex" alignItems="center" padding="12px 0" flexWrap="wrap">
          <Box
            bgColor="#fff"
            padding="8px 12px"
            display="flex"
            alignItems="center"
            marginRight={respDims(14)}
            mb={{ base: '12px', md: '0' }}
          >
            <Text fontSize="16px" color="#2c3e38" mb="4px" mr="8px">
              学年：
            </Text>
            <Select
              showSearch
              value={currentSemester}
              placeholder="请选择学期"
              onChange={handleSemesterChange}
              style={{
                width: '200px',
                borderColor: '#F3F4F6',
                borderRadius: '4px',
                background: '#fff !important'
              }}
            >
              {semesters.map((sem) => (
                <Option key={sem.id} value={sem.id}>
                  {getSemesterLabel(sem)}
                </Option>
              ))}
            </Select>
          </Box>
          <Box
            bgColor="#fff"
            padding="8px 12px"
            display="flex"
            alignItems="center"
            marginRight={respDims(14)}
            mb={{ base: '12px', md: '0' }}
          >
            <Text fontSize="16px" color="#1D2129" mb="4px" mr="8px">
              年级：
            </Text>
            <Select
              style={{ minWidth: '130px' }}
              options={gradeOptions}
              placeholder="请选择年级"
              value={selectedGradesName}
              onChange={handleGradeChange}
            />
          </Box>
          <Box
            bgColor="#fff"
            padding="8px 12px"
            display="flex"
            alignItems="center"
            marginRight={respDims(14)}
            mb={{ base: '12px', md: '0' }}
          >
            <Text fontSize="16px" color="#1D2129" mb="4px" mr="8px">
              测试名称：
            </Text>
            <Cascader
              options={testOptions}
              placeholder="请选择测试名称"
              value={selectedTestData}
              onChange={handleTestDataChange}
              allowClear={false}
            />
          </Box>
          <Box
            bgColor="#fff"
            padding="8px 12px"
            display="flex"
            alignItems="center"
            mb={{ base: '12px', md: '0' }}
          >
            <Text fontSize="16px" color="#1D2129" mb="4px" mr="8px">
              科目：
            </Text>
            <Select
              options={subjectOptions}
              style={{ minWidth: '130px' }}
              placeholder="请选择科目"
              value={selectedSubjects}
              onChange={handleSubjectChange}
              mode="multiple"
              maxTagCount={3}
            />
          </Box>
          <Button
            type="primary"
            onClick={handleSearch}
            style={{ marginLeft: '12px', display: 'flex', alignItems: 'center' }}
            disabled={!isSearchEnabled} // 根据状态禁用按钮
          >
            <SvgIcon name="search" mr="6px"></SvgIcon> 查询
          </Button>

          <Dropdown
            menu={{
              items: exportItems,
              onClick: ({ key }) => {
                if (key === 'grade') {
                  handleExportReport('grade');
                } else if (key === 'subject') {
                  handleExportReport('subject');
                }
              }
            }}
            disabled={!isSearchEnabled || exportItems.length === 0}
            placement="bottomRight"
          >
            <Button
              style={{ marginLeft: '12px', display: 'flex', alignItems: 'center' }}
              loading={isExporting}
              disabled={!isSearchEnabled || exportItems.length === 0}
            >
              <SvgIcon name="download" mr="6px"></SvgIcon> 导出报告
            </Button>
          </Dropdown>
        </Box>
      </Flex>

      <SimpleGrid columns={8} spacing={3} mb={4} className="statistic-box">
        {statistics.map((item, index) => (
          <StatisticItem key={index} content={item.content} num={item.num} />
        ))}
      </SimpleGrid>

      <Box display="flex" w="100%" pb="12px" mt="-16px">
        <Box
          width="calc(38% - 12px)"
          backgroundColor="#fff"
          marginRight="12px"
          padding="16px 16px 20px 16px"
          borderRadius="12px"
          transition="box-shadow 0.3s ease"
          position="relative"
        >
          {Object.keys(chartOptions).length > 0 && (
            <Box display="flex" position="absolute" zIndex="999" right="0" marginRight="16px">
              <>
                <Select
                  style={{ maxWidth: '300px', minWidth: '120px', marginRight: '180px' }}
                  placeholder="选择学科"
                  options={chartSubjectsOptions}
                  value={selectedChartSubjects}
                  onChange={handleChartSubjectChange}
                  mode="multiple"
                  maxTagCount={4}
                />
                <Box
                  as="button"
                  position="absolute"
                  right="100px"
                  top="0"
                  zIndex="10"
                  borderRadius="4px"
                  cursor="pointer"
                  width="60px"
                  height="28px"
                  fontSize="14px"
                  background="#f7f8fa"
                  style={{ display: 'inline-block' }}
                  onClick={handleExport}
                  color={'#636C7B'}
                >
                  导出
                </Box>
                <Box
                  as="button"
                  position="absolute"
                  right="4px"
                  top="0"
                  zIndex="10"
                  borderRadius="4px"
                  cursor="pointer"
                  width="80px"
                  height="28px"
                  fontSize="14px"
                  background="#f7f8fa"
                  style={{ display: 'inline-block' }}
                  onClick={() => setIsModalVisible(true)}
                  color={'#636C7B'}
                >
                  查看详情
                </Box>
              </>
            </Box>
          )}

          <Box
            color="#1d2129"
            fontSize="16px"
            fontWeight="500"
            fontStyle="normal"
            marginBottom="18px"
          >
            数据汇总
          </Box>

          {isLoading && (
            <Box
              position="absolute"
              top="0"
              left="0"
              right="0"
              bottom="0"
              bg="rgba(255, 255, 255, 255)"
              display="flex"
              alignItems="center"
              justifyContent="center"
              zIndex="1"
            >
              <Lottie
                name="chating"
                w={respDims(`${(130 / 3) * 2}rpx`, 130 / 3)}
                h={respDims(`${(45 / 3) * 2}rpx`, 45 / 3)}
              />
            </Box>
          )}

          {Object.keys(chartOptions).length > 0 ? (
            <EChartsReact
              ref={echartsRef}
              option={chartOptions}
              style={{ borderRadius: '12px', position: 'relative', height: '300px' }}
            />
          ) : (
            <NoDataComponent type={isAuthority ? 'noPermission' : 'noData'} />
          )}

          {Object.keys(chartOptions).length > 0 && (
            <Box position={'relative'}>
              <BoardPane
                location={1}
                height="100px"
                ref={boardPaneRef1}
                examId={selectedTestData?.[1] || ''}
                selectedSubjects={selectedSubjects}
                selectedTestData={selectedTestData}
                selectedGradesName={selectedGradesName}
                selectedGradesId={selectedGradesId}
                editable={true}
                params={examSummary}
                fullScreenHeight="165px"
                fullScreenoffsetX="40"
                title="结论"
                backgroundColor="#f9f9f9"
                titleFontSize="14px"
                padding="10px"
                showFullScreenButtons
              />
            </Box>
          )}
        </Box>

        <Box
          // flex="1"
          backgroundColor="#fff"
          marginRight="12px"
          padding="16px 16px 20px 16px"
          borderRadius="12px"
          transition="box-shadow 0.3s ease"
          position="relative"
          width="calc(25% - 12px)"
        >
          <LevelScoreChart
            title="等级分段"
            ref={levelScoreChartRef}
            option={initGradeSegmentsChart}
            chartSubjectsOptions={chartSubjectsOptions}
            selectedChartSubjects={selectedChartSubjects}
            params={{
              year: selectedYear,
              term: selectedTerm,
              semesterId: currentSemester as string,
              gradeId: selectedGradesId,
              gradeName: selectedGradesName,
              examType: selectedTestData?.[0],
              examId: selectedTestData?.[1] || '',
              startDate: startDate,
              endDate: endDate,
              teacherType: teacherType
            }}
            selectedSubjects={selectedSubjects}
            selectedGradesName={selectedGradesName}
            selectedGradesId={selectedGradesId}
            isAuthority={isAuthority}
          />
        </Box>

        <Box
          flex="1"
          backgroundColor="#fff"
          padding="16px 16px 20px 16px"
          borderRadius="12px"
          transition="box-shadow 0.3s ease"
          position="relative"
          width="calc(38% - 12px)"
        >
          <ClassScoreChart
            title="班级成绩对比"
            ref={classScoreChartRef}
            option={initGradeSegmentsChart}
            chartSubjectsOptions={chartSubjectsOptions}
            selectedChartSubjects={selectedChartSubjects}
            params={{
              year: selectedYear,
              term: selectedTerm,
              semesterId: currentSemester as string,
              semester: semesters,
              gradeId: selectedGradesId,
              gradeName: selectedGradesName,
              examType: selectedTestData?.[0],
              examId: selectedTestData?.[1] || '',
              startDate: startDate,
              endDate: endDate,
              teacherType: teacherType
            }}
            selectedSubjects={selectedSubjects}
            selectedGradesName={selectedGradesName}
            selectedGradesId={selectedGradesId}
            isAuthority={isAuthority}
          />
        </Box>
      </Box>

      <Box display="flex" w="100%" pb="12px">
        <Box
          width="calc(38% - 12px)"
          backgroundColor="#fff"
          marginRight="12px"
          padding="16px 16px 20px 16px"
          borderRadius="12px"
          transition="box-shadow 0.3s ease"
          position="relative"
        >
          <ThreeTest
            ref={threeTestRef}
            option={initGradeSegmentsChart}
            chartSubjectsOptions={chartSubjectsOptions}
            selectedChartSubjects={selectedChartSubjects}
            params={{
              year: selectedYear,
              term: selectedTerm,
              semesterId: currentSemester as string,
              gradeId: selectedGradesId,
              gradeName: selectedGradesName,
              examType: selectedTestData?.[0],
              examId: selectedTestData?.[1] || '',
              examTime: selectedTestData?.[2] || '',
              startDate: startDate,
              endDate: endDate,
              teacherType: teacherType
            }}
            selectedSubjects={selectedSubjects}
            selectedGradesName={selectedGradesName}
            selectedGradesId={selectedGradesId}
            isAuthority={isAuthority}
          />
        </Box>
        <Box
          flex="1"
          backgroundColor="#fff"
          padding="16px 16px 20px 16px"
          borderRadius="12px"
          transition="box-shadow 0.3s ease"
          position="relative"
        >
          <GradeDivision
            ref={gradeDivisionRef}
            option={initGradeSegmentsChart}
            chartSubjectsOptions={chartSubjectsOptions}
            selectedChartSubjects={selectedChartSubjects}
            params={{
              year: selectedYear,
              term: selectedTerm,
              semesterId: currentSemester as string,
              gradeId: selectedGradesId,
              gradeName: selectedGradesName,
              examType: selectedTestData?.[0],
              examId: selectedTestData?.[1] || '',
              examTime: selectedTestData?.[2] || '',
              startDate: startDate,
              endDate: endDate,
              teacherType: teacherType
            }}
            selectedSubjects={selectedSubjects}
            selectedGradesName={selectedGradesName}
            selectedGradesId={selectedGradesId}
            isAuthority={isAuthority}
          />
        </Box>
      </Box>

      <Box display="flex" w="100%" pb="12px">
        {/* 各名次段人数对比*/}
        <RankDivision
          ref={rankDivisionRef}
          title="各名次段人数对比"
          data={rankDivisionData}
          boardPaneRef={boardPaneRef}
          chartSubjectsOptions={chartSubjectsOptions}
          selectedTestData={selectedTestData}
          selectedSubjects={selectedSubjects}
          selectedGradesName={selectedGradesName}
          selectedGradesId={selectedGradesId}
          examSummary={examSummary}
          selectedYear={selectedYear}
          selectedTerm={selectedTerm}
          teacherType={teacherType}
          isAuthority={isAuthority}
          chartOptions={chartOptions}
          startDate={startDate}
          endDate={endDate}
        />

        {/* 学业等级分布start */}
        <AcademicLevel
          ref={academicLevelRef}
          title="学业等级分布"
          data={levelDivisionData}
          selectedYear={selectedYear}
          selectedTerm={selectedTerm}
          teacherType={teacherType}
          selectedGradesId={selectedGradesId}
          selectedGradesName={selectedGradesName}
          selectedTestData={selectedTestData}
          chartSubjectsOptions={chartSubjectsOptions}
          boardPaneRef={boardPaneRef}
          selectedSubjects={selectedSubjects}
          examSummary={examSummary}
          chartOptions={chartOptions}
          isAuthority={isAuthority}
        />
        {/* 学业等级分布end */}
      </Box>
      {/* 薄弱学科分析start */}

      <Box display="flex" w="100%" pb="12px">
        <WeakSubject
          title="薄弱学科分析"
          ref={weakSubjectRef}
          chartSubjectsOptions={chartSubjectsOptions}
          selectedYear={selectedYear}
          selectedTerm={selectedTerm}
          selectedGradesName={selectedGradesName}
          examId={selectedTestData?.[1] || ''}
          selectedGradesId={Number(selectedGradesId)}
          currentSemester={currentSemester as string}
          teacherType={teacherType}
          examType={selectedTestData?.[0]}
          selectedSubjects={selectedSubjects}
          isAuthority={isAuthority}
          chartOptions={chartOptions}
        />
        <ProblemsAndMeasures
          ref={problemsAndMeasuresRef}
          examSummary={examSummary}
          examId={selectedTestData?.[1] || ''}
          selectedSubjects={selectedSubjects}
          selectedTestData={selectedTestData}
          selectedGradesName={selectedGradesName}
          selectedGradesId={selectedGradesId}
          selectedYear={selectedYear}
          selectedTerm={selectedTerm}
          teacherType={teacherType}
          startDate={startDate}
          endDate={endDate}
        />
      </Box>

      <DataSummaryModal
        ref={dataSummaryModalRef}
        isAuthority={isAuthority}
        chartOptions={chartOptions}
        subjectOptions={chartSubjectsOptions}
        isModalVisible={isModalVisible}
        setIsModalVisible={setIsModalVisible}
        selectedYear={selectedYear}
        selectedTerm={selectedTerm}
        startDate={startDate}
        endDate={endDate}
        selectedSubjects={selectedSubjects}
        selectedTestData={selectedTestData}
        selectedGradesName={selectedGradesName}
        selectedGradesId={selectedGradesId}
        onClose={() => {
          if (boardPaneRef1.current?.refresh) {
            boardPaneRef1.current.refresh();
          }
        }}
      />
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default QualityAnalysis;
