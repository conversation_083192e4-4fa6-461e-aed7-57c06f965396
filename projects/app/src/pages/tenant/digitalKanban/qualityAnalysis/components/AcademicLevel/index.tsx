import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react';
import { LevelDivisionType } from '@/types/api/tenant/digitalKanban/qualityAnalysis';
import { levelDivision } from '@/api/tenant/digitalKanban/qualityAnalysis';
import { Box } from '@chakra-ui/react';
import { Select, SelectProps } from 'antd';
import EChartsReact from 'echarts-for-react';
import BoardPane from '@/components/BoardPane';
import AcademicLevelModal from '@/pages/tenant/digitalKanban/qualityAnalysis/components/AcademicLevelModal';
import NoDataComponent from '../NoDataProps';
import { useQuery } from '@tanstack/react-query';
interface AcademicLevelProps {
  selectedYear: string;
  selectedTerm: number;
  teacherType: number;
  selectedGradesId: string;
  selectedGradesName: string;
  selectedTestData: any[];
  chartSubjectsOptions: any[];
  boardPaneRef: React.RefObject<any>;
  selectedSubjects: string[];
  examSummary: any;
  chartOptions: any;
  isAuthority: boolean;
  data: LevelDivisionType;
  title: string;
}
export interface AcademicLevelRef {
  getConclusion: () => string;
}

function AcademicLevel(props: AcademicLevelProps, ref: React.ForwardedRef<AcademicLevelRef>) {
  const {
    selectedYear,
    selectedTerm,
    teacherType,
    selectedGradesId,
    selectedGradesName,
    selectedTestData,
    chartSubjectsOptions,
    boardPaneRef,
    selectedSubjects,
    examSummary,
    chartOptions,
    isAuthority,
    data
  } = props;

  const [AcademicLevelEchartsOptions, setAcademicLevelEchartsOptions] = useState<any>({
    legend: {
      selectedMode: true,
      bottom: 0,
      icon: 'circle'
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';
        params.forEach(function (item: any) {
          result += item.marker + ' ' + item.seriesName + ': ' + item.data + '' + '<br/>';
        });
        return result;
      },
      textStyle: {
        align: 'left'
      }
    },
    grid: {
      top: '10%',
      left: '0%',
      right: '0%',
      bottom: '10%',
      containLabel: true
    },
    yAxis: {
      type: 'value',
      name: '班级人数占比 %',
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      },
      max: 100
    },
    xAxis: {
      type: 'category',
      data: ['全部班级', '七1班', '七2班', '七3班', '七4班', '七5班', '七6班'],
      axisLabel: {
        interval: 0
      }
    },
    label: {
      show: true,
      position: 'left',
      distance: 0,
      color: '#666',
      fontSize: 12
      // 数据处理
      // formatter: (params: any) => Math.round(params.value * 1000) / 1000
    },
    series: [
      {
        name: 'A等',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#175DFF'
        }
      },
      {
        name: 'B等',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#14C9C9'
        }
      },
      {
        name: 'C等',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#34A7FA'
        }
      },
      {
        name: 'D等',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#F7BA1E'
        },
        barWidth: '50%'
      },
      {
        name: 'E等',
        type: 'bar',
        stack: 'total',
        itemStyle: {
          color: '#FF6678'
        }
      }
    ]
  });
  const AcademicLevelEchartsRef = useRef<any>();
  const [isAcademicLevelModal, setIsAcademicLevelModal] = useState<boolean>(false);
  const academicLevelModalRef = useRef<any>();
  const [selectAcademicLevel, setSelectAcademicLevel] = useState<SelectProps['value']>(['全部']);
  const [levelDivisionData, setLevelDivision] = useState<LevelDivisionType>([]);

  const [selectAcademicLevelOptions, setSelectAcademicLevelOptions] = useState<
    SelectProps['options']
  >([]);

  useEffect(() => {
    setSelectAcademicLevelOptions(chartSubjectsOptions);
  }, [chartSubjectsOptions]);

  function handleSelectAcademicLevel(value: string) {
    setSelectAcademicLevel(value);
    fetchLevelDivisionData();
  }

  const setAcademicLevelEcharts = useCallback((response: LevelDivisionType) => {
    const levels = ['A等', 'B等', 'C等', 'D等', 'E等'];
    const series = levels.map((level) => ({
      name: level,
      type: 'bar',
      stack: 'total',
      barWidth: '23%',
      data: response.map((item) => {
        const ratio = item.levelDivisionRatios.find((ratio) => ratio.level === level)?.ratio;
        return ratio ?? 0;
      })
    }));
    const xData = response.map((item) => item.className);

    setAcademicLevelEchartsOptions((prevOptions: any) => ({
      ...prevOptions,
      xAxis: {
        data: xData
      },
      series: series
    }));
  }, []);
  // state 用于标识是否已获取到有效的初始化数据
  const [hasFetched, setHasFetched] = useState(false);
  // 仅在组件第一次挂载时，使用父组件初始 props 保存到 ref 中
  const initialParams = useRef({
    year: selectedYear,
    term: selectedTerm,
    teacherType: teacherType,
    gradeId: selectedGradesId,
    gradeName: selectedGradesName,
    subjectNames: selectedSubjects.length > 0 ? [selectedSubjects[0]] : [],
    examType: selectedTestData?.[0] || '',
    examId: selectedTestData?.[1]
  });
  // 当父组件传入 props 满足条件时，更新 initialParams，并标记 hasFetched 为 true（仅一次）
  useEffect(() => {
    if (
      !hasFetched &&
      selectedYear !== '' &&
      selectedGradesName !== '' &&
      teacherType !== undefined &&
      teacherType !== null &&
      selectedTestData?.[0] !== '' &&
      selectedTestData?.[1] !== '' &&
      selectedSubjects.length > 0
    ) {
      initialParams.current = {
        year: selectedYear,
        term: selectedTerm,
        teacherType: teacherType,
        gradeId: selectedGradesId,
        gradeName: selectedGradesName,
        subjectNames: [selectedSubjects[0]],
        examType: selectedTestData[0],
        examId: selectedTestData[1]
      };
      setHasFetched(true);
    }
  }, [
    hasFetched,
    selectedYear,
    selectedTerm,
    teacherType,
    selectedGradesId,
    selectedGradesName,
    selectedTestData,
    selectedSubjects
  ]);

  const { data: onceLevelDivision } = useQuery(
    ['onceLevelDivision'],
    () => {
      levelDivision({
        year: selectedYear,
        term: selectedTerm,
        teacherType: teacherType,
        gradeId: Number(selectedGradesId),
        gradeName: selectedGradesName,
        subjectNames: [selectAcademicLevel],
        examType: selectedTestData?.[0],
        examId: selectedTestData?.[1]
      })
        .then((response) => {
          setAcademicLevelEcharts(response);
          setLevelDivision(response);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    {
      staleTime: Infinity,
      refetchOnWindowFocus: false,
      enabled: hasFetched
    }
  );

  const fetchLevelDivisionData = useCallback(() => {
    levelDivision({
      year: selectedYear,
      term: selectedTerm,
      teacherType: teacherType,
      gradeId: Number(selectedGradesId),
      gradeName: selectedGradesName,
      subjectNames: [selectAcademicLevel],
      examType: selectedTestData?.[0],
      examId: selectedTestData?.[1]
    })
      .then((response) => {
        setAcademicLevelEcharts(response);
        setLevelDivision(response);
      })
      .catch((err) => {
        console.log(err);
      });
  }, [
    selectedYear,
    selectedTerm,
    teacherType,
    selectedGradesId,
    selectedGradesName,
    selectAcademicLevel,
    selectedTestData,
    setAcademicLevelEcharts
  ]);

  // 筛选多选科目之后默认渲染第一个科目列表数据
  useEffect(() => {
    if (data.length > 0) {
      setAcademicLevelEcharts(data);
    }
  }, [data, setAcademicLevelEcharts]);

  function handleAcademicLevelExport() {
    if (!AcademicLevelEchartsRef.current?.getEchartsInstance()) {
      return;
    }

    const chartInstance = AcademicLevelEchartsRef.current?.getEchartsInstance();

    // 显示加载指示器
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    // 设置 dataZoom 以显示完整图表
    chartInstance.setOption({
      tooltip: {
        show: false
      },
      xAxis: {
        axisLabel: {
          rotate: 30 // 增加旋转角度以减少重叠
        }
      }
    });

    // 等待图表更新完成后再导出
    setTimeout(() => {
      // 隐藏加载指示器
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '学业等级分布.png';
      link.click();
      link.remove();
      // 还原初始配置
      chartInstance.setOption({ ...AcademicLevelEchartsOptions });
    }, 1800); // 延迟以确保图表更新完成
  }

  useEffect(() => {
    // 如果是多选则默认选择第一个
    if (selectedSubjects.length >= 1) {
      setSelectAcademicLevel(selectedSubjects[0]);
    }
    // 如果顶部筛选科目为空则清空选择
    if (selectedSubjects.length === 0) {
      setSelectAcademicLevel([]);
    }
  }, [selectedSubjects]);

  const boardPaneParams = {
    academic_year: selectedYear,
    semester: selectedTerm === 1 ? '第一学期' : '第二学期',
    grade: selectedGradesName,
    test_type: selectedTestData?.[0] || '',
    test_name: '期末质量分析',
    subject: selectAcademicLevel[0] === '全部' ? '全部科目' : selectAcademicLevel[0],
    // subject: '全部科目',
    data_block_name: '学业等级分布',
    data_render_type: '柱状图',
    data: levelDivisionDataConversion(levelDivisionData),
    analysis_requirements: '请你对本次测试的各班级学业等级分布情况进行分析'
  };

  function levelDivisionDataConversion(data: typeof levelDivisionData) {
    const result = data.map((item) => {
      return {
        class_name: item.className,
        A_level_rate: item.levelDivisionRatios[0].ratio,
        B_level_rate: item.levelDivisionRatios[1].ratio,
        C_level_rate: item.levelDivisionRatios[2].ratio,
        D_level_rate: item.levelDivisionRatios[3].ratio,
        E_level_rate: item.levelDivisionRatios[4].ratio
      };
    });
    return result;
  }

  return (
    <>
      <Box
        flex="1"
        backgroundColor="#fff"
        padding="16px 16px 20px 16px"
        borderRadius="12px"
        transition="box-shadow 0.3s ease"
        position="relative"
      >
        <Box display={'flex'} alignItems={'center'}>
          <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
            学业等级分布
          </Box>
          <Select
            style={{
              maxWidth: '300px',
              minWidth: '120px',
              marginRight: '10px',
              marginLeft: 'auto'
            }}
            placeholder="选择学科"
            options={selectAcademicLevelOptions}
            value={selectAcademicLevel}
            onChange={handleSelectAcademicLevel}
          />
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="85px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block', marginRight: '10px' }}
            onClick={() => {
              setIsAcademicLevelModal(true);
            }}
            color={'#636C7B'}
          >
            设置分数段
          </Box>
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block' }}
            onClick={handleAcademicLevelExport}
            color={'#636C7B'}
          >
            导出
          </Box>
        </Box>

        {Object.keys(chartOptions).length > 0 ? (
          <>
            <EChartsReact
              style={{ marginBottom: '14px' }}
              ref={AcademicLevelEchartsRef}
              option={AcademicLevelEchartsOptions}
            />
            <Box position={'relative'}>
              <BoardPane
                ref={boardPaneRef}
                location={8}
                height="100px"
                examId={selectedTestData?.[1] || ''}
                selectedSubjects={[selectAcademicLevel]}
                selectedTestData={selectedTestData}
                selectedGradesName={selectedGradesName}
                selectedGradesId={selectedGradesId}
                editable={true}
                params={boardPaneParams}
                fullScreenHeight="165px"
                fullScreenoffsetX="40"
                title="结论"
                backgroundColor="#f9f9f9"
                titleFontSize="14px"
                padding="10px"
                showFullScreenButtons
                option={
                  '数据表明，八年级3班和八年级4班在优秀率（分别为80%和80.43%）和平均分（分别为386.28和386.83）方面表现最为突出，而八年级1班、八年级6班和八年级7班的合格率均为0%，且平均分（分别为347.15、344.32和332.01）低于年级平均值线356.86，需重点关注和改进。'
                }
              />
            </Box>
          </>
        ) : (
          <NoDataComponent type={isAuthority ? 'noPermission' : 'noData'} />
        )}
      </Box>

      <AcademicLevelModal
        isModalVisible={isAcademicLevelModal}
        setModalVisible={setIsAcademicLevelModal}
        fetchLevelDivisionData={fetchLevelDivisionData}
      />
    </>
  );
}
export default forwardRef(AcademicLevel);
