import React from 'react';
import { Box, Text } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';

interface NoDataProps {
  type: 'noPermission' | 'noData' | 'comingSoon';
  height?: string;
}

const NoDataComponent: React.FC<NoDataProps & { height?: string }> = ({
  type,
  height = '380px'
}) => {
  const isSmall = height === '168px';
  return (
    <Box
      textAlign="center"
      padding="20px"
      h={height}
      border="1px solid #F4F4F4"
      marginTop="10px"
      borderRadius="8px"
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
    >
      {type === 'noPermission' ? (
        <>
          <SvgIcon
            name="chatPower"
            w={isSmall ? '120px' : '177px'}
            h={isSmall ? '120px' : '177px'}
          />
          <Text fontSize={isSmall ? '16px' : '20px'} fontWeight="bold" padding="7px 0">
            您无权限查看该板块
          </Text>
          <Text fontSize={isSmall ? '12px' : '16px'} color="#86909C">
            请与学校管理员联系，获取对应权限
          </Text>
        </>
      ) : type === 'noData' ? (
        <>
          <SvgIcon
            name="chatEmpty"
            w={isSmall ? '120px' : '177px'}
            h={isSmall ? '120px' : '177px'}
          />
          <Text fontSize={isSmall ? '16px' : '20px'} fontWeight="bold">
            暂无数据
          </Text>
          <Text fontSize={isSmall ? '12px' : '16px'} color="gray.500">
            因智学网无对应数据，暂无数据显示
          </Text>
        </>
      ) : (
        <>
          <SvgIcon
            name="stayTuned"
            w={isSmall ? '160px' : '177px'}
            h={isSmall ? '120px' : '177px'}
          />
          <Text fontSize={isSmall ? '16px' : '20px'} fontWeight="rgba(29, 33, 41, 0.44)">
            即将上线，敬请期待
          </Text>
        </>
      )}
    </Box>
  );
};

export default NoDataComponent;
