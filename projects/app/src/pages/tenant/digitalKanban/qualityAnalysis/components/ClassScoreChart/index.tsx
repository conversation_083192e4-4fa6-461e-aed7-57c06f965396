import { Box } from '@chakra-ui/react';
import { Select } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import * as echarts from 'echarts';
import EChartsReact from 'echarts-for-react';
import NoDataComponent from '../NoDataProps';
import { getGradeComparison, exportSubjectLevel } from '@/api/kanban';
import { GradeComparisonResponse, ClassGradeInfo } from '@/types/api/kanban';
import { initClassPerformanceContrast } from '../../index';
import BoardPane from '@/components/BoardPane';

interface GradeComparisonData extends ClassGradeInfo { }

interface ClassScoreChartProps {
  option: any;
  chartSubjectsOptions: any;
  selectedChartSubjects: any;
  params: any;
  selectedSubjects: any;
  selectedGradesName: string;
  selectedGradesId: string;
  isAuthority: boolean;
  title: string;
}

export interface ClassScoreChartRef {
  fetchGradeComparison: () => void;
}

function ClassScoreChart(props: ClassScoreChartProps, ref: React.ForwardedRef<ClassScoreChartRef>) {
  const { params, isAuthority, selectedSubjects } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const [option, setOption] = useState<any>({});
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string>('全部');
  const [chartData, setChartData] = useState<GradeComparisonData[]>([]);
  const [chartSubjectsOptions, setChartSubjectsOptions] = useState<any[]>([]);
  const [isAuthoritys, setIsAuthoritys] = useState(false);
  const [value, setValue] = useState<string>('');
  const echartsRef = useRef<EChartsReact>(null);
  const boardPaneRef = useRef<any>(null);

  useEffect(() => {
    console.log('props.chartSubjectsOptions', props.chartSubjectsOptions);
    setChartSubjectsOptions(props.chartSubjectsOptions);
  }, [props.chartSubjectsOptions]);

  const fetchGradeComparison = async () => {
    console.log('selectedChartSubjects444', selectedChartSubjects);

    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects;

    if (hasAllParams) {
      getGradeComparison({
        ...params,
        subjectNames: [selectedChartSubjects]
      }).then((res: GradeComparisonResponse) => {
        if (res?.classGradeInfo && Array.isArray(res.classGradeInfo)) {
          // 更新scoreData对象
          const newScoreData: any = {};
          res.classGradeInfo.forEach((item) => {
            newScoreData[item.className] = {
              A: item.excellentRate,
              B: item.qualifiedRate,
              C: item.avgScore
            };
          });
          console.log('newScoreData', newScoreData);
          // 创建新的图表配置
          const newOption = initClassPerformanceContrast({
            scoreData: newScoreData,
            classNames: res.classGradeInfo.map((item) => item.className),
            excellentRates: res.classGradeInfo.map((item) => item.excellentRate),
            qualifiedRates: res.classGradeInfo.map((item) => item.qualifiedRate),
            avgScores: res.classGradeInfo.map((item) => item.avgScore),
            gradeAverage: res.gradeAverage || undefined,
            targetScore: res.targetScore || undefined
          });

          setOption(newOption);
          setChartData(res.classGradeInfo);
        }
      });
    } else {
      setOption({});
      setChartData([]);
    }
  };

  useEffect(() => {
    fetchGradeComparison();
  }, [value]);

  useImperativeHandle(ref, () => ({
    fetchGradeComparison
  }));

  useEffect(() => {
    if (selectedSubjects) {
      setSelectedChartSubjects(selectedSubjects[0]);
    }
  }, [selectedSubjects]);

  const handleExport = () => {
    if (!echartsRef.current?.getEchartsInstance()) {
      return;
    }

    const chartInstance = echartsRef.current?.getEchartsInstance();

    // 显示加载指示器
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    setTimeout(() => {
      chartInstance.hideLoading();
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '班级成绩对比.png';
      link.click();
    }, 1000);
  };

  // useEffect(() => {
  //   setOption({});
  //   setChartData([]);
  // }, [params.gradeName]);

  // useEffect(() => {
  //   console.log('params.semester', params.currentSemester);
  // }, [params.currentSemester]);

  return (
    <Box>
      <Box display="flex" justifyContent="space-between">
        <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
          班级成绩对比
        </Box>
        <Box display="flex">
          <Select
            options={chartSubjectsOptions}
            value={selectedChartSubjects}
            onChange={(value) => {
              setSelectedChartSubjects(value);
              setValue(value);
            }}
            placeholder="选择学科"
            style={{ minWidth: '200px' }}
          />
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block', marginLeft: '10px' }}
            onClick={handleExport}
            color={'#636C7B'}
          >
            导出
          </Box>
        </Box>
      </Box>
      {Object.keys(option).length > 0 ? (
        <EChartsReact
          ref={echartsRef}
          option={option}
          style={{
            borderRadius: '12px',
            height: '310px'
          }}
        />
      ) : (
        <NoDataComponent type={isAuthoritys ? 'noPermission' : 'noData'} />
      )}
      {Object.keys(option).length > 0 && (
        <Box position={'relative'}>
          <BoardPane
            ref={boardPaneRef}
            location={3}
            height="100px"
            examId={params.examId}
            selectedTestData={[selectedChartSubjects]}
            selectedSubjects={selectedChartSubjects !== '' ? [selectedChartSubjects] : []}
            selectedGradesName={params.gradeName}
            selectedGradesId={params.gradeId}
            params={{
              academic_year: params.year,
              semester: params.term === 1 ? '第一学期' : '第二学期',
              grade: params.gradeName,
              test_type: params.examType,
              test_name: params.examName,
              subject: selectedChartSubjects === '全部' ? '全部' : selectedChartSubjects,
              data_block_name: '班级成绩对比',
              data_render_type: '柱状图',
              data: chartData.map((item) => ({
                class_name: item.className,
                excellence_rate: item.excellentRate,
                passing_rate: item.qualifiedRate,
                average_score: item.avgScore
              })),
              analysis_requirements: '请你对本次测试的各班级的班级成绩对比进行分析'
            }}
            editable={true}
            fullScreenHeight="165px"
            fullScreenoffsetX="40"
            title="结论"
            backgroundColor="#f9f9f9"
            titleFontSize="14px"
            padding="10px"
            showFullScreenButtons
          />
        </Box>
      )}
    </Box>
  );
}

export default forwardRef<ClassScoreChartRef, ClassScoreChartProps>(ClassScoreChart);
