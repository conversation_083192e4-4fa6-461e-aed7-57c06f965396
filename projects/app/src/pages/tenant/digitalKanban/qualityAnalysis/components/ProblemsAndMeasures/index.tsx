import { getConclusion } from '@/api/kanban';
import BoardPaneAll from '@/components/BoardPaneAll';

import { Box, Tab, TabList, TabPanel, TabPanels, Tabs } from '@chakra-ui/react';
import { useRef, useState, useImperativeHandle, forwardRef } from 'react';

interface ProblemsAndMeasuresProps {
  [keyof: string]: any;
}

export interface ProblemsAndMeasuresRef {
  getProblemsAndMeasuresConclusions: () => Promise<{
    problems: {
      teachingRoutine: string;
      collectivePreparation: string;
      layeredTeaching: string;
      other: string;
    };
    measures: {
      subjectTeacher: string;
      preparationGroup: string;
      classTeacher: string;
      other: string;
    };
  }>;
}

const ProblemsAndMeasures = forwardRef<ProblemsAndMeasuresRef, ProblemsAndMeasuresProps>(
  function ProblemsAndMeasures(props, ref) {
    const {
      examSummary,
      examId,
      selectedSubjects,
      selectedTestData,
      selectedGradesName,
      selectedGradesId,
      selectedYear,
      selectedTerm,
      teacherType,
      startDate,
      endDate
    } = props;

    // 存在问题 AI生成结论参数
    const [problemsAIParams, setProblemsAIParams] = useState({
      academic_year: selectedYear,
      // "semester": "第一学期",
      grade: selectedGradesName,
      test_type: selectedTestData?.[0] || '',
      test_name: '期末质量分析',
      subject: '全部学科',
      data_block_name: '存在问题',
      data_render_type: '文字',
      data: [], // 从接口拿数据
      analysis_requirements:
        '请你综合对本次测试的所有数据的分析结论进行分析，分析该年级在本次测试中存在的问题'
    });

    // 获取存在问题和改进措施的所有结论
    const getProblemsAndMeasuresConclusions = async () => {
      try {
        const conclusionParams = {
          examId: selectedTestData?.[1] || '',
          gradeId: selectedGradesId || '',
          gradeName: selectedGradesName || '',
          subjectNames: selectedSubjects || []
        };

        const conclusions = await getConclusion(conclusionParams);

        // 存在问题的结论 (location 9-12)
        const teachingRoutine =
          conclusions.find((item: any) => item.location === 9)?.conclusion || '';
        const collectivePreparation =
          conclusions.find((item: any) => item.location === 10)?.conclusion || '';
        const layeredTeaching =
          conclusions.find((item: any) => item.location === 11)?.conclusion || '';
        const problemsOther =
          conclusions.find((item: any) => item.location === 12)?.conclusion || '';

        // 改进措施的结论 (location 13-16)
        const subjectTeacher =
          conclusions.find((item: any) => item.location === 13)?.conclusion || '';
        const preparationGroup =
          conclusions.find((item: any) => item.location === 14)?.conclusion || '';
        const classTeacher =
          conclusions.find((item: any) => item.location === 15)?.conclusion || '';
        const measuresOther =
          conclusions.find((item: any) => item.location === 16)?.conclusion || '';

        return {
          problems: {
            teachingRoutine,
            collectivePreparation,
            layeredTeaching,
            other: problemsOther
          },
          measures: {
            subjectTeacher,
            preparationGroup,
            classTeacher,
            other: measuresOther
          }
        };
      } catch (error) {
        console.error('获取存在问题和改进措施结论失败:', error);
        return {
          problems: {
            teachingRoutine: '',
            collectivePreparation: '',
            layeredTeaching: '',
            other: ''
          },
          measures: {
            subjectTeacher: '',
            preparationGroup: '',
            classTeacher: '',
            other: ''
          }
        };
      }
    };

    useImperativeHandle(ref, () => ({
      getProblemsAndMeasuresConclusions
    }));

    function CommonTab({ children }: any) {
      return (
        <Tab
          _selected={{ color: '#175DFF', background: '#e8f0fe', border: '1px solid #175DFF' }}
          color={'#636C7B'}
          bg={'#f4f4f4'}
          border={'none'}
          borderRadius={'6px'}
          marginRight={'12px'}
          _hover={{ bg: '#EAEAEA' }}
          width={'81px'}
          height={'28px'}
          fontSize={'12px'}
          padding={0}
        >
          {children}
        </Tab>
      );
    }

    return (
      <>
        <Box flex="1">
          <Box
            flex="1"
            backgroundColor="#fff"
            borderRadius="12px"
            transition="box-shadow 0.3s ease"
            position="relative"
            height={'220px'}
            padding={'16px 16px 12px 16px'}
          >
            <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
              存在问题
            </Box>
            <Tabs
              variant="unstyled"
              margin={'12px 0'}
              backgroundColor={'#0f0f0'}
              borderRadius={'6px'}
              isLazy
              lazyBehavior="unmount"
            >
              <TabList>
                <CommonTab>教学常规</CommonTab>
                <CommonTab>集体备课</CommonTab>
                <CommonTab>分层教学</CommonTab>
                <CommonTab>其他</CommonTab>
              </TabList>
              <TabPanels>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    location={9}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    location={10}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    location={11}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    location={12}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Box>

          <Box
            flex="1"
            backgroundColor="#fff"
            borderRadius="12px"
            transition="box-shadow 0.3s ease"
            position="relative"
            height={'220px'}
            marginTop={'12px'}
            padding={'16px 16px 12px 16px'}
          >
            <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
              改进措施
            </Box>

            <Tabs
              variant="unstyled"
              margin={'12px 0'}
              backgroundColor={'#0f0f0'}
              borderRadius={'6px'}
              isLazy
              lazyBehavior="unmount"
            >
              <TabList>
                <CommonTab>科任</CommonTab>
                <CommonTab>备课组</CommonTab>
                <CommonTab>班主任</CommonTab>
                <CommonTab>其他</CommonTab>
              </TabList>
              <TabPanels>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    // ref={BoardPaneAllRef}
                    location={13}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    // ref={BoardPaneAllRef}
                    location={14}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    // ref={BoardPaneAllRef}
                    location={15}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
                <TabPanel padding={0} marginTop={'12px'}>
                  <BoardPaneAll
                    // ref={BoardPaneAllRef}
                    location={16}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectedSubjects}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    selectedTerm={selectedTerm}
                    editable={true}
                    fullScreenHeight="165px"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                    buttonVisibility
                    selectedYear={selectedYear}
                  />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Box>
        </Box>
      </>
    );
  }
);

export default ProblemsAndMeasures;
