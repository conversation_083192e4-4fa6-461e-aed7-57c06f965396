import { Box } from '@chakra-ui/react';
import { Select, SelectProps, Space, Table, TableProps, Tag } from 'antd';
import React, {
  useEffect,
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
  useCallback
} from 'react';
import WeakSubectSetOptionsModal from '@/pages/tenant/digitalKanban/qualityAnalysis/components/WeakSubectSetOptionsModal';
import styled from '@emotion/styled';
import { getClassList, weakSubjectAnalysis } from '@/api/tenant/digitalKanban/qualityAnalysis';
import html2canvas from 'html2canvas';
import NoDataComponent from '../NoDataProps';
import { useQuery } from '@tanstack/react-query';
// 添加样式
const WeakSubjectTable = styled.div`
  .weak-subject-table {
    .ant-table-tbody > tr > td {
      padding: 9px 16px;
      height: 40px;
    }
    .ant-table-thead > tr > th {
      padding: 8px 16px;
      height: 22px;
      background: #f0f1f3;
    }
    .ant-table-cell {
      width: 85px;
      height: 40px;
      padding: 0;
    }
    .ant-table-cell-scrollbar {
      display: none;
    }
    /* 自定义滚动条样式，加粗滚动条 */
    * {
      scrollbar-width: auto !important;
      scrollbar-color: auto !important;
    }
    *::-webkit-scrollbar {
      width: 10px !important;
      height: 10px !important;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
`;

interface WeakSubjectSetOptionsProps {
  chartSubjectsOptions: Array<{ value: string; label: string }>;
  examId: string;
  selectedYear: string;
  selectedTerm: number;
  selectedGradesId: number;
  currentSemester: string | undefined;
  teacherType: number;
  selectedGradesName: string;
  examType: string;
  selectedSubjects: string[];
  fetchWeakSubjectAnalysis?: () => void;
  isAuthority: boolean;
  chartOptions: any;
  title: string;
}
export interface WeakSubjectRef {
  fetchWeakSubjectAnalysis: () => void;
}

const WeakSubjectSetOptions = forwardRef<WeakSubjectRef, WeakSubjectSetOptionsProps>(
  function WeakSubjectSetOptions(props, ref) {
    const {
      chartSubjectsOptions,
      examId,
      selectedYear,
      selectedTerm,
      selectedGradesId,
      currentSemester,
      teacherType,
      selectedGradesName,
      examType,
      selectedSubjects,
      isAuthority,
      chartOptions
    } = props;

    useImperativeHandle(ref, () => ({
      fetchWeakSubjectAnalysis() {
        fetchWeakSubjectAnalysis();
      }
    }));

    const [weakSubjectSetOptionsModal, setWeakSubjectSetOptionsModal] = useState<boolean>(false);

    // 班级列表
    const [classList, setClassList] = useState<Array<{ value: string; label: string }>>([]);
    // 选中的班级列表
    const [selectedClassList, setSelectedClassList] = useState<SelectProps['value']>('全部班级');
    // 选中的学科
    const [subjectName, setSubjectName] = useState<SelectProps['value']>(['全部']);
    // 学科列表
    const [subjectList, setSubjectList] = useState<Array<{ value: string; label: string }>>([]);
    // 当前看板学生总数
    const [totalStudentCount, setTotalStudentCount] = useState<number>(0);
    const [multiple, setMultiple] = useState(2);
    const [difference, setDifference] = useState(10);

    const tableRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      setSubjectList(chartSubjectsOptions);
    }, [chartSubjectsOptions]);

    useEffect(() => {
      const fetchGetClassList = () => {
        getClassList(examId)
          .then((res) => {
            const classList = res.map((item: string) => ({
              value: item,
              label: item
            }));
            setClassList(classList);
          })
          .catch((err) => {
            console.log(err);
          });
      };

      if (examId) {
        fetchGetClassList();
      }
    }, [examId]);

    const [data, setData] = useState([]);

    const columns: TableProps<any>['columns'] = [
      {
        title: '班级',
        dataIndex: 'className',
        key: 'className',
        align: 'center'
      },
      {
        title: '学生',
        dataIndex: 'userName',
        key: 'userName',
        align: 'center'
      },
      {
        title: '薄弱学科',
        dataIndex: 'subjectName',
        key: 'subjectName',
        align: 'center'
      },
      {
        title: '成绩',
        dataIndex: 'score',
        key: 'score',
        align: 'center'
      },
      {
        title: '任课教师',
        dataIndex: 'teacherName',
        key: 'teacherName',
        align: 'center'
      }
    ];

    function handleSelectClassListChange(className: string) {
      setSelectedClassList(className);
      fetchWeakSubjectAnalysis(className);
    }

    function handleChangeSubjectList(subjectNames: string) {
      setSubjectName(subjectNames);
      fetchWeakSubjectAnalysis(undefined, subjectNames);
    }

    // 仅在组件第一次挂载时，使用父组件初始 props 保存到 ref 中
    const initialParams = useRef({
      year: selectedYear,
      term: selectedTerm,
      semesterId: Number(currentSemester),
      teacherType: teacherType,
      gradeId: selectedGradesId,
      gradeName: selectedGradesName,
      examType: examType,
      examId: examId,
      subjectNames: [subjectName],
      className: selectedClassList === '全部班级' ? '全部' : selectedClassList,
      multiple: Number(multiple), // 倍数
      difference: Number(difference) // 差数
    });

    // state 用于标识是否已获取到有效的初始化数据
    const [hasFetched, setHasFetched] = useState(false);

    // 当父组件传入 props 满足条件时，更新 initialParams，并标记 hasFetched 为 true（仅一次）
    useEffect(() => {
      if (
        !hasFetched &&
        selectedYear !== '' &&
        selectedTerm &&
        Number(currentSemester) &&
        teacherType &&
        selectedGradesId &&
        selectedGradesName &&
        examType &&
        examId &&
        selectedSubjects.length > 0
      ) {
        initialParams.current = {
          year: selectedYear,
          term: selectedTerm,
          semesterId: Number(currentSemester),
          teacherType: teacherType,
          gradeId: selectedGradesId,
          gradeName: selectedGradesName,
          examType: examType,
          examId: examId,
          subjectNames: [subjectName],
          className: selectedClassList === '全部班级' ? '全部' : selectedClassList,
          multiple: Number(multiple), // 倍数
          difference: Number(difference) // 差数
        };
        setHasFetched(true);
      }
    }, [
      hasFetched,
      selectedYear,
      selectedTerm,
      teacherType,
      selectedGradesId,
      selectedGradesName,
      currentSemester,
      examType,
      examId,
      selectedSubjects,
      subjectName,
      selectedClassList,
      multiple,
      difference
    ]);

    const { data: onceWeakSubjectAnalysis } = useQuery(
      ['weakSubjectAnalysis'],
      () => {
        weakSubjectAnalysis({
          year: selectedYear,
          term: selectedTerm,
          semesterId: Number(currentSemester),
          teacherType: teacherType,
          gradeId: selectedGradesId,
          gradeName: selectedGradesName,
          examType: examType,
          examId: examId,
          subjectNames: [subjectName],
          className: selectedClassList === '全部班级' ? '全部' : selectedClassList,
          multiple: Number(multiple), // 倍数
          difference: Number(difference) // 差数
        }).then((res: any) => {
          setData(res);
          setTotalStudentCount(res.length);
        });
      },
      {
        staleTime: Infinity,
        refetchOnWindowFocus: false,
        enabled: hasFetched
      }
    );

    const fetchWeakSubjectAnalysis = useCallback(
      (className?: any, subjectNames?: any) => {
        weakSubjectAnalysis({
          year: selectedYear,
          term: selectedTerm,
          semesterId: Number(currentSemester),
          teacherType: teacherType,
          gradeId: selectedGradesId,
          gradeName: selectedGradesName,
          examType: examType,
          examId: examId,
          subjectNames: subjectNames ? [subjectNames] : [subjectName],
          className: className ?? (selectedClassList === '全部班级' ? '全部' : selectedClassList),
          multiple: Number(multiple), // 倍数
          difference: Number(difference) // 差数
        }).then((res: any) => {
          setData(res);
          setTotalStudentCount(res.length);
        });
      },
      [
        selectedYear,
        selectedTerm,
        currentSemester,
        teacherType,
        selectedGradesId,
        selectedGradesName,
        examType,
        examId,
        subjectName,
        selectedClassList,
        multiple,
        difference
      ]
    );

    const handleExport = async () => {
      if (tableRef.current) {
        try {
          // 临时保存原始样式
          const originalStyle = (tableRef.current.querySelector('.ant-table-body') as HTMLElement)
            ?.style.maxHeight;

          // 临时移除滚动限制
          if (tableRef.current.querySelector('.ant-table-body')) {
            (tableRef.current.querySelector('.ant-table-body') as HTMLElement).style.maxHeight =
              'none';
          }

          // 等待DOM更新
          await new Promise((resolve) => setTimeout(resolve, 100));

          const canvas = await html2canvas(tableRef.current, {
            backgroundColor: '#ffffff',
            scale: 1
          });

          const link = document.createElement('a');
          link.download = `薄弱学科分析-${selectedGradesName}.png`;
          link.href = canvas.toDataURL('image/png');
          link.click();

          // 还原原始样式
          if (tableRef.current.querySelector('.ant-table-body')) {
            (tableRef.current.querySelector('.ant-table-body') as HTMLElement).style.maxHeight =
              originalStyle;
          }
        } catch (error) {
          console.error('导出失败:', error);
        }
      }
    };

    useEffect(() => {
      // 如果是多选则默认选择第一个
      if (selectedSubjects.length >= 1) {
        setSubjectName(selectedSubjects[0]);
      }
      // 如果顶部筛选科目为空则清空选择
      if (selectedSubjects.length === 0) {
        setSubjectName([]);
      }
    }, [selectedSubjects]);

    return (
      <>
        <Box
          width="calc(38% - 12px)"
          marginRight="12px"
          backgroundColor="#fff"
          padding="16px 16px 20px 16px"
          borderRadius="12px"
          transition="box-shadow 0.3s ease"
          position="relative"
        >
          <Box display={'flex'}>
            <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
              薄弱学科分析
            </Box>
            <Select
              style={{
                maxWidth: '300px',
                minWidth: '120px',
                marginRight: '10px',
                marginLeft: 'auto'
              }}
              placeholder="全部班级"
              options={classList}
              value={selectedClassList}
              onChange={handleSelectClassListChange}
            />
            <Select
              style={{
                maxWidth: '300px',
                minWidth: '120px',
                marginRight: '10px'
              }}
              placeholder="选择学科"
              options={subjectList}
              value={subjectName}
              onChange={handleChangeSubjectList}
            />
            <Box
              as="button"
              borderRadius="4px"
              cursor="pointer"
              width="85px"
              height="28px"
              fontSize="14px"
              background="#f7f8fa"
              style={{ display: 'inline-block', marginRight: '10px', padding: '2px 8px' }}
              onClick={() => {
                setWeakSubjectSetOptionsModal(true);
              }}
              color={'#636C7B'}
            >
              设置规则
            </Box>
            <Box
              as="button"
              borderRadius="4px"
              cursor="pointer"
              width="46px"
              height="28px"
              fontSize="14px"
              background="#f7f8fa"
              style={{ display: 'inline-block', marginRight: '10px', padding: '2px 8px' }}
              onClick={handleExport}
              color={'#636C7B'}
            >
              导出
            </Box>
          </Box>

          <WeakSubjectTable ref={tableRef}>
            {Object.keys(chartOptions).length > 0 ? (
              <Table
                columns={columns}
                dataSource={data}
                pagination={false}
                scroll={{ y: 320 }}
                className="weak-subject-table"
                style={{
                  marginTop: '15px'
                }}
              />
            ) : (
              <NoDataComponent type={isAuthority ? 'noPermission' : 'noData'} />
            )}
          </WeakSubjectTable>
        </Box>

        <WeakSubectSetOptionsModal
          isModalVisible={weakSubjectSetOptionsModal}
          setModalVisible={setWeakSubjectSetOptionsModal}
          multiple={multiple}
          setMultiple={setMultiple}
          difference={difference}
          setDifference={setDifference}
          totalStudentCount={totalStudentCount}
        />
      </>
    );
  }
);

export default WeakSubjectSetOptions;
