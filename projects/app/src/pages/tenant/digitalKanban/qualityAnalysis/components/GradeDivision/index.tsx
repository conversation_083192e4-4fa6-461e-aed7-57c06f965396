import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { Box, Flex, Image, Text, forwardRef } from '@chakra-ui/react';
import {
  getGradeDivision,
  Section,
  GradeDivisionResponse,
  getGradeDivisionExport,
  getConclusion
} from '@/api/kanban';
import { Input as AntdInput, Select, TreeSelect, message } from 'antd';
import EChartsReact from 'echarts-for-react';
import NoDataComponent from '../NoDataProps';
import { RecentReportResponse } from '@/types/api/kanban';
import BoardPane from '@/components/BoardPane';
import GradeDivisionDialog from './GradeDivisonDialog';
import { generateScoreLabel } from './GradeDivisonDialog';
import { uploadFile } from '@/api/file';

interface ThreeTestProps {
  option: any;
  chartSubjectsOptions: any[];
  selectedChartSubjects: string[];
  params: any;
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  isAuthority: boolean;
}
export interface GradeDivisionRef {
  getData: () => void;
  getGradeDivisionChartImage: () => Promise<string>;
  getGradeDivisionBoardPaneContent: () => Promise<string>;
  getGradeDivisionTableImage: () => Promise<string>;
  getGradeDivisionTableBoardPaneContent: () => Promise<string>;
}

const GradeDivision = (props: ThreeTestProps, ref: React.ForwardedRef<GradeDivisionRef>) => {
  const {
    chartSubjectsOptions,
    params,
    selectedSubjects,
    selectedGradesName,
    selectedGradesId,
    isAuthority,
    option
  } = props;

  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>(['']);
  const [testOptions, setTestOptions] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any>({});
  const [rawData, setRawData] = useState<GradeDivisionResponse[]>([]);
  const [selectedTestData, setselectedTestData] = useState<any[]>([]);
  const [segmentValue, setSegmentValue] = useState<string>('50'); // 默认为50分/段
  const MAX_SCORE = 100; // 假设总分为100，可根据实际情况调整
  const [showTable, setShowTable] = useState<boolean>(false);
  const echartsRef = useRef<any>(null);
  const boardPaneRef = useRef<any>(null);
  const prevParamsRef = useRef('');
  const [value, setValue] = useState<string>('');

  // 获取各个分数段的学生人数
  const getScoreData = (data: GradeDivisionResponse[], className: string) => {
    const classData = data.find((item) => item.className === className);
    if (!classData) return [];

    return classData.sectionList
      .sort((a, b) => a.sort - b.sort)
      .map((section) => section.studentNumber);
  };

  // 计算总人数的辅助函数
  const calculateTotalStudents = (data: GradeDivisionResponse[]) => {
    return data.reduce((total, classData) => {
      const classTotal = classData.sectionList.reduce(
        (sum, section) => sum + section.studentNumber,
        0
      );
      return total + classTotal;
    }, 0);
  };

  // 生成图表配置
  const generateChartOptions = (data: GradeDivisionResponse[]) => {
    if (!data?.[0]?.sectionList) return null;

    // 按sort降序排序并生成标签
    const scoreLabels = [...data[0].sectionList]
      .sort((a, b) => a.sort - b.sort)
      .map((section) => generateScoreLabel(section));

    const classNames = data.map((item) => item.className);
    const totalStudents = calculateTotalStudents(data);

    // 计算需要显示的数据点数量
    const totalPoints = scoreLabels.length;
    // 计算一屏能显示的最佳点数（这里设置为8个点）
    const pointsPerView = 8;
    // 计算end的百分比，确保每个点的间距一致
    const endPercentage = Math.min(100, (pointsPerView / totalPoints) * 100);

    const baseOptions = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(0, 0, 0, 0.05)'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#eee',
        borderWidth: 1,
        padding: [10, 15],
        textStyle: {
          color: '#666'
        },
        formatter: function (params: any) {
          if (!Array.isArray(params)) {
            params = [params];
          }
          const scoreRange = params[0].axisValue;
          // 获取当前鼠标悬停的数据点
          const currentPoint = params[0];
          // 计算当前区间的总人数和占比
          const currentValue = currentPoint.value;
          const percentage = ((currentValue / totalStudents) * 100).toFixed(2);

          return `
            <div style="font-weight: bold; margin-bottom: 8px;">${currentPoint.seriesName}</div>
            <div style="margin-bottom: 8px;">
              <span style="display: inline-block; min-width: 80px;">区间：</span>
              <span style="font-weight: 500;">${scoreRange}</span>
            </div>
            <div style="margin-bottom: 8px;">
              <span style="display: inline-block; min-width: 80px;">区间占比：</span>
              <span style="font-weight: 500;">${percentage}%</span>
            </div>
            <div>
              <span style="display: inline-block; min-width: 80px;">区间人数：</span>
              <span style="font-weight: 500;">${currentValue}人</span>
            </div>`;
        }
      },
      legend: {
        data: classNames,
        bottom: '3%',
        type: 'scroll'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '12%',
        top: '13%',
        containLabel: true
      },
      dataZoom: [
        {
          show: parseInt(segmentValue) < 30, // 只在分段值小于30时显示
          type: 'slider',
          realtime: true,
          start: 0,
          end: Math.min(100, (8 / Math.ceil(100 / parseInt(segmentValue))) * 100), // 动态计算显示比例
          height: 8,
          bottom: 5,
          handleSize: 0,
          handleStyle: {
            color: '#D9D9D9',
            borderColor: 'transparent'
          },
          textStyle: {
            color: '#000',
            fontSize: 11
          },
          borderColor: '#fff',
          showDetail: false,
          fillerColor: '#eee',
          backgroundColor: '#fff',
          showDataShadow: false,
          zoomLock: false
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: Math.min(100, (8 / Math.ceil(100 / parseInt(segmentValue))) * 100),
          zoomLock: false
        }
      ],
      xAxis: {
        type: 'category',
        data: scoreLabels,
        axisLabel: {
          interval: parseInt(segmentValue) >= 30 ? 0 : 'auto', // 分段值大于等于30时显示所有标签
          rotate: 45
        },
        axisTick: {
          show: false // 不显示刻度线
        },
        axisLine: {
          show: false // 不显示X轴线
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#E0E0E0'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '班级人数',
          position: 'left',
          nameTextStyle: {
            padding: [0, 0, 0, -30] // 调整名称位置
          },
          axisLine: {
            show: false // 不显示竖线
          },
          axisTick: {
            show: false // 不显示刻度线
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '年级人数',
          position: 'right',
          nameTextStyle: {
            padding: [0, -30, 0, 0] // 调整名称位置
          },
          axisLine: {
            show: false // 不显示竖线
          },
          axisTick: {
            show: false // 不显示刻度线
          },
          splitLine: {
            show: false // 不显示右侧网格线
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: classNames.map((className) => ({
        name: className,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        yAxisIndex: className === '全部班级' ? 1 : 0, // 使用右侧Y轴显示全部班级数据
        data: getScoreData(data, className),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 12,
          color: 'inherit', // 使用与数据线相同的颜色
          padding: [2, 4],
          borderRadius: 2
        }
      }))
    };

    return baseOptions;
  };

  const fetchGradeDivision = async (subjects: string[], score: string) => {
    getGradeDivision({
      year: params.year,
      term: params.term,
      teacherType: params.teacherType,
      gradeId: params.gradeId,
      gradeName: params.gradeName,
      subjectNames: subjects,
      examType: params.examType,
      examId: params.examId,
      score: parseInt(score)
    }).then((res: GradeDivisionResponse[]) => {
      setRawData(res);
      const options = generateChartOptions(res);
      setChartData(options);
      // 强制更新图表
      if (echartsRef.current) {
        echartsRef.current.getEchartsInstance().setOption(options, true);
      }
    });
  };

  const getData = () => {
    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects &&
      selectedChartSubjects.length > 0;

    if (hasAllParams) {
      // 创建当前参数的唯一标识
      const paramsKey = JSON.stringify({
        ...params,
        subjectNames: selectedChartSubjects,
        score: segmentValue
      });

      // 只有当参数变化时才发起请求
      if (prevParamsRef.current !== paramsKey) {
        prevParamsRef.current = paramsKey;
        fetchGradeDivision(selectedChartSubjects, segmentValue);
      }
    } else {
      setChartData([]);
    }
  };

  // 获取成绩分段对比图表图片
  const getGradeDivisionChartImage = async (): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      if (!echartsRef.current?.getEchartsInstance()) {
        reject(new Error('成绩分段对比图表实例不存在'));
        return;
      }

      const chartInstance = echartsRef.current.getEchartsInstance();

      try {
        // 设置图表选项以优化导出
        chartInstance.setOption({
          tooltip: {
            show: false
          }
        });

        // 等待图表更新完成后再导出
        setTimeout(async () => {
          try {
            const imgData = chartInstance.getDataURL({
              type: 'png',
              pixelRatio: 2, // 提高分辨率
              backgroundColor: '#fff'
            });

            // 还原初始配置
            chartInstance.setOption({
              tooltip: {
                show: true
              }
            });

            // 将Base64转换为Blob
            const base64Data = imgData.split(',')[1];
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });

            // 创建文件对象
            const file = new File([blob], `${selectedGradesName}成绩分段对比图表.png`, { type: 'image/png' });

            // 上传文件到服务器
            const formData = new FormData();
            formData.append('file', file);

            try {
              const response = await uploadFile(formData);
              if (response && response.fileUrl) {
                resolve(response.fileUrl);
              } else {
                reject(new Error('上传成绩分段对比图表图片失败'));
              }
            } catch (uploadError) {
              reject(uploadError);
            }
          } catch (error) {
            reject(error);
          }
        }, 500); // 延迟以确保图表更新完成
      } catch (error) {
        reject(error);
      }
    });
  };

  // 获取成绩分段对比BoardPane内容
  const getGradeDivisionBoardPaneContent = async (): Promise<string> => {
    try {
      const conclusionParams = {
        examId: params.examId || '',
        gradeId: params.gradeId || '',
        gradeName: params.gradeName || '',
        subjectNames: selectedChartSubjects || []
      };

      const conclusions = await getConclusion(conclusionParams);
      const resData = conclusions.find((item: any) => item.location === 5); // location 5 是成绩分段对比结论

      return resData ? resData.conclusion : '';
    } catch (error) {
      console.error('获取成绩分段对比BoardPane内容失败:', error);
      return '';
    }
  };

  // 获取成绩分段表格图片
  const getGradeDivisionTableImage = async (): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      try {
        // 首先获取表格数据
        const res = await getGradeDivision({
          year: params.year,
          term: params.term,
          teacherType: params.teacherType,
          gradeId: params.gradeId,
          gradeName: params.gradeName,
          subjectNames: selectedChartSubjects,
          examType: params.examType,
          examId: params.examId,
          score: parseInt(segmentValue)
        });

        if (!res || res.length === 0) {
          reject(new Error('无法获取成绩分段表格数据'));
          return;
        }

        // 创建一个临时的表格元素来生成图片
        const tableContainer = document.createElement('div');
        tableContainer.style.position = 'absolute';
        tableContainer.style.left = '-9999px';
        tableContainer.style.top = '-9999px';
        tableContainer.style.backgroundColor = '#fff';
        tableContainer.style.padding = '20px';
        tableContainer.style.fontFamily = 'Arial, sans-serif';

        // 创建表格HTML
        let tableHTML = `
          <div style="text-align: center; margin-bottom: 20px; font-weight: bold; font-size: 16px;">
            ${selectedGradesName}各班${selectedChartSubjects[0] === '全部' ? '全部学科' : selectedChartSubjects[0]}成绩分段表格统计表
          </div>
          <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%; font-size: 12px;">
            <thead>
              <tr style="background-color: #f5f5f5;">
                <th rowspan="2" style="text-align: center; vertical-align: middle;">班级/分段</th>
        `;

        // 添加分数段表头
        if (res[0]?.sectionList) {
          const sortedSections = [...res[0].sectionList].sort((a, b) => b.sort - a.sort);
          sortedSections.forEach(section => {
            const label = `${section.leftSection}${section.leftScore},${section.rightScore}${section.rightSection}`;
            tableHTML += `<th colspan="2" style="text-align: center;">${label}</th>`;
          });
        }

        tableHTML += `
              </tr>
              <tr style="background-color: #f5f5f5;">
        `;

        // 添加人数和占比子表头
        if (res[0]?.sectionList) {
          const sortedSections = [...res[0].sectionList].sort((a, b) => b.sort - a.sort);
          sortedSections.forEach(() => {
            tableHTML += `<th style="text-align: center;">人数</th><th style="text-align: center;">占比%</th>`;
          });
        }

        tableHTML += `
              </tr>
            </thead>
            <tbody>
        `;

        // 添加数据行
        res.forEach(item => {
          tableHTML += `<tr><td style="text-align: center;">${item.className}</td>`;
          if (item.sectionList) {
            const sortedSections = [...item.sectionList].sort((a, b) => b.sort - a.sort);
            sortedSections.forEach(section => {
              tableHTML += `
                <td style="text-align: center;">${section.studentNumber || 0}</td>
                <td style="text-align: center;">${section.ratio?.toFixed(2) || '0.00'}</td>
              `;
            });
          }
          tableHTML += `</tr>`;
        });

        tableHTML += `
            </tbody>
          </table>
        `;

        tableContainer.innerHTML = tableHTML;
        document.body.appendChild(tableContainer);

        // 使用html2canvas生成图片
        const html2canvas = (await import('html2canvas')).default;
        const canvas = await html2canvas(tableContainer, {
          backgroundColor: '#ffffff',
          scale: 2,
          useCORS: true,
          allowTaint: true
        });

        // 清理临时元素
        document.body.removeChild(tableContainer);

        // 将canvas转换为blob
        canvas.toBlob(async (blob) => {
          if (!blob) {
            reject(new Error('生成表格图片失败'));
            return;
          }

          // 创建文件对象
          const file = new File([blob], `${selectedGradesName}成绩分段表格.png`, { type: 'image/png' });

          // 上传文件到服务器
          const formData = new FormData();
          formData.append('file', file);

          try {
            const response = await uploadFile(formData);
            if (response && response.fileUrl) {
              resolve(response.fileUrl);
            } else {
              reject(new Error('上传成绩分段表格图片失败'));
            }
          } catch (uploadError) {
            reject(uploadError);
          }
        }, 'image/png');

      } catch (error) {
        reject(error);
      }
    });
  };

  // 获取成绩分段表格BoardPane内容
  const getGradeDivisionTableBoardPaneContent = async (): Promise<string> => {
    try {
      const conclusionParams = {
        examId: params.examId || '',
        gradeId: params.gradeId || '',
        gradeName: params.gradeName || '',
        subjectNames: selectedChartSubjects || []
      };

      const conclusions = await getConclusion(conclusionParams);
      const resData = conclusions.find((item: any) => item.location === 5); // location 5 是成绩分段对比结论

      return resData ? resData.conclusion : '';
    } catch (error) {
      console.error('获取成绩分段表格BoardPane内容失败:', error);
      return '';
    }
  };

  useImperativeHandle(ref, () => ({
    getData,
    getGradeDivisionChartImage,
    getGradeDivisionBoardPaneContent,
    getGradeDivisionTableImage,
    getGradeDivisionTableBoardPaneContent
  }));

  // useEffect(() => {
  //   setChartData([]);
  // }, [params.gradeName]);

  // 验证输入值是否为[5,总分]区间的正整数
  const validateSegmentValue = (value: string) => {
    const numValue = parseInt(value);
    if (!/^\d+$/.test(value)) {
      return false; // 非整数
    }
    if (numValue < 5 || numValue > MAX_SCORE) {
      return false; // 不在[5,总分]区间
    }
    return true;
  };

  // 处理输入变化
  const handleSegmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSegmentValue(value);
  };

  // 处理输入框失焦事件
  const handleSegmentBlur = () => {
    // 如果为空或无效值，恢复默认值
    if (segmentValue === '' || !validateSegmentValue(segmentValue)) {
      const defaultValue =
        selectedChartSubjects.includes('全部') || selectedChartSubjects.length === 0 ? '50' : '10';
      setSegmentValue(defaultValue);
      message.warning(`请输入5-${MAX_SCORE}之间的整数`);
    }
  };
  useEffect(() => {
    // 检查 "全部" 是否在第一项，并且后面有新增选项
    if (
      selectedChartSubjects.length > 0 &&
      selectedChartSubjects[0] === '全部' &&
      selectedChartSubjects.length > 1
    ) {
      setSelectedChartSubjects(selectedChartSubjects.slice(1));
    }

    // 检查 "全部" 是否在最后一项，并且前面有其他选项
    if (
      selectedChartSubjects.length > 0 &&
      selectedChartSubjects[selectedChartSubjects.length - 1] === '全部' &&
      selectedChartSubjects.length > 1
    ) {
      // 只保留 "全部" 选项
      setSelectedChartSubjects(['全部']);
    }
  }, [selectedChartSubjects]);

  useEffect(() => {
    setSelectedChartSubjects(selectedSubjects);
  }, [selectedSubjects]);
  const handleExport = async () => {
    try {
      const response = await getGradeDivisionExport({
        year: params.year,
        term: params.term,
        teacherType: params.teacherType,
        gradeId: params.gradeId,
        gradeName: params.gradeName,
        subjectNames: selectedChartSubjects,
        examType: params.examType,
        examId: params.examId,
        score: parseInt(segmentValue)
      });

      // 创建一个 blob URL
      const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' });
      const url = window.URL.createObjectURL(blob);

      // 创建一个临时的 a 标签来触发下载
      const link = document.createElement('a');
      link.href = url;
      link.download = `成绩等级分段-${params.gradeName}-${selectedChartSubjects.join(',')}.xlsx`;
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
      console.error('Export failed:', error);
    }
  };

  useEffect(() => {
    if (!!segmentValue && segmentValue !== '0') {
      getData();
    }
  }, [value, segmentValue]);

  return (
    <Box>
      <Box display="flex" justifyContent="space-between">
        <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
          成绩分段对比统计图
        </Box>
        <Box display="flex">
          <AntdInput
            maxLength={3}
            placeholder="请输入分段值"
            value={segmentValue}
            onChange={handleSegmentChange}
            onBlur={handleSegmentBlur}
            style={{ width: '120px' }}
            suffix="分/段"
          />
          <Select
            options={chartSubjectsOptions}
            value={selectedChartSubjects[0]}
            onChange={(value) => {
              setValue(value);
              setSelectedChartSubjects([value]);
              setSegmentValue('50');
            }}
            placeholder="选择学科"
            style={{ minWidth: '100px', marginLeft: '10px' }}
          />
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block', marginLeft: '10px' }}
            onClick={handleExport}
            color={'#636C7B'}
          >
            导出
          </Box>
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block', marginLeft: '10px' }}
            onClick={() => setShowTable(true)}
            color={'#636C7B'}
          >
            查看表格
          </Box>
        </Box>
      </Box>
      {Object.keys(chartData).length > 0 ? (
        <>
          <EChartsReact
            ref={echartsRef}
            option={chartData}
            style={{
              borderRadius: '12px',
              height: '400px'
            }}
          />
          <Box position={'relative'}>
            <BoardPane
              ref={boardPaneRef}
              location={5}
              height="100px"
              examId={params.examId}
              selectedSubjects={selectedChartSubjects}
              selectedTestData={selectedTestData}
              selectedGradesName={selectedGradesName}
              selectedGradesId={selectedGradesId}
              editable={true}
              params={{
                academic_year: params.year,
                semester: params.term === 1 ? '第一学期' : '第二学期',
                grade: params.gradeName,
                test_type: params.examType,
                test_name: params.examName,
                subject: selectedChartSubjects,
                data_block_name: '成绩分段对比统计图',
                score_segment_size: parseInt(segmentValue),
                data_render_type: '折线图',
                data: rawData.map((item: GradeDivisionResponse) => ({
                  class_name: item.className,
                  score_segments: item.sectionList.map((section: Section) => ({
                    score_segment: `[${section.leftScore},${section.rightScore}]`,
                    student_number: section.studentNumber
                  }))
                })),
                analysis_requirements: '请你对本次测试的各班级的成绩分段人数对比统计进行分析'
              }}
              fullScreenHeight="165px"
              fullScreenoffsetX="40"
              title="结论"
              backgroundColor="#f9f9f9"
              titleFontSize="14px"
              padding="10px"
              showFullScreenButtons
            />
          </Box>
        </>
      ) : (
        <NoDataComponent height="500px" type={isAuthority ? 'noPermission' : 'noData'} />
      )}

      <GradeDivisionDialog
        onClose={() => {
          if (boardPaneRef.current?.refresh) {
            boardPaneRef.current.refresh();
          }
        }}
        isModalVisible={showTable}
        setIsModalVisible={setShowTable}
        params={params}
        score={segmentValue}
        chartSubjectsOptions={chartSubjectsOptions}
        value={selectedChartSubjects[0]}
      />
    </Box>
  );
};

export default forwardRef(GradeDivision);
