import {
  <PERSON>,
  Button,
  <PERSON>dal,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  useDisclosure,
  FormControl,
  FormErrorMessage
} from '@chakra-ui/react';
import { useEffect, useImperativeHandle, useState } from 'react';
import styled from '@emotion/styled';
import { WarningIcon } from '@chakra-ui/icons';
import { Form, Input, InputNumber } from 'antd';
import { ValidateStatus } from 'antd/es/form/FormItem';
import { queryScoreLevel, setScoreLevel } from '@/api/tenant/digitalKanban/qualityAnalysis';
import { Toast } from '@/utils/ui/toast';
import { LevelType } from '@/types/api/tenant/digitalKanban/qualityAnalysis';

const LevelSpan = styled.span`
  display: inline-block;
  width: 40px;
`;

const LevelSpanLine = styled.span`
  display: inline-block;
  width: 35px;
  height: 1px;
  border: 1px solid #000;
`;
const LevelNumberSpan = styled.span`
  width: 40px;
  margin-left: 13px;
`;

const StyledForm = styled(Form)`
  .ant-form-item-explain-error {
    width: 300px;
    font-size: 12px;
  }
  .ant-form-item-control {
    width: 66px;
    height: 22px;
    margin-right: 13px;
  }
  .ant-form-item-control-input {
    height: 22px;
  }
  .ant-input {
    height: 22px;
  }
  .ant-input-number {
    height: 22px;
    width: 100%;
  }
  .ant-input-number-input {
    height: 22px;
  }
  .ant-input-number-handler-wrap {
    display: none;
  }
`;

interface AcademicLevelModalProps {
  isModalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  fetchLevelDivisionData: () => void;
}

interface LevelConfig {
  help: string;
  validateStatus: ValidateStatus;
  scoreMin: number | string;
  scoreMax: number;
}

export default function AcademicLevelModal(props: AcademicLevelModalProps) {
  function fetchQueryScoreLevel() {
    queryScoreLevel({})
      .then((res) => {
        console.log('查询分数段', res);
        // 将查询到的分数修改为 levels 的数据格式
        setLevels({
          A: {
            help: '',
            validateStatus: 'success',
            scoreMin: res[0].scoreMin,
            scoreMax: res[0].scoreMax
          },
          B: {
            help: '',
            validateStatus: 'success',
            scoreMin: res[1].scoreMin,
            scoreMax: res[1].scoreMax
          },
          C: {
            help: '',
            validateStatus: 'success',
            scoreMin: res[2].scoreMin,
            scoreMax: res[2].scoreMax
          },
          D: {
            help: '',
            validateStatus: 'success',
            scoreMin: res[3].scoreMin,
            scoreMax: res[3].scoreMax
          },
          E: {
            help: '',
            validateStatus: 'success',
            scoreMin: res[4].scoreMin,
            scoreMax: res[4].scoreMax
          }
        });
      })
      .catch((err) => {
        console.log('查询分数段失败', err);
      });
  }
  useEffect(() => {
    fetchQueryScoreLevel();
  }, [props.isModalVisible]);
  const helpText = {
    default: '请输入分数占比值',
    error: '当前等级分数段与下一个等级分数段存在交集'
  };
  const [levels, setLevels] = useState<Record<string, LevelConfig>>({
    A: { help: '', validateStatus: 'success', scoreMin: 85, scoreMax: 100 },
    B: { help: '', validateStatus: 'success', scoreMin: 70, scoreMax: 85 },
    C: { help: '', validateStatus: 'success', scoreMin: 60, scoreMax: 70 },
    D: { help: '', validateStatus: 'success', scoreMin: 40, scoreMax: 60 },
    E: { help: '', validateStatus: 'success', scoreMin: 0, scoreMax: 40 }
  });

  // 添加一个状态来跟踪是否正在输入中文
  const [isComposing, setIsComposing] = useState(false);

  const handleInputChange = (value: number | string | null, type: string) => {
    // 如果输入为空，直接返回
    if (value === null || value === '') {
      setLevels((prev) => ({
        ...prev,
        [type]: {
          ...prev[type],
          scoreMin: ''
        }
      }));
      return;
    }

    // 转换为数字
    const numValue = typeof value === 'string' ? Number(value) : value;

    // 数值超过100直接返回
    if (numValue > 100) {
      return;
    }

    const nextLevel = Object.keys(levels)[Object.keys(levels).indexOf(type) + 1];
    const currentLevel = Object.keys(levels)[Object.keys(levels).indexOf(type)];
    const updates = {
      validateStatus: 'success' as ValidateStatus,
      help: '',
      scoreMin: numValue
    };

    setLevels((prev) => ({
      ...prev,
      [type]: {
        ...prev[type],
        ...updates
      }
    }));
  };

  function handleChangeScoreMax(value: number | string | null, type: 'A' | 'B' | 'C' | 'D' | 'E') {
    if (value === null || value === '') return;

    // 转换为数字
    const numValue = typeof value === 'string' ? Number(value) : value;

    if (numValue > 100) {
      return;
    }
    setLevels((prev) => ({
      ...prev,
      [type]: {
        ...prev[type],
        scoreMax: numValue
      }
    }));
  }

  function validateLevels(levels: Record<string, LevelConfig>, setLevels: Function) {
    let isValid = true;
    const updatedLevels = { ...levels };

    // 提取并排序等级（按 scoreMin 从小到大排序）
    const sortedLevels = Object.entries(levels)
      .map(([key, value]) => ({ name: key, ...value }))
      .sort((a, b) => Number(a.scoreMin) - Number(b.scoreMin));

    // 1. **检查 E 是否从 0 开始**
    const firstLevel = sortedLevels.find((level) => level.name === 'E');
    if (firstLevel && firstLevel.scoreMin !== 0) {
      updatedLevels['E'].validateStatus = 'error';
      updatedLevels['E'].help = helpText.error;
      isValid = false;
    }

    for (let i = 0; i < sortedLevels.length - 1; i++) {
      const current = sortedLevels[i];
      const next = sortedLevels[i + 1];

      // 2. **检查是否有交集**
      if (Number(current.scoreMax) > Number(next.scoreMin)) {
        updatedLevels[current.name].validateStatus = 'error';
        updatedLevels[next.name].validateStatus = 'error';

        updatedLevels[current.name].help = helpText.error;
        updatedLevels[next.name].help = helpText.error;
        isValid = false;
      }

      // 3. **检查是否有间隙**
      // if (current.scoreMax !== next.scoreMin) {
      //   updatedLevels[current.name].validateStatus = "error";
      //   updatedLevels[next.name].validateStatus = "error";
      //   updatedLevels[current.name].help = helpText.error;
      //   updatedLevels[next.name].help = helpText.error;
      //   isValid = false;
      // }
    }

    // **如果校验成功，重置所有 help 和 validateStatus**
    if (isValid) {
      Object.keys(updatedLevels).forEach((key) => {
        updatedLevels[key].validateStatus = 'success';
        updatedLevels[key].help = '';
      });
    }

    // 更新状态
    setLevels(updatedLevels);
    return isValid;
  }

  function handleConfim() {
    const isAllValid = validateLevels(levels, setLevels);
    if (isAllValid) {
      // levels 转换数据
      const scoreLevelList = Object.keys(levels).map((key, index) => ({
        level: `${key}等` as LevelType,
        scoreMin: Number(levels[key].scoreMin),
        scoreMax: Number(levels[key].scoreMax),
        sort: index + 1
      }));
      setScoreLevel({
        type: 1,
        scoreLevelList: scoreLevelList
      })
        .then((res) => {
          onClose();
          Toast.success('设置分数段成功');
          props.fetchLevelDivisionData();
        })
        .catch((err) => {
          console.log('设置分数段失败', err);
          Toast.error('设置分数段失败');
        });
    } else {
      Toast.error('请检查输入的分数段是否正确');
    }
  }

  function onClose() {
    props.setModalVisible(false);
  }

  return (
    <>
      <Modal isOpen={props.isModalVisible} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent
          width={'418px'}
          height={'432px'}
          w={'418px'}
          h={'432px'}
          justifyContent={'space-between'}
        >
          <ModalHeader paddingBottom={'32px'}>设置分数段</ModalHeader>
          <ModalBody marginTop={'10px'} padding={0}>
            <StyledForm>
              <Box
                display={'flex'}
                alignItems={'center'}
                height={'26px'}
                marginBottom={'20px'}
                justifyContent={'center'}
              >
                <Box>
                  <Form.Item
                    label="A等:"
                    validateStatus={levels.A.validateStatus}
                    help={levels.A.help}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      onChange={(value) => {
                        handleInputChange(value, 'A');
                        // 同时修改B级分数段的max
                        handleChangeScoreMax(value, 'B');
                      }}
                      value={levels.A.scoreMin}
                    />
                  </Form.Item>
                </Box>
                <LevelSpanLine />
                <LevelNumberSpan>100%</LevelNumberSpan>
              </Box>

              <Box
                display={'flex'}
                alignItems={'center'}
                marginBottom={'20px'}
                height={'26px'}
                justifyContent={'center'}
              >
                <Box>
                  <Form.Item
                    label="B等:"
                    validateStatus={levels.B.validateStatus}
                    help={levels.B.help}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      onChange={(value) => {
                        handleInputChange(value, 'B');
                        // 同时修改C级分数段的max
                        handleChangeScoreMax(value, 'C');
                      }}
                      value={levels.B.scoreMin}
                    />
                  </Form.Item>
                </Box>
                <LevelSpanLine />
                <LevelNumberSpan>{levels.B.scoreMax}%</LevelNumberSpan>
              </Box>

              <Box
                display={'flex'}
                alignItems={'center'}
                marginBottom={'20px'}
                height={'26px'}
                justifyContent={'center'}
              >
                <Box>
                  <Form.Item
                    label="C等:"
                    validateStatus={levels.C.validateStatus}
                    help={levels.C.help}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      onChange={(value) => {
                        handleInputChange(value, 'C');
                        // 同时修改D级分数段的max
                        handleChangeScoreMax(value, 'D');
                      }}
                      value={levels.C.scoreMin}
                    />
                  </Form.Item>
                </Box>
                <LevelSpanLine />
                <LevelNumberSpan>{levels.C.scoreMax}%</LevelNumberSpan>
              </Box>

              <Box
                display={'flex'}
                alignItems={'center'}
                marginBottom={'20px'}
                height={'26px'}
                justifyContent={'center'}
              >
                <Box>
                  <Form.Item
                    label="D等:"
                    validateStatus={levels.D.validateStatus}
                    help={levels.D.help}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      onChange={(value) => {
                        handleInputChange(value, 'D');
                        // 同时修改E级分数段的max
                        handleChangeScoreMax(value, 'E');
                      }}
                      value={levels.D.scoreMin}
                    />
                  </Form.Item>
                </Box>
                <LevelSpanLine />
                <LevelNumberSpan>{levels.D.scoreMax}%</LevelNumberSpan>
              </Box>

              <Box
                display={'flex'}
                alignItems={'center'}
                marginBottom={'20px'}
                height={'26px'}
                justifyContent={'center'}
              >
                <Box>
                  <Form.Item
                    label="E等:"
                    validateStatus={levels.E.validateStatus}
                    help={levels.E.help}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      onChange={(value) => handleInputChange(value, 'E')}
                      value={levels.E.scoreMin}
                    />
                  </Form.Item>
                </Box>
                <LevelSpanLine />
                <LevelNumberSpan>{levels.E.scoreMax}%</LevelNumberSpan>
              </Box>

              <Box display={'flex'} justifyContent={'center'}>
                <WarningIcon
                  position={'relative'}
                  top={'3px'}
                  color={'#FF7D00'}
                  marginRight={'3px'}
                ></WarningIcon>
                <span style={{ display: 'inline-block', width: '339px' }}>
                  所填值为分数占比值，各等级所填值依次递减，统计数据包含所填值。
                </span>
              </Box>
            </StyledForm>
          </ModalBody>

          <ModalFooter display={'flex'}>
            <Button
              _hover={{
                background: '#e9f1fe'
              }}
              marginRight={'8px'}
              onClick={onClose}
              background={'#e9f1fe'}
              color={'#3D7FFF'}
              width={'181px'}
            >
              取消
            </Button>
            <Button colorScheme="blue" width={'181px'} onClick={handleConfim}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
