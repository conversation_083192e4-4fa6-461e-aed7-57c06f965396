import { Toast } from '@/utils/ui/toast';
import {
  Box,
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay
} from '@chakra-ui/react';
import { Input } from 'antd';
import { useRef, useState } from 'react';

interface WeakSubectSetOptionsModalProps {
  isModalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  multiple: number;
  difference: number;
  setMultiple: (multiple: number) => void;
  setDifference: (difference: number) => void;
  totalStudentCount: number;
}

export default function WeakSubectSetOptionsModal(props: WeakSubectSetOptionsModalProps) {
  const { multiple, difference, setMultiple, setDifference, totalStudentCount } = props;
  // 单科总排名
  const [singleSubjectTotalRank, setSingleSubjectTotalRank] = useState<number>(multiple);
  // 总排名和单科排名差值
  const [totalRankAndSingleSubjectRankDifference, setTotalRankAndSingleSubjectRankDifference] =
    useState<number>(difference);

  const defaultSingleSubjectTotalRank = useRef(singleSubjectTotalRank);
  const defaultTotalRankAndSingleSubjectRankDifference = useRef(
    totalRankAndSingleSubjectRankDifference
  );

  function singleSubjectTotalRankChange(e: any) {
    const value = e.target.value;
    if (value <= 10 && value >= 0) {
      setSingleSubjectTotalRank(value);
    } else {
      Toast.error('仅支持输入0-10之间的正整数');
    }
  }

  function totalRankAndSingleSubjectRankDifferenceChange(e: any) {
    const value = e.target.value;
    if (value <= 10 && value >= 0) {
      setTotalRankAndSingleSubjectRankDifference(value);
    } else {
      Toast.error(`仅支持输入0-${totalStudentCount}的正整数`);
    }
  }

  function onClose() {
    props.setModalVisible(false);
    // 重置
    setSingleSubjectTotalRank(defaultSingleSubjectTotalRank.current);
    setTotalRankAndSingleSubjectRankDifference(
      defaultTotalRankAndSingleSubjectRankDifference.current
    );
  }

  function onConfirm() {
    props.setModalVisible(false);
    // 设置倍数
    setMultiple(singleSubjectTotalRank);
    defaultSingleSubjectTotalRank.current = singleSubjectTotalRank;
    // 设置差值
    setDifference(totalRankAndSingleSubjectRankDifference);
    defaultTotalRankAndSingleSubjectRankDifference.current =
      totalRankAndSingleSubjectRankDifference;
  }

  return (
    <>
      <Modal isOpen={props.isModalVisible} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent width={'418px'} height={'292px'} justifyContent={'space-between'}>
          <ModalHeader paddingBottom={'32px'}>设置薄弱学科划分规则</ModalHeader>
          <ModalCloseButton />
          <ModalBody marginTop={'10px'} padding={0}>
            <Box padding={'0 24px'} color={'#4E5969'}>
              <div>
                单科排名是总排名
                <Input
                  max={10}
                  min={0}
                  value={singleSubjectTotalRank}
                  style={{ width: '66px', height: '22px', margin: '0 13px', textAlign: 'center' }}
                  onChange={(e) => singleSubjectTotalRankChange(e)}
                />
                倍以上,
              </div>
              <div style={{ margin: '22px 0' }}>
                总排名和单科排名绝对值必须要差
                <Input
                  min={0}
                  value={totalRankAndSingleSubjectRankDifference}
                  style={{ width: '56px', height: '22px', margin: '0 2px', textAlign: 'center' }}
                  onChange={(e) => totalRankAndSingleSubjectRankDifferenceChange(e)}
                />
                名以上,
              </div>
              <div>属于这个学生的薄弱学科</div>
            </Box>
          </ModalBody>

          <ModalFooter display={'flex'}>
            <Button
              _hover={{
                background: '#e9f1fe'
              }}
              marginRight={'8px'}
              onClick={onClose}
              background={'#e9f1fe'}
              color={'#3D7FFF'}
              width={'181px'}
            >
              取消
            </Button>
            <Button colorScheme="blue" width={'181px'} onClick={onConfirm}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
