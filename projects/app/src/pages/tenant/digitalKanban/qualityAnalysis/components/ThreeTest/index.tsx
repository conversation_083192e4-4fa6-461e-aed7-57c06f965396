import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Box, Flex, Image, Input, Text, forwardRef } from '@chakra-ui/react';
import {
  getRecentReport,
  getStandardTestComparison,
  StandardTestComparisonItem
} from '@/api/kanban';
import { Input as AntdInput, Select, Menu, Dropdown, Checkbox, message, Tag } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import EChartsReact from 'echarts-for-react';
import NoDataComponent from '../NoDataProps';
import { useRef } from 'react';
import BoardPane from '@/components/BoardPane';

interface ThreeTestProps {
  option: any;
  chartSubjectsOptions: any[];
  selectedChartSubjects: string[];
  params: any;
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  isAuthority: boolean;
}
export interface ThreeTestRef {
  getData: () => void;
}

interface ExamOption {
  value: string;
  label: string;
  examTime?: string;
}

interface ExamTypeOption {
  value: string;
  label: string;
  children: ExamOption[];
}

interface SelectedTest {
  examType: string;
  exam: ExamOption;
}

const ThreeTest = (props: ThreeTestProps, ref: React.ForwardedRef<ThreeTestRef>) => {
  const {
    chartSubjectsOptions,
    params,
    selectedSubjects,
    selectedGradesName,
    selectedGradesId,
    isAuthority
  } = props;
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>(['全部']);
  const [testOptions, setTestOptions] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any>({});
  const [selectedTests, setSelectedTests] = useState<SelectedTest[]>([]);
  const [selectedTestData, setselectedTestData] = useState<any[]>([]);
  const [value, setValue] = useState<string>('');
  const echartsRef = useRef<any>(null);
  const boardPaneRef = useRef<any>(null);

  useEffect(() => {
    // 检查 "全部" 是否在第一项，并且后面有新增选项
    if (
      selectedChartSubjects.length > 0 &&
      selectedChartSubjects[0] === '全部' &&
      selectedChartSubjects.length > 1
    ) {
      setSelectedChartSubjects(selectedChartSubjects.slice(1));
    }

    // 检查 "全部" 是否在最后一项，并且前面有其他选项
    if (
      selectedChartSubjects.length > 0 &&
      selectedChartSubjects[selectedChartSubjects.length - 1] === '全部' &&
      selectedChartSubjects.length > 1
    ) {
      // 只保留 "全部" 选项
      setSelectedChartSubjects(['全部']);
    }
  }, [selectedChartSubjects]);

  const getData = () => {
    fetchGradeComparison();
    fetchStandardTestComparison();
  };

  useEffect(() => {
    setSelectedChartSubjects(selectedSubjects);
  }, [selectedSubjects]);

  useEffect(() => {
    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects.length > 0;

    if (hasAllParams) {
      fetchGradeComparison();
    } else {
      setChartData({});
      setselectedTestData([]);
      setTestOptions([]);
    }
  }, []);

  useImperativeHandle(ref, () => ({
    getData
  }));

  useEffect(() => {
    const newTestData = selectedTests.map((test) => [
      test.examType,
      test.exam.value,
      test.exam.examTime || ''
    ]);
    setselectedTestData(newTestData.flat());

    // 当选择测试变化时，重新获取数据
    fetchStandardTestComparison();
  }, [selectedTests]);

  // 监听 chartData 的变化，确保图表更新
  useEffect(() => {
    if (echartsRef.current && Object.keys(chartData).length > 0) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      chartInstance.setOption(chartData, true);
    }
  }, [chartData]);

  useEffect(() => {
    fetchStandardTestComparison();
  }, [value, selectedChartSubjects]);

  // useEffect(() => {
  //   setChartData({});
  // }, [params.gradeName]);

  // useEffect(() => {
  //   console.log('testValue', selectedChartSubjects);
  //   if (selectedChartSubjects.length > 0 && params.examId) {
  //     fetchStandardTestComparison();
  //   }
  // }, [selectedTestData, selectedChartSubjects, params.examId]);

  useEffect(() => {
    // 当测试选项发生变化时，清空已选择的测试
    setSelectedTests([]);
  }, [testOptions]);

  const fetchGradeComparison = async () => {
    getRecentReport({ ...params, subjectNames: selectedChartSubjects }).then((response) => {
      if (response && response.length > 0) {
        // 更新年级选项
        const formattedTestOptions = response.map((grade: any) => {
          return {
            value: grade.examTypeName,
            label: grade.examTypeName,
            disabled: true,
            children: grade.reportList.map((report: any) => ({
              value: report.examId,
              label: report.examName,
              examTime: report.examTime
            }))
          };
        });
        setTestOptions(formattedTestOptions);
      } else {
        setTestOptions([]);
      }
    });
  };

  const fetchStandardTestComparison = async () => {
    console.log('selectedTest55s', selectedTests);
    // 检查所有必要参数是否存在且不为空
    const requiredParams = {
      year: params.year,
      term: params.term,
      gradeName: params.gradeName,
      subjectNames: selectedChartSubjects,
      examId: params.examId,
      startDate: params.startDate,
      endDate: params.endDate
    };

    // 检查是否所有必要参数都有值
    const hasAllRequiredParams = Object.values(requiredParams).every(
      (value) => value !== undefined && value !== null && value !== ''
    );

    // 检查 selectedChartSubjects 是否有值
    const hasSubjects = selectedChartSubjects && selectedChartSubjects.length > 0;

    const ids = selectedTests.map((item) => item.exam.value) || '';

    // 只有当所有必要参数都存在且有效时才调用 API
    if (hasAllRequiredParams && hasSubjects) {
      getStandardTestComparison({
        year: params.year,
        term: params.term,
        gradeName: params.gradeName,
        subjectNames: selectedChartSubjects,
        examId: [params.examId, ...ids].join(','),
        startDate: params.startDate,
        endDate: params.endDate
      }).then((res: StandardTestComparisonItem[]) => {
        console.log('res333', res);
        if (!res || res.length === 0) {
          setChartData({});
          setselectedTestData([]);
          return;
        }

        // 获取所有唯一的考试名称和班级名称
        const examNames = [...new Set(res.map((item) => item.examName))];
        const classNames = [...new Set(res.map((item) => item.className))];

        // 为每个考试创建一个系列
        const series = examNames.map((examName, index) => {
          const data = classNames.map((className) => {
            const item = res.find((r) => r.className === className && r.examName === examName);
            return item ? item.standardScore : 0;
          });

          const colors = ['#5dc6c7', '#2d5cf6', '#58a5f3'];

          return {
            name: examName,
            type: 'bar',
            data: data,
            itemStyle: {
              color: colors[index % colors.length]
            }
          };
        });

        console.log('series', series);

        const option = {
          grid: {
            left: '4%',
            right: '4%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params: any) {
              let result = params[0].axisValue + '<br/>';
              params.forEach((item: any) => {
                result += `${item.marker} ${item.seriesName}: ${item.data.toFixed(2)}分<br/>`;
              });
              return result;
            },
            textStyle: {
              align: 'left',
              width: 200,
              overflow: 'break'
            },
            confine: true,
            extraCssText: 'max-width: 200px; white-space: normal; word-break: break-all;'
          },
          legend: {
            data: examNames,
            bottom: '3%',
            icon: 'circle'
          },
          xAxis: {
            type: 'category',
            data: classNames,
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: [
            {
              type: 'value',
              name: '标准分',
              nameTextStyle: {
                fontSize: 12,
                color: '#666'
              }
            }
          ],
          series: series
        };
        console.log('option', option);
        setChartData(option);
      });
    }
  };

  const handleExport = () => {
    if (!echartsRef.current?.getEchartsInstance()) {
      return;
    }

    const chartInstance = echartsRef.current?.getEchartsInstance();

    // 显示加载指示器
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    setTimeout(() => {
      chartInstance.hideLoading();
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '近三次测试标准分对比.png';
      link.click();
    }, 1000);
  };

  const isTestSelected = (examType: string, examValue: string) => {
    return selectedTests.some(
      (test) => test.examType === examType && test.exam.value === examValue
    );
  };

  const toggleTestSelection = (examType: ExamTypeOption, exam: ExamOption) => {
    setSelectedTests((prev) => {
      const isSelected = isTestSelected(examType.value, exam.value);
      if (isSelected) {
        return prev.filter(
          (test) => !(test.examType === examType.value && test.exam.value === exam.value)
        );
      }
      if (prev.length >= 2) {
        message.warning('最多只能选择两个测试');
        return prev;
      }
      return [...prev, { examType: examType.value, exam }];
    });
  };

  const renderMenu = () => {
    const { SubMenu } = Menu;
    return (
      <Menu>
        {testOptions.length === 0 ? (
          <Menu.Item disabled style={{ textAlign: 'center', color: '#86909c' }}>
            暂无数据
          </Menu.Item>
        ) : (
          testOptions.map((examType: ExamTypeOption) => (
            <SubMenu key={examType.value} title={examType.label}>
              {examType.children.map((exam: ExamOption) => (
                <Menu.Item key={exam.value}>
                  <Checkbox
                    checked={isTestSelected(examType.value, exam.value)}
                    onChange={() => toggleTestSelection(examType, exam)}
                  >
                    {exam.label}
                  </Checkbox>
                </Menu.Item>
              ))}
            </SubMenu>
          ))
        )}
      </Menu>
    );
  };

  const ids = selectedTests.map((item) => item.exam.value) || '';
  return (
    <Box>
      <Box display="flex" justifyContent="space-between">
        <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
          <Text noOfLines={1} isTruncated>
            近三次测试标准分对比
          </Text>
        </Box>
        <Box display="flex">
          <Select
            options={chartSubjectsOptions}
            value={selectedChartSubjects[0]}
            onChange={(value: string) => {
              setSelectedChartSubjects([value]);
              setValue(value);
            }}
            placeholder="选择学科"
            style={{ minWidth: '100px' }}
          />
          <Dropdown overlay={renderMenu()} trigger={['click']}>
            <Box
              display="inline-flex"
              alignItems="center"
              style={{
                minWidth: '200px',
                marginLeft: '10px',
                maxWidth: '400px',
                minHeight: '32px',
                padding: '4px 8px',
                background: '#fff',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                cursor: 'pointer',
                flexWrap: 'wrap',
                gap: '4px'
              }}
            >
              {selectedTests.length > 0 ? (
                <>
                  {selectedTests.map((test) => {
                    const label = testOptions
                      .find((opt: ExamTypeOption) => opt.value === test.examType)
                      ?.children.find(
                        (child: ExamOption) => child.value === test.exam.value
                      )?.label;
                    return (
                      <Tag
                        key={test.exam.value}
                        closable
                        onClose={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setSelectedTests((prev) =>
                            prev.filter(
                              (item) =>
                                !(
                                  item.examType === test.examType &&
                                  item.exam.value === test.exam.value
                                )
                            )
                          );
                        }}
                        style={{
                          margin: 0,
                          background: '#f2f3f5',
                          border: 'none',
                          borderRadius: '2px',
                          height: '24px',
                          lineHeight: '24px',
                          padding: '0 4px',
                          color: '#1d2129'
                        }}
                      >
                        {label && label.length > 15 ? label.substring(0, 15) + '...' : label}
                      </Tag>
                    );
                  })}
                </>
              ) : (
                <Text fontSize="14px" color="#86909c">
                  请选择测试
                </Text>
              )}
              <Box
                display="inline-flex"
                alignItems="center"
                justifyContent="center"
                marginLeft="auto"
              >
                <DownOutlined style={{ color: '#86909c' }} />
              </Box>
            </Box>
          </Dropdown>
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block', marginLeft: '10px' }}
            onClick={handleExport}
            color={'#636C7B'}
          >
            导出
          </Box>
        </Box>
      </Box>
      {Object.keys(chartData).length > 0 ? (
        <>
          <EChartsReact
            ref={echartsRef}
            option={chartData}
            style={{
              borderRadius: '12px',
              height: '400px'
            }}
            notMerge={true}
            lazyUpdate={false}
            opts={{ renderer: 'canvas' }}
          />
          <Box position={'relative'}>
            <BoardPane
              ref={boardPaneRef}
              location={4}
              height="100px"
              examId={[params.examId, ...ids].join(',')}
              selectedSubjects={selectedChartSubjects}
              selectedTestData={selectedTestData}
              selectedGradesName={selectedGradesName}
              selectedGradesId={selectedGradesId}
              editable={true}
              params={{
                academic_year: params.year,
                semester: params.term === 1 ? '第一学期' : '第二学期',
                grade: selectedGradesName,
                test_type: params.examType,
                test_name: '期末质量分析',
                subject: selectedChartSubjects.length > 0 ? selectedChartSubjects[0] : '全部学科',
                data_block_name: '近几次测试标准分对比',
                data_render_type: '柱状图',
                data: chartData?.series?.length
                  ? chartData.series[0].data.map((_: any, index: number) => ({
                      class_name: chartData?.xAxis?.data[index],
                      test_list: chartData.series.map((series: any) => ({
                        current_test_name: series.name,
                        sdandard_score: series.data[index]
                      }))
                    }))
                  : [],
                analysis_requirements: '请你对本次测试的各班级的近几次测试标准分数据进行分析'
              }}
              fullScreenHeight="165px"
              fullScreenoffsetX="40"
              title="结论"
              backgroundColor="#f9f9f9"
              titleFontSize="14px"
              padding="10px"
              showFullScreenButtons
            />
          </Box>
        </>
      ) : (
        <NoDataComponent height="500px" type={isAuthority ? 'noPermission' : 'noData'} />
      )}
      {Object.keys(chartData).length > 0 && <></>}
    </Box>
  );
};

export default forwardRef(ThreeTest);
