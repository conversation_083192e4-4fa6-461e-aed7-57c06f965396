import type React from 'react';
import { useEffect, useState } from 'react';
import { Modal, Table, Select, Button } from 'antd';
import { Box, Flex } from '@chakra-ui/react';
import { CloseOutlined } from '@ant-design/icons';
import { getSubjectLevel, exportSubjectLevel } from '@/api/kanban';

interface LevelScoreDialogProps {
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  params: any;
  chartSubjectsOptions: any[];
  value: string;
}

interface TableDataItem {
  key: string;
  class: string;
  aPlusCount: number;
  aPlusPercentage: string;
  aCount: number;
  aPercentage: string;
}

const columns = [
  {
    title: '班级',
    dataIndex: 'class',
    key: 'class',
    width: '25%'
  },
  {
    title: 'A+等',
    children: [
      {
        title: '人数',
        dataIndex: 'aPlusCount',
        key: 'aPlusCount',
        width: '12.5%'
      },
      {
        title: '比例',
        dataIndex: 'aPlusPercentage',
        key: 'aPlusPercentage',
        width: '12.5%'
      }
    ]
  },
  {
    title: 'A等',
    children: [
      {
        title: '人数',
        dataIndex: 'aCount',
        key: 'aCount',
        width: '12.5%'
      },
      {
        title: '比例',
        dataIndex: 'aPercentage',
        key: 'aPercentage',
        width: '12.5%'
      }
    ]
  }
];

const LevelScoreDialog: React.FC<LevelScoreDialogProps> = ({
  isModalVisible,
  setIsModalVisible,
  params,
  chartSubjectsOptions,
  value
}) => {
  // Sample data based on the image
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>(['']);
  const [tableData, setTableData] = useState<TableDataItem[]>([]);
  const [gradeName, setGradeName] = useState<string>('');
  const [chartData, setChartData] = useState<any[]>([]);

  const handleChartSubjectChange = (value: string) => {
    if (value === '全部') {
      setSelectedChartSubjects(['语文']);
    } else {
      setSelectedChartSubjects([value]);
    }
  };

  useEffect(() => {
    // 检查所有必要参数是否都有值
    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects &&
      selectedChartSubjects.length > 0;

    if (hasAllParams) {
      getSubjectLevel({
        ...params,
        subjectNames: selectedChartSubjects
      }).then((res) => {
        setChartData(res);
      });
    }
  }, [params, selectedChartSubjects]);

  useEffect(() => {
    if (!chartData || !Array.isArray(chartData)) return;

    // 从第一条数据的班级名称中提取年级信息
    if (chartData.length > 0) {
      const className = chartData[0].className;
      const gradeMatch = className.match(/(.+?)年级/);
      if (gradeMatch) {
        setGradeName(gradeMatch[1]);
      }
    }

    const formattedData: TableDataItem[] = chartData.map((item: any) => {
      const aPlusData = item.levelList.find((level: any) => level.evaluation === 'A+') || {
        studentNumber: 0,
        ratio: 0
      };
      const aData = item.levelList.find((level: any) => level.evaluation === 'A') || {
        studentNumber: 0,
        ratio: 0
      };

      return {
        key: item.index.toString(),
        class: item.className,
        aPlusCount: aPlusData.studentNumber,
        aPlusPercentage: `${aPlusData.ratio}%`,
        aCount: aData.studentNumber,
        aPercentage: `${aData.ratio}%`
      };
    });

    setTableData(formattedData);
  }, [chartData]);

  const handleExport = () => {
    exportSubjectLevel({
      ...params,
      subjectNames: selectedChartSubjects
    }).then((res) => {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(res.data);
      link.download = '学科等级分段.xlsx';
      link.click();
    });
  };

  useEffect(() => {
    console.log('value', value);
    if (value) {
      handleChartSubjectChange(value);
    }
  }, [value]);

  const Title = () => {
    return (
      <Flex justify="space-between" align="center" width="100%">
        <Box fontWeight="bold" fontSize="16px">
          等级分段表格
        </Box>
        <Flex align="center" gap={4}>
          <Select
            style={{ maxWidth: '300px', minWidth: '120px', marginRight: '10px' }}
            placeholder="选择学科"
            options={chartSubjectsOptions}
            value={selectedChartSubjects[0]}
            defaultValue={value}
            onChange={handleChartSubjectChange}
          />
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block' }}
            onClick={handleExport}
          >
            导出
          </Box>
          <CloseOutlined onClick={() => setIsModalVisible(false)} style={{ cursor: 'pointer' }} />
        </Flex>
      </Flex>
    );
  };

  return (
    <Modal
      footer={null}
      open={isModalVisible}
      onCancel={() => setIsModalVisible(false)}
      title={<Title />}
      width={800}
      closeIcon={null}
    >
      <Box bg="#f5f5f5" p={4} mb={4} borderRadius="4px">
        <Box fontWeight="bold" textAlign="center">
          {params.gradeName}各班{selectedChartSubjects[0]}等级分段表格统计表
        </Box>
      </Box>

      <Table
        columns={columns}
        dataSource={tableData}
        pagination={false}
        bordered
        size="middle"
        style={{ marginBottom: '24px' }}
      />
    </Modal>
  );
};

export default LevelScoreDialog;
