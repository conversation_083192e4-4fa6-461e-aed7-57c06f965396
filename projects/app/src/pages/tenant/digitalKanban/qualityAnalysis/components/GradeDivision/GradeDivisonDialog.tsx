import type React from 'react';
import { useEffect, useState, useCallback, useRef } from 'react';
import { Modal, Table, Select } from 'antd';
import { Box, Flex } from '@chakra-ui/react';
import { CloseOutlined } from '@ant-design/icons';
import {
  getGradeDivision,
  getGradeDivisionExport,
  GradeDivisionResponse,
  Section
} from '@/api/kanban';
import type { ColumnType, ColumnGroupType } from 'antd/es/table';
import BoardPane from '@/components/BoardPane';

// 生成分数区间标签的工具函数
export function generateScoreLabel(section: Section): string {
  return `${section.leftSection}${section.leftScore},${section.rightScore}${section.rightSection}`;
}

interface LevelScoreDialogProps {
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  params: any;
  chartSubjectsOptions: any[];
  score: string;
  value: string;
  onClose?: () => void;
}

interface TableDataItem {
  key: string;
  class: string;
  [key: string]: string | number; // 添加动态字段支持
}

// 生成分数区间的列定义
function generateScoreColumns(
  sectionList: Section[]
): (ColumnGroupType<TableDataItem> | ColumnType<TableDataItem>)[] {
  const baseColumn: (ColumnGroupType<TableDataItem> | ColumnType<TableDataItem>)[] = [
    {
      title: '班级/分段',
      dataIndex: 'class',
      key: 'class',
      fixed: 'left' as const,
      width: 80
    }
  ];

  // 按sort降序排序
  const sortedSections = [...sectionList].sort((a, b) => b.sort - a.sort);

  const scoreColumns = sortedSections.map((section) => {
    const title = generateScoreLabel(section);
    const baseKey = `${section.leftScore}_${section.rightScore}`;

    return {
      title,
      children: [
        {
          title: '人数',
          dataIndex: `count_${baseKey}`,
          key: `count_${baseKey}`,
          width: 60
        },
        {
          title: '占比%',
          dataIndex: `percentage_${baseKey}`,
          key: `percentage_${baseKey}`,
          width: 60
        }
      ]
    };
  });

  return [...baseColumn, ...scoreColumns];
}

const GradeDivisonDialog: React.FC<LevelScoreDialogProps> = ({
  isModalVisible,
  setIsModalVisible,
  params,
  chartSubjectsOptions,
  score,
  value,
  onClose
}) => {
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>(['全部']);
  const [tableData, setTableData] = useState<TableDataItem[]>([]);
  const [chartData, setChartData] = useState<any[]>([]);
  const prevParamsRef = useRef<any>(null);
  const prevSubjectsRef = useRef<string[]>(['全部']);
  const isFirstRender = useRef<boolean>(true);
  const boardPaneRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [rawData, setRawData] = useState<GradeDivisionResponse[]>([]);
  const [columns, setColumns] = useState<
    (ColumnGroupType<TableDataItem> | ColumnType<TableDataItem>)[]
  >([]);

  useEffect(() => {}, []);

  const handleChartSubjectChange = (value: string) => {
    if (value === '全部') {
      setSelectedChartSubjects(['全部']);
    } else {
      setSelectedChartSubjects([value]);
    }
  };
  useEffect(() => {
    if (value) {
      handleChartSubjectChange(value);
    }
  }, [value]);
  const fetchGradeDivisionData = useCallback(async () => {
    setLoading(true);
    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects &&
      selectedChartSubjects.length > 0;

    if (hasAllParams) {
      try {
        const res = await getGradeDivision({
          year: params.year,
          term: params.term,
          teacherType: params.teacherType,
          gradeId: params.gradeId,
          gradeName: params.gradeName,
          subjectNames: selectedChartSubjects || ['全部'],
          examType: params.examType,
          examId: params.examId,
          score: parseInt(score)
        });
        setChartData(res);
        setRawData(res);
        setLoading(false);
        if (boardPaneRef.current?.refresh) {
          boardPaneRef.current.refresh();
        }
      } catch (error) {
        console.error('获取等级分段数据失败:', error);
        setLoading(false);
      }
    }
  }, [params, selectedChartSubjects, score]);

  // 检查参数是否发生实质性变化
  const hasParamsChanged = useCallback(() => {
    if (!prevParamsRef.current) return true;

    // 检查params对象的关键属性是否变化
    const keyProps = ['year', 'term', 'gradeId', 'examId', 'examType'];
    for (const key of keyProps) {
      if (params[key] !== prevParamsRef.current[key]) {
        return true;
      }
    }

    // 检查selectedChartSubjects是否变化
    if (
      prevSubjectsRef.current.length !== selectedChartSubjects.length ||
      !prevSubjectsRef.current.every((subject, index) => subject === selectedChartSubjects[index])
    ) {
      return true;
    }

    // 检查score是否变化
    if (prevParamsRef.current.score !== score) {
      return true;
    }

    return false;
  }, [params, selectedChartSubjects, score]);

  useEffect(() => {
    // 首次渲染时不执行，等待初始化完成
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 只有当参数发生实质性变化时才调用API
    if (hasParamsChanged()) {
      fetchGradeDivisionData();

      // 更新引用值以便下次比较
      prevParamsRef.current = { ...params, score };
      prevSubjectsRef.current = [...selectedChartSubjects];
    }
  }, [fetchGradeDivisionData, hasParamsChanged, selectedChartSubjects]);

  // 当对话框打开时加载数据
  useEffect(() => {
    if (isModalVisible && isFirstRender.current === false) {
      fetchGradeDivisionData();
    }
  }, [isModalVisible, fetchGradeDivisionData]);

  useEffect(() => {
    if (rawData && rawData[0]?.sectionList) {
      setColumns(generateScoreColumns(rawData[0].sectionList));
    }
  }, [rawData]);

  useEffect(() => {
    if (!chartData || !Array.isArray(chartData)) return;
    const formattedData: TableDataItem[] = chartData.map((item: any) => {
      const baseData = {
        key: item.index.toString(),
        class: item.className
      };

      // 处理每个分数段的数据
      const scoreData = item.sectionList.reduce((acc: any, section: Section) => {
        const baseKey = `${section.leftScore}_${section.rightScore}`;
        return {
          ...acc,
          [`count_${baseKey}`]: section.studentNumber || 0,
          [`percentage_${baseKey}`]: section.ratio?.toFixed(2) || '0'
        };
      }, {});

      return { ...baseData, ...scoreData };
    });

    setTableData(formattedData);
  }, [chartData]);

  const handleExport = () => {
    getGradeDivisionExport({
      year: params.year,
      term: params.term,
      teacherType: params.teacherType,
      gradeId: params.gradeId,
      gradeName: params.gradeName,
      subjectNames: selectedChartSubjects,
      examType: params.examType,
      examId: params.examId,
      score: parseInt(score)
    }).then((res) => {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(res.data);
      link.download = '学科等级分段.xlsx';
      link.click();
    });
  };

  const handleClose = () => {
    setIsModalVisible(false);
    onClose?.();
  };

  const Title = () => {
    return (
      <Flex justify="space-between" align="center" width="100%">
        <Box fontWeight="bold" fontSize="16px">
          成绩分段表格
        </Box>
        <Flex align="center" gap={4}>
          <Select
            style={{ maxWidth: '300px', minWidth: '120px', marginRight: '10px' }}
            placeholder="选择学科"
            options={chartSubjectsOptions}
            value={selectedChartSubjects[0]}
            onChange={handleChartSubjectChange}
          />
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block' }}
            onClick={handleExport}
          >
            导出
          </Box>
          <CloseOutlined onClick={handleClose} style={{ cursor: 'pointer' }} />
        </Flex>
      </Flex>
    );
  };

  return (
    <Modal
      footer={null}
      open={isModalVisible}
      onCancel={handleClose}
      title={<Title />}
      width={1800}
      closeIcon={null}
    >
      <Box bg="#f5f5f5" p={4} mb={4} borderRadius="4px">
        <Box fontWeight="bold" textAlign="center">
          {params.gradeName}各班{selectedChartSubjects[0]}成绩分段表格统计表
        </Box>
      </Box>

      <Table
        loading={loading}
        columns={columns}
        dataSource={tableData}
        pagination={false}
        bordered
        size="middle"
        style={{ marginBottom: '24px' }}
        scroll={{ x: 'max-content' }}
      />
      <BoardPane
        ref={boardPaneRef}
        location={5}
        height="100px"
        examId={params.examId}
        selectedSubjects={selectedChartSubjects}
        selectedTestData={selectedChartSubjects}
        selectedGradesName={params.gradeName}
        selectedGradesId={params.gradeId}
        editable={true}
        params={{
          academic_year: params.year,
          semester: params.term === 1 ? '第一学期' : '第二学期',
          grade: params.gradeName,
          test_type: params.examType,
          test_name: params.examName,
          subject: selectedChartSubjects.includes('全部')
            ? '全部学科'
            : selectedChartSubjects.join(','),
          data_block_name: '成绩分段对比统计图',
          score_segment_size: parseInt(score),
          data_render_type: '表格',
          data: rawData.map((item: GradeDivisionResponse) => ({
            class_name: item.className,
            score_segments: item.sectionList.map((section: Section) => ({
              score_segment: `[${section.leftScore},${section.rightScore}]`,
              student_number: section.studentNumber
            }))
          })),
          analysis_requirements: '请你对本次测试的各班级的成绩分段人数对比统计进行分析'
        }}
        fullScreenHeight="auto"
        fullScreenoffsetX="40"
        title="结论"
        backgroundColor="#f9f9f9"
        titleFontSize="14px"
        padding="10px"
        showFullScreenButtons
      />
    </Modal>
  );
};

export default GradeDivisonDialog;
