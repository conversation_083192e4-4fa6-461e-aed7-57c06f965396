import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Box, Flex, Image, Input, Text, forwardRef } from '@chakra-ui/react';
import { getSubjectLevel, exportSubjectLevel } from '@/api/kanban';
import { Input as AntdInput, Select, message } from 'antd';
import EChartsReact from 'echarts-for-react';
import NoDataComponent from '../NoDataProps';
import { useRef } from 'react';
import LevelScoreDialog from './LevelScoreDialog';
import html2canvas from 'html2canvas';
import { uploadFile } from '@/api/file';

const buttonStyles = {
  bg: '#fff',
  borderRadius: '20px',
  fontSize: '12px',
  color: '#303133',
  padding: '0px 8px',
  border: '1px solid #f5f5f5',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  h: '28px',
  w: '52px'
};

// 首先定义一些类型
interface LevelItem {
  studentNumber: number;
  ratio: number;
  evaluation: string;
}

interface ChartDataItem {
  index: number;
  className: string;
  subjectName: string;
  levelList: LevelItem[];
}

interface LevelScoreChartProps {
  params: any;
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  chartSubjectsOptions: any[];
  defaultOption?: any; // 改名为 defaultOption
  isAuthority: boolean;
}

export interface LevelScoreChartRef {
  getData: () => void;
  getLevelScoreTableImage: () => Promise<string>;
}

const LevelScoreChart = (
  {
    params,
    selectedSubjects,
    selectedGradesName,
    selectedGradesId,
    chartSubjectsOptions,
    defaultOption, // 改名为 defaultOption
    isAuthority
  }: LevelScoreChartProps,
  ref: any
) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>([]);
  const [value, setValue] = useState<string>('');
  const [isAuthoritys, setIsAuthoritys] = useState(false);
  const [chartData, setChartData] = useState<ChartDataItem[]>([]);
  const [option, setOption] = useState<any>(defaultOption || {});
  const [selectedSubjectOption, setSelectedSubjectOption] = useState<any>([]);

  const echartsRef = useRef<any>(null);

  useEffect(() => {
    if (chartSubjectsOptions.length > 0) {
      const filteredOptions = chartSubjectsOptions.filter((option: any) => option.value !== '全部');
      setSelectedSubjectOption(filteredOptions);
    }
  }, [chartSubjectsOptions]);

  useEffect(() => {
    setIsAuthoritys(isAuthority);
  }, [isAuthority]);

  useEffect(() => {
    if (selectedSubjects.length > 0) {
      setSelectedChartSubjects([selectedSubjectOption[0].value]);
    } else {
      setSelectedChartSubjects([]);
    }
  }, [selectedSubjectOption]);

  const transformChartData = (data: ChartDataItem[]) => {
    if (!data?.length) return {};

    const xAxisData = data.map((item) => item.className);
    const aPlusData = data.map((item) => {
      const aPlusLevel = item.levelList.find((level: LevelItem) => level.evaluation === 'A+');
      return aPlusLevel ? Number(aPlusLevel.ratio.toFixed(1)) : 0;
    });
    const aData = data.map((item) => {
      const aLevel = item.levelList.find((level: LevelItem) => level.evaluation === 'A');
      return aLevel ? Number(aLevel.ratio.toFixed(1)) : 0;
    });

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let result = params[0].name + '<br/>';
          params.forEach(function (item: any) {
            result += item.marker + ' ' + item.seriesName + ': ' + item.data + '%' + '<br/>';
          });
          return result;
        },
        textStyle: {
          align: 'left'
        }
      },
      legend: {
        data: ['A+', 'A'],
        bottom: '3%',
        icon: 'circle'
      },
      grid: {
        left: '7%',
        right: '4%',
        bottom: '15%',
        top: '12%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} %'
        },
        name: '班级人数占比（%）'
      },
      series: [
        {
          name: 'A+',
          type: 'bar',
          data: aPlusData,
          itemStyle: {
            color: '#175DFF'
          },
          barWidth: 12,
          barGap: '30%'
        },
        {
          name: 'A',
          type: 'bar',
          data: aData,
          itemStyle: {
            color: '#14C9C9'
          },
          barWidth: 12
        }
      ]
    };
  };

  const handleExport = () => {
    if (selectedChartSubjects.length <= 0) {
      message.warning('请先选择学科');
      return;
    }
    if (chartData.length <= 0) {
      message.warning('暂无数据');
      return;
    }
    const chartInstance = echartsRef.current?.getEchartsInstance();
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    chartInstance.setOption({
      tooltip: {
        show: false
      }
    });

    // 等待图表更新完成后再导出
    setTimeout(() => {
      // 隐藏加载指示器
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '等级分段.png';
      link.click();

      // 还原初始配置
      chartInstance.setOption({
        tooltip: {
          show: true
        }
      });
    }, 1800); // 延迟以确保图表更新完成

    // exportSubjectLevel({
    //   ...params,
    //   subjectNames: selectedChartSubjects
    // }).then((res) => {
    //   console.log(res);
    //   const link = document.createElement('a');
    //   link.href = URL.createObjectURL(res.data);
    //   link.download = '学科等级分段.xlsx';
    //   link.click();
    //   chartInstance.hideLoading();
    // });
  };
  const resetChartOptions = () => {
    setOption({});
  };

  useEffect(() => {
    if (value) {
      getData();
    }
  }, [value]);

  // useEffect(() => {
  //   setChartData([]);
  //   setOption({});
  // }, [params.gradeName]);

  const handleChartSubjectChange = (value: string) => {
    setSelectedChartSubjects([value]);
    setValue(value);
  };

  const getData = () => {
    // 检查所有必要参数是否都有值
    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects &&
      selectedChartSubjects.length > 0;

    if (!hasAllParams) {
      resetChartOptions();
      return;
    } else {
      setChartData([]);
      setOption({});
    }

    let isSubscribed = true;

    const fetchData = async () => {
      try {
        const res = await getSubjectLevel({
          ...params,
          subjectNames: selectedChartSubjects
        });

        if (!isSubscribed) return;

        setChartData(res);

        if (res.length > 0) {
          const transformedOption = transformChartData(res);
          setOption(transformedOption);
        } else {
          resetChartOptions();
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        resetChartOptions();
      }
    };

    fetchData();

    return () => {
      isSubscribed = false;
    };
  };

  // 生成表格HTML的函数
  const generateLevelScoreTableHTML = (tableData: any[], gradeName: string, subjectName: string) => {
    const tableHTML = `
      <div style="padding: 20px; background: white; font-family: Arial, sans-serif;">
        <div style="background: #f5f5f5; padding: 16px; margin-bottom: 16px; border-radius: 4px;">
          <div style="font-weight: bold; text-align: center; font-size: 16px;">
            ${gradeName}各班${subjectName}等级分段表格统计表
          </div>
        </div>
        <table style="width: 100%; border-collapse: collapse; margin-top: 16px; margin-bottom: 16px;">
          <thead>
            <tr style="background-color: #fafafa;">
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600; width: 25%;">班级</th>
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600;" colspan="2">A+等</th>
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600;" colspan="2">A等</th>
            </tr>
            <tr style="background-color: #fafafa;">
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600; width: 25%;"></th>
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600; width: 12.5%;">人数</th>
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600; width: 12.5%;">比例</th>
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600; width: 12.5%;">人数</th>
              <th style="border: 1px solid #d9d9d9; padding: 8px; text-align: center; font-weight: 600; width: 12.5%;">比例</th>
            </tr>
          </thead>
          <tbody>
            ${tableData.map(row => `
              <tr>
                <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">${row.class}</td>
                <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">${row.aPlusCount}</td>
                <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">${row.aPlusPercentage}</td>
                <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">${row.aCount}</td>
                <td style="border: 1px solid #d9d9d9; padding: 8px; text-align: center;">${row.aPercentage}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;

    return tableHTML;
  };

  // 在不打开弹窗的情况下获取等级分段表格图片
  const getLevelScoreTableImage = async (): Promise<string> => {
    try {
      // 获取表格数据
      const res = await getSubjectLevel({
        ...params,
        subjectNames: selectedChartSubjects.length > 0 ? selectedChartSubjects : [selectedSubjects[0]]
      });

      if (!res || !res.length) {
        throw new Error('没有获取到等级分段表格数据');
      }

      // 从第一条数据的班级名称中提取年级信息
      let gradeName = '';
      if (res.length > 0) {
        const className = res[0].className;
        const gradeMatch = className.match(/(.+?)年级/);
        if (gradeMatch) {
          gradeName = gradeMatch[1];
        }
      }

      // 格式化表格数据
      const formattedData = res.map((item: any) => {
        const aPlusData = item.levelList.find((level: any) => level.evaluation === 'A+') || {
          studentNumber: 0,
          ratio: 0
        };
        const aData = item.levelList.find((level: any) => level.evaluation === 'A') || {
          studentNumber: 0,
          ratio: 0
        };

        return {
          key: item.index.toString(),
          class: item.className,
          aPlusCount: aPlusData.studentNumber,
          aPlusPercentage: `${aPlusData.ratio}%`,
          aCount: aData.studentNumber,
          aPercentage: `${aData.ratio}%`
        };
      });

      // 生成表格HTML
      const subjectName = selectedChartSubjects.length > 0 ? selectedChartSubjects[0] : selectedSubjects[0];
      const tableHTML = generateLevelScoreTableHTML(formattedData, gradeName, subjectName);

      // 创建一个临时的DOM元素来渲染表格
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = tableHTML;
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      document.body.appendChild(tempDiv);

      try {
        // 使用html2canvas将表格转换为canvas
        const canvas = await html2canvas(tempDiv, {
          scale: 2, // 提高清晰度
          backgroundColor: '#ffffff',
          logging: false,
          useCORS: true
        });

        // 将canvas转换为blob数据
        const blob = await new Promise<Blob>((resolve) => {
          canvas.toBlob((blob) => {
            if (blob) resolve(blob);
          }, 'image/png', 1.0);
        });

        // 创建文件对象
        const file = new File([blob], `${gradeName}各班${subjectName}等级分段表格统计表.png`, { type: 'image/png' });

        // 上传文件到服务器
        const formData = new FormData();
        formData.append('file', file);

        const response = await uploadFile(formData);

        if (response && response.fileUrl) {
          return response.fileUrl;
        } else {
          throw new Error('上传等级分段表格图片失败');
        }
      } finally {
        // 清理临时DOM元素
        document.body.removeChild(tempDiv);
      }
    } catch (error) {
      console.error('生成等级分段表格图片失败:', error);
      throw error;
    }
  };

  useImperativeHandle(ref, () => ({
    getData,
    getLevelScoreTableImage
  }));

  return (
    <Box>
      <LevelScoreDialog
        isModalVisible={isModalVisible}
        setIsModalVisible={setIsModalVisible}
        params={params}
        chartSubjectsOptions={chartSubjectsOptions}
        value={selectedChartSubjects[0]}
      />
      <Box display="flex">
        <Box
          color="#1d2129"
          fontSize="16px"
          fontWeight="500"
          fontStyle="normal"
          width="calc(30% - 12px)"
        >
          等级分段
        </Box>
        <Select
          style={{ maxWidth: '300px', minWidth: '120px', marginRight: '10px' }}
          placeholder="选择学科"
          options={selectedSubjectOption}
          value={selectedChartSubjects[0]}
          onChange={handleChartSubjectChange}
          mode={undefined}
        />
        <Box
          as="button"
          borderRadius="4px"
          cursor="pointer"
          width="60px"
          height="28px"
          fontSize="14px"
          background="#f7f8fa"
          style={{ display: 'inline-block' }}
          onClick={handleExport}
          color={'#636C7B'}
        >
          导出
        </Box>
        <Box
          as="button"
          borderRadius="4px"
          cursor="pointer"
          width="80px"
          height="28px"
          fontSize="14px"
          background="#f7f8fa"
          style={{ display: 'inline-block', marginLeft: '10px' }}
          onClick={() => setIsModalVisible(true)}
          color={'#636C7B'}
        >
          查看详情
        </Box>
      </Box>
      {Object.keys(option).length > 0 ? (
        <EChartsReact
          ref={echartsRef}
          option={option}
          style={{
            borderRadius: '12px',
            height: '400px'
          }}
        />
      ) : (
        <NoDataComponent type={isAuthoritys ? 'noPermission' : 'noData'} />
      )}
    </Box>
  );
};

export default forwardRef(LevelScoreChart);
