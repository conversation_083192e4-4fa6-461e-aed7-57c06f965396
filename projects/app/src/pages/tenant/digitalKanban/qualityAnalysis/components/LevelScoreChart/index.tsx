import React, { useState, useEffect, useImperativeHandle } from 'react';
import { Box, Flex, Image, Input, Text, forwardRef } from '@chakra-ui/react';
import { getSubjectLevel, exportSubjectLevel } from '@/api/kanban';
import { Input as AntdInput, Select, message } from 'antd';
import EChartsReact from 'echarts-for-react';
import NoDataComponent from '../NoDataProps';
import { useRef } from 'react';
import LevelScoreDialog from './LevelScoreDialog';

const buttonStyles = {
  bg: '#fff',
  borderRadius: '20px',
  fontSize: '12px',
  color: '#303133',
  padding: '0px 8px',
  border: '1px solid #f5f5f5',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  h: '28px',
  w: '52px'
};

// 首先定义一些类型
interface LevelItem {
  studentNumber: number;
  ratio: number;
  evaluation: string;
}

interface ChartDataItem {
  index: number;
  className: string;
  subjectName: string;
  levelList: LevelItem[];
}

interface LevelScoreChartProps {
  params: any;
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  chartSubjectsOptions: any[];
  defaultOption?: any; // 改名为 defaultOption
  isAuthority: boolean;
}

export interface LevelScoreChartRef {
  getData: () => void;
}

const LevelScoreChart = (
  {
    params,
    selectedSubjects,
    selectedGradesName,
    selectedGradesId,
    chartSubjectsOptions,
    defaultOption, // 改名为 defaultOption
    isAuthority
  }: LevelScoreChartProps,
  ref: any
) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedChartSubjects, setSelectedChartSubjects] = useState<string[]>([]);
  const [value, setValue] = useState<string>('');
  const [isAuthoritys, setIsAuthoritys] = useState(false);
  const [chartData, setChartData] = useState<ChartDataItem[]>([]);
  const [option, setOption] = useState<any>(defaultOption || {});
  const [selectedSubjectOption, setSelectedSubjectOption] = useState<any>([]);

  const echartsRef = useRef<any>(null);

  useEffect(() => {
    if (chartSubjectsOptions.length > 0) {
      const filteredOptions = chartSubjectsOptions.filter((option: any) => option.value !== '全部');
      setSelectedSubjectOption(filteredOptions);
    }
  }, [chartSubjectsOptions]);

  useEffect(() => {
    setIsAuthoritys(isAuthority);
  }, [isAuthority]);

  useEffect(() => {
    if (selectedSubjects.length > 0) {
      setSelectedChartSubjects([selectedSubjectOption[0].value]);
    } else {
      setSelectedChartSubjects([]);
    }
  }, [selectedSubjectOption]);

  const transformChartData = (data: ChartDataItem[]) => {
    if (!data?.length) return {};

    const xAxisData = data.map((item) => item.className);
    const aPlusData = data.map((item) => {
      const aPlusLevel = item.levelList.find((level: LevelItem) => level.evaluation === 'A+');
      return aPlusLevel ? Number(aPlusLevel.ratio.toFixed(1)) : 0;
    });
    const aData = data.map((item) => {
      const aLevel = item.levelList.find((level: LevelItem) => level.evaluation === 'A');
      return aLevel ? Number(aLevel.ratio.toFixed(1)) : 0;
    });

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let result = params[0].name + '<br/>';
          params.forEach(function (item: any) {
            result += item.marker + ' ' + item.seriesName + ': ' + item.data + '%' + '<br/>';
          });
          return result;
        },
        textStyle: {
          align: 'left'
        }
      },
      legend: {
        data: ['A+', 'A'],
        bottom: '3%',
        icon: 'circle'
      },
      grid: {
        left: '7%',
        right: '4%',
        bottom: '15%',
        top: '12%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} %'
        },
        name: '班级人数占比（%）'
      },
      series: [
        {
          name: 'A+',
          type: 'bar',
          data: aPlusData,
          itemStyle: {
            color: '#175DFF'
          },
          barWidth: 12,
          barGap: '30%'
        },
        {
          name: 'A',
          type: 'bar',
          data: aData,
          itemStyle: {
            color: '#14C9C9'
          },
          barWidth: 12
        }
      ]
    };
  };

  const handleExport = () => {
    if (selectedChartSubjects.length <= 0) {
      message.warning('请先选择学科');
      return;
    }
    if (chartData.length <= 0) {
      message.warning('暂无数据');
      return;
    }
    const chartInstance = echartsRef.current?.getEchartsInstance();
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    chartInstance.setOption({
      tooltip: {
        show: false
      }
    });

    // 等待图表更新完成后再导出
    setTimeout(() => {
      // 隐藏加载指示器
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '等级分段.png';
      link.click();

      // 还原初始配置
      chartInstance.setOption({
        tooltip: {
          show: true
        }
      });
    }, 1800); // 延迟以确保图表更新完成

    // exportSubjectLevel({
    //   ...params,
    //   subjectNames: selectedChartSubjects
    // }).then((res) => {
    //   console.log(res);
    //   const link = document.createElement('a');
    //   link.href = URL.createObjectURL(res.data);
    //   link.download = '学科等级分段.xlsx';
    //   link.click();
    //   chartInstance.hideLoading();
    // });
  };
  const resetChartOptions = () => {
    setOption({});
  };

  useEffect(() => {
    if (value) {
      getData();
    }
  }, [value]);

  // useEffect(() => {
  //   setChartData([]);
  //   setOption({});
  // }, [params.gradeName]);

  const handleChartSubjectChange = (value: string) => {
    setSelectedChartSubjects([value]);
    setValue(value);
  };

  const getData = () => {
    // 检查所有必要参数是否都有值
    const hasAllParams =
      params &&
      Object.values(params).every(
        (value) => value !== undefined && value !== null && value !== ''
      ) &&
      selectedChartSubjects &&
      selectedChartSubjects.length > 0;

    if (!hasAllParams) {
      resetChartOptions();
      return;
    } else {
      setChartData([]);
      setOption({});
    }

    let isSubscribed = true;

    const fetchData = async () => {
      try {
        const res = await getSubjectLevel({
          ...params,
          subjectNames: selectedChartSubjects
        });

        if (!isSubscribed) return;

        setChartData(res);

        if (res.length > 0) {
          const transformedOption = transformChartData(res);
          setOption(transformedOption);
        } else {
          resetChartOptions();
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        resetChartOptions();
      }
    };

    fetchData();

    return () => {
      isSubscribed = false;
    };
  };

  useImperativeHandle(ref, () => ({
    getData
  }));

  return (
    <Box>
      <LevelScoreDialog
        isModalVisible={isModalVisible}
        setIsModalVisible={setIsModalVisible}
        params={params}
        chartSubjectsOptions={chartSubjectsOptions}
        value={selectedChartSubjects[0]}
      />
      <Box display="flex">
        <Box
          color="#1d2129"
          fontSize="16px"
          fontWeight="500"
          fontStyle="normal"
          width="calc(30% - 12px)"
        >
          等级分段
        </Box>
        <Select
          style={{ maxWidth: '300px', minWidth: '120px', marginRight: '10px' }}
          placeholder="选择学科"
          options={selectedSubjectOption}
          value={selectedChartSubjects[0]}
          onChange={handleChartSubjectChange}
          mode={undefined}
        />
        <Box
          as="button"
          borderRadius="4px"
          cursor="pointer"
          width="60px"
          height="28px"
          fontSize="14px"
          background="#f7f8fa"
          style={{ display: 'inline-block' }}
          onClick={handleExport}
          color={'#636C7B'}
        >
          导出
        </Box>
        <Box
          as="button"
          borderRadius="4px"
          cursor="pointer"
          width="80px"
          height="28px"
          fontSize="14px"
          background="#f7f8fa"
          style={{ display: 'inline-block', marginLeft: '10px' }}
          onClick={() => setIsModalVisible(true)}
          color={'#636C7B'}
        >
          查看详情
        </Box>
      </Box>
      {Object.keys(option).length > 0 ? (
        <EChartsReact
          ref={echartsRef}
          option={option}
          style={{
            borderRadius: '12px',
            height: '400px'
          }}
        />
      ) : (
        <NoDataComponent type={isAuthoritys ? 'noPermission' : 'noData'} />
      )}
    </Box>
  );
};

export default forwardRef(LevelScoreChart);
