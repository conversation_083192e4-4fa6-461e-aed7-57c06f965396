import React, { useState, useEffect, useRef } from 'react';
import { Button, Select, Table } from 'antd';
import MyModal from '@/components/MyModal';
import { Box, ModalBody, Flex } from '@chakra-ui/react';
import { useUserStore } from '@/store/useUserStore';
import {
  getRankDivision,
  rankDivisionTableExport
} from '@/api/tenant/digitalKanban/qualityAnalysis';
import { RankDivisionModalType } from '@/types/api/tenant/digitalKanban/qualityAnalysis/compoents/RankDivisionModal';

interface RankDivisionModalProps {
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  subjectOptions: any;
  selectedYear: string;
  selectedTerm: number;
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  selectedTestData: string[];
  startDate: string;
  endDate: string;
  chartOptions: any;
  isAuthority: boolean;
  onClose: () => void;
  selectRankDivisionValue: string[];
}

const RankDivisionModal: React.FC<RankDivisionModalProps> = ({
  isModalVisible,
  isAuthority,
  setIsModalVisible,
  subjectOptions,
  selectedYear,
  selectedTerm,
  selectedSubjects,
  selectedGradesName,
  selectedGradesId,
  selectedTestData,
  startDate,
  endDate,
  chartOptions,
  selectRankDivisionValue,
  onClose
}) => {
  const [data, setData] = useState<RankDivisionModalType[]>([]);
  const [selectedSubjectsState, setSelectedSubjectsState] = useState<string>();
  const { teacherType } = useUserStore();
  const [viewMode, setViewMode] = useState<'table' | 'chart'>('table');
  const [isLoading, setIsLoading] = useState(false);
  const [chartHeight, setChartHeight] = useState<string>('');
  const handleCancel = () => {
    setIsModalVisible(false);
    onClose();
  };

  const handleExport = async () => {
    try {
      const response = await rankDivisionTableExport({
        year: selectedYear,
        term: selectedTerm,
        teacherType: teacherType,
        gradeId: selectedGradesId,
        gradeName: selectedGradesName,
        subjectNames: selectedSubjectsState ? [selectedSubjectsState] : selectedSubjects,
        examType: selectedTestData?.length ? selectedTestData[0] : '',
        examId: selectedTestData?.[1] || ''
      });
      const blob = new Blob([response.data as any], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${selectedGradesName}各班数据汇总统计表.xlsx`);
      document.body.appendChild(link);
      link.click();
      link?.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  useEffect(() => {
    if (isModalVisible) {
      const parasms = {
        year: selectedYear,
        term: selectedTerm,
        startDate: startDate,
        endDate: endDate,
        subjectNames: selectedSubjectsState ? [selectedSubjectsState] : selectedSubjects,
        gradeName: selectedGradesName,
        gradeId: selectedGradesId,
        examType: selectedTestData?.[0] || '',
        teacherType: teacherType,
        examId: selectedTestData?.[1] || ''
      };

      getRankDivision(parasms)
        .then((res) => {
          // 将数据转换为表格格式
          const tableData = res.map((item) => ({
            className: item.className,
            rank10: item.rankSectionList[0].studentNumber,
            rank50: item.rankSectionList[1].studentNumber,
            rank100: item.rankSectionList[2].studentNumber,
            rank200: item.rankSectionList[3].studentNumber
          }));
          setData(tableData);
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
        });
    }
  }, [
    isModalVisible,
    selectedSubjectsState,
    selectedYear,
    selectedTerm,
    startDate,
    endDate,
    selectedSubjects,
    selectedGradesName,
    selectedGradesId,
    selectedTestData,
    teacherType
  ]);

  const columns = [
    { title: '', dataIndex: 'className', key: 'className' },
    { title: '前10名', dataIndex: 'rank10', key: 'rank10' },
    { title: '前50名', dataIndex: 'rank50', key: 'rank50' },
    { title: '前100名', dataIndex: 'rank100', key: 'rank100' },
    { title: '前200名', dataIndex: 'rank200', key: 'rank200' }
  ];

  // 外面的下拉框改变，里面的下拉框跟着改变
  useEffect(() => {
    setSelectedSubjectsState(selectRankDivisionValue[0]);
  }, [selectRankDivisionValue]);

  return (
    <MyModal
      isOpen={isModalVisible}
      onClose={handleCancel}
      isCentered
      minW={chartHeight}
      w="80vw"
      h="800px"
      title={
        <Box
          style={{
            width: '100%',
            height: '50px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <span>各名次段人数对比</span>
          <Box display="flex" alignItems="center">
            <Select
              options={subjectOptions}
              style={{ width: '200px', marginRight: '16px' }}
              value={selectedSubjectsState}
              onChange={(value) => {
                setSelectedSubjectsState(value);
              }}
              placeholder="请选择科目"
              dropdownStyle={{ zIndex: 9999 }}
            />
            <Button onClick={handleExport}>导出</Button>
          </Box>
        </Box>
      }
    >
      <ModalBody mt="16px">
        {viewMode === 'table' && (
          <h2 style={{ textAlign: 'center', marginBottom: '16px' }}>
            {selectedGradesName}
            {selectedSubjectsState}各班数据汇总统计表
          </h2>
        )}
        <Box>
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            style={{ marginTop: '16px', marginBottom: '16px' }}
            rowKey="className"
          />
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default RankDivisionModal;
