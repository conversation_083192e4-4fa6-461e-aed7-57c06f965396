import { Box } from '@chakra-ui/react';
import { Select, SelectProps } from 'antd';
import EChartsReact from 'echarts-for-react';
import BoardPane from '@/components/BoardPane';
import React, {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import { RankDivisionType } from '@/types/api/tenant/digitalKanban/qualityAnalysis';
import { getRankDivision } from '@/api/tenant/digitalKanban/qualityAnalysis';
import RankDivisionModal from '@/pages/tenant/digitalKanban/qualityAnalysis/components/RankDivisionModal';
import NoDataComponent from '../NoDataProps';
import { useQuery } from '@tanstack/react-query';
import { getConclusion } from '@/api/kanban';
import { uploadFile } from '@/api/file';

interface RankDivisionProps {
  chartSubjectsOptions: any[];
  boardPaneRef: React.RefObject<any>;
  selectedTestData: any[];
  selectedSubjects: string[];
  selectedGradesName: string;
  selectedGradesId: string;
  examSummary: any;
  selectedYear: string;
  selectedTerm: number;
  teacherType: number;
  isAuthority: boolean;
  chartOptions: any;
  startDate: string;
  endDate: string;
  data: RankDivisionType;
  title: string;
}

export interface RankDivisionRef {
  getRankDivisionChartImage: () => Promise<string>;
  getRankDivisionBoardPaneContent: () => Promise<string>;
}

function RankDivision(props: RankDivisionProps, ref: React.Ref<RankDivisionRef>) {
  const {
    chartSubjectsOptions,
    boardPaneRef,
    selectedTestData,
    selectedSubjects,
    selectedGradesName,
    selectedGradesId,
    examSummary,
    selectedYear,
    selectedTerm,
    teacherType,
    isAuthority,
    chartOptions,
    startDate,
    endDate,
    data
  } = props;
  const [rankDivision, setRankDivision] = useState<RankDivisionType>([]);
  // 各名次段人数--- 选中的学科
  const [selectRankDivisionValue, setSelectRankDivisionValue] = useState<SelectProps['value']>([
    '全部'
  ]);
  const rankDivisionEchartsRef = useRef<any>();
  // 提取班级名称作为 x 轴数据
  const xAxisData = Array.isArray(rankDivision) ? rankDivision?.map((item) => item.className) : [];

  // 选择学科
  const [selectSubjectOptions, setSelectSubjectOptions] = useState<any>([]);

  useEffect(() => {
    setSelectSubjectOptions(chartSubjectsOptions);
  }, [chartSubjectsOptions]);

  // 提取各名次段的学生人数数据
  const rank10Data = Array.isArray(rankDivision)
    ? rankDivision.map(
      (item) => item.rankSectionList.find((rank) => rank.rank === 10)?.studentNumber || 0
    )
    : [];
  const rank50Data = Array.isArray(rankDivision)
    ? rankDivision.map(
      (item) => item.rankSectionList.find((rank) => rank.rank === 50)?.studentNumber || 0
    )
    : [];
  const rank100Data = Array.isArray(rankDivision)
    ? rankDivision.map(
      (item) => item.rankSectionList.find((rank) => rank.rank === 100)?.studentNumber || 0
    )
    : [];
  const rank200Data = Array.isArray(rankDivision)
    ? rankDivision.map(
      (item) => item.rankSectionList.find((rank) => rank.rank === 200)?.studentNumber || 0
    )
    : [];
  const rankDivisionChart = {
    title: {
      text: '',
      left: 'left'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';
        params.forEach(function (item: any) {
          result += item.marker + ' ' + item.seriesName + ': ' + item.data + '人' + '<br/>';
        });
        return result;
      },
      textStyle: {
        align: 'left'
      }
    },
    legend: {
      data: ['前10名', '前50名', '前100名', '前200名'],
      bottom: '0%',
      icon: 'circle'
    },
    grid: {
      top: '10%',
      left: '0%',
      right: '0%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}'
      },
      name: '人数'
    },
    series: [
      {
        name: '前10名',
        type: 'bar',
        data: rank10Data,
        itemStyle: {
          color: '#175DFF'
        },
        barWidth: 11,
        barGap: 0
      },
      {
        name: '前50名',
        type: 'bar',
        data: rank50Data,
        itemStyle: {
          color: '#14C9C9'
        },
        barWidth: 11
      },
      {
        name: '前100名',
        type: 'bar',
        data: rank100Data,
        itemStyle: {
          color: '#34A7FA'
        },
        barWidth: 11
      },
      {
        name: '前200名',
        type: 'bar',
        data: rank200Data,
        itemStyle: {
          color: '#F7BA1E'
        },
        barWidth: 11
      }
    ]
  };

  const handleSelectRankDivisionValueChange = (value: string) => {
    setSelectRankDivisionValue([value]);
    getRankDivisionData();
  };

  useEffect(() => {
    // 如果是多选则默认选择第一个
    if (selectedSubjects.length >= 1) {
      setSelectRankDivisionValue([selectedSubjects[0]]);
    }
    // 如果顶部筛选科目为空则清空选择
    if (selectedSubjects.length === 0) {
      setSelectRankDivisionValue([]);
    }
  }, [selectedSubjects]);

  // 点击查询之后渲染数据
  useEffect(() => {
    if (data.length > 0) {
      setRankDivision(data);
    }
  }, [data]);

  const handleRankDivisionExport = () => {
    if (!rankDivisionEchartsRef.current?.getEchartsInstance()) {
      return;
    }

    const chartInstance = rankDivisionEchartsRef.current?.getEchartsInstance();

    // 显示加载指示器
    chartInstance.showLoading('default', {
      text: '导出中...',
      color: '#175DFF',
      textColor: '#000',
      maskColor: '#fff', // 加深透明度
      zlevel: 0
    });

    // 设置 dataZoom 以显示完整图表
    chartInstance.setOption({
      tooltip: {
        show: false
      },
      xAxis: {
        axisLabel: {
          // rotate: 30 // 增加旋转角度以减少重叠
        }
      }
    });

    // 等待图表更新完成后再导出
    setTimeout(() => {
      // 隐藏加载指示器
      chartInstance.hideLoading();

      const imgData = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2, // 提高分辨率
        backgroundColor: '#fff'
      });

      const link = document.createElement('a');
      link.href = imgData;
      link.download = '各名次段人数对比.png';
      link.click();

      // 还原初始配置
      chartInstance.setOption(rankDivisionChart);
    }, 1800); // 延迟以确保图表更新完成
  };
  const [isRankDivsion, setIsRankDivsion] = useState<boolean>(false);
  // 仅在组件第一次挂载时，使用父组件初始 props 保存到 ref 中
  const initialParams = useRef({
    year: selectedYear,
    term: selectedTerm,
    teacherType: teacherType,
    gradeId: selectedGradesId,
    gradeName: selectedGradesName,
    subjectNames: selectedSubjects.length > 0 ? [selectedSubjects[0]] : [],
    examType: selectedTestData?.[0] || '',
    examId: selectedTestData?.[1]
  });

  // state 用于标识是否已获取到有效的初始化数据
  const [hasFetched, setHasFetched] = useState(false);

  // 当父组件传入 props 满足条件时，更新 initialParams，并标记 hasFetched 为 true（仅一次）
  useEffect(() => {
    if (
      !hasFetched &&
      selectedYear !== '' &&
      selectedGradesName !== '' &&
      teacherType !== undefined &&
      teacherType !== null &&
      selectedTestData?.[0] !== '' &&
      selectedTestData?.[1] !== '' &&
      selectedSubjects.length > 0
    ) {
      initialParams.current = {
        year: selectedYear,
        term: selectedTerm,
        teacherType: teacherType,
        gradeId: selectedGradesId,
        gradeName: selectedGradesName,
        subjectNames: [selectedSubjects[0]],
        examType: selectedTestData[0],
        examId: selectedTestData[1]
      };
      setHasFetched(true);
    }
  }, [
    hasFetched,
    selectedYear,
    selectedTerm,
    teacherType,
    selectedGradesId,
    selectedGradesName,
    selectedTestData,
    selectedSubjects
  ]);

  const { data: onceGetRankDivision } = useQuery(
    ['onceGetRankDivision'],
    async () => {
      const res = await getRankDivision(initialParams.current);
      setRankDivision(res);
    },
    {
      staleTime: Infinity,
      refetchOnWindowFocus: false,
      enabled: hasFetched
    }
  );

  const getRankDivisionData = useCallback(
    async (value?: string[]) => {
      try {
        const res = await getRankDivision({
          year: selectedYear,
          term: selectedTerm,
          teacherType: teacherType,
          gradeId: selectedGradesId,
          gradeName: selectedGradesName,
          subjectNames: selectRankDivisionValue,
          examType: selectedTestData?.[0] || '',
          examId: selectedTestData?.[1]
        });
        setRankDivision(res);
      } catch (error) {
        console.log(error);
      }
    },
    [
      selectedYear,
      selectedTerm,
      teacherType,
      selectedGradesId,
      selectedGradesName,
      selectRankDivisionValue,
      selectedTestData
    ]
  );

  const boardPaneParams = {
    academic_year: selectedYear,
    semester: selectedTerm === 1 ? '第一学期' : '第二学期',
    grade: selectedGradesName,
    test_type: selectedTestData?.[0] || '',
    test_name: '期末质量分析',
    subject: selectRankDivisionValue[0] === '全部' ? '全部科目' : selectRankDivisionValue[0],
    // subject: '全部科目',
    data_block_name: '各名次段人数对比',
    data_render_type: '柱状图',
    data: rankDivisionConversion(rankDivision),
    analysis_requirements: '请你对本次测试的各班级各名次段人数对比进行分析'
  };

  /** 数据格式转换 */
  function rankDivisionConversion(data: typeof rankDivision) {
    const result = data?.map((item) => {
      return {
        class_name: item.className,
        top10_in_grade_head_count: item.rankSectionList[0].studentNumber,
        top50_in_grade_head_count: item.rankSectionList[1].studentNumber,
        top100_in_grade_head_count: item.rankSectionList[2].studentNumber,
        top200_in_grade_head_count: item.rankSectionList[3].studentNumber
      };
    });
    return result;
  }

  // 获取各名次段人数对比图表图片
  const getRankDivisionChartImage = async (): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      if (!rankDivisionEchartsRef.current?.getEchartsInstance()) {
        reject(new Error('各名次段人数对比图表实例不存在'));
        return;
      }

      const chartInstance = rankDivisionEchartsRef.current.getEchartsInstance();

      try {
        // 设置图表选项以优化导出
        chartInstance.setOption({
          tooltip: {
            show: false
          }
        });

        // 等待图表更新完成后再导出
        setTimeout(async () => {
          try {
            const imgData = chartInstance.getDataURL({
              type: 'png',
              pixelRatio: 2, // 提高分辨率
              backgroundColor: '#fff'
            });

            // 还原初始配置
            chartInstance.setOption({
              tooltip: {
                show: true
              }
            });

            // 将Base64转换为Blob
            const base64Data = imgData.split(',')[1];
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });

            // 创建文件对象
            const file = new File([blob], `${selectedGradesName}各名次段人数对比图表.png`, { type: 'image/png' });

            // 上传文件到服务器
            const formData = new FormData();
            formData.append('file', file);

            try {
              const response = await uploadFile(formData);
              if (response && response.fileUrl) {
                resolve(response.fileUrl);
              } else {
                reject(new Error('上传各名次段人数对比图表图片失败'));
              }
            } catch (uploadError) {
              reject(uploadError);
            }
          } catch (error) {
            reject(error);
          }
        }, 500); // 延迟以确保图表更新完成
      } catch (error) {
        reject(error);
      }
    });
  };

  // 获取各名次段人数对比BoardPane内容
  const getRankDivisionBoardPaneContent = async (): Promise<string> => {
    try {
      const conclusionParams = {
        examId: selectedTestData?.[1] || '',
        gradeId: selectedGradesId || '',
        gradeName: selectedGradesName || '',
        subjectNames: selectRankDivisionValue || []
      };

      const conclusions = await getConclusion(conclusionParams);
      const resData = conclusions.find((item: any) => item.location === 7); // location 7 是各名次段人数对比结论

      return resData ? resData.conclusion : '';
    } catch (error) {
      console.error('获取各名次段人数对比BoardPane内容失败:', error);
      return '';
    }
  };

  useImperativeHandle(ref, () => ({
    getRankDivisionChartImage,
    getRankDivisionBoardPaneContent
  }));

  return (
    <>
      <Box
        width="calc(38% - 12px)"
        backgroundColor="#fff"
        marginRight="12px"
        padding="16px 16px 20px 16px"
        borderRadius="12px"
        transition="box-shadow 0.3s ease"
        position="relative"
      >
        <Box display={'flex'}>
          <Box color="#1d2129" fontSize="16px" fontWeight="500" fontStyle="normal">
            各名次段人数对比
          </Box>
          <Select
            style={{
              maxWidth: '300px',
              minWidth: '120px',
              marginRight: '10px',
              marginLeft: 'auto'
            }}
            placeholder="选择学科"
            options={selectSubjectOptions}
            value={selectRankDivisionValue}
            onChange={handleSelectRankDivisionValueChange}
          />
          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="60px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block' }}
            onClick={handleRankDivisionExport}
            color={'#636C7B'}
          >
            导出
          </Box>

          <Box
            as="button"
            borderRadius="4px"
            cursor="pointer"
            width="80px"
            height="28px"
            fontSize="14px"
            background="#f7f8fa"
            style={{ display: 'inline-block', marginLeft: '10px' }}
            onClick={() => setIsRankDivsion(true)}
            color={'#636C7B'}
          >
            查看表格
          </Box>
        </Box>
        <>
          {Object.keys(chartOptions).length > 0 ? (
            <>
              <Box
                flex="1"
                backgroundColor="#fff"
                marginRight="12px"
                borderRadius="12px"
                transition="box-shadow 0.3s ease"
                position="relative"
              >
                <EChartsReact
                  ref={rankDivisionEchartsRef}
                  option={rankDivisionChart}
                  style={{ borderRadius: '12px', marginBottom: '14px' }}
                />
                <Box position={'relative'}>
                  <BoardPane
                    ref={boardPaneRef}
                    location={7}
                    height="100px"
                    examId={selectedTestData?.[1] || ''}
                    selectedSubjects={selectRankDivisionValue}
                    selectedTestData={selectedTestData}
                    selectedGradesName={selectedGradesName}
                    selectedGradesId={selectedGradesId}
                    editable={true}
                    params={boardPaneParams}
                    fullScreenHeight="auto"
                    fullScreenoffsetX="40"
                    title="结论"
                    backgroundColor="#f9f9f9"
                    titleFontSize="14px"
                    padding="10px"
                    showFullScreenButtons
                  />
                </Box>
              </Box>
            </>
          ) : (
            <NoDataComponent type={isAuthority ? 'noPermission' : 'noData'} />
          )}
        </>
      </Box>

      <RankDivisionModal
        isAuthority={isAuthority}
        chartOptions={chartOptions}
        subjectOptions={chartSubjectsOptions}
        isModalVisible={isRankDivsion}
        setIsModalVisible={setIsRankDivsion}
        selectedYear={selectedYear}
        selectedTerm={selectedTerm}
        startDate={startDate}
        endDate={endDate}
        selectedSubjects={selectedSubjects}
        selectedTestData={selectedTestData}
        selectedGradesName={selectedGradesName}
        selectedGradesId={selectedGradesId}
        selectRankDivisionValue={selectRankDivisionValue}
        onClose={() => { }}
      />
    </>
  );
}

export default memo(forwardRef(RankDivision));
