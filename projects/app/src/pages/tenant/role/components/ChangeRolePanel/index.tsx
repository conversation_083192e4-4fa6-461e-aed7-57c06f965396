import { Box, FormControl, FormLabel, Flex, Button, ModalBody } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useEffect, useState } from 'react';
import { Select } from 'antd';
import { useToast } from '@/hooks/useToast';
import MyModal from '@/components/MyModal';
import { roleUpdateUser, getRoleList } from '@/api/tenant';

interface FormData {
  username: string;
  avatar?: string;
  avatarUrl?: string;
  roleId: string;
  phone?: string;
  password?: string;
}

const ChangeRolePanel = ({
  modalId,
  onClose
}: {
  modalId: string;
  onClose: (submited: boolean, modalId?: string) => void;
}) => {
  const { toast } = useToast();
  const [, setRefresh] = useState(false);
  const [roleId, setRoleId] = useState<number | undefined>(undefined);
  const [roleIdOptions, setRoleIdOptions] = useState<{ label: string; value: string }[]>([]);

  const {
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: async (data) => {
      // 过滤掉值为空的属性
      data = Object.fromEntries(
        Object.entries(data).filter(
          ([key, value]) => value !== undefined && value !== null && value !== ''
        )
      );

      if (modalId) {
        try {
          const res = await roleUpdateUser({ oldRoleId: modalId, roleId: data.roleId });

          toast({
            title: '操作成功',
            status: 'success'
          });
        } catch (error) {
          toast({
            title: '操作失败',
            status: 'error'
          });
        }
      }
    },
    onSuccess() {
      onClose(true);
    }
  });

  useEffect(() => {
    if (modalId) {
      // 获取角色列表
      getRoleList(modalId).then((res) => {
        const options = res.map((role: { id: string; name: string }) => ({
          label: role.name,
          value: role.id
        }));
        setRoleIdOptions(options);
      });
    }
  }, [modalId]);

  return (
    <MyModal isOpen={true} title="删除提示">
      <ModalBody>
        <Box>当前角色有用户关联，如需删除请配置更换当前用户的角色</Box>
        <Box p="20px">
          <FormControl mt="14px">
            <Flex
              alignItems="center"
              whiteSpace="nowrap"
              justifyContent="end"
              css={{
                '& .ant-select-selector': {
                  borderRadius: '2px'
                }
              }}
            >
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  更换角色
                </Box>
              </FormLabel>
              <Select
                style={{ width: '400px', height: '40px' }}
                onChange={(val) => {
                  setRoleId(val);
                  setValue('roleId', String(val));
                }}
                value={roleId}
                placeholder="请选择角色"
                options={roleIdOptions}
                dropdownStyle={{ zIndex: 9999 }}
              />
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex margin="auto">
                <Button
                  h="36px"
                  mr="24px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit as any)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>

                <Button
                  borderColor="#0052D9"
                  variant="outline"
                  h="36px"
                  color="#1A5EFF"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default ChangeRolePanel;
