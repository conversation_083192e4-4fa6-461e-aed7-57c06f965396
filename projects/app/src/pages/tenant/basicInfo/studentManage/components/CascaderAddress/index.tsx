import React, { useState, useEffect } from 'react';
import { Cascader } from 'antd';
import {
  getSystemRegionSelect,
  getSystemRegionLazyList
} from '@/api/tenant/teamManagement/student';
import {
  SystemRegionSelectType,
  SystemRegionSelectItem
} from '@/types/api/tenant/teamManagement/student';
import styles from '../../student.module.scss';

interface InitialValueType {
  provinceCode?: string;
  provinceName?: string;
  cityCode?: string;
  cityName?: string;
  districtCode?: string;
  districtName?: string;
}

interface CascaderAddressProps {
  onChange: (value: {
    provinceCode: string;
    provinceName: string;
    cityCode: string;
    cityName: string;
    districtCode: string;
    districtName: string;
    mode: string;
  }) => void;
  initialValue?: InitialValueType;
  mode?: string;
}

const CascaderAddress: React.FC<CascaderAddressProps> = ({ onChange, initialValue, mode }) => {
  const [options, setOptions] = useState<SystemRegionSelectItem[]>([]);
  const [loading, setLoading] = useState(false);

  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  useEffect(() => {
    if (initialValue && initialValue.provinceCode) {
      const newSelectedOptions = [
        initialValue.provinceName,
        initialValue.cityName,
        initialValue.districtName
      ].filter((option): option is string => typeof option === 'string');

      setSelectedOptions(newSelectedOptions);
    }
  }, [initialValue]);

  const loadData = async (selectedOptions: SystemRegionSelectItem[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    try {
      const response = await getSystemRegionLazyList(targetOption.value as string); // 使用类型断言

      if (response && response.length > 0) {
        targetOption.children = response.map((item) => ({
          value: item.code,
          label: item.name,
          isLeaf: item.level === 3,
          ...item
        }));
        setOptions([...options]);
      }
    } catch (error) {
      console.error('加载地区数据失败:', error);
    } finally {
      targetOption.loading = false;
    }
  };

  useEffect(() => {
    const fetchInitialOptions = async () => {
      setLoading(true);
      try {
        getSystemRegionSelect().then((res: SystemRegionSelectType) => {
          if (res?.length) {
            setOptions(
              res.map((item) => ({
                value: item.code,
                label: item.name,
                isLeaf: false,
                ...item
              }))
            );
          }
        });
      } catch (error) {
        console.error('加载初始地区数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialOptions();
  }, []);

  const handleChange = (value: any, selectedOptions: SystemRegionSelectItem[]) => {
    if (!value || value.length === 0 || !selectedOptions || selectedOptions.length === 0) {
      // 当选择被清空时
      setSelectedOptions([]);
      onChange({
        provinceCode: '',
        provinceName: '',
        cityCode: '',
        cityName: '',
        districtCode: '',
        districtName: '',
        mode: mode || 'default'
      });
      return;
    }

    const result = {
      provinceCode: selectedOptions[0]?.code ?? '',
      provinceName: selectedOptions[0]?.name ?? '',
      cityCode: selectedOptions[1]?.code ?? '',
      cityName: selectedOptions[1]?.name ?? '',
      districtCode: selectedOptions[2]?.code ?? '',
      districtName: selectedOptions[2]?.name ?? '',
      mode: mode || 'default'
    };

    // 只有选中最后一级时才更新 selectedOptions
    if (selectedOptions.length === 3) {
      setSelectedOptions(value);
      onChange(result);
    } else {
      setSelectedOptions([]);
      onChange({
        provinceCode: '',
        provinceName: '',
        cityCode: '',
        cityName: '',
        districtCode: '',
        districtName: '',
        mode: mode || 'default'
      });
    }
  };

  const displayRender = (labels: string[]) => {
    return labels.length === 3 ? labels.join(' / ') : '';
  };

  return (
    <Cascader
      options={options}
      loadData={loadData}
      value={selectedOptions}
      style={{
        width: '400px',
        height: '38px',
        zIndex: 999
      }}
      className={styles['custom-cascader']}
      onChange={handleChange}
      allowClear={true}
      changeOnSelect
      loading={loading}
      placeholder="请选择省/市/区"
      dropdownStyle={{ zIndex: 9999 }}
      disabled={mode === 'view'}
      displayRender={displayRender}
    />
  );
};

export default CascaderAddress;
