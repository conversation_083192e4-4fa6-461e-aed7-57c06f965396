import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  ModalBody
} from '@chakra-ui/react';
import { useForm, Controller, SubmitHandler } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import {
  createClientSchoolDept,
  updateClientSchoolDept
} from '@/api/tenant/teamManagement/student';
import MyModal from '@/components/MyModal';

interface FormData {
  name: string;
}

const ClassModal = ({
  classId,
  gradeName,
  parentId,
  name,
  onClose,
  onSuccess,
  ...props
}: {
  classId: string;
  parentId: string;
  name: string;
  gradeName: string;
  onClose: (submited: boolean, classId?: string) => void;
  onSuccess: () => void | Promise<void>;
} & BoxProps) => {
  const { t } = useTranslation();

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      name: ''
    }
  });

  const { mutate, isLoading: isSubmiting } = useRequest({
    mutationFn: (data: FormData) => {
      return classId
        ? updateClientSchoolDept({ id: classId, ...data })
        : createClientSchoolDept({ parentId, ...data });
    },
    onSuccess(res) {
      onSuccess();
      onClose(true, res.id);
    },
    successToast: classId ? '更新成功' : '新增成功'
  });

  const onSubmit: SubmitHandler<FormData> = (data) => {
    mutate(data);
  };

  useEffect(() => {
    if (classId) {
      setValue('name', name);
    }
  }, [classId, setValue]);

  return (
    <MyModal isOpen={true} title={classId ? '编辑班级' : '新增班级'}>
      <ModalBody>
        <Box p="20px" {...props}>
          <FormControl>
            <Flex alignItems="center" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                所属年级
              </FormLabel>
              <Flex flexDirection="column" w="400px" textAlign="left" pb="8px">
                {gradeName}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.name}>
            <Flex alignItems="center" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  班级名称
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: '请输入班级名称' }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      borderRadius="8px"
                      w="400px"
                      placeholder="请输入班级名称"
                      style={{ background: 'rgba(0,0,0,0.03)', border: 'none' }}
                    />
                  )}
                />
                {errors.name && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.name.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="24px">
            <Flex justifyContent="end ">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="end">
                <Button
                  borderColor="#0052D9"
                  h="36px"
                  variant={'grayBase'}
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default ClassModal;
