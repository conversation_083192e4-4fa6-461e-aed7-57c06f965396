.file-table {
  :global {
    .ant-table-thead {
      & > tr {
        & > th {
          color: rgba(0, 0, 0, 0.9) !important;
          background-color: #ffffff !important;

          &::before {
            display: none;
          }
        }
      }
    }

    .ant-table-tbody {
      .selected-row {
        background-color: #eff5fe !important; // 你可以根据需要调整颜色
      }
      & > tr {
        & > td {
          border-bottom: none !important;
        }

        &:hover > td {
          background-color: #f8fafc !important;
        }
      }
    }
  }
}

.space-tree {
  min-width: 100%;

  :global {
    .ant-tree-treenode {
      position: relative;
      align-items: center;
      padding: 0 8px 0 6px !important;
      border: 1px solid transparent;
      border-radius: 8px;

      .ant-tree-drop-indicator {
        display: none;
      }

      &.drag-over {
        border: 1px solid #3366ff;
      }

      &.drag-over-gap-bottom {
        &::after {
          content: ' ';
          height: 2px;
          background-color: #3366ff;
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }

      &.dragging {
        opacity: 0.5;
        &::after {
          display: none;
        }
      }

      .ant-tree-draggable-icon {
        display: none;
        width: 0;
      }

      &:hover {
        background-color: #f8fafc !important;

        .ant-tree-draggable-icon {
          display: block;
        }
      }

      .ant-tree-switcher {
        display: flex;
        justify-content: center;
        align-items: center;
        align-self: center;
        width: 16px;
        height: 16px;
        margin: 0 6px 0 12px;
      }

      .ant-tree-indent-unit {
        width: 6px;
      }
    }

    .ant-tree-treenode-selected {
      background-color: #f2f6ff !important;
      color: #3366ff;
      font-weight: 500;
      font-size: 14px;

      &:hover {
        background-color: #f2f6ff !important;
      }

      .ant-tree-node-content-wrapper {
        background-color: transparent;
      }
    }

    .ant-tree-node-content-wrapper {
      padding-left: 0px;
      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-title {
      white-space: nowrap;
    }
  }
}
.tree-transfer {
  :global {
    .ant-transfer-list-header-selected {
      display: none !important;
    }
    .ant-transfer-list-header-title {
      text-align: left;
    }
  }
}

.custom-select {
  :global {
    .ant-select-selector {
      background-color: #f7f7f7 !important;
    }
  }
}

.ant-tabs-tab {
  :global {
    .ant-tabs-tab-btn {
      font-size: 16px !important;
    }
  }
}

.datePicker {
  z-index: 9999;
}

.custom-cascader {
  :global {
    .ant-select-selector {
      background-color: #f6f6f6 !important;
      border: none !important;
      height: 38px !important;
      border-radius: 8px;
    }

    .ant-select-selection-placeholder {
      color: #959699; /* 设置占位符的字体颜色 */
      font-size: 14px;
    }
  }
}

/* styles.module.css */
.customPlaceholder {
  :global {
    .ant-picker-input > input::placeholder {
      font-size: 14px !important; // 设置您想要的字体大小
      color: #959699 !important;
    }
  }
}
