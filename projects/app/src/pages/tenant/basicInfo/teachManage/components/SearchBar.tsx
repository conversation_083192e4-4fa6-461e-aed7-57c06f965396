import React, { useState, useEffect } from 'react';
import { Button, Center } from '@chakra-ui/react';
import { Select, Input, Space, message } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { getClientSemesterPage } from '@/api/tenant/teamManagement/semester';
import { respDims } from '@/utils/chakra';

const { Option } = Select;

interface SearchParams {
  semesterId?: string | undefined;
  year?: string | undefined;
  type?: string | undefined;
  teacherName?: string | undefined;
}

interface Semester {
  id: string;
  year: string;
  type: 1 | 2;
  isCurrent: 0 | 1;
}

interface SemesterChangeInfo {
  semesterId: string;
  semester: Semester | null;
}

interface SearchBarProps {
  onSearch: (params: SearchParams) => void;
  onReset: () => void;
  onSemesterChange: (info: SemesterChangeInfo) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch, onReset, onSemesterChange }) => {
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [currentSemester, setCurrentSemester] = useState<string | undefined>(undefined);
  const [defaultSemester, setDefaultSemester] = useState<Semester | null>(null);
  const [teacherName, setTeacherName] = useState<string>('');

  useEffect(() => {
    fetchSemesters();
  }, []);

  const fetchSemesters = async () => {
    try {
      const response = await getClientSemesterPage({ current: 1, size: 999 });
      setSemesters(response.records || []);
      const current = response.records.find((sem) => sem.isCurrent === 1);
      if (current) {
        setCurrentSemester(current.id);
        setDefaultSemester(current);
        onSemesterChange({ semesterId: current.id, semester: current });
      }
    } catch (error) {
      message.error('获取学期列表失败');
    }
  };

  const handleSemesterChange = (value: string | undefined) => {
    setCurrentSemester(value);
    const selectedSemester = value ? semesters.find((sem) => sem.id === value) || null : null;
    onSemesterChange({ semesterId: value || '', semester: selectedSemester });
  };

  const handleSearch = () => {
    const selectedSemester = currentSemester
      ? semesters.find((sem) => sem.id === currentSemester)
      : undefined;
    onSearch({
      semesterId: currentSemester,
      year: selectedSemester?.year,
      type: selectedSemester?.type.toString(),
      teacherName: teacherName || undefined
    });
  };

  const handleReset = () => {
    setTeacherName('');
    if (defaultSemester) {
      setCurrentSemester(defaultSemester.id);
      onSemesterChange({ semesterId: defaultSemester.id, semester: defaultSemester });
    }
    onReset();
    handleSearch();
  };

  const getSemesterLabel = (sem: Semester) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  return (
    <Space size="small">
      <Select
        value={currentSemester}
        variant="filled"
        style={{
          width: 200,
          border: 'none',
          borderRadius: '8px'
        }}
        placeholder="请选择学期"
        onChange={handleSemesterChange}
      >
        {semesters.map((sem) => (
          <Option key={sem.id} value={sem.id}>
            {getSemesterLabel(sem)}
          </Option>
        ))}
      </Select>

      <Input
        placeholder="教师名称"
        style={{
          width: '457px',
          height: '36px',
          border: 'none',
          borderRadius: '8px',
          backgroundColor: 'rgba(0,0,0,0.03)'
        }}
        prefix={<SearchOutlined />}
        value={teacherName}
        onChange={(e) => setTeacherName(e.target.value)}
      />

      <Center>
        <Button
          onClick={() => {
            handleSearch();
          }}
          w={respDims(68)}
          colorScheme="primary"
          ml={respDims(16)}
        >
          查询
        </Button>
        <Button
          onClick={handleReset}
          w={respDims(68)}
          variant={'grayBase'}
          ml={respDims(16)}
          mr={respDims(16)}
        >
          重置
        </Button>
      </Center>
    </Space>
  );
};

export default SearchBar;
