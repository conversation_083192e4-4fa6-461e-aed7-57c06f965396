import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { Tabs, Card, Table, Space, Empty, Segmented } from 'antd';
import SearchBar from './components/SearchBar';
import { Box, useDisclosure, Button, border } from '@chakra-ui/react';
import PageContainer from '@/components/PageContainer';
import {
  getClientSchoolDeptManageTree,
  getClientSchoolDeptManageValid,
  getClientSchoolDeptManageCreate,
  getClientSchoolDeptSubjectManageTree,
  setClientSchoolDeptManageBind,
  setClientSchoolDeptSubjectManageBind
} from '@/api/tenant/teamManagement/teach';
import SettingModal from './components/SettingModal/index';
import SettingSubjectsModal from './components/SettingSubjectsModal/index';
import SynchronizationModal from './components/SynchronizationModal/index';
import ImportPanel from '@/components/ImportPanel';
import {
  TmbUser,
  DepartmentNode,
  ClientSchoolDeptManageTreeType,
  Department
} from '@/types/api/tenant/teamManagement/teach';
import SvgIcon from '@/components/SvgIcon';
import { Toast } from '@/utils/ui/toast';
import { ColumnType } from 'antd/es/table';
import MyTooltip from '@/components/MyTooltip';
import styles from '../index.module.scss';
import { serviceSideProps } from '@/utils/i18n';

const { TabPane } = Tabs;

enum RoleType {
  TeachingAdmin = 1, // 教学管理员
  PrimaryTeacher = 2, // 小学段管理教师
  MiddleTeacher = 3, // 初中段管理教师
  GradeLeader = 4, // 年级主任
  ClassTeacher = 5 // 班主任
}

// 使用一个函数来获取角色名称
function getRoleTypeName(roleType: RoleType): string {
  switch (roleType) {
    case RoleType.TeachingAdmin:
      return '教学管理员';
    case RoleType.PrimaryTeacher:
      return '小学段管理教师';
    case RoleType.MiddleTeacher:
      return '初中段管理教师';
    case RoleType.GradeLeader:
      return '年级主任';
    case RoleType.ClassTeacher:
      return '班主任';
    default:
      return '';
  }
}

interface Semester {
  semesterId: string;
  semester: {
    id: string;
    year: string;
    type: 1 | 2;
    startDate: string;
    endDate: string;
    isCurrent: 0 | 1;
    // 可以添加其他属性如 createTime, updateTime 等
  };
}

interface SetSelectedData {
  id: string;
  username: string;
}

interface SubjectTeachingManagementProps {
  treeData: Department[];
  onEditTeachers: (
    role: 'leader' | 'teacher' | 'sync',
    id: string | null,
    grade: string,
    subject?: string,
    className?: string
  ) => void;
}

interface Grade {
  [subjectName: string]: Department;
}

interface RecordType {
  className: string;
  isGrade: boolean;
}

const Teach: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('1');
  const [showContent, setShowContent] = useState<number>(1);
  const [currentSemester, setCurrentSemester] = useState<Semester | null>(null);
  const [semesterId, setSemesterId] = useState<string>('');
  const [treeData, setTreeData] = useState<DepartmentNode[]>([]);
  const [settingId, setSettingId] = useState<string>('');
  const [teacherType, setTeacherType] = useState<number>(1);
  const [title, setTitle] = useState<string>('');
  const [deptName, setDeptName] = useState<string>('');
  const [teacherName, setTeacherName] = useState<string>('');
  const [selectedUsers, setSelectedUsers] = useState<SetSelectedData[]>([]);
  const [subjectTreeData, setSubjectTreeData] = useState<DepartmentNode[]>([]);
  const [copiedUsers, setCopiedUsers] = useState<TmbUser[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const lastHoveredIdRef = useRef<string | null>(null);
  const lastCopyTimeRef = useRef<number>(0);

  const [semesterData, setSemesterData] = useState({
    year: '',
    type: 1 | 2
  });
  const [highlightedTmbIds, setHighlightedTmbIds] = useState<number[]>([]);
  const [hoveredCell, setHoveredCell] = useState<{
    level: string;
    id: string;
    current: string;
    users: TmbUser[];
  } | null>(null);

  const {
    isOpen: isOpenSettingModal,
    onOpen: onOpenSettingModal,
    onClose: onCloseSettingModal
  } = useDisclosure();

  const {
    isOpen: isOpenSettingSubjectsModal,
    onOpen: onOpenSettingSubjectsModal,
    onClose: onCloseSettingSubjectsModal
  } = useDisclosure();

  const {
    isOpen: isOpenSynchronizationModal,
    onOpen: onOpenSynchronizationModal,
    onClose: onCloseSynchronizationModal
  } = useDisclosure();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'c' && hoveredCell) {
        e.preventDefault();

        // const teacherType = hoveredCell.level === 'grade' ? 4 : 5;

        // setTeacherType(teacherType);

        const { users } = hoveredCell;
        setCopiedUsers(users);
        handleCopy(users);
      }
    };

    const handlePaste = (e: ClipboardEvent) => {
      if (hoveredCell) {
        e.preventDefault();
        handlePasteAction(hoveredCell.current, copiedUsers, Number(teacherType));
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('paste', handlePaste);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('paste', handlePaste);
    };
  }, [hoveredCell, copiedUsers]);

  const handlePasteAction = async (id: string, users: TmbUser[], teacherType: number) => {
    if (!id || users.length === 0) {
      // Toast.error('无法粘贴，没有复制的教师或无效的目标位置');
      return;
    }

    try {
      // 获取目标位置已经存在的教师列表
      const existingUsers = treeData.find((node) => node.id === id)?.tmbUserList || [];
      // 过滤掉已经存在的教师
      const newUsers = users.filter(
        (user) => !existingUsers.some((existingUser) => existingUser.tmbId === user.tmbId)
      );

      if (newUsers.length === 0) {
        Toast.info('目标位置已经包含所有要粘贴的教师');
        return;
      }

      const tmbIds = newUsers.map((user) => user.tmbId).join(',');
      if (activeTab === '1') {
        await setClientSchoolDeptManageBind({ id, tmbIds, teachType: teacherType });
      } else {
        await setClientSchoolDeptSubjectManageBind({ id, tmbIds, teachType: teacherType });
      }
      Toast.success('粘贴成功');
      refreshList();
    } catch (error) {
      console.error('粘贴失败:', error);
      Toast.error('粘贴失败，请重试');
    }
  };

  const handleCopy = (users: TmbUser[]) => {
    const currentTime = Date.now();
    const timeSinceLastCopy = currentTime - lastCopyTimeRef.current;

    // 如果距离上次复制的时间小于 500 毫秒，则不显示提示
    if (timeSinceLastCopy < 500) {
      return;
    }

    lastCopyTimeRef.current = currentTime;

    const copiedText = users.map((user) => user.userName).join(', ');
    navigator.clipboard
      .writeText(copiedText)
      .then(() => {
        Toast.success(`已复制 ${users.length} 个教师: ${copiedText}`);
        setCopiedUsers(users); // 确保在复制操作后更新 copiedUsers 状态
      })
      .catch((err) => {
        console.error('复制失败:', err);
        Toast.error('复制失败，请重试');
      });
  };

  const renderTmbUsers = (
    users: TmbUser[],
    level: string,
    id: string,
    isTableCell: boolean = false,
    roleType?: number
  ) => {
    const renderUserSpans = (users: TmbUser[], roleType?: number): JSX.Element[] => {
      const roleName = getRoleTypeName(roleType as RoleType);
      const userSpans = users.map((user) => (
        <span
          key={user.tmbId}
          style={{
            color: highlightedTmbIds.includes(Number(user.tmbId)) ? 'red' : '#000'
          }}
        >
          {user.userName}
        </span>
      ));

      // 将 roleName 添加到 userSpans 的开头
      return [
        <span
          key="roleName"
          style={{
            fontSize: '12px',
            color: '#606266',
            textAlign: 'left'
          }}
        >
          {roleName}:
        </span>,
        ...userSpans
      ];
    };

    const combineUserSpans = (userSpans: JSX.Element[]): JSX.Element => {
      return userSpans.reduce(
        (prev, curr, index) => (
          <>
            {prev}
            {index > 1 && ', '}
            {curr}
          </>
        ),
        <></>
      );
    };

    return (
      <span
        onMouseEnter={() => {
          if (isTableCell) {
            setHoveredCell({ level, id, current: id, users });
            lastHoveredIdRef.current = id;
          }
        }}
        onMouseLeave={() => {
          if (isTableCell) {
            setHoveredCell(null);
          }
        }}
        tabIndex={isTableCell ? 0 : undefined}
        style={{ cursor: isTableCell ? 'pointer' : 'default' }}
      >
        {combineUserSpans(renderUserSpans(users, roleType))}
      </span>
    );
  };

  const onSynchronization = () => {
    onOpenSynchronizationModal();
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (currentSemester) {
      if (key === '1') {
        // 加载年级班级教学管理数据
        getClientSchoolDeptManageTree({
          semesterId: currentSemester.semesterId,
          teacherName: teacherName
        }).then((res) => {
          setTreeData(res.treeList);
        });
      } else if (key === '2') {
        // 加载学科教学管理数据
        getClientSchoolDeptSubjectManageTree({
          semesterId: currentSemester.semesterId,
          teacherName: teacherName
        }).then((res) => {
          setSubjectTreeData(res.treeList);
        });
      }
    }
  };

  const SubjectTeachingManagement: React.FC<SubjectTeachingManagementProps> = ({
    treeData,
    onEditTeachers
  }) => {
    const [activeGrade, setActiveGrade] = useState<string>(() => {
      return localStorage.getItem('activeGrade') || '一年级';
    });
    const [hoveredCell, setHoveredCell] = useState<{ id: string; users: TmbUser[] } | null>(null);
    const [copiedUsers, setCopiedUsers] = useState<TmbUser[]>([]);
    const tableRef = useRef<HTMLDivElement>(null);
    const scrollPosition = useRef<{ scrollTop: number; scrollLeft: number }>({
      scrollTop: 0,
      scrollLeft: 0
    });

    const renderTeachers = (teacherInfo: TmbUser[]) => {
      return teacherInfo.map((t, index) => (
        <React.Fragment key={t.tmbId}>
          <span
            style={{
              color: highlightedTmbIds.includes(Number(t.tmbId)) ? 'red' : 'inherit'
            }}
          >
            {t.userName}
          </span>
          {index < teacherInfo.length - 1 && ', '}
        </React.Fragment>
      ));
    };

    const handleGradeChange = (value: any) => {
      setActiveGrade(value);
      localStorage.setItem('activeGrade', value);
    };

    // 按年级和学科分组数据
    const groupedData = useMemo(() => {
      return treeData.reduce((acc: Record<string, Grade>, item) => {
        if (!acc[item.deptName]) {
          acc[item.deptName] = {};
        }
        acc[item.deptName][item.subjectName] = item;
        return acc;
      }, {});
    }, [treeData]);

    // 获取所有年级
    const grades = useMemo(() => {
      return Object.keys(groupedData).sort((a, b) => {
        const gradeOrder = [
          '一年级',
          '二年级',
          '三年级',
          '四年级',
          '五年级',
          '六年级',
          '七年级',
          '八年级',
          '九年级'
        ];
        return gradeOrder.indexOf(a) - gradeOrder.indexOf(b);
      });
    }, [groupedData]);

    // 获取当前年级的学科
    const subjects = useMemo(() => {
      return Object.keys(groupedData[activeGrade] || {});
    }, [groupedData, activeGrade]);

    const handlePasteAction = async (id: string) => {
      if (!id || copiedUsers.length === 0) {
        Toast.error('无法粘贴到此处');
        return;
      }

      try {
        // 记录当前滚动条位置
        scrollPosition.current.scrollTop = tableRef.current?.scrollTop || 0;
        scrollPosition.current.scrollLeft = tableRef.current?.scrollLeft || 0;

        // 获取目标位置已经存在的教师列表
        const existingUsers = treeData.find((node) => node.id === id)?.tmbUserList || [];
        // 过滤掉已经存在的教师
        const newUsers = copiedUsers.filter(
          (user) => !existingUsers.some((existingUser) => existingUser.tmbId === user.tmbId)
        );

        if (newUsers.length === 0) {
          Toast.info('目标位置已经包含所有要粘贴的教师');
          return;
        }

        const tmbIds = newUsers.map((user) => user.tmbId).join(',');
        await setClientSchoolDeptSubjectManageBind({ id, tmbIds, teachType: teacherType });
        Toast.success('粘贴成功');

        // if (currentSemester) {
        //   getClientSchoolDeptManageValid(currentSemester.semesterId).then((res) => {
        //     const response = res;
        //     setShowContent(response.isExisted);

        //     if (response.isExisted === 2 && currentSemester.semesterId) {
        //       // 刷新学科教学管理数据
        //       getClientSchoolDeptSubjectManageTree({
        //         semesterId: currentSemester.semesterId,
        //         teacherName: teacherName || ''
        //       }).then((subjectTreeRes: { treeList: any[] }) => {
        //         setSubjectTreeData(subjectTreeRes.treeList);
        //       });
        //     }
        //   });
        // }
        refreshList();

        // setTimeout(() => {
        // }, 5000);
      } catch (error) {
        console.error('粘贴失败:', error);
        Toast.error('粘贴失败，请重试');
      }
    };

    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 'c' && hoveredCell) {
          e.preventDefault();
          setCopiedUsers(hoveredCell.users);
          Toast.success('复制成功');
        }
      };

      const handlePaste = (e: ClipboardEvent) => {
        if (hoveredCell) {
          e.preventDefault();
          handlePasteAction(hoveredCell.id);
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('paste', handlePaste);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('paste', handlePaste);
      };
    }, [hoveredCell, copiedUsers]);

    useEffect(() => {
      // 恢复滚动条位置
      if (tableRef.current) {
        tableRef.current.scrollTop = scrollPosition.current.scrollTop;
        tableRef.current.scrollLeft = scrollPosition.current.scrollLeft;
      }
    }, [treeData]);

    // 表格列定义
    const columns: ColumnType<RecordType>[] = useMemo(
      () => [
        {
          title: '年级班级/学科',
          dataIndex: 'className',
          key: 'className',
          width: 128,
          fixed: 'left' as 'left',
          onCell: () => ({
            style: {
              backgroundColor: '#f9f9f9'
            }
          })
        },
        ...subjects.map((subject) => ({
          title: subject,
          key: subject,
          width: 150,
          render: (_: any, record: RecordType) => {
            const subjectData = groupedData[activeGrade]?.[subject];
            let teacherInfo: TmbUser[] = [];
            let role: string = '';
            let classData: Department | undefined;
            let cellId: string | null = null;

            if (record.isGrade) {
              teacherInfo = (subjectData?.tmbUserList || []) as unknown as TmbUser[];
              role = '组长';
              cellId = subjectData?.id ?? null;
            } else {
              classData = subjectData?.children?.find((c) => c.deptName === record.className);
              teacherInfo = (classData?.tmbUserList || []) as unknown as TmbUser[];
              role = '教师';
              cellId = classData?.id ?? null;
            }

            return (
              <Box
                onMouseEnter={() => {
                  setHoveredCell({ id: cellId!, users: teacherInfo });
                }}
                onMouseLeave={() => {
                  setHoveredCell(null);
                }}
                onKeyDown={(e) => {
                  if ((e.ctrlKey || e.metaKey) && e.key === 'c' && teacherInfo.length > 0) {
                    e.preventDefault();
                    setCopiedUsers(teacherInfo);
                    Toast.success('复制成功');
                  }
                }}
                onPaste={(e) => {
                  e.preventDefault();
                  if (cellId) {
                    handlePasteAction(cellId);
                  }
                }}
                tabIndex={0}
                style={{
                  position: 'relative',
                  height: '100%',
                  width: '100%',
                  backgroundColor: hoveredCell?.id === cellId ? 'primary.50' : 'transparent',
                  transition: 'background-color 0.3s',
                  cursor: 'pointer'
                }}
              >
                <Box padding="8px">
                  <span>
                    {role}:{teacherInfo.length > 0 ? renderTeachers(teacherInfo) : '未设置'}
                  </span>
                  <span
                    style={{
                      position: 'absolute',
                      right: '5px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      opacity: hoveredCell?.id === cellId ? 1 : 0,
                      transition: 'opacity 0.3s'
                    }}
                  >
                    <SvgIcon
                      name="editIcon"
                      w="16px"
                      h="16px"
                      cursor="pointer"
                      onClick={() =>
                        onEditTeachers(
                          record.isGrade ? 'leader' : 'teacher',
                          cellId,
                          activeGrade,
                          subject,
                          record.className
                        )
                      }
                    />
                  </span>
                </Box>
              </Box>
            );
          }
        }))
      ],
      [activeGrade, groupedData, subjects, onEditTeachers, highlightedTmbIds, hoveredCell]
    );

    // 生成表格数据
    const tableData = useMemo(() => {
      if (treeData.length === 0) {
        return [];
      }
      const data = [{ className: activeGrade, isGrade: true }];
      const classNames =
        groupedData[activeGrade]?.[subjects[0]]?.children?.map((c) => c.deptName) || [];
      classNames.forEach((className) => {
        data.push({ className, isGrade: false });
      });
      return data;
    }, [activeGrade, groupedData, subjects, treeData]);

    return treeData.length === 0 ? (
      <Box w="390px" height="100%" margin="auto" pt="10%" textAlign="center">
        <SvgIcon name="empty" w="100px" h="100px" cursor="pointer" />
        {semesterData.year && semesterData.type ? (
          <Box fontSize="16px" color="#303133" textAlign="left" mt={4} w="100px">
            {`当前${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期，暂未同步学生管理中最新的年班级，请同步后进行年班级教学管理设置。`}
          </Box>
        ) : (
          <Empty description="请选择学期后进行操作。" />
        )}
        <Button colorScheme="blue" onClick={onAdd} style={{ width: '390px', marginTop: '24px' }}>
          同步年级班级
        </Button>
      </Box>
    ) : (
      <Box>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb="6px">
          <Segmented value={activeGrade} onChange={handleGradeChange} options={grades} />
          <Box
            display="flex"
            alignItems="center"
            ml="16px"
            position="absolute"
            top="86px"
            right="40px"
          >
            <ImportPanel
              templateUrl="/schoolDeptManage/downloadTemplate"
              importUrl="/ai-evaluate/schoolDeptManage/import"
              onUploadSuccess={refreshList}
            />
            <Button onClick={() => onEditTeachers('sync', null, activeGrade)} ml={3}>
              同步更新其他学期
            </Button>
            <TooltipContent />
          </Box>
        </Box>
        <Box ref={tableRef}>
          <Table
            bordered
            dataSource={tableData}
            columns={columns}
            pagination={false}
            rowKey="className"
            scroll={{ x: 'max-content' }}
            style={{ borderRadius: 'none' }}
          />
        </Box>
      </Box>
    );
  };

  const TooltipContent = () => {
    return (
      <MyTooltip
        label={
          <Box>
            <Box>• 用途：教学管理设置主要用于分配教师的教学数据可见范围及管理；</Box>
            <Box>
              •
              快捷操作：可复制（Ctrl+C）已设置的教师，粘贴（Ctrl+V）至其他未设置或已设置的格子中，或鼠标右击复制粘贴。
            </Box>
          </Box>
        }
        placement="top"
      >
        <SvgIcon name="doubt" w="20px" h="20px" cursor="pointer" ml="15px" />
      </MyTooltip>
    );
  };

  const handleEditSubjectTeachers = (
    role: 'leader' | 'teacher' | 'sync',
    id: string | null,
    grade: string,
    subject?: string,
    className?: string
  ) => {
    let title = '';
    let deptName = '';
    let teacherType = 0; // 新增变量用于存储角色类型

    switch (role) {
      case 'leader':
        title = '学科组长设置';
        deptName = `${grade} ${subject}`;
        teacherType = 6; // 设置为学科组长
        break;
      case 'teacher':
        title = '任课教师设置';
        deptName = `${grade}${className}(${subject})`;
        teacherType = 7; // 设置为学科教师
        break;
      case 'sync':
        onOpenSynchronizationModal();
        return;
      default:
        title = '';
    }

    const findSelectedUsers = (id: string) => {
      const findInTree = (tree: Department[]): { username: string; id: string }[] => {
        for (const item of tree) {
          if (item.id === id) {
            return (item.tmbUserList || []).map((user: any) => ({
              username: user.userName,
              id: user.tmbId
            }));
          }
          if (item.children) {
            const found = findInTree(item.children);
            if (found.length) return found;
          }
        }
        return [];
      };

      return findInTree(subjectTreeData);
    };

    const selectedUsers = id ? findSelectedUsers(id) : [];

    setSettingId(id || '');
    setTitle(title);
    setDeptName(deptName);
    setTeacherType(teacherType); // 设置角色类型
    setSelectedUsers(selectedUsers);
    onOpenSettingSubjectsModal();
  };

  const renderEmptyState = () => {
    const isGradeTab = activeTab === '1';
    const tabDescription = isGradeTab ? '年班级' : '学科';

    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100%"
        pt="200px"
      >
        <SvgIcon name="empty" w="100px" h="100px" cursor="pointer" />
        {semesterData.year && semesterData.type ? (
          <Box fontSize="16px" color="#303133" textAlign="center" mt={4} w="390px">
            {`当前${semesterData.year}学年第${semesterData.type === 1 ? '一' : '二'}学期，暂未同步学生管理中最新的${tabDescription}，请同步后进行${tabDescription}教学管理设置。`}
          </Box>
        ) : (
          <Empty description="请选择学期后进行操作。" />
        )}

        <Box
          w="390px"
          h="40px"
          textAlign="center"
          lineHeight="40px"
          bg="primary.50"
          color="primary.500"
          borderRadius="12px"
          mt="26px"
          cursor="pointer"
          fontWeight="500"
          onClick={onAdd}
        >
          同步{tabDescription}
        </Box>
      </Box>
    );
  };

  const renderContent = () => {
    const [hoveredItem, setHoveredItem] = useState<string | null>(null);

    const maxClassCounts = useMemo(() => {
      return (
        treeData[0]?.children?.reduce(
          (acc, department) => {
            const departmentMax =
              department.children?.reduce((gradeMax, grade) => {
                return Math.max(gradeMax, grade.children?.length || 0);
              }, 0) || 0;

            if (department.deptName.includes('小学')) {
              acc.primary = Math.max(acc.primary, departmentMax);
            } else if (department.deptName.includes('初中')) {
              acc.middle = Math.max(acc.middle, departmentMax);
            }

            return acc;
          },
          { primary: 0, middle: 0 }
        ) || { primary: 0, middle: 0 }
      );
    }, [treeData]);

    const renderEditIcon = (onClick: () => void, itemId: string, right: string | number = 8) => (
      <SvgIcon
        name="editIcon"
        w="16px"
        h="16px"
        cursor="pointer"
        onClick={onClick}
        style={{
          display: hoveredItem === itemId ? 'inline-block' : 'none',
          position: 'absolute',
          right: typeof right === 'number' ? `${right}px` : right,
          top: '50%',
          transform: 'translateY(-50%)'
        }}
      />
    );

    return (
      <Box>
        <Box display="flex" alignItems="center" justifyContent="space-between" position="relative">
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            style={{ width: '100%', height: '100%', paddingTop: '22px' }}
          >
            <TabPane tab="年级班级教学管理" key="1">
              {showContent === 2 && treeData.length ? (
                <Box
                  style={{
                    backgroundColor: '#f8f9fa',
                    borderRadius: '0px',
                    boxShadow: 'none',
                    padding: '0px',
                    height: '100%'
                  }}
                >
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    h="60px"
                    padding="0 16px"
                    border="1px solid #e8e8e8"
                    borderBottom="0"
                  >
                    <span
                      style={{ fontSize: '14px', color: '#303133', position: 'relative' }}
                      onMouseEnter={() => setHoveredItem('school')}
                      onMouseLeave={() => setHoveredItem(null)}
                    >
                      {/* {RoleType.TeachingAdmin}:{' '} */}
                      {renderTmbUsers(
                        treeData[0]?.tmbUserList || [],
                        'school',
                        treeData[0]?.id,
                        undefined,
                        1
                      )}
                      {renderEditIcon(
                        () =>
                          handleEditTeachers(
                            'school',
                            treeData[0].id,
                            treeData[0].deptName,
                            '',
                            RoleType.TeachingAdmin
                          ),
                        'school',
                        -16
                      )}
                    </span>
                  </Box>

                  {treeData[0]?.children?.map((department) => (
                    <Card
                      key={department.id}
                      bodyStyle={{ padding: 0 }}
                      style={{
                        backgroundColor: '#fff',
                        borderRadius: '0px',
                        boxShadow: 'none',
                        padding: '0px',
                        fontSize: '14px',
                        color: '#303133'
                      }}
                      title={
                        <Box
                          style={{ position: 'relative' }}
                          display="inline-block"
                          fontSize="14px"
                          color="#303133"
                          fontWeight="400"
                          position="relative"
                          onMouseEnter={() => setHoveredItem(`department-${department.id}`)}
                          onMouseLeave={() => setHoveredItem(null)}
                        >
                          {renderTmbUsers(
                            department.tmbUserList || [],
                            'department',
                            department.id,
                            undefined,
                            department.deptName.includes('小学')
                              ? RoleType.PrimaryTeacher
                              : RoleType.MiddleTeacher
                          )}
                          {renderEditIcon(
                            () =>
                              handleEditTeachers(
                                'department',
                                department.id,
                                department.deptName,
                                '',
                                department.deptName.includes('小学')
                                  ? RoleType.PrimaryTeacher
                                  : RoleType.MiddleTeacher
                              ),
                            `department-${department.id}`,
                            -16
                          )}
                        </Box>
                      }
                    >
                      <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                        {department.children?.map((grade) => {
                          const maxClassCount = department.deptName.includes('小学')
                            ? maxClassCounts.primary
                            : maxClassCounts.middle;

                          return (
                            <div key={grade.id} style={{ width: '16.66%', padding: '0px' }}>
                              <table
                                style={{ width: '100%', borderCollapse: 'collapse' }}
                                className={styles['table']}
                              >
                                <tbody>
                                  <tr>
                                    <td
                                      style={{
                                        border: '1px solid #e8e8e8',
                                        fontSize: '12px',
                                        color: '#606266',
                                        backgroundColor: '#F9FAFB',
                                        height: '46px',
                                        width: '92px',
                                        padding: '0 8px',
                                        textAlign: 'center'
                                      }}
                                    >
                                      {grade.deptName}
                                    </td>
                                    <td
                                      style={{
                                        border: '1px solid #e8e8e8',
                                        height: '46px',
                                        padding: '0 8px',
                                        fontSize: '12px',
                                        color: '#606266',
                                        backgroundColor:
                                          hoveredItem === `grade-${grade.id}`
                                            ? 'primary.50'
                                            : '#fff',
                                        transition: 'background-color 0.3s',
                                        position: 'relative',
                                        textAlign: 'center',
                                        cursor: 'pointer'
                                      }}
                                      onMouseEnter={() => setHoveredItem(`grade-${grade.id}`)}
                                      onMouseLeave={() => setHoveredItem(null)}
                                    >
                                      {renderTmbUsers(
                                        grade.tmbUserList,
                                        'grade',
                                        grade.id,
                                        true,
                                        4
                                      )}
                                      {renderEditIcon(
                                        () =>
                                          handleEditTeachers(
                                            'grade',
                                            grade.id,
                                            grade.deptName,
                                            '',
                                            RoleType.GradeLeader
                                          ),
                                        `grade-${grade.id}`
                                      )}
                                    </td>
                                  </tr>
                                  {[...Array(maxClassCount)].map((_, index) => {
                                    const classItem = grade.children?.[index];
                                    return (
                                      <tr key={classItem ? classItem.id : `empty-${index}`}>
                                        <td
                                          style={{
                                            border: '1px solid #e8e8e8',
                                            borderBottom: '0px',
                                            height: '46px',
                                            padding: '0 8px',
                                            fontSize: '12px',
                                            color: '#606266',
                                            backgroundColor: '#F9FAFB',
                                            width: '92px',
                                            textAlign: 'center'
                                          }}
                                        >
                                          {classItem ? classItem.deptName : ''}
                                        </td>
                                        <td
                                          style={{
                                            border: '1px solid #e8e8e8',
                                            borderBottom: '0px',
                                            height: '46px',
                                            padding: '0 8px',
                                            fontSize: '12px',
                                            color: '#606266',
                                            backgroundColor: classItem
                                              ? hoveredItem === `class-${classItem.id}`
                                                ? 'primary.50'
                                                : '#fff'
                                              : '#fff',
                                            transition: 'background-color 0.3s',
                                            position: 'relative',
                                            userSelect: classItem ? 'auto' : 'none',
                                            pointerEvents: classItem ? 'auto' : 'none',
                                            textAlign: 'center',
                                            cursor: 'pointer'
                                          }}
                                          onMouseEnter={() =>
                                            classItem && setHoveredItem(`class-${classItem.id}`)
                                          }
                                          onMouseLeave={() => classItem && setHoveredItem(null)}
                                        >
                                          {classItem ? (
                                            <>
                                              {renderTmbUsers(
                                                classItem.tmbUserList,
                                                'class',
                                                classItem.id,
                                                true,
                                                5
                                              )}
                                              {renderEditIcon(
                                                () =>
                                                  handleEditTeachers(
                                                    'class',
                                                    classItem.id,
                                                    grade.deptName,
                                                    classItem.deptName,
                                                    RoleType.ClassTeacher
                                                  ),
                                                `class-${classItem.id}`
                                              )}
                                            </>
                                          ) : (
                                            ''
                                          )}
                                        </td>
                                      </tr>
                                    );
                                  })}
                                </tbody>
                              </table>
                            </div>
                          );
                        })}
                      </div>
                    </Card>
                  ))}
                </Box>
              ) : (
                renderEmptyState()
              )}
            </TabPane>
            <TabPane tab="学科教学管理" key="2">
              <Box width="100%">
                {subjectTreeData.length ? (
                  <SubjectTeachingManagement
                    treeData={subjectTreeData}
                    onEditTeachers={handleEditSubjectTeachers}
                  />
                ) : (
                  renderEmptyState()
                )}
              </Box>
            </TabPane>
          </Tabs>
          <Box display="flex" alignItems="center" position="absolute" top="22px" right="12px">
            <ImportPanel
              templateUrl="/client/schoolDeptManage/downloadTemplate"
              importUrl="/huayun-ai/client/schoolDeptManage/import"
              onUploadSuccess={refreshList}
            />
            <Button colorScheme="blue" ml={3} onClick={onSynchronization}>
              同步更新其他学期
            </Button>
            <TooltipContent />
          </Box>
        </Box>
      </Box>
    );
  };

  const handleEditTeachers = (
    level: string,
    id: string,
    gradeName?: string,
    className?: string,
    teacherType?: number
  ) => {
    let title = '';
    let deptName = '';
    let selectedUsers: { username: string; id: string }[] = []; // 存储包含 username 和 id 的对象数组

    const findDept = (depts: DepartmentNode[], targetId: string): DepartmentNode | null => {
      for (const dept of depts) {
        if (dept.id === targetId) {
          return dept;
        }
        if (dept.children) {
          const found = findDept(dept.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const dept = findDept(treeData, id);
    if (dept && dept.tmbUserList) {
      selectedUsers = dept.tmbUserList.map((user) => ({
        username: user.userName,
        id: user.tmbId
      }));
    }

    switch (level) {
      case 'school':
        title = `${getRoleTypeName(RoleType.TeachingAdmin)}设置`;
        break;
      case 'department':
        title = `${getRoleTypeName(RoleType.PrimaryTeacher)}设置`;
        deptName = gradeName || '';
        break;
      case 'department1':
        title = `${getRoleTypeName(RoleType.MiddleTeacher)}设置`;
        deptName = gradeName || '';
        break;
      case 'grade':
        title = `${getRoleTypeName(RoleType.GradeLeader)}设置`;
        deptName = gradeName || '';
        break;
      case 'class':
        title = `${getRoleTypeName(RoleType.ClassTeacher)}设置`;
        deptName = gradeName && className ? `${gradeName}${className}` : '';
        break;
      default:
        title = '';
    }

    // 设置状态以打开模态框
    setSettingId(id);
    setTeacherType(Number(teacherType));
    setTitle(title);
    setDeptName(deptName);
    setSelectedUsers(selectedUsers);
    onOpenSettingModal();
  };

  const handleSemesterChange = (semesterData: any | null) => {
    setCurrentSemester(semesterData);
    if (semesterData && semesterData.semester) {
      const newSemesterData = {
        year: semesterData.semester.year,
        type: semesterData.semester.type
      };
      setSemesterData(newSemesterData);

      setSemesterId(semesterData.semesterId);
      getClientSchoolDeptManageValid(semesterData.semesterId).then((res: { isExisted: number }) => {
        setShowContent(res.isExisted);

        if (res.isExisted === 2) {
          getClientSchoolDeptManageTree({
            semesterId: semesterData.semesterId,
            teacherName: teacherName
          }).then((res: ClientSchoolDeptManageTreeType) => {
            setTreeData(res.treeList);
          });
          getClientSchoolDeptSubjectManageTree({
            semesterId: semesterData.semesterId,
            teacherName: teacherName
          }).then((res: ClientSchoolDeptManageTreeType) => {
            setSubjectTreeData(res.treeList);
          });
        }
      });
    } else {
      setShowContent(0);
    }
  };

  const handleSearch = async (values: {
    semesterId?: string;
    year?: string;
    type?: string;
    teacherName?: string;
  }) => {
    const { semesterId, year, type, teacherName } = values;
    setTeacherName(teacherName || '');
    setSemesterData({
      year: year || '',
      type: type === '1' ? 1 : type === '2' ? 2 : 1 // 默认为1
    });
    setSemesterId(semesterId || '');

    if (semesterId) {
      try {
        // 获取年级班级教学管理数据
        const gradeClassResponse = await getClientSchoolDeptManageTree({
          semesterId,
          teacherName: teacherName || ''
        });

        // 获取学科教学管理数据
        const subjectResponse = await getClientSchoolDeptSubjectManageTree({
          semesterId,
          teacherName: teacherName || ''
        });

        if (gradeClassResponse && gradeClassResponse.treeList) {
          setTreeData(gradeClassResponse.treeList);
          setShowContent(2);

          // 只有在有搜索词时才设置高亮
          if (teacherName) {
            setHighlightedTmbIds((gradeClassResponse.tmbIds || []).map((id) => Number(id)));
          } else {
            // 如果搜索词为空，清除高亮
            setHighlightedTmbIds([]);
          }
        } else {
          setTreeData([]);
          setShowContent(1);
          setHighlightedTmbIds([]);
        }

        // 设置学科教学管理数据
        if (subjectResponse && subjectResponse.treeList) {
          setSubjectTreeData(subjectResponse.treeList);

          // 只有在有搜索词时才设置高亮
          if (teacherName) {
            setHighlightedTmbIds((subjectResponse.tmbIds || []).map((id) => Number(id)));
          } else {
            // 如果搜索词为空，清除高亮
            setHighlightedTmbIds([]);
          }
        } else {
          setSubjectTreeData([]);
        }
      } catch (error) {
        console.error('获取数据时出错:', error);
        Toast.error('获取数据失败，请重试');
        setHighlightedTmbIds([]);
      }
    } else {
      setHighlightedTmbIds([]);
      refreshList();
    }
  };

  const onAdd = () => {
    getClientSchoolDeptManageCreate(semesterId).then((res) => {
      Toast.success('操作成功');
      refreshList();
    });
  };

  const refreshList = useCallback(() => {
    if (currentSemester) {
      getClientSchoolDeptManageValid(currentSemester.semesterId).then((res) => {
        const response = res;
        setShowContent(response.isExisted);

        if (response.isExisted === 2 && currentSemester.semesterId) {
          // 刷新年级班级教学管理数据
          getClientSchoolDeptManageTree({
            semesterId: currentSemester.semesterId,
            teacherName: teacherName || ''
          }).then((treeRes: { treeList: DepartmentNode[] }) => {
            setTreeData(treeRes.treeList);
            // 在刷新列表时，如果没有搜索词，清除高亮
            if (!teacherName) {
              setHighlightedTmbIds([]);
            }
          });

          // 刷新学科教学管理数据
          getClientSchoolDeptSubjectManageTree({
            semesterId: currentSemester.semesterId,
            teacherName: teacherName || ''
          }).then((subjectTreeRes: { treeList: any[] }) => {
            setSubjectTreeData(subjectTreeRes.treeList);
          });
        }
      });
    }
  }, [currentSemester, teacherName]);

  const onReset = () => {};

  return (
    <PageContainer p="24px">
      <Box mb="24px" overflowY="auto" h="100%" ref={containerRef}>
        <SearchBar
          onSearch={handleSearch}
          onSemesterChange={handleSemesterChange}
          onReset={onReset}
        />
        {renderContent()}
      </Box>

      {isOpenSettingModal && (
        <SettingModal
          teacherType={teacherType}
          settingId={settingId}
          title={title}
          deptName={deptName}
          onClose={onCloseSettingModal}
          selectedUsers={selectedUsers as unknown as { username: string; id: string }[]}
          onSuccess={refreshList}
        />
      )}

      {isOpenSettingSubjectsModal && (
        <SettingSubjectsModal
          settingId={settingId}
          title={title}
          deptName={deptName}
          onClose={onCloseSettingSubjectsModal}
          selectedUsers={selectedUsers as unknown as { username: string; id: string }[]}
          onSuccess={refreshList}
          teacherType={teacherType}
        />
      )}

      {isOpenSynchronizationModal && (
        <SynchronizationModal
          activeTab={activeTab}
          semesterData={semesterData}
          semesterId={semesterId}
          onClose={onCloseSynchronizationModal}
          onSuccess={refreshList}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default Teach;
