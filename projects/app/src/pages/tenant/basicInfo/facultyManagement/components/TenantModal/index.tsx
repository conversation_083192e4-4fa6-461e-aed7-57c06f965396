import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Flex,
  Button,
  BoxProps,
  InputRightElement,
  InputGroup,
  ModalBody
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useRequest } from '@/hooks/useRequest';
import { useState, useEffect, useMemo } from 'react';
import { Select, TreeSelect } from 'antd';
import { useTranslation } from 'next-i18next';
import {
  createTenantUser,
  updateTenantUser,
  getRoleList,
  detailTenantUser,
  getDeptList
} from '@/api/tenant';
import { useToast } from '@/hooks/useToast';
import SvgIcon from '@/components/SvgIcon';
import MyModal from '@/components/MyModal';
import UploadImage from '@/components/UploadImage';

interface FormData {
  username: string;
  avatar?: string;
  roleId: string;
  avatarUrl?: string;
  deptIds?: string[];
  phone: string;
  password?: string;
}

const TenantPanel = ({
  tenantId,
  selectedTenantId,
  onClose,
  onSuccess,
  ...props
}: {
  tenantId: string;
  selectedTenantId: string;
  onClose: (submited: boolean, selectedTenantId?: string, tenantId?: string) => void;
  onSuccess: () => void;
} & BoxProps) => {
  const { t } = useTranslation();
  const [, setRefresh] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [roleId, setRoleId] = useState<string | undefined>(undefined);
  const [deptIds, setDeptIds] = useState<string[]>();
  const [roleIdOptions, setRoleIdOptions] = useState<{ label: string; value: string }[]>([]);
  const [deptIdOptions, setDeptIdOptions] = useState<{ title: string; value: string }[]>([]);

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      roleId: '',
      deptIds: []
    }
  });

  const { mutate: onSubmit, isLoading: isSubmiting } = useRequest({
    mutationFn: (data) => {
      data.deptIds = deptIds?.map((item) => String(item));
      return tenantId ? updateTenantUser({ id: tenantId, ...data }) : createTenantUser(data);
    },
    onSuccess() {
      onSuccess();
      onClose(true);
    },
    successToast: tenantId ? '更新成功' : '新增成功'
  });

  useEffect(() => {
    if (!tenantId) return;
    detailTenantUser(tenantId).then((res) => {
      setValue('username', res.username);
      setValue('phone', res.phone);
      setValue('avatar', res.avatar);
      setValue('avatarUrl', res.avatarFile ? res.avatarFile.fileUrl : '');
      setValue('roleId', res.roleId);
      setRoleId(res.roleId);
      setValue(
        'deptIds',
        res.deptIds.map((item: number) => String(item))
      );
      setDeptIds(res.deptIds.map((item: number) => String(item)));
      setRefresh((state) => !state);
    });
  }, [tenantId, setValue]);

  useEffect(() => {
    console.log(selectedTenantId, 'selectedTenantId');

    if (selectedTenantId && !tenantId) {
      setDeptIds([selectedTenantId]);
      setValue('deptIds', [selectedTenantId]);
    }
  }, [selectedTenantId, setValue]);

  useEffect(() => {
    // 获取角色列表
    getRoleList().then((res) => {
      const options = res.map((role) => ({
        label: role.name,
        value: role.id
      }));
      setRoleIdOptions(options);
    });
    const transformData = (nodes: any[]): { title: string; value: string; children?: any[] }[] => {
      return nodes.map((node) => {
        const transformedNode = {
          title: node.name,
          value: node.id,
          children: node.children ? transformData(node.children) : undefined
        };
        return transformedNode;
      });
    };
    getDeptList().then((res: any) => {
      const transformedData = transformData(res);
      setDeptIdOptions(transformedData);
    });
  }, []);

  const handleImageSelect = (type: keyof FormData, fileKey: string, fileUrl: string) => {
    setValue(type, fileKey);
    setValue(`${type}Url` as keyof FormData, fileUrl);
    setRefresh((state) => !state);
  };

  const onDeptChange = (value: string[]) => {
    setDeptIds(value);
  };

  return (
    <MyModal isOpen={true} title={tenantId ? '编辑成员' : '添加成员'}>
      <ModalBody>
        <Box p="20px" {...props}>
          <FormControl isInvalid={!!errors.username}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  成员名称
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('username', {
                    required: '请输入成员名称',
                    minLength: {
                      value: 2,
                      message: '至少输入2个字符'
                    },
                    maxLength: {
                      value: 20,
                      message: '最多输入不超过20个字符'
                    }
                  })}
                  placeholder="请输入成员名称"
                />
                {errors.username && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.username.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex
              alignItems="center"
              whiteSpace="nowrap"
              justifyContent="end"
              css={{
                '& .ant-select-selector': {
                  borderRadius: '2px'
                }
              }}
            >
              <FormLabel color="#4E5969" fontSize="14px">
                <Box>所属部门</Box>
              </FormLabel>
              <TreeSelect
                style={{ width: '400px', zIndex: 999 }}
                value={deptIds}
                multiple
                treeData={deptIdOptions}
                placeholder="请选择所属部门"
                treeDefaultExpandAll
                onChange={onDeptChange}
                dropdownStyle={{ zIndex: 9999 }} // 设置下拉菜单的 z-index
              />
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex
              alignItems="center"
              whiteSpace="nowrap"
              justifyContent="end"
              css={{
                '& .ant-select-selector': {
                  borderRadius: '2px'
                }
              }}
            >
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  角色
                </Box>
              </FormLabel>
              <Select
                style={{ width: '400px', height: '40px' }}
                onChange={(val) => {
                  setRoleId(val);
                  setValue('roleId', val); // 确保 roleId 被包含在表单数据中
                }}
                value={roleId}
                placeholder="请选择角色"
                options={roleIdOptions}
                dropdownStyle={{ zIndex: 9999 }}
              />
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  _before={{
                    content: '"*"',
                    color: '#F53F3F'
                  }}
                >
                  手机号码
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <Input
                  borderRadius="2px"
                  w="400px"
                  {...register('phone', {
                    required: '请输入手机号码',
                    pattern: {
                      value: /^1[3-9]\d{9}$/,
                      message: '请输入有效的手机号'
                    }
                  })}
                  isDisabled={!!tenantId}
                  placeholder="请输入手机号码"
                />
                {errors.phone && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.phone.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px" isInvalid={!!errors.password}>
            <Flex alignItems="baseline" whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                <Box
                  {...(!tenantId && {
                    _before: {
                      content: '"*"',
                      color: '#F53F3F'
                    }
                  })}
                >
                  密码
                </Box>
              </FormLabel>
              <Flex flexDirection="column">
                <InputGroup>
                  <Input
                    borderRadius="2px"
                    w="400px"
                    type={showPassword ? 'text' : 'password'}
                    {...register(
                      'password',
                      !tenantId
                        ? {
                            required: '请输入密码'
                          }
                        : {}
                    )}
                    placeholder="请输入密码"
                    autoComplete="new-password"
                  />
                  <InputRightElement>
                    <SvgIcon
                      name={showPassword ? 'eye' : 'eyeOff'}
                      w="18px"
                      h="18px"
                      onClick={() => setShowPassword((state) => !state)}
                    />
                  </InputRightElement>
                </InputGroup>
                {errors.password && (
                  <Box color="#F53F3F" fontSize="13px" mt="8px">
                    {errors.password.message}
                  </Box>
                )}
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex whiteSpace="nowrap" justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px">
                头像
              </FormLabel>
              <Flex flexDirection="column" w="400px">
                <UploadImage
                  imageUrl={getValues('avatarUrl')}
                  onImageSelect={(fileKey, fileUrl) =>
                    handleImageSelect('avatar', fileKey, fileUrl)
                  }
                  maxSizeMB={5}
                  placeholder="选择图片"
                  showPlaceholderAsBox={true}
                />
              </Flex>
            </Flex>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="end">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="end">
                <Button
                  borderColor="#0052D9"
                  variant="grayBase"
                  h="36px"
                  borderRadius="8px"
                  onClick={() => onClose(false)}
                >
                  取消
                </Button>
                <Button
                  h="36px"
                  ml="16px"
                  borderRadius="8px"
                  onClick={handleSubmit(onSubmit as any)}
                  isLoading={isSubmiting}
                >
                  确定
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default TenantPanel;
