import React, { useEffect, useState } from 'react';
import { Box, FormControl, FormLabel, Flex, Button, ModalBody, useToast } from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { getDeptList, replaceDept } from '@/api/tenant';
import { TreeSelect } from 'antd';
import MyModal from '@/components/MyModal';
import { Toast } from '@/utils/ui/toast';

// 定义 TransferResourceModalProps 接口
interface TransferResourceModalProps {
  isDeptOpen: boolean;
  onDeptClose: () => void;
  onSuccess: () => void;
  tenantId: string;
}

const TransferResourceModal: React.FC<TransferResourceModalProps> = ({
  isDeptOpen,
  onDeptClose,
  onSuccess,
  tenantId
}) => {
  const {
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>();
  const toast = useToast();
  // const [deptId, setDeptId] = useState<string | null>(null);
  const [deptId, setDeptId] = useState<string | undefined>(undefined);
  const [deptIdOptions, setDeptIdOptions] = useState<{ title: string; value: string }[]>([]);

  const onSubmit = (data: FormData) => {
    // 调用转移资源的API或处理逻辑
    if (deptId) {
      replaceDept({ replaceId: deptId, id: tenantId }).then((res) => {
        Toast.success('操作成功');
        onDeptClose();
        onSuccess();
      });
    } else {
      toast({
        title: '请选择一个部门',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    }
  };

  const onChange = (value: string) => {
    setDeptId(value);
  };

  useEffect(() => {
    const transformData = (nodes: any[]): { title: string; value: string; children?: any[] }[] => {
      return nodes.map((node) => {
        const transformedNode = {
          title: node.name,
          value: node.id,
          children: node.children ? transformData(node.children) : undefined
        };
        return transformedNode;
      });
    };
    getDeptList().then((res: any) => {
      const transformedData = transformData(res);
      const filteredData = transformedData.filter((item: any) => item.value !== tenantId);
      setDeptIdOptions(filteredData);
    });
  }, []);

  return (
    <MyModal isOpen={isDeptOpen} title={'删除提示'} onClose={onDeptClose}>
      <ModalBody>
        <Box p="20px">
          <Box>当前部门有用户关联，如果删除请配置更换当前用户的部门</Box>
          <FormControl>
            <FormControl mt="14px">
              <Flex
                alignItems="center"
                whiteSpace="nowrap"
                justifyContent="end"
                css={{
                  '& .ant-select-selector': {
                    borderRadius: '2px'
                  }
                }}
              >
                <FormLabel color="#4E5969" fontSize="14px">
                  <Box
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                  >
                    更换部门
                  </Box>
                </FormLabel>
                <TreeSelect
                  style={{ width: '400px', height: '40px', zIndex: 999 }}
                  value={deptId}
                  treeData={deptIdOptions}
                  placeholder="请选择所属部门"
                  treeDefaultExpandAll
                  onChange={onChange}
                  dropdownStyle={{ zIndex: 9999 }} // 设置下拉菜单的 z-index
                />
              </Flex>
            </FormControl>
          </FormControl>

          <FormControl mt="14px">
            <Flex justifyContent="center">
              <FormLabel color="#4E5969" fontSize="14px"></FormLabel>
              <Flex w="400px" justifyContent="center">
                <Button h="36px" mr="24px" borderRadius="2px" onClick={handleSubmit(onSubmit)}>
                  确定
                </Button>

                <Button
                  borderColor="#0052D9"
                  variant="outline"
                  h="36px"
                  color="#1A5EFF"
                  borderRadius="2px"
                  onClick={onDeptClose}
                >
                  取消
                </Button>
              </Flex>
            </Flex>
          </FormControl>
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default TransferResourceModal;
