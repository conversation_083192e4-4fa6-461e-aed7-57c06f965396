import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  Flex,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
  useDisclosure
} from '@chakra-ui/react';
import { Tree, Modal } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import { serviceSideProps } from '@/utils/i18n';
import { TenantItemType } from '@/types/api/tenant';
import { MessageBox } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import {
  getDeptList,
  createDept,
  updateDept,
  deleteDept,
  sortDept,
  getTenantUserPage,
  deleteTenantUser,
  updateStatusTenantUser
} from '@/api/tenant';
import { useToast } from '@/hooks/useToast';
import PageContainer from '@/components/PageContainer';
import { DownOutlined } from '@ant-design/icons';
import MyTable from '@/components/MyTable';
import ImportPanel from '@/components/ImportPanel';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import TenantModal from './components/TenantModal/index';
import TransferResourceModal from './components/TransferResourceModal/index';
import ChangeDeptModal from './components/ChangeDeptModal/index';
import { useUserStore } from '@/store/useUserStore';
import styles from './member.module.scss';

interface Dept {
  id: string;
  name: string;
  tenantId: string;
  parentId: string;
  deptUserNum?: number;
  children?: Dept[];
}

interface TreeNode {
  title: string;
  key: string;
  tenantId: string;
  parentId: string;
  children?: TreeNode[];
}

const convertToTreeData = (data: Dept[]): TreeNode[] => {
  return data.map((item) => ({
    title: item.name,
    key: item.id,
    tenantId: item.tenantId,
    parentId: item.parentId,
    deptUserNum: item.deptUserNum,
    children: item.children ? convertToTreeData(item.children) : []
  }));
};

const TenantMember = () => {
  const { toast } = useToast();
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [newDeptName, setNewDeptName] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const tableRef = useRef<MyTableRef>(null);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [addingParentKey, setAddingParentKey] = useState<string | null>(null);

  const [selectedTenantId, setSelectedTenantId] = useState('');

  const containerRef = useRef<HTMLDivElement>(null);

  const [modalTenantId, setModalTenantId] = useState('');

  const { userInfo } = useUserStore();

  const inputRef = useRef<HTMLInputElement>(null); // 创建输入框的引用

  const {
    isOpen: isOpenTenantModal,
    onOpen: onOpenTenantModal,
    onClose: onCloseTenantModal
  } = useDisclosure();

  const { isOpen, onOpen, onClose } = useDisclosure();

  const { isOpen: isDeptOpen, onOpen: onDeptOpen, onClose: onDeptClose } = useDisclosure();

  interface TreeNode {
    key: string;
    title: string;
    children?: TreeNode[];
    pos?: string;
    parentId: string;
    deptUserNum?: number;
  }

  interface DropInfo {
    node: TreeNode;
    dragNode: TreeNode;
    dropPosition: number;
    dropToGap: boolean;
    event: React.MouseEvent;
  }

  useEffect(() => {
    fetchDeptList();

    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsAdding(false);
        setIsEditing(false);
        setAddingParentKey(null); // 重置 addingParentKey
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isAdding || isEditing) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    }
  }, [isAdding, isEditing]);

  useEffect(() => {
    if (selectedTenantId) {
      tableRef.current?.reload();
    }
  }, [selectedTenantId]);

  const fetchDeptList = async () => {
    const res = await getDeptList();
    const data = convertToTreeData(res);
    setTreeData(data);
    setExpandedKeys(getAllKeys(data)); // 设置所有节点的 key

    // 设置默认选中的节点
    if (data.length > 0) {
      setSelectedKeys([data[0].key]);
      setSelectedTenantId(data[0].key);
    }
  };

  const getAllKeys = (nodes: TreeNode[]): string[] => {
    let keys: string[] = [];
    nodes.forEach((node) => {
      keys.push(node.key);
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children));
      }
    });
    return keys;
  };

  const handleAddDept = (parentId: string) => {
    if (!newDeptName) {
      toast({
        title: '部门名称不能为空',
        status: 'error'
      });
      return;
    }
    createDept({ name: newDeptName, parentId }).then(() => {
      toast({
        title: '部门添加成功',
        status: 'success'
      });
      setNewDeptName('');
      setIsAdding(false);
      setAddingParentKey(null); // 重置 addingParentKey
      fetchDeptList(); // 重新获取部门列表
    });
  };

  const handleEditDept = (node: TreeNode) => {
    if (!newDeptName) {
      toast({
        title: '部门名称不能为空',
        status: 'error'
      });
      return;
    }
    updateDept({ id: node.key, name: newDeptName, parentId: node.parentId }).then(() => {
      toast({
        title: '部门编辑成功',
        status: 'success'
      });
      setNewDeptName('');
      setIsEditing(false);
      fetchDeptList(); // 重新获取部门列表
    });
  };

  const handleDeleteDept = async (node: TreeNode) => {
    const params = {
      current: 1,
      deptId: node.key,
      searchKey: '',
      searchType: '',
      size: 999,
      status: ''
    };
    const res = await getTenantUserPage(params);

    if (!res.records.length) {
      Modal.confirm({
        title: '删除提示',
        content: '删除当前部门不可恢复，确定删除当前部门吗？',
        onOk: () => {
          deleteDept({ id: node.key }).then(() => {
            toast({
              title: '部门删除成功',
              status: 'success'
            });
            fetchDeptList(); // 重新获取部门列表
          });
        }
      });
    } else {
      setModalTenantId(node.key);
      onDeptOpen();
    }
  };

  const onAdd = () => {
    setModalTenantId('');
    onOpenTenantModal();
  };

  const onEdit = (id: string) => {
    setModalTenantId(id);
    onOpenTenantModal();
  };

  const onTransferResources = (id: string) => {
    setModalTenantId(id);
    onOpen();
  };

  const findNodeById = (nodes: TreeNode[], id: string): TreeNode | null => {
    for (const node of nodes) {
      if (node.key === id) {
        return node;
      }
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  const handleSelect = (selectedKeys: React.Key[], info: any) => {
    const selectedNode = info.node;
    const key = selectedNode?.key;
    if (!isNaN(Number(key))) {
      setSelectedTenantId(key);
      setSelectedKeys([key]); // 更新 selectedKeys 状态
    }
  };

  const handleCloseTenantModal = (submited: boolean, tenantId?: string) => {
    onCloseTenantModal();
    setModalTenantId('');
  };

  const handleDrop = (info: DropInfo) => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = (info.node.pos || '0').split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const loop = (
      data: TreeNode[],
      key: string,
      callback: (item: TreeNode, index: number, arr: TreeNode[]) => void
    ) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children!, key, callback);
        }
      }
    };

    const data = [...treeData];

    let dragObj: TreeNode;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        item.children.push(dragObj!);
      });
    } else {
      let ar: TreeNode[] = [];
      let i: number;
      loop(data, dropKey, (item, index, arr) => {
        ar = arr;
        i = index;
      });

      if (dropPosition === -1) {
        ar.splice(i!, 0, dragObj!);
      } else {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }

    setTreeData(data);
    setExpandedKeys(getAllKeys(data)); // 确保 getAllKeys 返回的是 string[]

    // 调用排序接口
    const getSortedDepts = (nodes: TreeNode[], parentId: string = '0'): any[] => {
      return nodes.map((node, index) => ({
        id: node.key,
        sort: index,
        parentId: parentId,
        children: node.children ? getSortedDepts(node.children, node.key) : []
      }));
    };

    const sortedDepts = getSortedDepts(data);

    sortDept({ param: sortedDepts }).then(() => {
      fetchDeptList(); // 重新获取部门列表
    });
  };

  const onDelete = (id: string) => {
    MessageBox.confirm({
      title: '删除提示',
      content: '删除当前成员不可恢复，确定删除当前成员吗？',
      onOk: async () => {
        await deleteTenantUser(id);
        Toast.success('删除成功');
        // 重新加载场景列表
        tableRef.current?.reload();
      }
    });
  };

  const onSetStatus = (id: string, status: number) => {
    const title = status == 1 ? '启用提示' : '禁用提示';
    const content =
      status == 1
        ? '启用成员账号则可正常登录使用，确认启用？'
        : '禁用成员账号则不可登录使用，确认禁用？';
    MessageBox.confirm({
      title,
      content,
      onOk: async () => {
        updateStatusTenantUser({ id, status }).then((res) => {
          Toast.success('操作成功');
          tableRef.current?.reload();
        });
        tableRef.current?.reload();
      }
    });
  };

  const renderTitle = (node: TreeNode, level: number) => {
    const maxWords = 16; // 最大字符数
    const minWords = 10; // 最小字符数
    const words = Math.max(minWords, maxWords - level * 2);
    const displayTitle =
      node.title.length > words ? `${node.title.slice(0, words)}...` : node.title;

    return (
      <Flex
        alignItems="center"
        justifyContent="space-between"
        boxSizing="border-box"
        p="6px 0"
        title={node.title}
      >
        {isEditing && selectedNode?.key === node.key ? (
          <Flex alignItems="center">
            <Input
              ref={inputRef} // 绑定输入框引用
              w="100%"
              value={newDeptName}
              onChange={(e) => setNewDeptName(e.target.value)}
              placeholder="请输入部门名称"
            />
            <SvgIcon
              color="primary.500"
              name="check"
              w={18}
              h={18}
              onClick={() => handleEditDept(node)}
              ml="12px"
            />
          </Flex>
        ) : (
          <Box overflow="hidden" whiteSpace="nowrap" textOverflow="ellipsis" width="100%">
            {displayTitle + ' (' + (node.deptUserNum ? node.deptUserNum : 0) + ')'}
          </Box>
        )}

        <Popover placement="bottom-end">
          <PopoverTrigger>
            <Box display="flex" justifyContent="center" alignItems="center">
              <SvgIcon name="more" w={19} h={19} />
            </Box>
          </PopoverTrigger>
          <PopoverContent
            w="160px"
            borderRadius="8px"
            padding="8px"
            sx={{
              background: '#FFFFFF',
              boxShadow: '0px 2px 2px 0px rgba(0,0,0,0), 0px 4px 3px 0px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB'
            }}
          >
            {level < 5 && (
              <Box
                w="100%"
                h="36px"
                display="flex"
                justifyContent="flex-start"
                alignItems="center"
                padding="12px"
                boxSizing="border-box"
                borderRadius="8px"
                onClick={() => {
                  setSelectedNode(node);
                  setIsAdding(true);
                  setAddingParentKey(node.key); // 设置 addingParentKey
                  setNewDeptName(''); // 重置 newDeptName
                  setIsEditing(false); // 重置编辑状态
                }}
                sx={{
                  _hover: {
                    backgroundColor: '#F8FAFC'
                  }
                }}
              >
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  fontSize="15px"
                  fontWeight="500"
                  color="#303133"
                >
                  <SvgIcon name="plus" w={18} h={18} mr="12px" />
                  子部门
                </Box>
              </Box>
            )}
            <Box
              w="100%"
              h="36px"
              display="flex"
              justifyContent="flex-start"
              alignItems="center"
              padding="12px"
              boxSizing="border-box"
              borderRadius="8px"
              sx={{
                _hover: {
                  backgroundColor: '#F8FAFC'
                }
              }}
              onClick={() => {
                setSelectedNode(node);
                setIsEditing(true);
                setNewDeptName(node.title as string);
                setIsAdding(false); // 重置添加状态
              }}
            >
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                fontSize="15px"
                fontWeight="500"
                color="#303133"
              >
                <SvgIcon name="editLine" w={18} h={18} mr="12px" />
                编辑
              </Box>
            </Box>
            <Box
              w="100%"
              h="36px"
              display="flex"
              justifyContent="flex-start"
              alignItems="center"
              padding="12px"
              boxSizing="border-box"
              borderRadius="8px"
              sx={{
                _hover: {
                  backgroundColor: '#F8FAFC'
                }
              }}
              onClick={() => handleDeleteDept(node)}
            >
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                fontSize="15px"
                fontWeight="500"
                color="#303133"
              >
                <SvgIcon name="trash" w={18} h={18} mr="12px" />
                删除
              </Box>
            </Box>
          </PopoverContent>
        </Popover>
      </Flex>
    );
  };

  const renderTreeNodes = (data: TreeNode[], level: number = 1): React.ReactNode => {
    return data.map((node) => (
      <Tree.TreeNode title={renderTitle(node, level)} key={node.key}>
        {node.children && node.children.length > 0
          ? renderTreeNodes(node.children, level + 1)
          : null}
        {isAdding && addingParentKey === node.key && (
          <Tree.TreeNode title={renderAddInput(node)} key={`adding-${node.key}`}>
            {/* 空节点 */}
          </Tree.TreeNode>
        )}
      </Tree.TreeNode>
    ));
  };

  const renderAddInput = (node: TreeNode) => {
    return (
      <Flex alignItems="center">
        <Input
          w="100%"
          value={newDeptName}
          ref={inputRef}
          onChange={(e) => setNewDeptName(e.target.value)}
          placeholder="请输入子部门名称"
        />
        <SvgIcon
          name="check"
          color="primary.500"
          w={18}
          h={18}
          onClick={() => handleAddDept(node.key)}
          ml="12px"
        />
      </Flex>
    );
  };

  const handleUploadSuccess = () => {
    tableRef.current?.reload();
  };

  // 自定义样式
  const statusStyles = {
    enabled: {
      color: '#52c41a',
      display: 'flex',
      alignItems: 'center'
    },
    disabled: {
      color: '#bfbfbf',
      display: 'flex',
      alignItems: 'center'
    },
    dot: {
      height: '8px',
      width: '8px',
      borderRadius: '50%',
      display: 'inline-block',
      marginRight: '8px'
    }
  };

  const columns = [
    {
      title: '序号',
      key: 'order',
      width: 80,
      render: (_: any, __: any, index: number) => {
        return <>{index + 1}</>;
      }
    },
    {
      title: '成员名称',
      key: 'username',
      dataIndex: 'username'
    },
    {
      title: '手机号码',
      key: 'phone',
      dataIndex: 'phone'
    },
    {
      title: '所属部门',
      key: 'deptName',
      dataIndex: 'deptName'
    },
    {
      title: '角色',
      key: 'roleName',
      dataIndex: 'roleName'
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime'
    },
    {
      title: '状态',
      key: 'status',
      width: 80,
      dataIndex: 'status',
      render: (status: number) => {
        return (
          <Box style={status === 1 ? statusStyles.enabled : statusStyles.disabled}>
            <Box
              style={{
                ...statusStyles.dot,
                backgroundColor: status === 1 ? '#52c41a' : '#bfbfbf'
              }}
            />
            {status === 1 ? '启用' : '禁用'}
          </Box>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 220,
      render: (_: any, tenant: TenantItemType) => (
        <Flex>
          <Button color="#0052D9" variant="link" onClick={() => onEdit(tenant.id)}>
            编辑
          </Button>
          {tenant.status === 1 ? (
            <Button color="#0052D9" variant="link" onClick={() => onSetStatus(tenant.id, 2)}>
              禁用
            </Button>
          ) : (
            <Button color="#0052D9" variant="link" onClick={() => onSetStatus(tenant.id, 1)}>
              启用
            </Button>
          )}
          <Button color="#0052D9" variant="link" onClick={() => onDelete(tenant.id)}>
            删除
          </Button>
          {userInfo?.roleId === '3' && (
            <Button color="#0052D9" variant="link" onClick={() => onTransferResources(tenant.id)}>
              转移资源
            </Button>
          )}
        </Flex>
      )
    }
  ];

  const ButtonsComponent = () => (
    <Box>
      <Button mx="16px" h="36px" borderRadius="8px" onClick={onAdd}>
        新增
      </Button>
      <ImportPanel
        templateUrl="/client/tenant/user/downloadTemplate"
        importUrl="/huayun-ai/client/tenant/user/import"
        onUploadSuccess={handleUploadSuccess}
      ></ImportPanel>
    </Box>
  );

  return (
    <PageContainer>
      <Box display="flex" h="100%">
        <Box
          w="400px"
          borderRight="1px solid #F3F4F6"
          bg="#fff"
          ref={containerRef}
          overflow="hidden"
          borderRadius="24px 0 0 24px"
        >
          <Box
            h="76px"
            borderBottom="1px solid #E5E7EB"
            fontSize="16px"
            fontWeight="600"
            color="#303133"
            lineHeight="76px"
            pl="24px"
          >
            组织架构
          </Box>

          <Box p="8px 20px 100px 20px" h="100%" overflowY="auto">
            <Tree
              className={styles['space-tree']}
              expandedKeys={expandedKeys}
              onExpand={(keys) => setExpandedKeys(keys as string[])}
              draggable
              blockNode
              switcherIcon={<DownOutlined />}
              onDrop={handleDrop}
              onSelect={handleSelect}
              selectedKeys={selectedKeys}
            >
              {renderTreeNodes(treeData)}
            </Tree>
          </Box>
        </Box>
        <Box overflow="auto" w="100%">
          {selectedTenantId && (
            <MyTable
              ref={tableRef}
              api={getTenantUserPage}
              columns={columns}
              defaultQuery={{
                deptId: selectedTenantId
              }}
              headerConfig={{
                showHeader: true,
                SearchComponent: SearchBar,
                ButtonsComponent: ButtonsComponent
              }}
            />
          )}
        </Box>
      </Box>
      {isOpenTenantModal && (
        <TenantModal
          selectedTenantId={selectedTenantId}
          tenantId={modalTenantId}
          onClose={handleCloseTenantModal}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
      {isOpen && (
        <TransferResourceModal
          tenantId={modalTenantId}
          isOpen={isOpen}
          onClose={onClose}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
      {isDeptOpen && (
        <ChangeDeptModal
          tenantId={modalTenantId}
          isDeptOpen={isDeptOpen}
          onDeptClose={onDeptClose}
          onSuccess={() => tableRef.current?.reload()}
        />
      )}
    </PageContainer>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default TenantMember;
