import React, { memo } from 'react';
import PageContainer from '@/components/PageContainer';
import { Flex } from '@chakra-ui/react';
import { serviceSideProps } from '@/utils/i18n';
import { useTranslation } from 'react-i18next';
import RulesTab from './components/RulesTab';

const tabs = [
  { name: '评价规则', value: 'rules' },
  { name: '评价维度和项目', value: 'dimensions' }
] as const;

interface EvaluationManagementProps {
  appName: string;
  tenantName: string;
  userName: string;
}

const EvaluationManagement: React.FC<EvaluationManagementProps> = ({
  appName,
  tenantName,
  userName
}) => {
  const { t } = useTranslation();

  return (
    <PageContainer
      bgColor="#FFFFFF"
      backdropFilter="blur(3.700000047683716px)"
      background="rgba(255, 255, 255, 0.06)"
      borderRadius="20px 0 0 0"
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      border="1px solid #FFF"
    >
      <Flex w="100%" h="100%" flexDir="column">
        <RulesTab></RulesTab>
      </Flex>
    </PageContainer>
  );
};

export const getServerSideProps = async (context: any) => {
  return {
    props: {
      appName: context.query?.appName || '',
      tenantName: context.query?.tenantName || '',
      userName: context.query?.userName || '',
      ...(await serviceSideProps(context))
    }
  };
};

export default memo(EvaluationManagement);
