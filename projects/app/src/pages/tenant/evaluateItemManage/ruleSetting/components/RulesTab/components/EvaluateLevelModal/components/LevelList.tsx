import React, { useEffect, useState } from 'react';
import { Box, Flex, VStack, Center, Spinner, Text, HStack, MenuButton } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import { useEvaluateLevel } from './EvaluateLevelProvider';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import {
  getScoreLevelList,
  deleteScoreLevel,
  getScoreLevelDetail
} from '@/api/tenant/evaluate/score';

import { useToast } from '@/hooks/useToast';
import { MessageBox } from '@/utils/ui/messageBox';
import { EvaluaScoreLevel } from '@/types/api/tenant/evaluate/score';
import { respDims } from '@/utils/chakra';

const LevelList = () => {
  const { t } = useTranslation();
  const {
    selectedEvaluateLevel,
    setSelectedEvaluateLevel,
    scoreGrades,
    setScoreGrades,
    unSaveBeformConfirm,
    setFormStatus,
    formStatus,
    handleSelectGrade,
    form,
    createName,
    setCreateName
  } = useEvaluateLevel();
  const { toast } = useToast();

  // 在组件内部
  const { isLoading, refetch } = useQuery(['init'], () => getScoreLevelList({}), {
    onSuccess: (data) => {
      if (data.length > 0) {
        if (createName) {
          const newData = data.find((item) => item.name == createName);
          if (newData) {
            handleSelectGrade({ ...newData });
            setCreateName('');
          }
        } else if (data.find((item) => item.id == selectedEvaluateLevel?.id)) {
          handleSelectGrade(selectedEvaluateLevel ? { ...selectedEvaluateLevel } : null);
        } else {
          handleSelectGrade(data[0]);
        }
      } else {
        setFormStatus(null);
      }

      setScoreGrades([...data]);
    }
  });

  useEffect(() => {
    if (createName) {
      refetch();
    }
  }, [createName, refetch]);

  const handleDelete = (grade: EvaluaScoreLevel) => {
    MessageBox.confirm({
      title: '删除',
      content: '删除该评分等级后，不可恢复，确认删除？',
      onOk: async () => {
        deleteScoreLevel({ id: grade.id! }).then(() => {
          refetch();
          toast({ status: 'success', title: t('删除成功') });
        });
      }
    });
  };

  const handleAddGrade = () => {
    setSelectedEvaluateLevel(null);
    setFormStatus('add');
    const newForm = {
      name: '',
      status: true,
      values: []
    };
    setTimeout(() => {
      form.setFieldsValue(newForm);
    }, 0);
  };

  useEffect(() => {}, [scoreGrades]);

  return (
    <Flex w="100%" flexDir="column" h="100%">
      <Box w="100%" flex="1" overflow="auto">
        {isLoading ? (
          <Center h="100%">
            <Spinner size="xl" />
          </Center>
        ) : (
          <VStack align="start" spacing={0} padding={respDims(10)}>
            {scoreGrades?.map((grade) => (
              <Box
                key={grade.id}
                w="full"
                p={2}
                pl={respDims(20)}
                mb={respDims(10)}
                position="relative"
                bg={selectedEvaluateLevel?.id === grade.id ? '#f8f9fb' : 'white'}
                borderLeft="solid 3px #fff"
                borderColor={selectedEvaluateLevel?.id === grade.id ? 'primary.500' : 'white'}
                cursor="pointer"
                onClick={() => handleSelectGrade(grade)}
                _hover={{
                  boxShadow:
                    '0px 0px 15px 0px rgba(92,92,92,0.09), 0px 2px 4px 0px rgba(75,86,115,0.07)',
                  '& .delete-button': { display: 'flex' }
                }}
              >
                <Box>
                  <Flex
                    justifyContent="space-between"
                    flexDir="column"
                    color={selectedEvaluateLevel?.id === grade.id ? 'primary.500' : '#303133'}
                  >
                    <Flex
                      position="relative"
                      color={selectedEvaluateLevel?.id === grade.id ? 'primary.500' : '#303133'}
                      fontWeight={selectedEvaluateLevel?.id === grade.id ? 'bold' : 'normal'}
                      alignItems="center"
                      mb={respDims(5)}
                    >
                      {grade.name}
                      {(grade?.relatedCount || 0) <= 0 && (
                        <Center
                          className="delete-button"
                          display="none"
                          w={respDims(30)}
                          h={respDims(30)}
                          position="absolute"
                          right="0"
                          top="50%"
                          transform="translateY(-50%)"
                          _hover={{ bg: 'myWhite.600' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(grade);
                          }}
                        >
                          <Center>
                            <SvgIcon
                              name="trash"
                              color={'#d43b3b'}
                              w={respDims(16)}
                              h={respDims(16)}
                            />
                          </Center>
                        </Center>
                      )}
                    </Flex>
                    <Box
                      color="#909399"
                      fontSize="14px"
                    >{`已被${grade.relatedCount}个评价指标关联`}</Box>
                  </Flex>
                </Box>
              </Box>
            ))}
            {formStatus === 'add' && (
              <Box
                w="full"
                p={2}
                pl={respDims(20)}
                position="relative"
                borderRight="solid 3px #fff"
                bg={'#eff4fe'}
                borderColor={'primary.600'}
              >
                {'新增等级 '}
              </Box>
            )}
          </VStack>
        )}
      </Box>
      {
        <Flex
          justifyContent="center"
          alignItems="center"
          padding={respDims(10)}
          borderTop="1px solid #E5E7EB"
          h={respDims(80)}
        >
          <Box
            as="button"
            width="173px"
            height="32px"
            borderRadius="8px"
            border="1px solid"
            borderColor="primary.500"
            display="flex"
            alignItems="center"
            justifyContent="center"
            cursor="pointer"
            _hover={{ bg: '#eff4fe' }}
            onClick={handleAddGrade}
          >
            <HStack spacing={2}>
              <SvgIcon name="plus" w="16px" h="16px" color="primary.500" />
              <Text color="primary.500" fontSize="14px">
                添加评分项目
              </Text>
            </HStack>
          </Box>
        </Flex>
      }
    </Flex>
  );
};

export default LevelList;
