import React, { useCallback, useMemo, useRef } from 'react';
import { Box, Flex, Text, Button as ChakraButton, Tag, Image } from '@chakra-ui/react';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { useIndicator } from '../IndicatorContext';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EditProjectModal from './EditProjectModal';
import { respDims } from '@/utils/chakra';
import { AddIcon } from '@chakra-ui/icons';
import { MyTableRef } from '@/components/MyTable/types';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import IndicatorsTab from '../IndicatorsTab';
import { Button } from 'antd';
import {
  deleteProject,
  sortProject,
  listPageProjectV2,
  copyProject,
  schoolDeptSubjectManageSubjectsV2
} from '@/api/tenant/evaluate/project';
import { EvaluaProject } from '@/types/api/tenant/evaluate/project';
import { useToast } from '@/hooks/useToast';
import { promisifyConfirm, promisifyDelete } from '@/utils/ui/messageBox';
import { myToFixed } from '@/utils/tools';
import { createStyles } from 'antd-style';

const useStyle = createStyles(({ css, token }) => {
  const { antCls } = token as any;
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `
  };
});
const ProjectList: React.FC = () => {
  const { styles } = useStyle();
  const { selectedProject, setSelectedProject, selectedDimension, selectedDimensionCategory } =
    useIndicator();
  const { openOverlay } = useOverlayManager();

  // 使用React Query获取学科列表
  const { data: subjects, isLoading: subjectsLoading } = useQuery(
    ['subjects'],
    schoolDeptSubjectManageSubjectsV2,
    {
      staleTime: Infinity // 可以根据需要调整
    }
  );

  const tableRef = useRef<MyTableRef>(null);
  const router = useRouter();
  const { toast } = useToast();
  const columns: ColumnsType<EvaluaProject> = useMemo(() => {
    return [
      {
        title: '评价项目',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        render: (name, record) => (
          <Flex alignItems="center" flex="1">
            <Box>{record.name}</Box>
            <Box ml={2}>
              {record.subjectIds?.slice(0, 2).map((item) => {
                return (
                  <Tag key={item} color="#2BA471" bgColor="#E3F9E9" ml={2}>
                    {subjects?.find((subject) => subject.id === item)?.name}
                  </Tag>
                );
              })}
              {(record.subjectIds?.length || 0) > 2 && (
                <Tag color="#2BA471" bgColor="#E3F9E9" ml={2}>
                  +{(record.subjectIds?.length || 0) - 2}
                </Tag>
              )}
            </Box>
          </Flex>
        )
      },
      {
        title: '评分占比',
        dataIndex: 'scoreRate',
        key: 'scoreRate',
        align: 'center',
        width: 100,
        render: (scoreRate) => `${myToFixed(scoreRate * 100, 2)}%`
      },
      {
        title: '关联指标(个)',
        width: 100,
        dataIndex: 'indicatorCount',
        key: 'indicatorCount',
        align: 'center'
      },
      {
        title: '更新时间',
        width: 160,
        dataIndex: 'updateTime',
        key: 'updateTime',
        align: 'center'
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 160,
        render: (_, record) => (
          <Flex justifyContent="flex-start">
            <Button type="link" onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Button type="link" onClick={() => handleCopy(record)}>
              复制
            </Button>
            <Button type="link" onClick={() => handleSettingIndicator(record)}>
              设置评价指标
            </Button>
            <Button type="link" danger onClick={() => handleDelete(record)}>
              删除
            </Button>
          </Flex>
        )
      }
    ];
  }, [subjects, selectedDimensionCategory, selectedDimension]);

  const handleEdit = (record: EvaluaProject) => {
    openOverlay({
      Overlay: EditProjectModal,
      props: {
        parentId: selectedDimension?.id || '',
        typeId: Number(selectedDimensionCategory?.id) || 0,
        formStatus: 'edit',
        initialData: {
          ...record
          // subjectIds: record.subjects.map((item) => String(item.subjectId))
        },
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleAdd = () => {
    openOverlay({
      Overlay: EditProjectModal,
      props: {
        parentId: selectedDimension?.id || '',
        typeId: Number(selectedDimensionCategory?.id) || 0,
        formStatus: 'create',
        onSuccess: () => {
          tableRef.current?.reload();
        }
      }
    });
  };

  const handleCopy = async (record: EvaluaProject) => {
    await promisifyConfirm({
      title: '确定复制该项目？'
    });
    await copyProject({ id: record.id! });
    toast({
      status: 'success',
      title: '复制成功'
    });
    tableRef.current?.reload();
  };

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: EvaluaProject[]) => {
      const { active, over } = event;
      if (active.id !== over.id) {
        const sortParams = newDataSource.map((item, index) => ({
          id: item.id!,
          sort: index + 1
        }));

        await sortProject(sortParams);
        toast({
          status: 'success',
          title: '操作成功'
        });
        tableRef.current?.reload();
      }
    },
    [sortProject]
  );

  const handleSettingIndicator = useCallback(
    (record: EvaluaProject) => {
      openOverlay({
        Overlay: IndicatorsTab,
        props: {
          projectId: record.id!,
          name: record.name!,
          paths: [selectedDimensionCategory, selectedDimension, record],
          onRefresh() {
            tableRef.current?.reload();
          }
        }
      });
    },
    [selectedDimensionCategory, selectedDimension]
  );

  const handleDelete = async (record: EvaluaProject) => {
    await promisifyDelete({
      title: '确定删除该项目？'
    });
    try {
      await deleteProject({ id: record.id! });
      toast({
        status: 'success',
        title: '删除成功'
      });
      tableRef.current?.reload();
    } catch (error) {
      console.error('删除项目失败:', error);
    }
  };

  const onRow = useCallback(
    (record: EvaluaProject) => ({
      onClick: () => {
        if (record.id !== selectedProject?.id) {
          setSelectedProject(record);
        }
      }
    }),
    [selectedProject, setSelectedProject]
  );

  return (
    <Flex flexDirection="column" h="100%" w="100%">
      <Box fontSize={respDims(18, 14)} fontWeight="600" color="#1D2129">
        评价项目
        <ChakraButton aria-label="Add dimension" variant={'grayBase'} ml={4} visibility="hidden">
          添加
        </ChakraButton>
      </Box>
      <Flex bg="#f7f9fb" borderRadius="8px" flex="1" h="0" p={respDims(16)} flexDirection="column">
        <Box
          flex="1"
          overflow="auto"
          css={{
            '& .selected-row': {
              background: '#eff5fe!important'
            }
          }}
        >
          <MyTable
            ref={tableRef}
            columns={columns}
            api={listPageProjectV2}
            defaultQuery={{
              dimensionId: selectedDimension?.id || ''
            }}
            queryConfig={{
              enabled: !!selectedDimension?.id
            }}
            dragConfig={{
              enabled: true,
              rowKey: 'id',
              onDragEnd: handleDragSortEnd
            }}
            className={styles.customTable}
            scroll={{ x: 'max-content' }}
            emptyConfig={{
              EmptyPicComponent: () => (
                <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
              )
            }}
            boxStyle={{
              px: '0',
              py: '0',
              borderRadius: '12px',
              css: {
                '& .ant-table': {
                  borderRadius: '12px 12px 12px 12px!important',
                  overflow: 'hidden!important'
                },
                '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                  borderBottom: 'none'
                }
              }
            }}
            size="small"
            headerConfig={{
              showIfEmpty: true,
              showHeader: false
            }}
            pageConfig={{
              showPaginate: false
            }}
            onRow={onRow}
            rowClassName={(record) => (selectedProject?.id === record.id ? 'selected-row' : '')}
          />
        </Box>
        <Flex justifyContent="flex-end">
          <Box w="100%" pt={respDims(20)}>
            {selectedDimension && (
              <ChakraButton
                bg="#fff"
                color="#4E5969"
                w="100%"
                leftIcon={<AddIcon />}
                onClick={handleAdd}
              >
                新增评价项目
              </ChakraButton>
            )}
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default ProjectList;
