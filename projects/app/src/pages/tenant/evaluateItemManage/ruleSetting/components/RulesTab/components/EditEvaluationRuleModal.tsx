import React, { useState, useEffect, useMemo, ChangeEvent } from 'react';
import {
  Form,
  Input,
  Select,
  Radio,
  DatePicker,
  Table,
  Row,
  Col,
  Cascader,
  TreeDataNode
} from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SelectPersonModal, { SubDeptType } from './SelectPersonModal';
import SelectIndicatorModal from './SelectIndicatorModal/index';
import { Box, Flex, HStack, Link, Text, Button } from '@chakra-ui/react';
import MyModal from '@/components/MyModal';
import { useQuery } from '@tanstack/react-query';
import { ModalBody, ModalFooter } from '@chakra-ui/react';
import MyTooltip from '@/components/MyTooltip';
import { respDims } from '@/utils/chakra';
import { QuestionOutlineIcon } from '@chakra-ui/icons';
import styles from '@/pages/index.module.scss';
import SvgIcon from '@/components/SvgIcon';
import {
  getEvaluationRuleDetail,
  addEvaluationRule,
  updateEvaluationRule,
  getTeachers,
  getProjectIndicatorTree,
  getDimensionReflectionList,
  getEntranceTree,
  getEvaluatorIdList,
  addEvaluationRuleZhuxiang,
  updateEvaluationRuleZhuxiang
} from '@/api/tenant/evaluate/rule';

import {
  EvaluateeType,
  MatchType,
  PeriodType,
  RuleStatus,
  Term,
  EvaluateeTypeMap,
  MatchTypeMap,
  PeriodTypeMap,
  TermMap,
  HasSubIndicator,
  EvaluatedWayEnumMap,
  EvaluatedWayEnum,
  Status
} from '@/constants/api/tenant/evaluate/rule';
import { useToast } from '@/hooks/useToast';
import {
  EvaluationRuleDetail,
  EvaluationRule,
  TeacherType,
  EvaluaRuleEvaluatee,
  EvaluaRuleEvaluator,
  GetEvaluatorIdListParams
} from '@/types/api/tenant/evaluate/rule';
import { getClientSchoolDeptTree } from '@/api/tenant/teamManagement/student';
import { termItem } from '@/types/api/tenant/teamManagement/semester';
import 'dayjs/locale/zh-cn';
import dayjs from 'dayjs';
import { treeFind, treeFindPaths, treeToList } from '@/utils/tree';
import { useRouter } from 'next/router';

import { Button as AntButton } from 'antd';
import { RadioChangeEvent } from 'antd/lib';
import { rest } from 'lodash';
import SelectClazzModal from './SelectClazzModal';
import { log } from 'console';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import { useSystemStore } from '@/store/useSystemStore';
import { EvaluaIndactorVO } from '@/types/api/tenant/evaluate/indicator';
import SemesterSelect from '@/pages/tenant/teacherEvaluate/components/SemesterSelect';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';
dayjs.extend(localizedFormat as any);
dayjs.extend(customParseFormat as any);
dayjs.locale('zh-cn');
dayjs.extend(weekday as any);
dayjs.extend(localeData as any);

interface EditEvaluationRuleModalProps {
  onClose: () => void;
  onSuccess: () => void;
  ruleId?: string;
  formStatus: 'add' | 'edit' | 'copy';
}

const { Option } = Select;
const { RangePicker } = DatePicker;

const EditEvaluationRuleModal: React.FC<EditEvaluationRuleModalProps> = ({
  onClose,
  onSuccess,
  ruleId,
  formStatus
}) => {
  const [form] = Form.useForm<EvaluationRuleDetail>();
  const [matchType, setMatchType] = useState<MatchType>(MatchType.AutoMatch);

  const { openOverlay, OverlayContainer } = useOverlayManager();
  const { toast } = useToast();

  const { data: entranceTree } = useQuery(['entranceTree'], () =>
    getEntranceTree({}).then((res) =>
      res.map((item) => {
        // 因为第一级id重复，修改一下，
        return {
          ...item,
          disabled: !item.children?.length,
          id: item.id + '1'
        };
      })
    )
  );

  const { data: ruleDetail, isLoading } = useQuery(
    ['evaluationRule', ruleId],
    () => getEvaluationRuleDetail({ id: ruleId! }),
    {
      enabled: (formStatus === 'edit' || formStatus === 'copy') && !!ruleId,
      onSuccess: (data) => {
        data.startTime = dayjs(data.startTime) as any;
        data.endTime = dayjs(data.endTime) as any;
        data.evaluatedWays =
          data.evaluatedWays?.map((item: EvaluatedWayEnum) => Number(item)) || [];
        data.indicators = data.indicators?.map((item) => item + '') || [];

        if (formStatus === 'copy') {
          const { id, ...restData } = data;
          restData.copyId = id!;
          restData.name = restData.name + '_1';
          form.setFieldsValue(restData);
        } else {
          form.setFieldsValue(data);
        }

        setMatchType(data.matchType!);
      }
    }
  );

  // const { data: teachers } = useQuery(['INIT'], () => getTeachers({}));
  const teachers = [{}];

  const router = useRouter();
  const { isLoading: isTreeLoading, data: treeData } = useQuery(
    ['clazz-treeData'],
    getClientSchoolDeptTree,
    {
      onSuccess(treeData) {
        // 如果在这里也需要设置路径,可以这样使用:
      }
    }
  );

  const processInDisabled = useMemo(() => {
    if (ruleDetail?.status == RuleStatus.NotStarted) {
      return false;
    }
    return ruleDetail && !(formStatus === 'copy');
  }, [ruleDetail, formStatus]);

  useEffect(() => {
    if (ruleDetail?.evaluateds && treeData?.length) {
      setTimeout(() => {
        const evaluateePaths = treeFindPaths(
          treeData || [],
          ruleDetail?.evaluateds?.map((e) => String(e)) || []
        );
        const formattedPaths = Object.values(evaluateePaths).map((path) =>
          path.map((node) => node.id)
        );

        form.setFieldValue('evaluateds', formattedPaths);
      }, 100);
    }
  }, [treeData, ruleDetail, form]);

  useEffect(() => {
    if (ruleDetail?.reflectionId && entranceTree?.length) {
      setTimeout(() => {
        const entryPaths = treeFindPaths(
          entranceTree || [],
          [ruleDetail.reflectionId?.toString()],
          'id',
          'children'
        );

        const formattedPaths = Object.values(entryPaths).map((path) =>
          path.map((node) => node.id)
        )[0];

        form.setFieldValue('reflectionId', formattedPaths);
      }, 100);
    }
  }, [ruleDetail, form, entranceTree]);

  const handleSelectPerson = (field: 'evaluators' | 'evaluatedDetails') => {
    const defaultSelectEvaluators =
      field === 'evaluators'
        ? form.getFieldValue('evaluators')
        : form.getFieldValue('evaluatedDetails')?.map((it: { tmbId: string }) => it.tmbId) || [];

    openOverlay({
      Overlay: SelectPersonModal,
      props: {
        formStatus: processInDisabled ? 'view' : 'select',
        defaultSelectEvaluators: defaultSelectEvaluators,
        onClose: () => {},
        onSelect: (data: TeacherType[]) => {
          if (field === 'evaluators') {
            form.setFieldsValue({ evaluators: data.map((it) => it.id) });
          } else if (field === 'evaluatedDetails') {
            form.setFieldsValue({
              evaluatedDetails: data.map((it) => ({ deptId: it.deptId, tmbId: it.id }))
            });
          } else {
            // form.setFieldsValue({ evaluateds: data });
          }
        }
      }
    });
  };

  const handleSelectIndicator = (formStatus: 'select' | 'view') => {
    const formData = form.getFieldsValue();

    openOverlay({
      Overlay: SelectIndicatorModal,
      props: {
        formStatus: formStatus,
        formData: form.getFieldsValue(),
        onClose: () => {},
        onSuccess: (selectedIndicators: string[]) => {
          form.setFieldValue('indicators', selectedIndicators);
          form.validateFields(['indicators']);
        }
      }
    });
  };

  const handleAutoGetIndicator = async (evaluateds: string[]) => {
    const formData = form.getFieldsValue();
    const reflectionId = formData.reflectionId
      ? formData.reflectionId[formData.reflectionId?.length - 1]
      : '';
    getProjectIndicatorTree({
      reflectionId: reflectionId
    }).then((res) => {
      const indicatorList = res.map((item) => item.indicatorList).flat();

      const indicatorListLeaf = treeToList(indicatorList, 'sub')
        .filter((item) => item.hasSub === HasSubIndicator.No && item.status === Status.Normal)

        .map((item) => item.id);

      if (indicatorListLeaf && indicatorListLeaf.length) {
        toast({
          status: 'success',
          title: `匹配到${indicatorListLeaf.length}个指标`
        });
        form.setFieldValue('indicators', indicatorListLeaf);
      } else {
        toast({
          status: 'warning',
          title: '匹配指标为空'
        });
      }
      form.validateFields();
    });
  };

  const handleSubmit = async () => {
    try {
      if (!teachers?.length) {
        return toast({
          title: '请同步教师信息',
          status: 'warning'
        });
      }
      const values = await form.validateFields();

      values.evaluators = form.getFieldValue('evaluators');

      if (values.startTime) {
        values.startTime = dayjs(values.startTime).format('YYYY-MM-DD HH:mm:ss');
      }
      if (values.endTime) {
        values.endTime = dayjs(values.endTime).format('YYYY-MM-DD HH:mm:ss');
      }
      values.evaluateds = values.evaluateds
        ?.filter((path) => !treeData?.some((node) => node.id === path[path.length - 1]))
        .map((path) => path[path.length - 1]);

      if (!values.evaluators?.length && matchType === MatchType.CustomMatch) {
        return toast({
          status: 'warning',
          title: '请选择评价人'
        });
      }

      values.reflectionId =
        (values?.reflectionId && values.reflectionId[values.reflectionId.length - 1]) || '';

      values.indicators = form.getFieldValue('indicators');
      values.evaluatedType = form.getFieldValue('evaluatedType');
      values.evaluatorType = form.getFieldValue('evaluatorType');

      // delete values.evaluatedVOs
      // delete values.evaluators
      // delete values.indicators

      let apiFunc;
      if (formStatus === 'add' || formStatus === 'copy') {
        Reflect.deleteProperty(values, 'id');

        const isProfessionalAssessment =
          getReflection(values.reflectionId)?.name ===
          evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name;
        if (isProfessionalAssessment) {
          apiFunc = addEvaluationRuleZhuxiang;
        } else {
          apiFunc = addEvaluationRule;
        }
      } else {
        values.id = ruleDetail?.id;
        const isProfessionalAssessment =
          getReflection(values.reflectionId)?.name ===
          evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name;
        if (isProfessionalAssessment) {
          apiFunc = updateEvaluationRuleZhuxiang;
        } else {
          apiFunc = updateEvaluationRule;
        }
      }
      console.log(values);

      const result = await apiFunc(values);
      toast({
        status: 'success',
        title: `${formStatus === 'edit' ? '编辑' : '添加'}成功`
      });
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const validateTimeRange = (_: any, value: any) => {
    const { startTime, endTime } = form.getFieldsValue(['startTime', 'endTime']);
    const now = dayjs();

    if (!startTime || !endTime) {
      return Promise.reject(new Error('请选择开始时间和结束时间'));
    }

    if (startTime.isAfter(endTime)) {
      return Promise.reject(new Error('开始时间不能晚于结束时间'));
    }

    if (endTime.isBefore(now)) {
      return Promise.reject(new Error('结束时间不能早于当前时间'));
    }

    return Promise.resolve();
  };
  const handleMatchTypeChange = (e: RadioChangeEvent) => {
    setMatchType(e.target.value);
    form.setFieldValue('evaluateds', []);
    form.setFieldValue('evaluators', []);
    form.setFieldValue('indicators', []);
  };

  const hanldeLookClazz = () => {
    const evaluatees = form.getFieldValue('evaluatees');
    const paths = treeFindPaths(
      treeData || [],
      ruleDetail?.evaluateds?.map((e) => String(e)) || []
    );

    openOverlay({
      Overlay: SelectClazzModal,
      props: {
        // onClose: () => { },
        selectClasses: Object.values(paths).map((value, index) => {
          return {
            id: index,
            name: value.map((item) => item.deptName).join('/')
          };
        })
      }
    });
  };

  const handleClazzChange = () => {
    getMatchTeachers();
  };

  const handleEntranceChange = (value: any) => {
    // value 是string[],找到目标node
    const node = treeFind(
      entranceTree || [],
      (node, parent, level) => node.id === value[value.length - 1] && level >= 1
    );

    if (node) {
      form.setFieldValue('evaluatedType', node?.evaluatedType);
      form.setFieldValue('evaluatorType', node?.evaluatorType);
    }
    form.setFieldValue('evaluatedDetails', []);
    form.setFieldValue('evaluators', []);
    form.setFieldValue('evaluateds', []);
    form.setFieldValue('evaluatedWays', []);

    form.validateFields(['evaluatedType', 'evaluatorType']);

    getMatchTeachers();
  };

  const getReflection = (reflectionId: string) => {
    const res = treeFind(
      entranceTree || [],
      (node, parent, level) => node.id === reflectionId && level >= 1
    );
    return res;
  };

  const getMatchTeachers = async () => {
    const {
      evaluateds: evaluateds,
      reflectionId,
      semesterId,
      matchType,
      evaluatedWays
    } = form.getFieldsValue();
    form.setFieldValue('evaluators', []);
    let params: GetEvaluatorIdListParams = {};

    const reflection = await getReflection(
      (reflectionId && reflectionId[reflectionId.length - 1]) || ''
    );
    const isProfessionalAssessment =
      reflection?.name ===
      evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name;

    if (matchType === MatchType.AutoMatch && isProfessionalAssessment) {
      if (!reflectionId?.length || !semesterId || !evaluatedWays?.length) {
        return;
      }
      params = {
        reflectionId: reflectionId && reflectionId[reflectionId.length - 1],
        semesterId,
        evaluateds: evaluatedWays.map((way) => way.toString())
      };
    } else {
      if (!evaluateds?.length || !reflectionId?.length || !semesterId) {
        return;
      }
      params = {
        evaluateds: evaluateds?.map((path) => path[path.length - 1]),
        reflectionId: reflectionId && reflectionId[reflectionId.length - 1],
        semesterId
      };
    }
    try {
      const res = await getEvaluatorIdList(params);
      form.setFieldValue(
        'evaluators',
        res.map((item) => item)
      );
    } catch (error) {
      form.setFieldValue('evaluators', []);
    }
  };

  return (
    <MyModal
      title={
        formStatus === 'add'
          ? '添加评价规则'
          : formStatus === 'edit'
            ? '编辑评价规则'
            : '复制评价规则'
      }
      isOpen={true}
      onClose={onClose}
      closeOnOverlayClick={false}
      minW={['850px']}
    >
      <ModalBody pt={respDims(24)} borderBottom="1px solid #E5E7EB">
        <OverlayContainer></OverlayContainer>
        <Form
          form={form}
          initialValues={
            ruleDetail || {
              matchType: MatchType.AutoMatch,
              evaluatedType: undefined,
              evaluatorType: undefined
            }
          }
          className={`${styles['my-form']} ${styles['my-form-vertical']}`}
          layout="vertical"
        >
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.evaluateds !== currentValues.evaluateds ||
              prevValues.term !== currentValues.term ||
              prevValues.periodType !== currentValues.periodType ||
              prevValues.startTime !== currentValues.startTime ||
              prevValues.endTime !== currentValues.endTime ||
              prevValues.evaluators !== currentValues.evaluators ||
              prevValues.indicators !== currentValues.indicators ||
              prevValues.semesterId !== currentValues.semesterId ||
              prevValues.evaluatedType !== currentValues.evaluatedType ||
              prevValues.evaluatorType !== currentValues.evaluatorType ||
              prevValues.matchType !== currentValues.matchType ||
              prevValues.evaluatedWays !== currentValues.evaluatedWays ||
              prevValues.reflectionId !== currentValues.reflectionId
            }
          >
            {({ getFieldValue }) => {
              const evaluateds = getFieldValue('evaluateds');
              const semesterId = getFieldValue('semesterId');
              const periodType = getFieldValue('periodType');
              const startTime = getFieldValue('startTime');
              const endTime = getFieldValue('endTime');
              const evaluators = getFieldValue('evaluators');
              const indicators = getFieldValue('indicators');
              const evaluatedType = getFieldValue('evaluatedType') as EvaluateeType;
              const evaluatorType = getFieldValue('evaluatorType') as EvaluateeType;
              const matchType = getFieldValue('matchType') as MatchType;
              const reflectionId = getFieldValue('reflectionId');
              const evaluatedDetails = getFieldValue('evaluatedDetails') as {
                deptId: string;
                tmbId: string;
              }[];
              const evaluatedWays = getFieldValue('evaluatedWays') as EvaluatedWayEnum[];
              const enabledIndacatorSelect = semesterId && periodType && endTime && startTime;

              const isProfessionalAssessment =
                getReflection(reflectionId && reflectionId[reflectionId.length - 1])?.name ===
                evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name;

              return (
                <>
                  <Form.Item name="id" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item name="name" label="评价规则名称" rules={[{ required: true }]}>
                    <Input placeholder="请输入评价规则名称" disabled={processInDisabled} />
                  </Form.Item>

                  <Form.Item
                    name="matchType"
                    label="评价方和被评价方匹配规则"
                    rules={[{ required: true, message: '请选择匹配规则' }]}
                  >
                    <Radio.Group onChange={handleMatchTypeChange} disabled={processInDisabled}>
                      {Object.values(MatchTypeMap).map(({ value, label }) => (
                        <Radio key={value} value={value}>
                          <Flex alignItems="center">
                            {label}
                            <MyTooltip
                              label={
                                value === MatchType.AutoMatch
                                  ? '系统根据年级、班级、学科自动生成评价方式下具体的评价人和相应的评价方式下具体的被评价人'
                                  : '手动指定评价方式下具体的评价人、和被评价方式下具体的被评价人'
                              }
                              maxW={respDims(200)}
                              placement="top"
                            >
                              <QuestionOutlineIcon
                                w={'0.9rem'}
                                ml={respDims(10)}
                                mb={respDims(1)}
                              />
                            </MyTooltip>
                          </Flex>
                        </Radio>
                      ))}
                    </Radio.Group>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="reflectionId"
                        label={
                          <Flex justifyContent="space-between" alignItems="center">
                            <Text>评价入口</Text>
                          </Flex>
                        }
                        rules={[{ required: true, message: '请选择评价入口' }]}
                      >
                        <Cascader
                          options={entranceTree}
                          placeholder="请选择评价入口"
                          onChange={handleEntranceChange}
                          fieldNames={{ label: 'name', value: 'id', children: 'children' }}
                          showCheckedStrategy={Cascader.SHOW_CHILD}
                          dropdownStyle={{ zIndex: 2000 }}
                          disabled={processInDisabled}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      {teachers?.length ? (
                        <Form.Item
                          name="evaluatorType"
                          label={
                            <Flex justifyContent="space-between" alignItems="center" w="100%">
                              <Text>评价方</Text>
                              {
                                <Text>
                                  已{matchType == MatchType.CustomMatch ? '选择' : '匹配'}：教师/
                                  {evaluators?.length || 0}人
                                </Text>
                              }
                            </Flex>
                          }
                          rules={[{ required: true, message: '请选择评价方' }]}
                        >
                          {matchType === MatchType.CustomMatch ? (
                            <Flex
                              onClick={() => handleSelectPerson('evaluators')}
                              w="100%"
                              bg="#F6F6F6"
                              borderRadius="8px"
                              justifyContent="center"
                              alignItems="center"
                              py={respDims(7)}
                              cursor="pointer"
                              color="primary.500"
                            >
                              <SvgIcon name="plus" mr={respDims(6)}></SvgIcon>
                              {processInDisabled ? '查看' : '选择评价方'}
                            </Flex>
                          ) : (
                            <Select
                              disabled={processInDisabled}
                              placeholder="请选择评价方"
                              dropdownStyle={{ zIndex: 2000 }}
                            >
                              {EvaluateeTypeMap[evaluatorType] && (
                                <Option key={evaluatorType} value={evaluatorType}>
                                  {EvaluateeTypeMap[evaluatorType].label}
                                </Option>
                              )}
                            </Select>
                          )}
                        </Form.Item>
                      ) : (
                        <>
                          教师列表为空，请
                          <AntButton
                            type="link"
                            onClick={() => {
                              // 打开新窗口
                              window.open('/tenant/basicInfo/teachManage', '_blank');
                            }}
                          >
                            前往教学管理同步
                          </AntButton>
                        </>
                      )}
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        name="evaluatedType"
                        label={
                          <Flex justifyContent="space-between" alignItems="center">
                            <Text>被评价方</Text>
                          </Flex>
                        }
                        rules={[{ required: true, message: '请选择被评价方' }]}
                      >
                        {
                          <Select
                            disabled={processInDisabled}
                            placeholder="请选择被评价方"
                            dropdownStyle={{ zIndex: 2000 }}
                            value={evaluatedType}
                          >
                            {EvaluateeTypeMap[evaluatedType] && (
                              <Option key={evaluatedType} value={evaluatedType}>
                                {EvaluateeTypeMap[evaluatedType].label}
                              </Option>
                            )}
                          </Select>
                        }
                      </Form.Item>
                    </Col>

                    {!isProfessionalAssessment ? (
                      <Col span={12}>
                        <Form.Item
                          name="evaluateds"
                          label={
                            <HStack>
                              <Text>评价年级和班级</Text>
                              {processInDisabled && <Link onClick={hanldeLookClazz}> 查看 </Link>}
                            </HStack>
                          }
                          rules={[{ required: true, message: '请选择评价年级和班级' }]}
                        >
                          <Cascader
                            disabled={processInDisabled}
                            options={treeData || []}
                            multiple
                            fieldNames={{
                              label: 'deptName',
                              value: 'id',
                              children: 'children'
                            }}
                            showCheckedStrategy={Cascader.SHOW_CHILD}
                            dropdownStyle={{ overflowX: 'auto', zIndex: 2000 }}
                            maxTagCount="responsive"
                            placeholder="请选择评价年级和班级"
                            onChange={handleClazzChange}
                            displayRender={(label, selectedOptions) => {
                              return label.join('/');
                            }}
                          />
                        </Form.Item>
                      </Col>
                    ) : matchType === MatchType.AutoMatch ? (
                      <Col span={12}>
                        <Form.Item
                          name="evaluatedWays"
                          label="被评价对象"
                          rules={[{ required: true }]}
                        >
                          <Select
                            disabled={processInDisabled}
                            placeholder="请选择被评价对象"
                            mode="multiple"
                            dropdownStyle={{ zIndex: 2000 }}
                            onChange={(value) => {
                              getMatchTeachers();
                            }}
                          >
                            {Object.values(EvaluatedWayEnumMap).map(({ value, label }) => (
                              <Option key={value} value={value}>
                                {label}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                    ) : (
                      <Col span={12}>
                        <Form.Item
                          name="evaluatedDetails"
                          label={
                            <Flex justifyContent="space-between" alignItems="center" w="100%">
                              <Text>被评价对象</Text>
                              {
                                <Text>
                                  已选择：教师/
                                  {evaluatedDetails?.length || 0}人
                                </Text>
                              }
                            </Flex>
                          }
                          rules={[{ required: true, message: '请选择被评价对象' }]}
                        >
                          <Flex
                            onClick={() => handleSelectPerson('evaluatedDetails')}
                            w="100%"
                            bg="#F6F6F6"
                            borderRadius="8px"
                            justifyContent="center"
                            alignItems="center"
                            py={respDims(7)}
                            cursor="pointer"
                            color="primary.500"
                          >
                            <SvgIcon name="plus" mr={respDims(6)}></SvgIcon>
                            {processInDisabled ? '查看' : '选择被评价对象'}
                          </Flex>
                        </Form.Item>
                      </Col>
                    )}

                    {}
                  </Row>

                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item
                        name="semesterId"
                        label="评价学年学期"
                        rules={[{ required: true }]}
                      >
                        <Box display="none">{semesterId}</Box>
                        <SemesterSelect
                          value={semesterId}
                          onChange={(value) => {
                            form.setFieldValue('semesterId', value);
                          }}
                          onSelect={(value) => {
                            form.setFieldValue('periodType', undefined);
                          }}
                          disabled={processInDisabled}
                          dropdownStyle={{ zIndex: 2000 }}
                          placeholder="请选择评价学年"
                          style={{
                            width: '100%'
                          }}
                        ></SemesterSelect>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="periodType" label="评价周期" rules={[{ required: true }]}>
                        <Select
                          disabled={processInDisabled}
                          dropdownStyle={{ zIndex: 2000 }}
                          placeholder="请选择评价周期"
                        >
                          {Object.values(PeriodTypeMap).map(({ value, label }) => (
                            <Option key={value} value={value}>
                              {label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="评价时间"
                        name="timeRange"
                        rules={[{ required: true, validator: validateTimeRange }]}
                      >
                        <Form.Item name="startTime" noStyle>
                          <DatePicker
                            disabled={processInDisabled}
                            showTime={{ format: 'HH:mm' }}
                            format="YYYY-MM-DD HH:mm"
                            style={{ width: '45%' }}
                            popupStyle={{ zIndex: 2000 }}
                            onChange={() => {
                              form.validateFields(['timeRange']);
                            }}
                          />
                        </Form.Item>
                        <span
                          style={{ display: 'inline-block', width: '10%', textAlign: 'center' }}
                        >
                          ~
                        </span>
                        <Form.Item name="endTime" noStyle>
                          <DatePicker
                            showTime={{ format: 'HH:mm' }}
                            format="YYYY-MM-DD HH:mm"
                            style={{ width: '45%' }}
                            popupStyle={{ zIndex: 2000 }}
                            onChange={() => {
                              form.validateFields();
                            }}
                          />
                        </Form.Item>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item
                    label="关联指标"
                    name="indicators"
                    rules={[{ required: true, message: '请选择评价指标' }]}
                  >
                    {
                      <HStack alignItems="center">
                        {matchType === MatchType.AutoMatch ? (
                          <AntButton
                            type={'primary'}
                            onClick={() => handleAutoGetIndicator(evaluateds)}
                            disabled={
                              (isProfessionalAssessment && !evaluatedWays?.length) ||
                              (!isProfessionalAssessment && !evaluateds?.length) ||
                              !enabledIndacatorSelect
                            }
                          >
                            匹配评价指标
                          </AntButton>
                        ) : (
                          <AntButton
                            type={'primary'}
                            onClick={() => handleSelectIndicator('select')}
                            disabled={
                              (isProfessionalAssessment && !evaluatedDetails?.length) ||
                              (!isProfessionalAssessment && !evaluateds?.length) ||
                              !enabledIndacatorSelect
                            }
                          >
                            选择评价指标
                          </AntButton>
                        )}
                        {indicators?.length > 0 && (
                          <AntButton onClick={() => handleSelectIndicator('view')}>查看</AntButton>
                        )}
                        {indicators?.length > 0 && (
                          <Text>已关联 {(indicators || []).length} 个指标</Text>
                        )}
                      </HStack>
                    }
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>
        </Form>
      </ModalBody>
      <ModalFooter>
        <Button onClick={onClose} style={{ marginRight: 8 }} variant={'grayBase'}>
          取消
        </Button>
        <Button onClick={handleSubmit} variant="primary">
          完成
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditEvaluationRuleModal;
