import React, { useCallback, useEffect, useState } from 'react';
import { Form, Input, Select, InputNumber, Radio, Checkbox, Space, Switch } from 'antd';
import { useTranslation } from 'next-i18next';
import { useRequest } from '@/hooks/useRequest';
import {
  addIndicatorCreate,
  indicatorIconCreateAdd,
  getIndicatorIconList,
  updateIndactor,
  deleteIndicatorIcon
} from '@/api/tenant/evaluate/indicator';
import MyModal from '@/components/MyModal';
import { ModalBody, ModalFooter, Button, Image, Box, Grid, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import styles from '@/pages/index.module.scss';
import { getScoreLevelList, getScoreLevelValueList } from '@/api/tenant/evaluate/score';
import { Indicator, EvaluaIndactorVO, IconListType } from '@/types/api/tenant/evaluate/indicator';
import {
  IndicatorType,
  EvaluationType,
  HasSubIndicator,
  EducationStage,
  ScoreInputType,
  UseLogo,
  NeedSignature,
  Status,
  EvaluateLevelStatus
} from '@/constants/api/tenant/evaluate/rule';
import { useQuery } from '@tanstack/react-query';
import Avatar from '@/components/Avatar';
import { useSelectFile } from '@/hooks/useSelectFile';
import { uploadImage } from '@/utils/file';
import { Toast } from '@/utils/ui/toast';
import { getErrText } from '@/utils/string';
import { useToast } from '@/hooks/useToast';
import { myToFixed } from '@/utils/tools';
import { EvaluaScoreLevel, EvaluaScoreLevelValue } from '@/types/api/tenant/evaluate/score';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EvaluateLevelModal from '../RulesTab/components/EvaluateLevelModal';
import SvgIcon from '@/components/SvgIcon';
const { Option } = Select;

const EditIndicatorModal = ({
  onClose,
  onSuccess,
  formStatus,
  indicatorId,
  initialData = {
    scoreInputType: ScoreInputType.Input,
    stage: [EducationStage.JuniorHighSchool, EducationStage.PrimarySchool]
  },
  parentNode,
  projectId,
  parentId
}: {
  onClose: () => void;
  onSuccess: () => void;
  formStatus: 'create' | 'edit';
  indicatorId?: string;
  initialData?: Partial<Indicator>;
  projectId?: string;
  parentId?: string;
  parentNode?: EvaluaIndactorVO;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [iconList, setIconList] = useState<IconListType[]>([]);
  const [selectedIcon, setSelectedIcon] = useState<string | null>(null);
  const [scoreLevelList, setScoreLevelList] = useState<EvaluaScoreLevel[]>([]);
  const [scoreLevelValueList, setScoreLevelValueList] = useState<EvaluaScoreLevelValue[]>([]);
  const [scoreLevelId, setScoreLevelId] = useState<string | null>(null);
  const [maxScoreRate, setMaxScoreRate] = useState<number>(100);
  const { openOverlay, OverlayContainer } = useOverlayManager();
  const { toast } = useToast();
  const [hoveredIconId, setHoveredIconId] = useState<string | null>(null);

  const { File, onOpen: onOpenSelectFile } = useSelectFile({
    fileType: '.jpg,.png',
    multiple: false
  });

  const typeMap = {
    create: {
      title: t('添加评价指标'),
      action: addIndicatorCreate
    },
    edit: {
      title: t('编辑评价指标'),
      action: updateIndactor
    }
  };

  useEffect(() => {
    if (parentNode) {
      const parentScoreRate = (parentNode.scoreRate || 0) * 100;
      const siblingsScoreRate = (parentNode.sub || [])
        .filter((child) => child.id !== indicatorId)
        .reduce((sum, child) => sum + (child.scoreRate || 0) * 100, 0);

      const availableScoreRate = parentScoreRate - siblingsScoreRate;
      setMaxScoreRate(myToFixed(Math.max(0, availableScoreRate), 2));
    } else {
      setMaxScoreRate(100);
    }
  }, [parentNode, indicatorId]);

  const onOpenEvaluateLevel = () => {
    openOverlay({
      Overlay: EvaluateLevelModal,
      props: {
        onClose: () => {},
        onSuccess() {
          scoreLevelListRefetch();
        }
      }
    });
  };

  const toggleIcon = (fileKey: string) => {
    if (selectedIcon === fileKey) {
      setSelectedIcon(null);
    } else {
      setSelectedIcon(fileKey);
    }
  };

  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: (data: Indicator) => {
      let modifiedData = { ...data };

      if (modifiedData.scoreRate) {
        modifiedData.scoreRate = myToFixed(modifiedData.scoreRate / 100, 4);
      }

      if (Array.isArray(modifiedData.stage)) {
        modifiedData.stage = modifiedData.stage.join(',');
      }

      modifiedData.needSign = modifiedData.needSign ? NeedSignature.Yes : NeedSignature.No;
      modifiedData.required = modifiedData.required ? NeedSignature.Yes : NeedSignature.No;

      if (modifiedData.score === null || modifiedData.score === undefined) {
        modifiedData.score = '' as any;
      }

      if (formStatus === 'create') {
        return addIndicatorCreate(modifiedData);
      } else {
        if (!indicatorId) throw new Error('Indicator ID is required for editing');
        return updateIndactor(modifiedData);
      }
    },
    onSuccess: () => {
      onClose();
      onSuccess();
      Toast.success('操作成功');
    }
  });

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      console.log(values, selectedIcon, '1');
      // 检查是否勾选了"使用图标"但没有选择具体图标
      if (values.isUseLogo && !selectedIcon) {
        toast({
          status: 'warning',
          title: '请选择图标'
        });
        return;
      }

      const indicatorData: Indicator = {
        ...values,
        type: IndicatorType.EvaluationIndicator,
        parentId: parentNode ? parentId : undefined,
        projectId: parentNode ? projectId : parentId,
        hasSub: values.hasSub ? HasSubIndicator.Yes : HasSubIndicator.No,
        iconFileKey: values.isUseLogo ? selectedIcon : undefined
      };
      if (formStatus === 'edit' && indicatorId) {
        indicatorData.id = indicatorId;
      }
      onSave(indicatorData);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const [existChild, setExistChild] = useState(initialData.hasSub === HasSubIndicator.Yes);
  const handleExist = (event: CheckboxChangeEvent) => {
    setExistChild(event.target.checked);
    if (event.target.checked) {
      form.setFieldValue('isUseLogo', !event.target.checked);
    }
  };

  const handleDeleteIcon = (id: string) => {
    deleteIndicatorIcon({ id }).then(() => {
      Toast.success('删除成功');
      refetch();
    });
  };

  const { refetch } = useQuery(['iconList'], () => getIndicatorIconList(), {
    onSuccess(data) {
      setIconList(data || []);
    }
  });

  const { data: scoreLevelData, refetch: scoreLevelListRefetch } = useQuery(
    ['scoreLevelList'],
    () => getScoreLevelList({}),
    {
      onSuccess(data) {
        setScoreLevelList(data.filter((item) => item.status === EvaluateLevelStatus.Normal) || []);
      }
    }
  );

  const { refetch: fetchScoreLevelValues } = useQuery(
    ['scoreLevelValueList'],
    () => getScoreLevelValueList({ scoreLevelId: scoreLevelId! || initialData.scoreLevelId! }),
    {
      enabled: !!(scoreLevelId && initialData.scoreLevelId),
      onSuccess: (data) => {
        setScoreLevelValueList(data || []);
      }
    }
  );

  const onSelectFile = useCallback(
    async (e: File[]) => {
      const file = e[0];
      if (!file) return;
      try {
        const data = await uploadImage(file, {
          maxWidthOrHeight: 300
        });
        indicatorIconCreateAdd({ fileKey: data.fileKey }).then((res) => {
          if (res) {
            refetch();
          }
        });
      } catch (err: any) {
        Toast.warning({
          title: getErrText(err, t('common.error.Select avatar failed'))
        });
      }
    },
    [refetch]
  );

  useEffect(() => {
    refetch();
  }, [refetch]);

  useEffect(() => {
    if (
      scoreLevelData &&
      initialData.scoreLevelId &&
      initialData.evaluationType === EvaluationType.ScoreLevelValue
    ) {
      setScoreLevelId(initialData.scoreLevelId);
      // form.setFieldsValue({ scoreLevelValueId: Number(initialData.scoreLevelValueId) });
      fetchScoreLevelValues();
    }
  }, [scoreLevelData, initialData.scoreLevelId, fetchScoreLevelValues]);

  useEffect(() => {
    if (scoreLevelId) {
      fetchScoreLevelValues();
    }
  }, [scoreLevelId, fetchScoreLevelValues]);

  useEffect(() => {
    if (initialData.iconFileKey) {
      form.setFieldsValue({ isUseLogo: 1 });
      setSelectedIcon(initialData.iconFileKey);
    }
  }, [initialData.iconFileKey]);

  useEffect(() => {
    if (initialData.scoreRate) {
      form.setFieldsValue({ scoreRate: initialData.scoreRate * 100 });
    }
  }, [initialData.scoreRate]);

  useEffect(() => {
    form.setFieldsValue({ hasSub: initialData.hasSub === HasSubIndicator.Yes ? true : false });
  }, [initialData.hasSub]);

  const handleScoreLevelChange = (value: string) => {
    setScoreLevelId(value);
    form.setFieldsValue({ scoreLevelId: value, scoreLevel: undefined });
  };

  return (
    <MyModal
      title={typeMap[formStatus].title}
      isOpen
      onClose={onClose}
      w={respDims(700)}
      closeOnOverlayClick={false}
    >
      <ModalBody mt={respDims(10)}>
        <OverlayContainer></OverlayContainer>
        <Box
          // 让底部按钮阴影显示完全
          pb="10px"
          maxH={respDims(550)}
          overflow="auto"
        >
          <Form
            layout="vertical"
            className={styles['my-form']}
            form={form}
            initialValues={{
              ...initialData,
              hasSub: initialData.hasSub === HasSubIndicator.Yes ? true : false
            }}
          >
            <Form.Item
              name="name"
              label="评价指标名称"
              rules={[{ required: true, message: '请输入评价指标名称' }]}
            >
              <Input placeholder="请输入评价指标名称" />
            </Form.Item>

            <Form.Item name="hasSub" valuePropName="checked">
              <Checkbox disabled={!!(indicatorId && initialData.hasSub)} onChange={handleExist}>
                存在下级指标
              </Checkbox>
            </Form.Item>

            {!existChild && (
              <>
                <Form.Item
                  name="stage"
                  label="评价学段"
                  rules={[{ required: true, message: '请选择评价学段' }]}
                >
                  <Checkbox.Group>
                    <Checkbox value={EducationStage.PrimarySchool}>小学</Checkbox>
                    <Checkbox value={EducationStage.JuniorHighSchool}>初中</Checkbox>
                  </Checkbox.Group>
                </Form.Item>

                <Flex pos="relative" w="100%">
                  <Form.Item
                    name="evaluationType"
                    label="评价方式"
                    rules={[{ required: true, message: '请选择评价方式' }]}
                    style={{ width: '100%' }}
                  >
                    <Select
                      dropdownStyle={{ zIndex: 2000 }}
                      placeholder="请选择评价方式"
                      style={{ flex: 1, width: '100%' }}
                    >
                      <Option value={EvaluationType.Score}>评分</Option>
                      <Option value={EvaluationType.Comment}>评语</Option>
                      <Option value={EvaluationType.ScoreLevelValue}>评等级</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) =>
                      prevValues.evaluationType !== currentValues.evaluationType
                    }
                  >
                    {({ getFieldValue }) => {
                      const evaluationType = getFieldValue('evaluationType');
                      return evaluationType === EvaluationType.ScoreLevelValue ? (
                        <Box
                          pos="absolute"
                          top="0"
                          right="0"
                          ml={2}
                          color="#4065f6"
                          cursor="pointer"
                          onClick={onOpenEvaluateLevel}
                        >
                          设置评分等级
                        </Box>
                      ) : null;
                    }}
                  </Form.Item>
                </Flex>

                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.evaluationType !== currentValues.evaluationType
                  }
                >
                  {({ getFieldValue }) => {
                    const evaluationType = getFieldValue('evaluationType');

                    if (evaluationType === EvaluationType.Score) {
                      return (
                        <>
                          <Form.Item
                            name="scoreInputType"
                            label="录入方式"
                            rules={[{ required: true, message: '请选择录入方式' }]}
                          >
                            <Radio.Group>
                              <Radio value={ScoreInputType.Input}>输入</Radio>
                              <Radio value={ScoreInputType.Fixed}>固定值</Radio>
                            </Radio.Group>
                          </Form.Item>

                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) =>
                              prevValues.scoreInputType !== currentValues.scoreInputType
                            }
                          >
                            {({ getFieldValue }) => {
                              const scoreInputType = getFieldValue('scoreInputType');

                              if (scoreInputType === ScoreInputType.Input) {
                                return (
                                  <Form.Item label="分值范围" required>
                                    <Flex alignItems="center">
                                      <Form.Item
                                        name="scoreMin"
                                        noStyle
                                        rules={[
                                          { required: true, message: '请输入最小值' },
                                          {
                                            type: 'number',
                                            min: -1000,
                                            max: 1000,
                                            message: '最小值必须在-1000到1000之间'
                                          }
                                        ]}
                                      >
                                        <InputNumber
                                          style={{ width: '100%' }}
                                          min={-1000}
                                          max={1000}
                                          placeholder="最小值"
                                        />
                                      </Form.Item>
                                      <Box m="0 10px">-</Box>
                                      <Form.Item
                                        name="scoreMax"
                                        noStyle
                                        rules={[
                                          { required: true, message: '请输入最大值' },
                                          {
                                            type: 'number',
                                            min: -1000,
                                            max: 1000,
                                            message: '最大值必须在-1000到1000之间'
                                          },
                                          ({ getFieldValue }) => ({
                                            validator(_, value) {
                                              if (!value || getFieldValue('scoreMin') <= value) {
                                                return Promise.resolve();
                                              }
                                              return Promise.reject(
                                                new Error('最大值必须大于或等于最小值')
                                              );
                                            }
                                          })
                                        ]}
                                      >
                                        <InputNumber
                                          style={{ width: '100%' }}
                                          min={-1000}
                                          max={1000}
                                          placeholder="最大值"
                                        />
                                      </Form.Item>
                                    </Flex>
                                  </Form.Item>
                                );
                              }
                              return null;
                            }}
                          </Form.Item>

                          <Form.Item
                            name="score"
                            label="评分默认值"
                            rules={[
                              ({ getFieldValue }) => ({
                                validator(_, value) {
                                  const scoreMin = getFieldValue('scoreMin');
                                  const scoreMax = getFieldValue('scoreMax');
                                  const scoreInputType = getFieldValue('scoreInputType');
                                  if (
                                    scoreInputType === ScoreInputType.Fixed ||
                                    value === '' ||
                                    value === undefined ||
                                    value === null
                                  ) {
                                    return Promise.resolve();
                                  }
                                  if (value >= scoreMin && value <= scoreMax) {
                                    return Promise.resolve();
                                  }
                                  return Promise.reject(
                                    new Error(
                                      `评分默认值必须在${scoreMin || 0}到${scoreMax || 0}之间`
                                    )
                                  );
                                }
                              }),
                              ({ getFieldValue }) => ({
                                required: getFieldValue('scoreInputType') === ScoreInputType.Fixed,
                                message: '请输入评分占比'
                              })
                            ]}
                          >
                            <InputNumber style={{ width: '100%' }} min={-1000} max={1000} />
                          </Form.Item>

                          <Form.Item
                            name="scoreRate"
                            label="评分占比"
                            rules={[
                              { required: true, message: '请输入评分占比' },
                              {
                                type: 'number',
                                min: 0,
                                max: maxScoreRate,
                                message:
                                  maxScoreRate == 0
                                    ? '已没有剩余评分占比，请调整'
                                    : `评分占比必须在0-${maxScoreRate}%之间`
                              }
                            ]}
                          >
                            <InputNumber
                              style={{ width: '100%' }}
                              min={0}
                              max={100}
                              addonAfter={'%'}
                              precision={2}
                            />
                          </Form.Item>
                        </>
                      );
                    }

                    if (evaluationType === EvaluationType.Comment) {
                      return (
                        <>
                          <Form.Item name="needSign" valuePropName="checked">
                            <Checkbox>评语需要签名</Checkbox>
                          </Form.Item>

                          <Form.Item name="required" valuePropName="checked">
                            <Flex alignItems="center">
                              <Box color="#4e5969" mr="4px" fontSize="14px">
                                是否必填
                              </Box>
                              <Switch
                                defaultChecked={
                                  initialData.required === NeedSignature.Yes ? true : false
                                }
                                onChange={(checked) => {
                                  form.setFieldsValue({ required: checked });
                                }}
                              ></Switch>
                            </Flex>
                          </Form.Item>
                        </>
                      );
                    }

                    if (evaluationType === EvaluationType.ScoreLevelValue) {
                      return (
                        <>
                          <Form.Item
                            name="scoreLevelId"
                            label="评分项目"
                            rules={[{ required: true, message: '请选择评分项目' }]}
                          >
                            <Select
                              dropdownStyle={{ zIndex: 2000 }}
                              placeholder="请选择评分项目"
                              loading={isLoading}
                              onChange={handleScoreLevelChange}
                            >
                              {scoreLevelList.map((level) => (
                                <Option key={level.id} value={level.id}>
                                  {level.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>
                          <Form.Item name="scoreLevelValueId" label="评分等级默认值">
                            <Select
                              dropdownStyle={{ zIndex: 2000 }}
                              placeholder="请选择评分等级默认值"
                              loading={isLoading}
                            >
                              {scoreLevelValueList.map((level) => (
                                <Option key={level.id} value={level.id}>
                                  {level.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </>
                      );
                    }

                    return null;
                  }}
                </Form.Item>
              </>
            )}

            {existChild && (
              <Form.Item
                name="scoreRate"
                label="评分占比"
                rules={[
                  { required: true, message: '请输入评分占比' },
                  {
                    type: 'number',
                    min: 0,
                    max: maxScoreRate,
                    message:
                      maxScoreRate == 0
                        ? '已没有剩余评分占比，请调整'
                        : `评分占比必须在0-${maxScoreRate}%之间`
                  }
                ]}
              >
                <InputNumber min={0} max={100} addonAfter={'%'} precision={2} />
              </Form.Item>
            )}

            {!existChild && (
              <Form.Item name="isUseLogo" valuePropName="checked">
                <Checkbox>使用图标</Checkbox>
              </Form.Item>
            )}
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isUseLogo !== currentValues.isUseLogo
              }
            >
              {({ getFieldValue }) => {
                const isUseLogo = getFieldValue('isUseLogo');
                if (isUseLogo) {
                  return (
                    <Grid
                      templateColumns="repeat(8, 1fr)"
                      gap={2}
                      bgColor="#f6f6f6"
                      borderRadius="8px"
                      p="14px 16px 15px 23px"
                    >
                      {iconList.map((icon) => (
                        <Flex
                          w="42px"
                          h="42px"
                          key={icon.id}
                          onClick={() => toggleIcon(icon.fileKey)}
                          borderRadius="full"
                          cursor="pointer"
                          alignItems="center"
                          justifyContent="center"
                          border={
                            selectedIcon === icon.fileKey
                              ? '1px solid #165dff'
                              : '1px solid transparent'
                          }
                          _hover={{ boxShadow: 'md' }}
                          position="relative"
                          onMouseEnter={() => setHoveredIconId(icon.id)}
                          onMouseLeave={() => setHoveredIconId(null)}
                        >
                          <Image
                            w="40px"
                            h="40px"
                            src={icon?.fileUrl}
                            objectFit={'cover'}
                            alt={`Icon ${icon.id}`}
                            borderRadius="50%"
                            userSelect="none"
                            draggable={false}
                            border="3px solid #fff"
                          />

                          {hoveredIconId === icon.id && (
                            <SvgIcon
                              pos="absolute"
                              top="-3px"
                              right="-3px"
                              onClick={() => handleDeleteIcon(icon.id)}
                              w="14px"
                              h="14px"
                              name="close"
                            />
                          )}
                        </Flex>
                      ))}
                      <Avatar
                        isAddIcon
                        onAddClick={onOpenSelectFile}
                        flexShrink={0}
                        w={['38px']}
                        h={['38px']}
                        cursor={'pointer'}
                        userSelect="none"
                      />
                    </Grid>
                  );
                }
                return null;
              }}
            </Form.Item>
          </Form>
        </Box>
      </ModalBody>
      <ModalFooter>
        <Button variant="grayBase" mr={3} onClick={onClose}>
          {t('取消')}
        </Button>
        <Button onClick={handleOk} isLoading={isLoading}>
          {t('确定')}
        </Button>
      </ModalFooter>

      <File onSelect={onSelectFile} />
    </MyModal>
  );
};

export default EditIndicatorModal;
