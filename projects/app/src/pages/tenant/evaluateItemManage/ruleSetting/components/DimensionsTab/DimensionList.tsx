import React, { useCallback, useState, useRef, useEffect } from 'react';
import { Box, Flex, Button as ChakraButton, Button, Image } from '@chakra-ui/react';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { useIndicator } from '../IndicatorContext';
import { Input, InputRef } from 'antd';
import { respDims } from '@/utils/chakra';
import { AddIcon } from '@chakra-ui/icons';
import { MyTableRef } from '@/components/MyTable/types';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DimenType, EvaluaDimension } from '@/types/api/tenant/evaluate/dimension';
import {
  listDimension,
  addDimensionV2,
  deleteDimension,
  sortDimension,
  listDimenType,
  deleteDimenType,
  updateDimension
} from '@/api/tenant/evaluate/dimension';
import { useToast } from '@/hooks/useToast';
import { MessageBox, promisifyDelete } from '@/utils/ui/messageBox';
import SvgIcon from '@/components/SvgIcon';
import EditDimensionCategory from './EditDimensionCategory';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { Toast } from '@/utils/ui/toast';

const DimensionList: React.FC = () => {
  const {
    selectedDimensionCategory,
    selectedDimension,
    setSelectedDimension,
    setSelectedDimensionCategory
  } = useIndicator();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const tableRef = useRef<MyTableRef>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [dimensions, setDimensions] = useState<EvaluaDimension[]>([]);
  const inputRef = useRef<InputRef>(null); // 使用 InputRef 类型

  useEffect(() => {
    if (editingId === 'new' && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editingId]);

  const {
    data: dimensionsData,
    isFetching,
    refetch
  } = useQuery(
    ['dimensions', selectedDimensionCategory],
    () =>
      listDimension({
        typeId: selectedDimensionCategory?.id!
      }),

    {
      enabled: !!selectedDimensionCategory,
      onSuccess: (data) => {
        console.log('dimensionsData', data);
        setDimensions(data);
        setEditingId('');
        if (data.length > 0) {
          setSelectedDimension(data[0] || '');
        } else {
          setSelectedDimension(null);
        }
      }
    }
  );

  const addMutation = useMutation(addDimensionV2, {
    onSuccess: (newDimension) => {
      queryClient.invalidateQueries(['dimensions', selectedDimensionCategory]);
      refetch();
      toast({
        title: '添加成功',
        status: 'success'
      });
      setEditingId(null);
    },
    onError: (error) => {}
  });

  const updateMutation = useMutation(updateDimension, {
    onSuccess: () => {
      queryClient.invalidateQueries(['dimensions', selectedDimensionCategory]);
      toast({
        title: '更新成功',
        status: 'success'
      });
      setEditingId(null);
    }
  });

  const deleteMutation = useMutation(deleteDimension, {
    onSuccess: () => {
      queryClient.invalidateQueries(['dimensions', selectedDimensionCategory]);
      toast({
        title: '删除成功',
        status: 'success',
        duration: 2000,
        isClosable: true
      });
    }
  });

  const handleEdit = (record: EvaluaDimension) => {
    setEditingId(record.id || null);
    setEditingName(record.name || '');
  };

  const handleSaveEdit = (record: EvaluaDimension) => {
    if (!editingName) {
      return Toast.info('请输入名称');
    }

    if (record.id === 'new') {
      addMutation.mutate({
        name: editingName,
        tenantId: selectedDimensionCategory?.id,
        typeId: selectedDimensionCategory?.id || 1
      });
    } else if (record.id) {
      updateMutation.mutate({
        id: record.id,
        name: editingName,
        // status: record.status,
        typeId: selectedDimensionCategory?.id
      });
    }
  };

  const handleCancelEdit = () => {
    if (editingId === 'new') {
      setDimensions(dimensions.filter((d) => d.id !== 'new'));
    }
    setEditingId(null);
  };
  const handleDelete = async (record: EvaluaDimension) => {
    await promisifyDelete({
      title: '确定删除该维度？'
    });
    deleteMutation.mutate({ id: record.id! });
  };

  const handleAdd = () => {
    if (editingId) {
      // 如果已经在编辑状态，聚焦到当前编辑的输入框
      if (inputRef.current) {
        inputRef.current.focus();
      }
      toast({
        title: '请先完成当前编辑',
        status: 'warning',
        duration: 2000,
        isClosable: true
      });
      return;
    }
    const newDimension: EvaluaDimension = {
      id: 'new',
      name: '',
      // sortNo: dimensions.length || 0,
      typeId: selectedDimensionCategory?.id
    };
    setDimensions([...dimensions, newDimension]);
    setEditingId('new');
    setEditingName('');
  };

  const columns: ColumnsType<EvaluaDimension> = [
    {
      title: '维度',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => {
        if (editingId === record.id) {
          return (
            <Input
              ref={inputRef}
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              onPressEnter={() => handleSaveEdit(record as EvaluaDimension)}
              autoFocus
            />
          );
        }
        return text;
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 90,
      render: (_, record) => (
        <Flex alignItems="center" justifyContent="flex-end">
          {editingId === record.id ? (
            <>
              <Box
                color="#0052D9"
                fontSize={respDims(14, 12)}
                cursor="pointer"
                mr={respDims(8, 4)}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSaveEdit(record as EvaluaDimension);
                }}
              >
                保存
              </Box>
              <Box
                color="#F6685D"
                fontSize={respDims(14, 12)}
                cursor="pointer"
                mr={respDims(8, 4)}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCancelEdit();
                }}
              >
                取消
              </Box>
            </>
          ) : (
            <>
              <Box
                color="#0052D9"
                fontSize={respDims(14, 12)}
                cursor="pointer"
                mr={respDims(8, 4)}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEdit(record as EvaluaDimension);
                }}
              >
                编辑
              </Box>
              <Box
                color="#F6685D"
                fontSize={respDims(14, 12)}
                cursor="pointer"
                mr={respDims(8, 4)}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(record as EvaluaDimension);
                }}
              >
                删除
              </Box>
            </>
          )}
        </Flex>
      )
    }
  ];

  const { openOverlay } = useOverlayManager();

  const {
    data: tabs,
    isLoading,
    error
  } = useQuery(['dimensionCategories'], () => listDimenType({}), {
    onSuccess: (data) => {
      if (data && data[0]) {
        setSelectedDimensionCategory(data[0] || '');
      } else {
        setSelectedDimensionCategory(null);
      }
    }
  });

  const onCurrentTab = (tab: DimenType) => {
    setSelectedDimensionCategory(tab);
  };

  const handleAddTab = () => {
    openOverlay({
      Overlay: EditDimensionCategory,
      props: {
        formStatus: 'create',
        onSuccess: () => queryClient.invalidateQueries(['dimensionCategories'])
      }
    });
  };

  const handleEditTab = (id: string, name: string) => {
    openOverlay({
      Overlay: EditDimensionCategory,
      props: {
        formStatus: 'edit',
        id: id,
        initialName: name,
        onSuccess: () => {
          queryClient.invalidateQueries(['dimensionCategories']);
          toast({
            title: '更新成功',
            status: 'success'
          });
        }
      }
    });
  };

  const handleDeleteTab = async (id: string) => {
    try {
      await promisifyDelete({
        title: '确定删除该维度归属？'
      });
      await deleteDimenType({ id: id });
      queryClient.invalidateQueries(['dimensionCategories']);
    } catch (error) {
      console.error('Delete failed:', error);
    }
  };

  const onRow = useCallback(
    (record: EvaluaDimension) => ({
      onClick: () => {
        if (record.id !== 'new') {
          setSelectedDimension(record);
        }
      }
    }),
    [setSelectedDimension]
  );

  const handleDragSortEnd = useCallback(
    async (event: any, newDataSource: EvaluaDimension[]) => {
      const { active, over } = event;
      if (editingId) {
        return toast({
          title: '正在编辑中，请先完成编辑',
          status: 'warning',
          duration: 2000,
          isClosable: true
        });
      }

      if (active.id !== over.id) {
        const sortParams = newDataSource.map((item, index) => ({
          id: item.id!,
          sort: index + 1
        }));

        await sortDimension(sortParams);
        toast({
          status: 'success',
          title: '操作成功',
          duration: 2000,
          isClosable: true
        });
        refetch();
      }
    },
    [editingId, sortDimension, refetch, toast]
  );

  return (
    <Flex flexDirection="column" h="100%" w="100%">
      <Flex
        mb={respDims(10)}
        fontSize={respDims(18, 14)}
        alignItems="center"
        justifyContent="space-between"
        fontWeight="600"
        color="#1D2129"
      >
        评价维度
        {/* <Button aria-label="Add dimension" variant={'grayBase'} onClick={handleAddTab} ml={4}>
          <SvgIcon name="plus"> </SvgIcon>
          添加
        </Button> */}
      </Flex>
      <Flex
        bg="#f7f9fb"
        borderRadius="8px"
        flex="1"
        overflow="hidden"
        p={respDims(16)}
        flexDirection="column"
      >
        <Flex alignItems="center" justifyContent="space-between">
          <Flex alignItems="stretch" flexShrink="0" ml={respDims(10)} overflowX={'auto'} w="100%">
            {tabs?.map((tab: DimenType) => (
              <Flex
                key={tab.id}
                alignItems="center"
                pb="3px"
                whiteSpace="nowrap"
                m="10px 32px 16px 0"
                position="relative"
                {...(tab.id?.toString() === selectedDimensionCategory?.id
                  ? {
                      color: 'primary.500',
                      _after: {
                        position: 'absolute',
                        content: '""',
                        left: '0',
                        right: '0',
                        bottom: '-1px',
                        w: '100%',
                        height: '2px',
                        bgColor: 'primary.500'
                      }
                    }
                  : {
                      color: '#4E5969'
                    })}
                fontSize="14px"
                fontWeight="bold"
                cursor="pointer"
                _hover={{
                  '.action-btn': {
                    display: 'flex'
                  }
                }}
                onClick={() => onCurrentTab(tab)}
              >
                <Box>{tab.name}</Box>
                {/* <Flex ml={2} className="action-btn" display="none"> */}
                {/* <SvgIcon
                    name="edit"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditTab(tab.id?.toString() || '', tab.name);
                    }}
                    mr={respDims(8)}
                    color="#4E5969"
                  /> */}
                {/* <SvgIcon
                    name="trash"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTab(tab.id?.toString() || '');
                    }}
                    color="#4E5969"
                  /> */}
                {/* </Flex> */}
              </Flex>
            ))}

            <Flex
              whiteSpace="nowrap"
              opacity="0"
              alignItems="center"
              pb="3px"
              m="10px 32px 16px 0"
              position="relative"
              fontSize="14px"
              fontWeight="bold"
              cursor="pointer"
            ></Flex>
          </Flex>
        </Flex>
        {!!selectedDimensionCategory && (
          <>
            <Box
              flex="1"
              overflow="auto"
              css={{
                '& .selected-row': {
                  background: '#eff5fe!important'
                }
              }}
            >
              <MyTable
                ref={tableRef}
                columns={columns}
                loading={
                  isFetching ||
                  addMutation.isLoading ||
                  updateMutation.isLoading ||
                  deleteMutation.isLoading
                }
                dragConfig={{
                  enabled: true,
                  rowKey: 'id',
                  onDragEnd: handleDragSortEnd
                }}
                emptyConfig={{
                  EmptyPicComponent: () => (
                    <Image src="/imgs/common/empty.svg" w={respDims(85, 65)} alt="" />
                  )
                }}
                dataSource={dimensions}
                boxStyle={{
                  px: '0',
                  py: '0',
                  borderRadius: '12px',
                  css: {
                    '& .ant-table': {
                      borderRadius: '12px 12px 12px 12px!important',
                      overflow: 'hidden!important'
                    },
                    '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                      borderBottom: 'none'
                    }
                  }
                }}
                size="small"
                headerConfig={{
                  showHeader: false
                }}
                showHeader={false}
                pageConfig={{
                  showPaginate: false
                }}
                onRow={onRow}
                rowClassName={(record) =>
                  selectedDimension?.id === record.id ? 'selected-row' : ''
                }
              />
            </Box>
            <Flex justifyContent="flex-end">
              <Box w="100%" pt={respDims(20)}>
                <ChakraButton
                  bg="#fff"
                  color="#4E5969"
                  w="100%"
                  leftIcon={<AddIcon />}
                  onClick={handleAdd}
                >
                  新增维度
                </ChakraButton>
              </Box>
            </Flex>
          </>
        )}
      </Flex>
    </Flex>
  );
};

export default DimensionList;
