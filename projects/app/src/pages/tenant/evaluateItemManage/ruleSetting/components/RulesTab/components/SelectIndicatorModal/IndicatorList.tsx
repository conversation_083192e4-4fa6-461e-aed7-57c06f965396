import React, { Key, useEffect, useMemo, useRef, useState } from 'react';
import { useIndicator } from './IndicatorContext';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { getIndactorsByProject } from '@/api/tenant/evaluate/indicator';
import {
  EvaluaIndactorVO,
  GetIndactorTreeByProjectParams
} from '@/types/api/tenant/evaluate/indicator';
import {
  EducationStage,
  EducationStageMap,
  EvaluationType,
  EvaluateTypeMap,
  HasSubIndicator,
  HasSubIndicatorMap,
  ScoreInputType,
  ScoreInputTypeMap,
  Status
} from '@/constants/api/tenant/evaluate/rule';
import { TableRowSelection } from 'antd/lib/table/interface';
import SearchBar, { SearchCondition } from './SearchBar';
import { Box, Flex } from '@chakra-ui/react';
import { filterTree, treeToList } from '@/utils/tree';
import { useQuery } from '@tanstack/react-query';
import { myToFixed } from '@/utils/tools';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import CustomExpandIcon from '@/pages/tenant/evaluateItemManage/components/customExpandIcon';

const IndicatorList = () => {
  const { formData, currentIndicatorList, selectIndicators, setSelectIndicators } = useIndicator();
  const tableRef = useRef<MyTableRef>(null);

  const [keyword, setKeyword] = useState<SearchCondition>({});

  const filterIndicatorList = useMemo(() => {
    return filterTree(
      currentIndicatorList,
      (item) => {
        let flag1 = keyword.searchKey ? !!item.name?.includes(keyword.searchKey) : true;

        // @ts-ignore
        // let flag2:any = true;
        // console.log(item?.stage?.split(','), keyword.stage,1111);
        // if (typeof item.stage === 'string') {
        console.log(keyword.stage, currentIndicatorList);

        let flag2 =
          typeof item.stage === 'string' && keyword.stage !== '0' && keyword.stage !== undefined
            ? !!item.stage.split(',').find((item) => item == keyword.stage)
            : true;
        // }

        let flag3 = keyword.evaluationType ? item.evaluationType == keyword.evaluationType : true;

        return flag1 && flag2 && flag3;
      },
      {
        childProps: 'sub'
      }
    );
  }, [currentIndicatorList, keyword]);

  useEffect(() => {
    handleSearch({
      searchKey: '',
      stage: undefined,
      evaluationType: undefined
    });
  }, [currentIndicatorList]);

  const handleSearch = (keyword: SearchCondition) => {
    setKeyword(keyword);
    tableRef.current?.setQuery(keyword);
  };

  const columns: ColumnsType<EvaluaIndactorVO> = [
    {
      title: '评价指标',
      dataIndex: 'name',
      key: 'name',
      width: '250px'
    },
    {
      title: '存在下级指标',
      dataIndex: 'hasSub',
      key: 'hasSub',
      render: (value: HasSubIndicator) => HasSubIndicatorMap[value]?.label || '-'
    },
    {
      title: '评价学段',
      dataIndex: 'stage',
      key: 'stage',
      render: (value: string, record) => {
        if (!value || record.hasSub === HasSubIndicator.Yes) return '-';

        return (
          value
            .split(',')
            .map((stage) => EducationStageMap[stage as unknown as EducationStage]?.label)
            .filter(Boolean)
            .join('、') || '-'
        );
      }
    },
    {
      title: '评价方式',
      dataIndex: 'evaluationType',
      key: 'evaluationType',
      render: (value: EvaluationType, record) => {
        if (record.hasSub === HasSubIndicator.Yes) return '-';
        return EvaluateTypeMap[value as keyof typeof EvaluateTypeMap]?.label || '-';
      }
    },
    {
      title: '分值范围',
      dataIndex: 'scoreRange',
      key: 'scoreRange',
      render: (_, record) => {
        if (record.hasSub === HasSubIndicator.Yes) return '-';
        if (
          record.scoreInputType === ScoreInputType.Fixed ||
          record.evaluationType !== EvaluationType.Score
        )
          return '-';
        const min =
          record.scoreMin !== undefined && record.scoreMin !== null ? record.scoreMin : '-';
        const max =
          record.scoreMax !== undefined && record.scoreMax !== null ? record.scoreMax : '-';
        if (min == '-' && max == '-') {
          return '-';
        }
        return `${min} 至 ${max}`;
      }
    },
    {
      title: '评分默认值',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      render: (_, record) => {
        if (
          (record.evaluationType !== EvaluationType.Score &&
            record.evaluationType !== EvaluationType.ScoreLevelValue) ||
          record.hasSub === HasSubIndicator.Yes
        ) {
          return '-';
        }

        if (record.evaluationType == EvaluationType.Score) {
          return record.score !== undefined && record.score !== null ? record.score : '-';
        }

        if (record.evaluationType == EvaluationType.ScoreLevelValue) {
          return record.defaultLevelValue !== undefined && record.defaultLevelValue !== null
            ? record.defaultLevelValue
            : '-';
        }
      }
    },
    {
      title: '评分占比',
      dataIndex: 'scoreRate',
      key: 'scoreRate',
      align: 'center',
      render: (scoreRate, record) => {
        if (record.hasSub === HasSubIndicator.Yes) {
          return `${myToFixed(scoreRate * 100, 2)}%`;
        }
        if (record.evaluationType !== EvaluationType.Score) {
          return '-';
        }
        return `${myToFixed(scoreRate * 100, 2)}%`;
      }
    }
  ];

  const rowSelection: TableRowSelection<EvaluaIndactorVO> = {
    checkStrictly: false,
    selectedRowKeys: selectIndicators.filter((id) => id !== undefined),
    onSelect: (
      record: EvaluaIndactorVO,
      selected: boolean,
      selectedRows: EvaluaIndactorVO[],
      nativeEvent
    ) => {
      const selectIds = [
        ...treeToList([record], 'sub')
          .filter((item) => item.hasSub === HasSubIndicator.No && item.status === Status.Normal)
          .map((item) => item.id!)
      ];
      console.log('selectIds', selectIds);

      const otherSelects = selectIndicators.filter((item) => !selectIds.includes(item));
      console.log('otherSelects', otherSelects);

      if (selected) {
        setSelectIndicators([...otherSelects, ...selectIds]);
      } else {
        console.log('otherSelects', otherSelects);

        setSelectIndicators(otherSelects);
      }
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: EvaluaIndactorVO[],
      changeRows: EvaluaIndactorVO[]
    ) => {
      const currentIndicatorListFlat = treeToList(filterIndicatorList, 'sub').filter(
        (item) => item.hasSub === HasSubIndicator.No && item.status === Status.Normal
      );
      console.log(currentIndicatorListFlat, 1111);

      const otherSelects = selectIndicators.filter(
        (item) => !currentIndicatorListFlat.find((it) => it.id == item)
      );

      if (selectIndicators.length === otherSelects.length + currentIndicatorListFlat.length) {
        selected = false;
      }

      if (selected) {
        setSelectIndicators([...otherSelects, ...currentIndicatorListFlat.map((item) => item.id!)]);
      } else {
        setSelectIndicators(otherSelects);
      }
    },
    getCheckboxProps: (record: EvaluaIndactorVO) => ({
      disabled: record.status === Status.Disabled
    })
  };

  return (
    <MyTable
      columns={columns}
      ref={tableRef}
      pageConfig={{
        showPaginate: false
      }}
      pagination={false}
      showHeader={true}
      dataSource={filterIndicatorList}
      boxStyle={{
        p: 1
      }}
      emptyConfig={{
        EmptyPicComponent: () => <></>
      }}
      headerConfig={{
        HeaderComponent: (props) => {
          return (
            <Flex w="100%" justifyContent="flex-end">
              <SearchBar {...props} onSearch={handleSearch} />
            </Flex>
          );
        }
      }}
      rowSelection={rowSelection}
      expandable={{
        defaultExpandAllRows: true,
        expandIcon: CustomExpandIcon,
        childrenColumnName: 'sub'
      }}
      rowKey="id"
    />
  );
};

export default IndicatorList;
