import React, { useEffect, useState } from 'react';
import { Tree, Input, TreeDataNode } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { Box, Button, Flex, ModalBody, ModalFooter } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import MyModal from '@/components/MyModal';
import { useQuery } from '@tanstack/react-query';
import MyBox from '@/components/common/MyBox';
import { respDims } from '@/utils/chakra';
import { getClientSchoolDeptTree } from '@/api/tenant/teamManagement/student';
import { Department } from '@/types/api/tenant/teamManagement/student';
import StudentTable from './StudentTable';
import TeacherTable from './TeacherTable';
import { EvaluateeType } from '@/constants/api/tenant/evaluate/rule';
import {
  DeptTeacherType,
  EvaluaRuleEvaluator,
  TeacherType
} from '@/types/api/tenant/evaluate/rule';
import { getTeacherByTree } from '@/api/tenant/evaluate/rule';
import MyTable from '@/components/MyTable';

export enum SubDeptType {
  Stage = 1,
  Grade = 2,
  Class = 3
}

export interface TreeNode extends DataNode {
  key: string;
  title: string;
  children?: TreeNode[];
  id: string;
  deptName: string;
  subDeptType: SubDeptType;
  parentId: string;
  teachers: TeacherType[];
}

const convertDepartmentToTreeNode = (dept: DeptTeacherType): TreeNode => ({
  key: dept.id,
  title: dept.deptName,
  id: dept.id,
  deptName: dept.deptName,
  subDeptType: dept.subDeptType,
  parentId: dept.parentId,
  teachers: dept.teachers,
  children: dept.children ? dept.children.map(convertDepartmentToTreeNode) : undefined
});

const SelectClazzModal = ({
  onClose,
  selectClasses
}: {
  onClose: () => void;
  selectClasses: any;
}) => {
  const columns = [
    {
      title: '',
      dataIndex: 'name',
      key: 'name'
    }
  ];

  return (
    <MyModal title={'查看班级'} minW={respDims(450)} isOpen>
      <ModalBody
        borderTop="1px solid #E2E8F0"
        borderBottom="1px solid #E2E8F0"
        borderColor={'#E2E8F0'}
        py={0}
      >
        <Box height="300px">
          <MyTable
            rowKey={'id'}
            showHeader={false}
            pageConfig={{
              showPaginate: false
            }}
            pagination={false}
            columns={columns}
            dataSource={selectClasses}
          />
        </Box>
      </ModalBody>
      <ModalFooter>
        <Button variant={'grayBase'} mr={3} onClick={onClose}>
          {'取消'}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default SelectClazzModal;
