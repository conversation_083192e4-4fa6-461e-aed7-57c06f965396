import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Tree, Input, TreeDataNode } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { Box, Button, Flex, ModalBody, ModalFooter } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import MyModal from '@/components/MyModal';
import { useQuery } from '@tanstack/react-query';
import MyBox from '@/components/common/MyBox';
import { respDims } from '@/utils/chakra';
import { getClientSchoolDeptTree, getClientStudentPage } from '@/api/tenant/teamManagement/student';
import { Department } from '@/types/api/tenant/teamManagement/student';
import StudentTable from './StudentTable';
import TeacherTable from './TeacherTable';
import { EvaluateeType } from '@/constants/api/tenant/evaluate/rule';
import {
  DeptTeacherType,
  EvaluaRuleEvaluator,
  TeacherType
} from '@/types/api/tenant/evaluate/rule';
import { getTeacherByTree } from '@/api/tenant/evaluate/rule';
import styles from '@/pages/index.module.scss';
import { filterTree } from '@/utils/tree';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { debounce } from 'lodash';

export enum SubDeptType {
  Stage = 1,
  Grade = 2,
  Class = 3
}

export interface TreeNode extends DataNode {
  key: string;
  title: string;
  children?: TreeNode[];
  id: string;
  deptName: string;
  subDeptType: SubDeptType;
  parentId: string;
}

const convertDepartmentToTreeNode = (dept: Department): TreeNode => ({
  key: dept.id,
  title: dept.deptName,
  id: dept.id,
  deptName: dept.deptName,
  subDeptType: dept.subDeptType,
  parentId: dept.parentId,
  children: dept.children ? dept.children.map(convertDepartmentToTreeNode) : undefined
});

const SelectStudentModal = ({
  onClose,
  onSelect,
  type,
  formStatus,
  defaultSelectClazzIds
}: {
  onClose: () => void;
  onSelect: (selectedPersonIds: string[]) => void;
  type: 'evaluator' | 'evaluatee';
  formStatus: 'view' | 'select';
  defaultSelectClazzIds: string[];
}) => {
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [defaultExpandedKeys, setDefaultExpandedKeys] = useState<React.Key[]>([]);

  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const { t } = useTranslation();
  const [selectedDept, setSelectedDept] = useState<TreeNode | null>(null);
  const tableRef = useRef<MyTableRef>(null);
  const { isLoading: isTreeLoading } = useQuery(['treeData'], () => getClientSchoolDeptTree(), {
    onSuccess(data) {
      data = filterTree(data, (node) => {
        return defaultSelectClazzIds.includes(node.id);
      });
      const convertedData = (data || []).map(convertDepartmentToTreeNode);
      setTreeData(convertedData);

      // 默认选中第一个节点
      if (convertedData.length > 0) {
        const firstNode = convertedData[0];
        setSelectedKeys([firstNode.key]);
        setSelectedDept(firstNode);
      }
    }
  });

  const onTreeSelect = (selectedKeys: React.Key[], info: any) => {
    const node = info.node as TreeNode;
    setSelectedDept(node);
    setSelectedKeys([node.key]);
  };
  const columns = [
    {
      title: '学号',
      key: 'code',
      dataIndex: 'code'
    },
    {
      title: '学生姓名',
      key: 'name',
      dataIndex: 'name'
    },
    {
      title: '性别',
      key: 'sex',
      dataIndex: 'sex',
      render: (value: number) => {
        return <Box>{Number(value) === 1 ? '男' : '女'}</Box>;
      }
    }
  ];

  useEffect(() => {
    if (treeData.length > 0) {
      const allKeys = getAllKeys(treeData);
      setDefaultExpandedKeys(allKeys);
    }
  }, [treeData]);

  const onSearch = debounce(() => {
    tableRef.current?.setQuery({
      name: searchValue
    });
  }, 1000);

  const getAllKeys = (data: TreeNode[]): React.Key[] => {
    return data.reduce((keys: React.Key[], node) => {
      keys.push(node.key);
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children));
      }
      return keys;
    }, []);
  };

  useEffect(() => {
    if (tableRef.current) {
      onSearch();
    }
  }, [searchValue]);

  return (
    <MyModal title={'查看学生'} minW={'1150px'} isOpen>
      <ModalBody
        borderTop="1px solid #E2E8F0"
        borderBottom="1px solid #E2E8F0"
        borderColor={'#E2E8F0'}
        py={0}
      >
        <Flex h={'70vh'} overflow="hidden">
          <MyBox
            isLoading={isTreeLoading}
            width="20%"
            h={'100%'}
            py={2}
            borderRight="1px solid #E2E8F0"
            mr={2}
            overflow="auto"
          >
            {defaultExpandedKeys.length != 0 && (
              <Tree
                treeData={treeData}
                onSelect={onTreeSelect}
                defaultExpandAll
                defaultExpandParent
                autoExpandParent
                defaultExpandedKeys={defaultExpandedKeys}
                selectedKeys={selectedKeys}
              />
            )}
          </MyBox>
          <Flex
            width="80%"
            py={2}
            className={styles['my-form']}
            h={'100%'}
            overflow="hidden"
            flexDir="column"
          >
            <Input
              placeholder={t('请输入学生名称')}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              style={{ marginBottom: 0 }}
            />
            <MyTable
              api={getClientStudentPage}
              columns={columns}
              ref={tableRef}
              boxStyle={{
                px: respDims(0),
                py: respDims(16)
              }}
              defaultQuery={{
                stageId:
                  selectedDept?.subDeptType === SubDeptType.Stage ? selectedDept?.id : undefined,
                gradeId:
                  selectedDept?.subDeptType === SubDeptType.Grade ? selectedDept?.id : undefined,
                clazzId:
                  selectedDept?.subDeptType === SubDeptType.Class ? selectedDept?.id : undefined
              }}
              headerConfig={{
                showHeader: false
              }}
            ></MyTable>
          </Flex>
        </Flex>
      </ModalBody>
      <ModalFooter>
        <Button onClick={onClose} variant={'grayBase'}>
          {t('common.Close')}
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default SelectStudentModal;
