import React, { useRef, useMemo, useCallback, useState, useEffect } from 'react';
import { Button, TableColumnsType, Tag } from 'antd';
import { Box, Flex, Text } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import SearchBar from './components/SearchBar';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { useToast } from '@/hooks/useToast';
import { MessageBox, promisifyConfirm, promisifyDelete } from '@/utils/ui/messageBox';
import EditEvaluationRuleModal from './components/EditEvaluationRuleModal';
import {
  getEvaluationRuleList,
  copyEvaluationRule,
  deleteEvaluationRule,
  getEvaluationRuleDetail,
  getEntranceTree
} from '@/api/tenant/evaluate/rule';
import {
  RuleStatus,
  PeriodType,
  Term,
  TermMap,
  PeriodTypeMap,
  EvaluateeTypeMap,
  EvaluateeType,
  MatchType
} from '@/constants/api/tenant/evaluate/rule';
import {
  DimensionReflectionVO,
  EvaluationRuleDetail,
  TeacherType
} from '@/types/api/tenant/evaluate/rule';
import { respDims } from '@/utils/chakra';
import SelectPersonModal from './components/SelectPersonModal';
import SelectStudentModal from './components/SelectStudentModal';
import { createStyles } from 'antd-style';
import { treeFind } from '@/utils/tree';
import { useQuery } from '@tanstack/react-query';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';

const useStyle = createStyles(({ css, token }) => {
  const { antCls } = token as any;
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `
  };
});
const RulesTab = ({}) => {
  const actionRef = useRef<MyTableRef>(null);
  const { openOverlay } = useOverlayManager();
  const { toast } = useToast();
  const { styles } = useStyle();
  const [entranceTree, setEntranceTree] = useState<DimensionReflectionVO[]>([]);
  const entranceTreeRef = useRef<DimensionReflectionVO[]>([]);
  useEffect(() => {
    getEntranceTreeData();
  }, []);
  async function getEntranceTreeData() {
    const data = await getEntranceTree({}).then((res) =>
      res.map((item) => {
        // 因为第一级id重复，修改一下，
        return {
          ...item,
          disabled: !item.children?.length,
          id: item.id + '1'
        };
      })
    );
    entranceTreeRef.current = data as any;
    setEntranceTree(data as any);
  }

  const handleCopy = async (record: EvaluationRuleDetail) => {
    openOverlay({
      Overlay: EditEvaluationRuleModal,
      props: {
        ruleId: record.id,
        formStatus: 'copy',
        onClose: () => {},
        onSuccess() {
          actionRef.current?.reload();
        }
      }
    });
  };

  const handleDelete = async (record: EvaluationRuleDetail) => {
    try {
      await promisifyDelete({
        title: '删除确认',
        content: '确定要删除这条评价规则吗？'
      });
      const result = await deleteEvaluationRule({ id: record.id! });
      toast({
        status: 'success',
        title: '删除成功'
      });
      actionRef.current?.reload();
    } catch (error) {
      // 用户取消删除操作
    }
  };

  const handleEdit = (record: EvaluationRuleDetail) => {
    openOverlay({
      Overlay: EditEvaluationRuleModal,
      props: {
        formStatus: 'edit',
        ruleId: record.id,
        onClose: () => {},
        onSuccess() {
          actionRef.current?.reload();
        }
      }
    });
  };

  const handleEvaluateCount = async (
    record: EvaluationRuleDetail,
    type: 'evaluatorCount' | 'evaluatedCount'
  ) => {
    if (record.loading) return;
    record.loading = true;

    try {
      const ruleDetail = await getEvaluationRuleDetail({ id: record.id! });
      const reflection = getReflection(ruleDetail.reflectionId || '');

      const isProfessionalAssessment =
        reflection?.name ==
        evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name;

      record.loading = false;
      if (isProfessionalAssessment) {
        // 如果是师德，特殊处理

        let defaultSelectEvaluators = ruleDetail.evaluators || [];
        if (ruleDetail.matchType === MatchType.CustomMatch && type === 'evaluatedCount') {
          defaultSelectEvaluators = ruleDetail.evaluatedDetails?.map((item) => item.tmbId) || [];
        }

        openOverlay({
          Overlay: SelectPersonModal,
          props: {
            formStatus: 'view',
            defaultSelectEvaluators,
            onClose: () => {},
            onSelect: (data: TeacherType[]) => {
              console.log(data);
            }
          }
        });

        return;
      }

      if (
        (type === 'evaluatorCount' && record.evaluatorType === EvaluateeType.Student) ||
        (type === 'evaluatedCount' && record.evaluatedType === EvaluateeType.Student)
      ) {
        openOverlay({
          Overlay: SelectStudentModal,
          props: {
            type: 'evaluatee',
            formStatus: 'view',
            defaultSelectClazzIds: ruleDetail.evaluateds || [],
            onClose: () => {},
            onSelect: (data: string[]) => {
              console.log(data);
            }
          }
        });
      } else {
        let defaultSelectEvaluators =
          (type === 'evaluatorCount' ? ruleDetail.evaluators : ruleDetail.evaluateds) || [];

        openOverlay({
          Overlay: SelectPersonModal,
          props: {
            formStatus: 'view',
            defaultSelectEvaluators,
            onClose: () => {},
            onSelect: (data: TeacherType[]) => {
              console.log(data);
            }
          }
        });
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      record.loading = false;
    }
  };

  const columns: TableColumnsType<EvaluationRuleDetail> = useMemo(() => {
    return [
      {
        title: '评价规则名称',
        dataIndex: 'name',
        key: 'name',
        width: 160
      },
      {
        title: '评价方',
        dataIndex: 'evaluatorCount',
        key: 'evaluatorCount',
        width: 120,
        // 数量可以点击
        render: (num: number, record: EvaluationRuleDetail) => (
          <Box>
            {`${record.evaluatorType && EvaluateeTypeMap[record.evaluatorType].label}`}(
            <Text
              onClick={() => handleEvaluateCount(record, 'evaluatorCount')}
              color="primary.500"
              display="inline"
              cursor="pointer"
            >
              {record.evaluatorCount}人
            </Text>
            )
          </Box>
        )
      },
      {
        title: '被评价方',
        dataIndex: 'evaluatedCount',
        key: 'evaluatedCount',
        width: 120,
        render: (num: number, record: EvaluationRuleDetail) => (
          <Box>
            {`${record.evaluatedType && EvaluateeTypeMap[record.evaluatedType].label}`} (
            <Text
              onClick={() => handleEvaluateCount(record, 'evaluatedCount')}
              color="primary.500"
              display="inline"
              cursor="pointer"
            >
              {record.evaluatedCount}人
            </Text>
            )
          </Box>
        )
      },

      // {
      //   title: '评价学年',
      //   dataIndex: 'semesterId',
      //   key: 'semesterId',
      //   render: (semesterId: string) => {

      //     const semester = semesterListTermLevel.find(item => item.id == semesterId);
      //     return semester ? semester.year : '';
      //   }
      // },
      // {
      //   title: '评价学期',
      //   dataIndex: 'semesterId',
      //   key: 'semesterId',
      //   render: (semesterId: string) => {
      //     const semester = semesterListTermLevel.find(item => item.id == semesterId);
      //     const getSemesterLabel = (sem: { year: string; type: number }) => {
      //       const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
      //       return `${typeMap[sem.type] || ''}`;
      //     };
      //     return semester ? getSemesterLabel(semester) : '';
      //   }
      // },
      {
        title: '评价周期',
        dataIndex: 'periodType',
        key: 'periodType',
        width: 100,
        render: (periodType: PeriodType) =>
          PeriodTypeMap[periodType] && PeriodTypeMap[periodType].label
      },
      {
        title: '评价时间',
        key: 'evaluationTime',
        width: 160,
        render: (_: any, record: EvaluationRuleDetail) =>
          `${record.startTime?.slice(0, 16)} ~ ${record.endTime?.slice(0, 16)}`
      },
      {
        title: '关联指标(个)',
        dataIndex: 'indicatorCount',
        key: 'indicatorCount',
        width: 120
      },
      {
        title: '评价状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render: (status: RuleStatus) => {
          const statusMap = {
            [RuleStatus.NotStarted]: { color: 'orange', text: '未开始' },
            [RuleStatus.InProgress]: { color: 'green', text: '进行中' },
            [RuleStatus.Finished]: { color: 'default', text: '已结束' }
          };
          return (
            <Tag color={statusMap[status] && statusMap[status]!.color}>
              {statusMap[status] && statusMap[status].text}
            </Tag>
          );
        }
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 180
      },
      {
        title: '操作',
        key: 'action',
        width: 220,
        fixed: 'right',
        render: (_: any, record: EvaluationRuleDetail) => (
          <>
            {record.ruleStatus !== RuleStatus.Finished && (
              <Button type="link" onClick={() => handleEdit(record)}>
                编辑
              </Button>
            )}

            <Button type="link" onClick={() => handleCopy(record)}>
              复制
            </Button>
            {record.ruleStatus !== RuleStatus.InProgress && (
              <Button type="link" danger onClick={() => handleDelete(record)}>
                删除
              </Button>
            )}
          </>
        )
      }
    ];
  }, []);

  const getReflection = (reflectionId: string) => {
    const treeData = [...(entranceTreeRef.current || [])];
    if (!entranceTreeRef.current) return null;

    const res = treeFind(
      treeData,
      (node, parent, level) => node.id.toString() == reflectionId && level >= 1
    );
    return res;
  };

  return (
    <MyTable
      columns={columns}
      api={getEvaluationRuleList}
      ref={actionRef}
      className={styles.customTable}
      scroll={{ x: 'max-content' }}
      emptyConfig={{
        EmptyPicComponent: () => <></>
      }}
      cacheKey="ruleTabs"
      tableWrapperStyle={{
        background: '#fff',
        px: respDims(32)
      }}
      boxStyle={{
        px: 0,
        pt: respDims(16)
      }}
      footerStyle={{
        background: '#fff',
        px: respDims(32),
        pb: respDims(16)
      }}
      headerConfig={{
        showHeader: true,
        showIfEmpty: true,
        headerStyle: {
          mb: 0
        },
        HeaderComponent: (props) => (
          <Box w="100%">
            <Flex justifyContent="space-between" w="100%" alignItems="center" px={respDims(12)}>
              <Box></Box>

              <SearchBar {...props}></SearchBar>
            </Flex>
            <Box w="100%" h={respDims(16)} bg="#fff" mt={respDims(16)}></Box>
          </Box>
        )
      }}
    />
  );
};

export default RulesTab;
