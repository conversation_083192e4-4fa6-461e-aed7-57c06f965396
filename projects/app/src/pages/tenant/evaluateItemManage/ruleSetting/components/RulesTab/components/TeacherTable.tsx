import React, { Key, useEffect, useMemo } from 'react';
import { Table } from 'antd';
import { TreeNode, SubDeptType } from './SelectPersonModal';
import { EvaluaProject, SubjectType } from '@/types/api/tenant/evaluate/project';
import { Tag } from '@chakra-ui/react';
import MyTable from '@/components/MyTable';
import { EvaluaRuleEvaluator } from '@/types/api/tenant/evaluate/rule';

interface TeacherTableProps {
  selectedRows: string[];
  onSelectChange: (selectedRows: string[]) => void;
  selectedDept: TreeNode | null;
  formStatus: 'view' | 'select';
  searchValue: string;
}

const TeacherTable: React.FC<TeacherTableProps> = ({
  selectedRows,
  onSelectChange,
  selectedDept,
  formStatus,
  searchValue
}) => {
  const columns = [
    { title: '姓名', dataIndex: 'username', key: 'username' },
    { title: '角色', dataIndex: 'roleName', key: 'roleName' },
    {
      title: '学科',
      dataIndex: 'subjects',
      key: 'subjects',
      render: (value: SubjectType[]) => {
        return (
          <>
            {value?.slice(0, 2).map((item, index) => {
              return (
                <Tag color="#2BA471" bgColor="#E3F9E9" mr={2} key={index}>
                  {item.subjectName}
                </Tag>
              );
            })}
          </>
        );
      }
    }
  ];

  const handleSelectChange = (_: any, newSelectedRows: EvaluaRuleEvaluator[]) => {
    const currentDeptTeacherIds = [
      ...new Set(selectedDept?.teachers?.map((teacher) => teacher.id))
    ];

    // 保留其他部门的选中行
    const otherDeptSelectedRows = selectedRows?.filter(
      (rowId) => !currentDeptTeacherIds.find((id) => id == rowId)
    );

    // 合并当前部门的新选择和其他部门的选择
    const updatedSelectedRows = [
      ...otherDeptSelectedRows,
      ...newSelectedRows.map((item) => item.id)
    ].filter((id): id is string => id !== undefined); // 过滤掉 undefined

    onSelectChange(updatedSelectedRows);
  };

  // 获取当前部门的选中行
  const currentDeptSelectedRowKeys = useMemo(() => {
    const currentDeptTeacherIds = [
      ...new Set(selectedDept?.teachers?.map((teacher) => teacher.id))
    ];
    return (
      selectedRows
        ?.filter((rowId) => !!currentDeptTeacherIds.find((id) => id == rowId))
        .map((rowId) => String(rowId)) || []
    );
  }, [selectedRows, selectedDept]);

  return (
    <MyTable
      rowKey="id"
      rowSelection={{
        selectedRowKeys: currentDeptSelectedRowKeys as Key[],
        onChange: handleSelectChange,
        getCheckboxProps: (record) => {
          return {
            disabled: formStatus === 'view'
          };
        }
      }}
      boxStyle={{
        py: 0,
        px: 0
      }}
      pageConfig={{
        showPaginate: false
      }}
      pagination={false}
      columns={columns}
      dataSource={selectedDept?.teachers.filter((teacher) => {
        if (searchValue) {
          return (
            teacher.username?.includes(searchValue) ||
            teacher.subjects?.some((item) => item.subjectName?.includes(searchValue))
          );
        }
        return true;
      })}
    />
  );
};

export default TeacherTable;
