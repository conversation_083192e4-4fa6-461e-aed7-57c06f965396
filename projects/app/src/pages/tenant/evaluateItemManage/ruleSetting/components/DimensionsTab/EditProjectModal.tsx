import React from 'react';
import { Form, Input, Select, InputNumber, Radio } from 'antd';
import { useTranslation } from 'next-i18next';
import { useRequest, useRequest2 } from '@/hooks/useRequest';
import {
  addProjectV2,
  schoolDeptSubjectManageSubjectsV2,
  updateProject,
  updateProjectV2
} from '@/api/tenant/evaluate/project';
import MyModal from '@/components/MyModal';
import { <PERSON>dalBody, ModalFooter, Button } from '@chakra-ui/react';
import styles from '@/pages/index.module.scss';
import { EvaluaProject, EvaluaProjectCreateReq } from '@/types/api/tenant/evaluate/project';
import { ProjectType } from '@/constants/api/tenant/evaluate/rule';
import { useQuery } from '@tanstack/react-query';
import { respDims } from '@/utils/chakra';

const { Option } = Select;

const EditProjectModal = ({
  onClose,
  onSuccess,
  formStatus,
  initialData = {},
  parentId,
  typeId
}: {
  onClose: () => void;
  onSuccess: () => void;
  formStatus: 'create' | 'edit';
  parentId: string;
  typeId: number;
  initialData?: Partial<EvaluaProject>;
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const typeMap = {
    create: {
      title: t('添加评价项目'),
      action: addProjectV2
    },
    edit: {
      title: t('编辑评价项目'),
      action: updateProject
    }
  };
  // 使用React Query获取学科列表
  const { data: subjects, isLoading: subjectsLoading } = useQuery(
    ['subjects'],
    schoolDeptSubjectManageSubjectsV2,
    {
      staleTime: Infinity // 可以根据需要调整
    }
  );
  const { mutate: onSave, isLoading } = useRequest({
    mutationFn: async (projectData: EvaluaProjectCreateReq) => {
      if (formStatus === 'create') {
        return await addProjectV2(projectData);
      } else {
        return await updateProjectV2(projectData);
      }
    },
    onSuccess: () => {
      onClose();
      onSuccess && onSuccess();
    },
    successToast: t('操作成功')
  });

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      // {
      //   "dimensionId": 0,
      //   "name": "string",
      //   "scoreRate": 0,
      //   "subjectIds": [
      //     0
      //   ],
      //   "tenantId": 0,
      //   "type": 0
      // }
      const projectData: EvaluaProjectCreateReq = {
        ...values,
        type: values.type,
        scoreRate: values.scoreRate / 100,
        dimensionId: parentId
      };

      onSave(projectData);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  // 将初始值从小数转换为百分比
  const initialValues = {
    ...initialData,
    scoreRate: initialData.scoreRate ? initialData.scoreRate * 100 : undefined,
    subjectIds: initialData.subjectIds?.map((item) => item + '') || []
  };

  return (
    <MyModal title={typeMap[formStatus].title} isOpen onClose={onClose} closeOnOverlayClick={false}>
      <ModalBody pt={respDims(20)}>
        <Form
          form={form}
          layout="vertical"
          initialValues={initialValues}
          className={styles['my-form']}
        >
          <Form.Item name="id" hidden={true}>
            <Input />
          </Form.Item>
          <Form.Item
            name="name"
            label="评价项目名称"
            rules={[{ required: true, message: '请输入评价项目名称' }]}
          >
            <Input placeholder="请输入评价项目名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="项目类型"
            rules={[{ required: true, message: '请选择项目类型' }]}
          >
            <Radio.Group>
              <Radio value={ProjectType.Subject}>学科</Radio>
              <Radio value={ProjectType.NonSubject}>非学科</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const projectType = getFieldValue('type');

              return projectType === ProjectType.Subject ? (
                <Form.Item
                  name="subjectIds"
                  label="学科"
                  rules={[{ required: true, message: '请选择学科' }]}
                >
                  <Select
                    mode="multiple"
                    placeholder="请选择学科"
                    dropdownStyle={{ zIndex: 2000 }}
                    loading={subjectsLoading}
                  >
                    {subjects?.map((subject) => (
                      <Option key={subject.id} value={subject.id}>
                        {subject.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : (
                <></>
              );
            }}
          </Form.Item>

          <Form.Item
            name="scoreRate"
            label="评分占比"
            rules={[
              { required: true, message: '请输入评分占比' },
              { type: 'number', min: 0, max: 100, message: '占比必须在0-100之间' }
            ]}
          >
            <InputNumber min={0} max={100} addonAfter={'%'} />
          </Form.Item>
        </Form>
      </ModalBody>
      <ModalFooter>
        <Button variant="grayBase" mr={3} onClick={onClose}>
          {t('取消')}
        </Button>
        <Button onClick={handleOk} isLoading={isLoading}>
          提交
        </Button>
      </ModalFooter>
    </MyModal>
  );
};

export default EditProjectModal;
