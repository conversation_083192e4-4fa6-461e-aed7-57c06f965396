import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import { EvaluationRuleDetail, ProjectIndicatorTreeType } from '@/types/api/tenant/evaluate/rule';

import { HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import { getProjectIndicatorTree } from '@/api/tenant/evaluate/rule';
import { EvaluaIndactorVO } from '@/types/api/tenant/evaluate/indicator';

interface SearchCondition {
  keyword: string;
  evaluationStage: string;
  evaluationMethod: string;
}

interface IndicatorContextType {
  formData: EvaluationRuleDetail;
  keyword: string;
  projectIndicatorTree: ProjectIndicatorTreeType[];
  currenProjectId: string;
  currentIndicatorList: EvaluaIndactorVO[];
  selectIndicators: string[];
  setCurrenProjectId: (id: string) => void;
  setProjectIndicatorTree: (tree: ProjectIndicatorTreeType[]) => void;
  setKeyword: (keyword: string) => void;
  setSelectIndicators: (indicators: string[]) => void;
  formStatus: 'select' | 'view';
}

const IndicatorContext = createContext<IndicatorContextType | undefined>(undefined);

interface IndicatorProviderProps {
  children: ReactNode;
  formStatus: 'select' | 'view';
  formData?: EvaluationRuleDetail;
}

export const IndicatorProvider: React.FC<IndicatorProviderProps> = ({
  children,
  formData = {} as EvaluationRuleDetail,
  formStatus
}) => {
  const [keyword, setKeyword] = useState<string>('');
  const [projectIndicatorTree, setProjectIndicatorTree] = useState<ProjectIndicatorTreeType[]>([]);
  const [currenProjectId, setCurrenProjectId] = useState<string>('');
  const [selectIndicators, setSelectIndicators] = useState<string[]>(formData?.indicators || []);

  const initData = useCallback(async () => {
    const res = await getProjectIndicatorTree({
      reflectionId: formData.reflectionId
        ? formData.reflectionId[formData.reflectionId?.length - 1]
        : ''
    });
    setCurrenProjectId(res[0].id);
    setProjectIndicatorTree(res);
  }, [formData]);

  useEffect(() => {
    initData();
  }, [initData]);

  const filterIndicators = useMemo(() => {
    setSelectIndicators(formData?.indicators || []);
  }, [formData]);

  const currentIndicatorList = useMemo(() => {
    return projectIndicatorTree.find((item) => item.id === currenProjectId)?.indicatorList || [];
  }, [projectIndicatorTree, currenProjectId]);

  return (
    <IndicatorContext.Provider
      value={{
        formData,
        keyword,
        projectIndicatorTree,
        currenProjectId,
        setCurrenProjectId,
        setProjectIndicatorTree,
        setKeyword,
        currentIndicatorList,
        selectIndicators,
        setSelectIndicators,
        formStatus
      }}
    >
      {children}
    </IndicatorContext.Provider>
  );
};

export const useIndicator = () => {
  const context = useContext(IndicatorContext);
  if (!context) {
    throw new Error('useIndicator must be used within an IndicatorProvider');
  }
  return context;
};

export default IndicatorProvider;
