import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Box, Flex, Text, Button as ChakraButton, Tag } from '@chakra-ui/react';
import { ColumnsType } from 'antd/es/table';
import MyTable from '@/components/MyTable';
import { useIndicator } from './IndicatorContext';
import { respDims } from '@/utils/chakra';
import { MyTableRef } from '@/components/MyTable/types';

import { useToast } from '@/hooks/useToast';
import { promisifyDelete } from '@/utils/ui/messageBox';
import { treeToList, treeTraverse } from '@/utils/tree';
import { HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import MyTooltip from '@/components/MyTooltip';
import { ProjectIndicatorTreeType } from '@/types/api/tenant/evaluate/rule';
import { useQuery } from '@tanstack/react-query';
import { schoolDeptSubjectManageSubjectsV2 } from '@/api/tenant/evaluate/project';

const ProjectList: React.FC = () => {
  const { projectIndicatorTree, setCurrenProjectId, currenProjectId, formData, selectIndicators } =
    useIndicator();
  const tableRef = useRef<MyTableRef>(null);

  // 使用React Query获取学科列表
  const { data: subjects, isLoading: subjectsLoading } = useQuery(
    ['subjects'],
    schoolDeptSubjectManageSubjectsV2,
    {
      staleTime: Infinity // 可以根据需要调整
    }
  );

  const columns: ColumnsType<ProjectIndicatorTreeType> = useMemo(() => {
    return [
      {
        title: '评价项目',
        dataIndex: 'name',
        key: 'name',
        render: (_: any, record) => {
          return (
            <Flex alignItems="center" justifyContent="space-between">
              <Flex alignItems="center" flex="1">
                <MyTooltip label={record.name}>
                  <Box maxWidth="140px" className="textEllipsis">
                    {record.name}
                  </Box>
                </MyTooltip>
                <Box>
                  {record.subjectIds?.slice(0, 1).map((item) => {
                    return (
                      <Tag color="#2BA471" bgColor="#E3F9E9" ml={1} my={1} key={item}>
                        {subjects?.find((subject) => subject.id === item)?.name}
                      </Tag>
                    );
                  })}
                  {(record.subjectIds?.length || 0) > 1 && (
                    <Tag color="#2BA471" bgColor="#E3F9E9" ml={2}>
                      +{(record.subjectIds?.length || 0) - 1}
                    </Tag>
                  )}
                </Box>
              </Flex>
            </Flex>
          );
        }
      },

      {
        title: '占比',
        dataIndex: 'indactorName',
        key: 'indactorName',
        width: '150px',
        render: (_: any, record) => {
          const selectLength = treeToList(record.indicatorList, 'sub').filter((item) =>
            selectIndicators.includes(item.id!)
          ).length;
          const allLength = treeToList(record.indicatorList, 'sub').filter(
            (item) => item.hasSub === HasSubIndicator.No
          ).length;

          return (
            <Flex alignItems="center" justifyContent="flex-end">
              <Flex alignItems="center">
                <Box>占比{Math.floor((record.scoreRate || 0) * 100)}%</Box>
                {<Box ml={2}> ({`${selectLength}/${allLength}`})</Box>}
              </Flex>
            </Flex>
          );
        }
      }
    ];
  }, [subjects]);

  const onRow = useCallback(
    (record: ProjectIndicatorTreeType) => ({
      onClick: () => {
        if (record.id !== currenProjectId) {
          setCurrenProjectId(record.id!);
        }
      }
    }),
    [setCurrenProjectId, currenProjectId]
  );

  return (
    <Flex flexDirection="column" h="100%" w="100%">
      <Flex bg="#f7f9fb" borderRadius="8px" flex="1" h="0" p={respDims(16)} flexDirection="column">
        <Box
          flex="1"
          overflow="auto"
          css={{
            '& .selected-row': {
              background: '#eff5fe!important',
              borderRadius: '8px'
            }
          }}
        >
          {
            <MyTable
              ref={tableRef}
              columns={columns}
              dataSource={projectIndicatorTree}
              showHeader={false}
              boxStyle={{
                px: '0',
                py: '0',
                borderRadius: '12px',
                css: {
                  '& .ant-table': {
                    borderRadius: '12px 12px 12px 12px!important',
                    overflow: 'hidden!important'
                  },
                  '.ant-table tbody .ant-table-row:last-child .ant-table-cell': {
                    borderBottom: 'none'
                  }
                }
              }}
              size="small"
              headerConfig={{
                showHeader: false
              }}
              pageConfig={{
                showPaginate: false
              }}
              onRow={onRow}
              rowClassName={(record) => (currenProjectId === record.id ? 'selected-row' : '')}
            />
          }
        </Box>
      </Flex>
    </Flex>
  );
};

export default ProjectList;
