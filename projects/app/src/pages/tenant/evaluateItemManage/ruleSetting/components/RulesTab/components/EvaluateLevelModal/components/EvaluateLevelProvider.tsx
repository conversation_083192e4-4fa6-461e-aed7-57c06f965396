import React, { createContext, useContext, useState, ReactNode, useMemo, useCallback } from 'react';
import {
  EvaluaScoreLevel,
  EvaluaScoreLevel as EvaluateLevel
} from '@/types/api/tenant/evaluate/score';
import { Form } from 'antd';
import { promisifyConfirm, promisifyDelete, promisifyWarning } from '@/utils/ui/messageBox';
import { getScoreLevelDetail } from '@/api/tenant/evaluate/score';
import { useRequest } from '@/hooks/useRequest';
import {
  EvaluateLevelStatus,
  EvaluateLevelType,
  IsHint
} from '@/constants/api/tenant/evaluate/rule';

interface EvaluateLevelContextProps {
  selectedEvaluateLevel: EvaluateLevel | null;
  setSelectedEvaluateLevel: (scoreGrade: EvaluateLevel | null) => void;
  scoreGrades: EvaluateLevel[] | null;
  setScoreGrades: (scoreGrade: EvaluateLevel[] | null) => void;
  formStatus: 'add' | 'edit' | null;
  setFormStatus: (status: 'add' | 'edit' | null) => void;
  form: any; // Antd Form instance
  initialValues: any;
  setInitialValues: (values: any) => void;
  hasFormChanged: () => boolean;
  unSaveBeformConfirm: () => Promise<any>;
  handleSelectGrade: (grade: EvaluaScoreLevel | null) => void;
  isloading: boolean;
  createName: string;
  setCreateName: (name: string) => void;
  type: EvaluateLevelType;
  setType: (type: EvaluateLevelType) => void;
}

interface EvaluateLevelProviderProps {
  children: ReactNode;
}

const EvaluateLevelContext = createContext<EvaluateLevelContextProps | undefined>(undefined);

export const EvaluateLevelProvider: React.FC<EvaluateLevelProviderProps> = ({ children }) => {
  const [selectedEvaluateLevel, setSelectedEvaluateLevel] = useState<EvaluateLevel | null>(null);
  const [scoreGrades, setScoreGrades] = useState<EvaluateLevel[] | null>(null);
  const [formStatus, setFormStatus] = useState<'add' | 'edit' | null>(null);
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState<any>(null);
  const [createName, setCreateName] = useState<string>('');
  const [type, setType] = useState<EvaluateLevelType>(
    initialValues?.type || EvaluateLevelType.Score
  );

  const [isloading, setIsloading] = useState(false);
  const hasFormChanged = useCallback(() => {
    if (createName) return false;
    if (!initialValues) return false;

    const currentValues = form.getFieldsValue();

    // 比较名称和状态
    if (
      currentValues.name !== initialValues.name ||
      currentValues.status !== initialValues.status
    ) {
      return true;
    }

    // 比较详情数组
    if (currentValues.values?.length !== initialValues.values?.length) {
      return true;
    }
    for (let i = 0; i < (currentValues.values?.length || 0); i++) {
      const currentDetail = currentValues.values[i];
      const initialDetail = initialValues.values[i];

      if (!initialDetail) return true; // 新增的项

      if (
        currentDetail.name !== initialDetail.name ||
        currentDetail.scoreMin !== initialDetail.scoreMin ||
        currentDetail.scoreMax !== initialDetail.scoreMax ||
        currentDetail.hint !== initialDetail.hint ||
        currentDetail.text !== initialDetail.text
      ) {
        return true;
      }
    }
    return false;
  }, [initialValues, form]);

  const unSaveBeformConfirm = useCallback(async (): Promise<any> => {
    if (!hasFormChanged()) {
      return;
    }
    return promisifyConfirm({
      title: '当前页面有未保存的修改，是否继续？'
    });
  }, [hasFormChanged]);

  const handleSelectGrade = async (grade: EvaluaScoreLevel | null) => {
    if (!grade) {
      setSelectedEvaluateLevel(null);
      return;
    }
    await unSaveBeformConfirm();
    setSelectedEvaluateLevel(grade);
    setFormStatus('edit');

    setIsloading(true);

    try {
      const detailDetail = await getScoreLevelDetail({ id: grade.id! });

      const formData = {
        name: detailDetail.name,
        status: detailDetail.status === EvaluateLevelStatus.Normal ? true : false,
        values: detailDetail.values.map((item) => ({
          ...item,
          hint: item.hint === IsHint.Yes ? true : false
        })),
        type: detailDetail.type
      };
      setInitialValues(formData);
      setType(formData.type);
      form.setFieldsValue(formData);
    } catch (error) {
      console.error(error);
    } finally {
      setIsloading(false);
    }
  };

  return (
    <EvaluateLevelContext.Provider
      value={{
        selectedEvaluateLevel,
        setSelectedEvaluateLevel,
        formStatus,
        setFormStatus,
        scoreGrades,
        setScoreGrades,
        form,
        initialValues,
        setInitialValues,
        hasFormChanged,
        unSaveBeformConfirm,
        handleSelectGrade,
        isloading,
        createName,
        setCreateName,
        type,
        setType
      }}
    >
      {children}
    </EvaluateLevelContext.Provider>
  );
};

export const useEvaluateLevel = () => {
  const context = useContext(EvaluateLevelContext);
  if (!context) {
    throw new Error('useEvaluateLevel must be used within a EvaluateLevelProvider');
  }
  return context;
};

export default EvaluateLevelProvider;
