import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, Text, Flex, Button, HStack } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { respDims } from '@/utils/chakra';
import { Form, Input, InputNumber, Radio, Space, Switch } from 'antd';
import { useEvaluateLevel } from './EvaluateLevelProvider';
import { useToast } from '@/hooks/useToast';
import MyTable from '@/components/MyTable';
import SvgIcon from '@/components/SvgIcon';
import { TableProps } from 'antd';
import { useQuery, useMutation } from '@tanstack/react-query';
import { updateScoreLevel, createScoreLevel, getScoreLevelList } from '@/api/tenant/evaluate/score';
import { EvaluaScoreLevelValue, EvaluaScoreLevel } from '@/types/api/tenant/evaluate/score';
import MyBox from '@/components/common/MyBox';
import styles from '@/pages/index.module.scss';
import { promisifyDelete } from '@/utils/ui/messageBox';
import { MyTableRef } from '@/components/MyTable/types';
import {
  EvaluateLevelStatus,
  EvaluateLevelType,
  EvaluateLevelTypeMap,
  IsHint
} from '@/constants/api/tenant/evaluate/rule';
import DraggableBox from './DraggableBox';
import QuestionTip from '@/components/MyTooltip/QuestionTip';
import myStyles from './index.module.scss';

const LevelDetail = ({ onSuccess, onClose }: { onSuccess: () => void; onClose: () => void }) => {
  const { t } = useTranslation();
  const {
    selectedEvaluateLevel,
    setSelectedEvaluateLevel,
    scoreGrades,
    setScoreGrades,
    form,
    formStatus,
    initialValues,
    setInitialValues,
    unSaveBeformConfirm,
    isloading,
    createName,
    setCreateName,
    setFormStatus,
    type,
    setType
  } = useEvaluateLevel();
  const { toast } = useToast();
  const actionRef = useRef<MyTableRef>(null);

  const validateScoreRanges = (details: EvaluaScoreLevelValue[]) => {
    if (details?.length === 0) return;

    if (type === EvaluateLevelType.Text) {
      return;
    }

    const sortedDetails = [...details].sort((a, b) => (a.scoreMin || 0) - (b.scoreMin || 0));
    const errors: { index: number; message: string }[] = [];

    for (let i = 0; i <= sortedDetails.length - 1; i++) {
      const current = sortedDetails[i];
      const next = sortedDetails[i + 1];

      if (Number(current.scoreMax) <= Number(current.scoreMin)) {
        errors.push({
          index: i,
          message: `${current.name}(${current.scoreMin}-${current.scoreMax})分数范围不正确`
        });
      }

      // 检查是否有未定义的值
      if (
        current.scoreMin === undefined ||
        current.scoreMax === undefined ||
        next?.scoreMin === undefined ||
        next?.scoreMax === undefined
      ) {
        continue;
      }

      // 检查重叠

      if (Number(current.scoreMax) >= Number(next.scoreMin)) {
        errors.push({
          index: i,
          message: `${current.name}(${current.scoreMin}-${current.scoreMax})与${next.name}(${next.scoreMin}-${next.scoreMax})的分数范围重叠`
        });
      }

      // 检查不连续
      if (Number(current.scoreMax) != Number(next.scoreMin - 1)) {
        errors.push({
          index: i,
          message: `${current.name}(${current.scoreMax})与${next.name}(${next.scoreMin})之间的分数范围不连续`
        });
      }
    }

    return errors.length > 0
      ? errors.map((item, index) => `${index + 1}、${item.message}`).join(';\n\r')
      : undefined;
  };

  const updateMutation = useMutation(
    async (formData: any) => {
      const { name, status, values, type } = formData;
      const updatedLevel = {
        id: selectedEvaluateLevel!.id!,
        name,
        status: status ? 1 : 0,
        type,

        relatedCount: selectedEvaluateLevel!.relatedCount,
        values: values.map((detail: any, index: number) => ({
          ...detail,
          isHint: detail.isHint ? IsHint.Yes : IsHint.No,
          sortNo: index + 1
        }))
      };

      // 更新评分等级
      await updateScoreLevel(updatedLevel);
      return updatedLevel;
    },
    {
      onSuccess: (updatedLevel) => {
        setScoreGrades(
          scoreGrades?.map((item) => (item.id === updatedLevel.id ? updatedLevel : item)) || null
        );
        setInitialValues({
          ...updatedLevel,
          status: updatedLevel.status === EvaluateLevelStatus.Normal ? true : false,
          values: updatedLevel.values.map((item: any) => ({
            ...item,
            isHint: item.isHint === IsHint.Yes ? true : false
          }))
        });
        setSelectedEvaluateLevel(updatedLevel);
        toast({ status: 'success', title: '保存成功' });
        onSuccess();
      }
    }
  );

  const createMutation = useMutation(
    async (formData: any) => {
      const { name, status, values, type } = formData;
      console.log(values, 'values');

      const newLevel = {
        name,
        type,
        status: status ? EvaluateLevelStatus.Normal : EvaluateLevelStatus.Disabled,
        values: values.map((detail: any, index: number) => ({
          ...detail,
          id: undefined,
          isHint: detail?.isHint ? IsHint.Yes : IsHint.No,
          sortNo: index + 1
        }))
      };

      // 创建评分等级
      await createScoreLevel(newLevel);

      return newLevel;
    },
    {
      onSuccess: (newLevel) => {
        setInitialValues(undefined);
        // setSelectedEvaluateLevel(newLevel);
        setFormStatus('edit');
        setCreateName(newLevel.name);
        toast({ status: 'success', title: '创建成功' });
        onSuccess();
      }
    }
  );

  const handleDelete = async (id: string) => {
    await promisifyDelete({
      title: t('确定删除该评分等级吗？')
    });

    const values = form.getFieldValue('values');
    const updatedDetails = values.filter(
      (item: EvaluaScoreLevelValue) => item.id?.toString() != id
    );
    form.setFieldsValue({ values: updatedDetails });
  };

  const clearDynamicFieldsValidate = () => {
    const values = form.getFieldValue('values') || [];
    const resetFields = values.flatMap((_: any, index: any) =>
      ['name', 'scoreMin', 'scoreMax', 'text', 'isHint'].map((field) => ({
        name: ['values', index, field],
        errors: []
      }))
    );
    resetFields.push({
      name: ['values'],
      errors: []
    });
    form.setFields(resetFields);
  };

  const handleSave = async () => {
    try {
      let values = await form.validateFields();
      // 添加自定义校验
      const error = validateScoreRanges(values.values);
      if (error) {
        toast({ status: 'error', title: error });
        return;
      }
      values = {
        ...values,
        values: values.values.map((item: any) => {
          if (item.id?.startsWith('new')) {
            return {
              ...item,
              id: undefined
            };
          }
          return item;
        })
      };
      if (selectedEvaluateLevel) {
        await updateMutation.mutateAsync(values);
      } else {
        await createMutation.mutateAsync(values);
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleAdd = () => {
    const values = form.getFieldValue('values') || [];

    const newData: EvaluaScoreLevelValue = {
      scoreLevelId: selectedEvaluateLevel?.id || undefined,
      name: '',
      id: 'new' + new Date().getTime().toString(),
      isHint: false as any,
      scoreMin: undefined,
      scoreMax: undefined
    };
    form.setFieldsValue({ values: [...values, newData] });
    setTimeout(() => {
      clearDynamicFieldsValidate();
      actionRef.current?.scrollToBottom();
    }, 0);
  };

  const renderLevelItem = (record: EvaluaScoreLevelValue, index: number, DragHandle: React.FC) => {
    return (
      <Box border="1px solid #E5E7EB" borderRadius="md" p={2}>
        <Form.Item name={['values', index, 'id']} hidden>
          <Input type="hidden" />
        </Form.Item>
        <Flex justifyContent="space-between" p={2} bg="white" alignItems="start" borderRadius="md">
          <HStack spacing={4} alignItems="start">
            <Box mt={2}>
              <DragHandle />
            </Box>
            <Form.Item
              validateTrigger={[]}
              name={['values', index, 'name']}
              rules={[
                { required: true, message: '请输入评价等级' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const values = getFieldValue('values') || [];
                    const isDuplicate = values.some(
                      (item: EvaluaScoreLevelValue, idx: number) =>
                        item.name === value && idx !== index
                    );
                    if (isDuplicate) {
                      return Promise.reject(new Error('名称不能重复'));
                    }
                    return Promise.resolve();
                  }
                })
              ]}
              style={{ marginBottom: 0 }}
            >
              <Input
                style={{ width: 80 }}
                placeholder="等级"
                onChange={(e) => {
                  form.validateFields([['values', index, 'name']]);
                }}
              />
            </Form.Item>
            <span style={{ lineHeight: '32px' }}>对应:</span>
            {type === EvaluateLevelType.Score ? (
              <>
                <Form.Item
                  validateTrigger={[]}
                  name={['values', index, 'scoreMin']}
                  rules={[{ required: true, message: '请输入最小值' }]}
                  style={{ marginBottom: 0 }}
                >
                  <InputNumber
                    style={{ width: 80 }}
                    placeholder="最小值"
                    max={1000}
                    min={0}
                    onBlur={() => {
                      form.validateFields([['values', index, 'scoreMin']]);
                    }}
                  />
                </Form.Item>
                <span style={{ lineHeight: '32px' }}>-</span>
                <Form.Item
                  validateTrigger={[]}
                  name={['values', index, 'scoreMax']}
                  rules={[{ required: true, message: '请输入最大值' }]}
                  style={{ marginBottom: 0 }}
                >
                  <InputNumber
                    style={{ width: 80 }}
                    placeholder="最大值"
                    max={1000}
                    min={0}
                    onBlur={() => {
                      form.validateFields([['values', index, 'scoreMax']]);
                    }}
                  />
                </Form.Item>
                <span style={{ lineHeight: '32px' }}>分数段</span>
              </>
            ) : (
              <>
                <Form.Item
                  validateTrigger={[]}
                  name={['values', index, 'text']}
                  rules={[{ required: true, message: '请输入文本' }]}
                  style={{ marginBottom: 0 }}
                >
                  <Input
                    style={{ width: '300px' }}
                    placeholder="请输入文本"
                    onBlur={() => {
                      form.validateFields([['values', index, 'text']]);
                    }}
                  />
                </Form.Item>
              </>
            )}
          </HStack>

          <SvgIcon
            cursor="pointer"
            name="trash"
            color="#ff3e3c"
            mt={2}
            w="16px"
            h="16px"
            onClick={() => handleDelete(record.id!)}
            style={{ cursor: 'pointer' }}
          />
        </Flex>
        {type === EvaluateLevelType.Text && (
          <Flex ml={'40px'} alignItems="center">
            <Box mr={2} color="#4E5969" fontSize="12px">
              选项提示开关
            </Box>
            <Form.Item
              validateTrigger={[]}
              name={['values', index, 'isHint']}
              rules={[{ required: true, message: '是否提示' }]}
              style={{ marginBottom: 0 }}
            >
              <Switch />
            </Form.Item>
          </Flex>
        )}
      </Box>
    );
  };

  return (
    <Flex flexDir="column" w="100%" h="100%">
      {
        <MyBox
          isLoading={isloading}
          w="100%"
          p={4}
          pb={0}
          borderRadius="md"
          flex="1"
          overflow="auto"
        >
          {formStatus && (
            <Form form={form} layout="vertical" className={styles['my-form']} validateTrigger={[]}>
              <Form.Item
                validateTrigger={[]}
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.values !== currentValues.values
                }
              >
                {({ getFieldValue }) => {
                  const values = getFieldValue('values');
                  return (
                    <>
                      <Form.Item
                        validateTrigger={[]}
                        label="评分项目名称"
                        name="name"
                        rules={[{ required: true, message: '请输入评分项目名称' }]}
                      >
                        <Input
                          placeholder="请输入评分项目名称"
                          onBlur={() => {
                            form.validateFields([['name']]);
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        validateTrigger={[]}
                        label="评分项目类型"
                        name="type"
                        rules={[{ required: true, message: '请选择评分项目类型' }]}
                        style={{ display: 'none' }}
                      >
                        <Input type="hidden" />
                      </Form.Item>
                      <Form.Item
                        validateTrigger={[]}
                        className={myStyles['my-radio-group']}
                        label={
                          <Flex
                            alignItems="center"
                            justifyContent="space-between"
                            flex={1}
                            w="100%"
                          >
                            <Flex alignItems="center">
                              <Box
                                mr={1}
                                css={{
                                  '&::after': {
                                    content: '"*"',
                                    color: '#ff4d4f',
                                    marginLeft: '2px'
                                  }
                                }}
                              >
                                评分等级设置
                              </Box>
                              <QuestionTip
                                placement="top"
                                label="选项提示开关：如开启开关，评价指标时选择该选项，则提示为一票否决，评分为0；如不开启则正常计算分值。"
                              ></QuestionTip>
                            </Flex>
                            <Radio.Group
                              buttonStyle="solid"
                              optionType="button"
                              value={type}
                              disabled={!!(formStatus === 'edit')}
                              onChange={(e) => {
                                setType(e.target.value);
                                form.setFieldsValue({ type: e.target.value });
                              }}
                            >
                              {Object.values(EvaluateLevelTypeMap).map((item) => (
                                <Radio key={item.value} value={item.value}>
                                  {item.label}
                                </Radio>
                              ))}
                            </Radio.Group>
                          </Flex>
                        }
                        name="values"
                        rules={[
                          { required: true, message: '请设置评分等级' },
                          {
                            validator: async (_, value) => {
                              const errors = validateScoreRanges(value);
                              if (errors) {
                                throw new Error(errors);
                              }
                            }
                          }
                        ]}
                        shouldUpdate={(prevValues, currentValues) => prevValues != currentValues}
                        style={{ marginBottom: '0px' }}
                      >
                        <DraggableBox<EvaluaScoreLevelValue>
                          dataSource={values || []}
                          rowKey="id"
                          onDragEnd={(event, newDataSource) => {
                            form.setFieldsValue({ values: newDataSource });
                          }}
                          renderItem={(item, index, DragHandle) =>
                            renderLevelItem(item, index, DragHandle)
                          }
                        />
                      </Form.Item>
                      <Box
                        as="button"
                        width="173px"
                        height="32px"
                        borderRadius="8px"
                        border="1px solid"
                        borderColor="primary.500"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        w="100%"
                        cursor="pointer"
                        _hover={{ bg: '#eff4fe' }}
                        onClick={handleAdd}
                      >
                        <HStack spacing={2}>
                          <SvgIcon name="plus" w="16px" h="16px" color="primary.500" />
                          <Text color="primary.500" fontSize="14px">
                            添加评分等级
                          </Text>
                        </HStack>
                      </Box>
                      <Form.Item
                        style={{ marginTop: '10px' }}
                        validateTrigger={[]}
                        label="评分项目状态"
                        name="status"
                        valuePropName="checked"
                        rules={[{ required: true, message: '请选择评分项目状态' }]}
                      >
                        <Flex>
                          <Switch
                            disabled={!!selectedEvaluateLevel?.relatedCount}
                            value={getFieldValue('status')}
                            onChange={(checked) =>
                              form.setFieldsValue({ ...values, status: checked })
                            }
                          ></Switch>
                          {!!selectedEvaluateLevel?.relatedCount && (
                            <Box ml={2} color="#4E5969" fontSize={respDims(14, 12)}>
                              当前评分项目已被评价指标关联,不可禁用
                            </Box>
                          )}
                        </Flex>
                      </Form.Item>
                    </>
                  );
                }}
              </Form.Item>
            </Form>
          )}
        </MyBox>
      }
      <Flex
        justifyContent="flex-end"
        alignItems="center"
        padding={respDims(10)}
        mr={respDims(12)}
        borderTop="1px solid #E5E7EB"
        h={respDims(80)}
      >
        <Button
          variant="grayBase"
          mr={3}
          onClick={async () => {
            await unSaveBeformConfirm();
            onClose();
          }}
        >
          {t('取消')}
        </Button>
        {formStatus && (
          <Button
            colorScheme="blue"
            isLoading={updateMutation.isLoading || createMutation.isLoading}
            onClick={handleSave}
          >
            {selectedEvaluateLevel ? t('保存') : t('创建')}
          </Button>
        )}
      </Flex>
    </Flex>
  );
};

export default LevelDetail;
