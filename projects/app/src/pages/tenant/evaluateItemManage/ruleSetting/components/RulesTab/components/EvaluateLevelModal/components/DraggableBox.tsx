import React, { useContext, useEffect, useMemo, useState } from 'react';
import { DndContext, UniqueIdentifier } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { HolderOutlined } from '@ant-design/icons';
import { Box, Button } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';

interface DragContextType {
  setDraggingRowKey: (rowKey?: UniqueIdentifier) => void;
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: any;
}

const DragContext = React.createContext<DragContextType>({
  setDraggingRowKey: () => {}
});

const RowContext = React.createContext<RowContextProps>({});

// 拖拽手柄组件
const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <SvgIcon
      name="menu2"
      style={{ cursor: 'move' }}
      color="#D5D5D5"
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

// 可拖拽的Box组件
interface SortableItemProps {
  id: UniqueIdentifier;
  children: React.ReactNode;
}

const SortableItem: React.FC<SortableItemProps> = ({ id, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id });

  const style = {
    transform: CSS.Translate.toString(transform),
    transition,
    position: 'relative' as const,
    zIndex: isDragging ? 6 : 'auto'
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners]
  );

  const { setDraggingRowKey } = useContext(DragContext);
  useEffect(() => {
    isDragging && setDraggingRowKey(id);
  }, [isDragging, id, setDraggingRowKey]);

  return (
    <RowContext.Provider value={contextValue}>
      <Box ref={setNodeRef} style={style} {...attributes} mb={4}>
        {children}
      </Box>
    </RowContext.Provider>
  );
};

interface DraggableBoxProps<T extends { id?: string | number }> {
  dataSource: T[];
  rowKey: keyof T;
  onDragEnd: (event: any, newDataSource: T[]) => void;
  renderItem: (item: T, index: number, DragHandle: React.FC) => React.ReactNode;
}

const DraggableBox = <T extends { id?: string | number }>({
  dataSource,
  rowKey,
  onDragEnd,
  renderItem
}: DraggableBoxProps<T>) => {
  const [draggingRowKey, setDraggingRowKey] = useState<UniqueIdentifier>();

  const handleDragEnd = (event: any) => {
    setDraggingRowKey(undefined);
    const { active, over } = event;

    if (!active || !over || active.id === over.id) {
      return;
    }

    const oldIndex = dataSource.findIndex((item) => item[rowKey] === active.id);
    const newIndex = dataSource.findIndex((item) => item[rowKey] === over.id);
    const newDataSource = arrayMove([...dataSource], oldIndex, newIndex);
    onDragEnd(event, newDataSource);
  };

  const items = dataSource.map((item, index) => ({
    id: item[rowKey]?.toString() || `temp-${index}`
  }));

  return (
    <DragContext.Provider value={{ setDraggingRowKey }}>
      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={handleDragEnd}>
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          <Box>
            {dataSource.map((item, index) => {
              const itemId = item[rowKey]?.toString() || `temp-${index}`;
              return (
                <SortableItem key={itemId} id={itemId}>
                  {renderItem(item, index, DragHandle)}
                </SortableItem>
              );
            })}
          </Box>
        </SortableContext>
      </DndContext>
    </DragContext.Provider>
  );
};

export default DraggableBox;
