import React, { useState, useEffect } from 'react';
import { Flex, Button, HStack, InputGroup, InputRightElement, Input } from '@chakra-ui/react';
import { SearchBarProps } from '@/components/MyTable/types';
import { useRouter } from 'next/router';
import { respDims } from '@/utils/chakra';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import EditEvaluationRuleModal from './EditEvaluationRuleModal';
import EvaluateLevelModal from './EvaluateLevelModal/index';
import { EvaluationRuleDetail, EvaluationRuleListParams } from '@/types/api/tenant/evaluate/rule';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/pages/index.module.scss';

const SearchBar = ({
  onSearch,
  query,
  tableInstance
}: SearchBarProps<EvaluationRuleListParams, EvaluationRuleDetail>) => {
  const router = useRouter();
  const [searchKey, setSearchKey] = useState(query?.name || '');
  const { openOverlay, OverlayContainer } = useOverlayManager();

  useEffect(() => {
    setSearchKey(query?.name || '');
  }, [router, query]);

  const handleSearch = () => {
    const params = { ...query, name: searchKey };
    onSearch && onSearch(params);
  };

  useEffect(() => {
    handleSearch();
  }, [searchKey]);

  const handleReset = () => {
    setSearchKey('');
    onSearch && onSearch({ ...query, name: '' });
  };

  const onAdd = () => {
    openOverlay({
      Overlay: EditEvaluationRuleModal,
      props: {
        formStatus: 'add',
        onClose: () => {},
        onSuccess() {
          tableInstance?.reload();
        }
      }
    });
  };

  const onOpenEvaluateLevel = () => {
    openOverlay({
      Overlay: EvaluateLevelModal,
      props: {
        onClose: () => {},
        onSuccess() {
          tableInstance?.reload();
        }
      }
    });
  };

  return (
    <Flex
      alignItems="center"
      justifyContent="space-between"
      className={styles['my-form']}
      mr="24px"
    >
      <HStack>
        {/* <Input
          placeholder="请输入评价规则名称"
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
          suffix={<SvgIcon name="search"></SvgIcon>}
          rootClassName="custom-input"
          style={{
            width: '200px',
            borderRadius: '8px',
            height: '36px',
            background: 'rgba(0, 0, 0, 0.03)!important'
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSearch();
            }
          }}
        /> */}
        <InputGroup
          w="220px"
          h="36px"
          borderRadius={respDims(8)}
          boxSizing="border-box"
          overflow="hidden"
        >
          <InputRightElement h="100%">
            <SvgIcon name="search" color="#4E5969" />
          </InputRightElement>
          <Input
            placeholder="请输入评价规则名称"
            value={searchKey}
            h="100%"
            color="#000"
            background="rgba(0, 0, 0, 0.03)"
            fontSize={respDims('14fpx')}
            border="none"
            _focus={{
              border: 'none',
              outline: 'none',
              boxShadow: 'none'
            }}
            _placeholder={{
              color: '#4E5969',
              fontSize: 'inherit'
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch();
              }
            }}
            onChange={(e) => {
              setSearchKey(e.target.value);
            }}
          />
        </InputGroup>

        <Button onClick={handleReset} variant={'grayBase'}>
          重置
        </Button>

        <Button
          ml="4px"
          h="36px"
          colorScheme="primary"
          variant="outline"
          onClick={onOpenEvaluateLevel}
        >
          管理评分等级
        </Button>

        <Button ml="4px" h="36px" variant="primary" onClick={onAdd}>
          <SvgIcon name="plus"></SvgIcon> 添加评价规则
        </Button>
      </HStack>

      <OverlayContainer />
    </Flex>
  );
};

export default SearchBar;
