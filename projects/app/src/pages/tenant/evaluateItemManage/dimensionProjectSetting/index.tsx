import React from 'react';
import DimensionsTab from '../ruleSetting/components/DimensionsTab';
import { Flex } from '@chakra-ui/react';
import { serviceSideProps } from '@/utils/i18n';
const DimensionProjectSetting: React.FC = () => {
  return (
    <Flex flex="1" h="100%" bg="#fff" flexDirection="column" borderRadius="24px 0 0 0">
      <DimensionsTab />
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: { ...(await serviceSideProps(context)) }
  };
}

export default DimensionProjectSetting;
