import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { Box } from '@chakra-ui/react';

const CustomExpandIcon = (props: any) => {
  const { expanded, onExpand, record } = props;

  const children = [...(record.sub || []), ...(record.children || []), ...(record.subs || [])];

  return children?.length ? (
    <Box
      display="inline-flex"
      cursor="pointer"
      justifyContent="center"
      alignItems="center"
      onClick={(e) => onExpand(record, e)}
      color={expanded ? '#1A5EFF' : '#909399'}
      style={{ verticalAlign: 'middle', marginBottom: '2px' }}
    >
      {!expanded ? (
        <SvgIcon name="chevronUpCircle" fontSize={respDims(24, 22)} mr={1} />
      ) : (
        <SvgIcon name="chevronUpCircle" mr={1} style={{ transform: 'rotate(180deg)' }} />
      )}
    </Box>
  ) : (
    <></>
  );
};

export default CustomExpandIcon;
