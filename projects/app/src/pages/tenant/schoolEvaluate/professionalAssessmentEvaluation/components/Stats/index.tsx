import React, { useRef, useState, useEffect, useMemo } from 'react';
import { Box, ChakraProps, Flex, HStack, Button } from '@chakra-ui/react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ColumnsType } from 'antd/es/table';
import { respDims } from '@/utils/chakra';
import CustomExpandIcon from '@/pages/tenant/evaluateItemManage/components/customExpandIcon';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import RadioStyles from '@/pages/index.module.scss';
import {
  EvaluationType,
  HasSubIndicator,
  IsHint,
  PeriodType
} from '@/constants/api/tenant/evaluate/rule';
import { RelTimeType } from '@/pages/tenant/teacherEvaluate/components/RelTimeSelect';
import { EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import { Dropdown, Radio, Modal } from 'antd';
import { addClazzEvaluate, getTeacherIndicatorTree } from '@/api/tenant/evaluate/process';
import { treeToList } from '@/utils/tree';
import { cloneDeep } from 'lodash';
import { Toast } from '@/utils/ui/toast';

enum SetType {
  ScoreSetting = 1,
  TextSetting = 2
}

enum TeacherStatusType {
  notStart = 0,
  processing = 1,
  completed = 2
}
const Stats = ({
  type,
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  evaluatedId,
  menuId,
  ruleId,
  semesterId,
  relTime,
  title,
  subTitle,
  indactors,
  onSuccess,
  status,
  evaluationList,
  periodType,
  ...props
}: {
  type: 'clazz' | 'student';
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  evaluatedId?: string;
  menuId?: string;
  ruleId?: string;
  semesterId?: string;
  relTime?: RelTimeType;
  title?: string;
  subTitle?: string;
  indactors: EvaluateIndactorType[];
  onSuccess?: () => void;
  status: number;
  evaluationList: {
    evaluationType: EvaluationType;
    score: number;
    scoreLevelValue: string;
    sendWord: string;
  }[];
  periodType: string;
} & ChakraProps) => {
  const tableRef = useRef<MyTableRef>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEvaluated, setIsEvaluated] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [originalData, setOriginalData] = useState<EvaluateIndactorType[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(true);

  const [data, setData] = useState<EvaluateIndactorType[]>(() => {
    if (!indactors?.length) return [];

    return cloneDeep(
      indactors.map((item, index) => ({
        ...item,
        id: index.toString(),
        name: item.projectName,
        key: index.toString(),
        scoreLevelValueId: item.scoreLevelValueId || '',
        scoreLevelId: item.scoreLevelId || '',
        scoreLevelValue: item.scoreLevelValue || ''
      }))
    );
  });

  const queryClient = useQueryClient();

  const queryKey = useMemo(
    () => ({
      id: evaluatedId,
      ruleId,
      semesterId,
      menuId,
      periodType
    }),
    [evaluatedId, ruleId, semesterId, menuId, periodType]
  );

  useEffect(() => {
    if (periodType === String(PeriodType.Instant)) {
      setIsEvaluated(false);
    } else {
      setIsEvaluated(evaluationList?.length > 0);
    }
    setIsEditing(false);
  }, [evaluatedId, periodType, evaluationList]);

  const { isFetching } = useQuery(
    ['indactorDetail', queryKey],
    () =>
      getTeacherIndicatorTree({
        evaluatedId: evaluatedId!,
        menuId: menuId!,
        ruleId: ruleId!,
        semesterId: semesterId!
      }),
    {
      enabled: !!(evaluatedId && ruleId && semesterId && menuId),
      staleTime: 0,
      cacheTime: 0,
      onSuccess: (data) => {
        if (data && data.length) {
          const oldIndactors = data?.map((item, index) => {
            item.id = index + '';
            item.name = item.name;
            return {
              ...item
            };
          });
          setData(oldIndactors || []);
          setOriginalData(cloneDeep(oldIndactors || []));
          setIsEditing(false);
        } else {
          if (indactors?.length && ruleId && semesterId && menuId) {
            setData(
              cloneDeep(
                indactors.map((item, index) => ({
                  ...item,
                  id: index.toString(),
                  name: item.projectName,
                  key: index.toString(),
                  scoreLevelValueId: '',
                  scoreLevelId: '',
                  scoreLevelValue: ''
                }))
              )
            );
          } else {
            setData([]);
          }
          setIsEvaluated(false);
          setIsEditing(false);
        }
      }
    }
  );

  const handleScoreLevelChange = (record: EvaluateIndactorType, target: any) => {
    const targetValueInfo = record.scoreLevelValues.find((item) => item.id == target.target.value);

    if (targetValueInfo?.isHint === IsHint.Yes) {
      Modal.confirm({
        centered: true,
        title: '提示',
        content: `您选择【${targetValueInfo?.name}】 选项为一票否决，该老师得分将为 0 ，确定继续评分吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          record.scoreLevelId = targetValueInfo?.scoreLevelId || '';
          record.scoreLevelValueId = targetValueInfo?.id || '';
          record.scoreLevelValue = targetValueInfo?.name || '';
          setData([...data]);
        }
      });
    } else {
      record.scoreLevelId = targetValueInfo?.scoreLevelId || '';
      record.scoreLevelValueId = targetValueInfo?.id || '';
      record.scoreLevelValue = targetValueInfo?.name || '';
      setData([...data]);
    }
  };

  const columns: ColumnsType<EvaluateIndactorType> = [
    {
      title: '评价内容',
      dataIndex: 'name',
      key: 'name',
      width: '90%',
      render: (_, record) => {
        const displayName = record.name || record.projectName;
        if (record.hasChildren && displayName === '师德考核') {
          return (
            <Flex alignItems="center" ml="24px" mt="-22px" w="100%">
              <Box color="#303133" fontSize="14px" minW="60px" fontWeight="400">
                师德考核
              </Box>
              <Box ml={respDims(6)} minW="300px" color="#FF9A2E" fontSize="13px" fontWeight="400">
                （师德考核项中 的B、C 选项为一票否决，请客观评价）
              </Box>
            </Flex>
          );
        }
        return displayName;
      }
    },
    {
      title: '评价项',
      dataIndex: 'score',
      key: 'score',
      width: '10%',
      render: (_, record) => {
        if (record.hasSub === HasSubIndicator.Yes || record.hasChildren) {
          return <></>;
        }

        return (
          <Box
            className={RadioStyles['teacher-evaluate-radio']}
            css={{
              '.ant-radio-group': {
                width: '100%',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }
            }}
          >
            <Radio.Group
              value={record.scoreLevelValueId}
              onChange={(target) => handleScoreLevelChange(record, target)}
            >
              <HStack spacing={2} overflow="hidden">
                {record.scoreLevelValues
                  .filter((item) => !isEvaluated || item.id === record.scoreLevelValueId)
                  .map((item) => (
                    <Radio.Button
                      key={item.id}
                      value={item.id}
                      style={{
                        maxWidth: '200px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                      title={
                        item.type === SetType.ScoreSetting
                          ? item.name
                          : item.name + (item.text ? `(${item.text})` : '')
                      }
                    >
                      {item.type === SetType.ScoreSetting
                        ? item.name
                        : item.name + (item.text ? `(${item.text})` : '')}
                    </Radio.Button>
                  ))}
              </HStack>
            </Radio.Group>
          </Box>
        );
      }
    }
  ];

  const handleEdit = () => {
    Modal.confirm({
      title: '提示',
      content: `该老师您已评价，确定更改评价吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        setIsEditing(true);
        setIsEvaluated(false);
      }
    });
  };

  const onReset = () => {
    if (isEditing) {
      setData(cloneDeep(originalData));
    } else {
      setData(
        cloneDeep(
          indactors?.map((item, index) => {
            return {
              ...item,
              id: index + '',
              name: item.projectName
            };
          })
        ) || []
      );
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    const indicators = treeToList(data)
      .filter((node) => {
        return node.hasSub !== HasSubIndicator.Yes && !node.hasChildren;
      })
      .map((item) => {
        if (item.scoreLevelValueId && !item.scoreLevelValue) {
          const targetValueInfo = item.scoreLevelValues.find(
            (it) => it.id == item.scoreLevelValueId
          );
          item.scoreLevelId = targetValueInfo?.scoreLevelId;
          item.scoreLevelValueId = targetValueInfo?.id;
          item.scoreLevelValue = targetValueInfo?.name || '';
        }
        return {
          indicatorId: item.id,
          dimensionId: item.dimensionId,
          evaluationType: item.evaluationType,
          scoreLevelId: item.scoreLevelId,
          scoreLevelValueId: item.scoreLevelValueId,
          scoreLevelValue: item.scoreLevelValue
        };
      });

    const invalidIndicators = indicators.filter(
      (indicator) => !indicator.scoreLevelValueId || indicator.scoreLevelValueId == '0'
    );
    if (invalidIndicators.length > 0) {
      Toast.error('所有评价项都必须有评分');
      setIsLoading(false);
      return;
    }

    const payload = {
      menuId: menuId,
      semesterId,
      ruleId,
      evaluatedIds: [evaluatedId],
      indicators
    };

    try {
      await addClazzEvaluate(payload as any);
      Toast.success('评价成功');
      onSuccess?.();
      if (periodType !== String(PeriodType.Instant)) {
        setIsEvaluated(true);
      }
      if (periodType !== String(PeriodType.Instant)) {
        queryClient.invalidateQueries(['indactorDetail', queryKey]);
      }
    } catch (error) {
      Toast.error('评价失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Flex {...props} h="100%">
      <Flex pl={respDims(24)} flex="1 0 0" flexDir="column" align="stretch" position="relative">
        <Flex justifyContent="space-between" align="center">
          <Flex align="center" mt="-8px">
            <Box
              w={respDims(7)}
              h={respDims('14fpx')}
              bgColor="#175DFF"
              borderRadius={respDims(8)}
            ></Box>
            <Box
              ml={respDims(6)}
              color="rgba(0,0,0,0.9)"
              fontSize={respDims('16fpx')}
              lineHeight={respDims('22fpx')}
              fontWeight="bold"
            >
              学校师德和专业考核综合评价
            </Box>
          </Flex>

          {relTime?.label ? (
            <Dropdown
              open={isDropdownOpen}
              onOpenChange={(visible) => setIsDropdownOpen(visible)}
              menu={{
                items: [
                  {
                    label: (
                      <Flex direction="column" p="10px">
                        <Box
                          fontSize="17px"
                          pb="14px"
                          fontWeight="500"
                          color="#000"
                          borderBottom="1px solid #E5E7EB"
                        >
                          评价说明
                        </Box>
                        <Flex direction="column" mt="20px">
                          <Flex alignItems="center">
                            <Box w="10px" h="10px" mr="12px" bg="#165DFF" borderRadius="50%" />
                            <Box
                              color="#4E5969"
                              ml="8px"
                              fontSize="14px"
                              fontWeight="400"
                              lineHeight="22px"
                            >
                              评价时间：{relTime?.label}，在评价时段内可修改评
                              <br />
                              价，评价时间截止后将不可修改和评价。
                            </Box>
                          </Flex>
                          <Flex alignItems="center" mt="16px">
                            <Box w="10px" h="10px" mr="12px" bg="#165DFF" borderRadius="50%" />
                            <Box
                              color="#4E5969"
                              ml="8px"
                              fontSize="14px"
                              fontWeight="400"
                              lineHeight="22px"
                            >
                              默认评价项为系统设置，您可根据实际情况修改评价项的选择。
                            </Box>
                          </Flex>
                          <Flex justifyContent="end" mt="28px">
                            <Button
                              fontSize="14px"
                              fontWeight="400"
                              onClick={() => setIsDropdownOpen(false)}
                            >
                              我已知悉
                            </Button>
                          </Flex>
                        </Flex>
                      </Flex>
                    ),
                    key: 'evaluate-description'
                  }
                ]
              }}
            >
              <Box
                fontSize="14px"
                color="#606266"
                border="1px solid #A8ABB2"
                borderRadius="8px"
                fontWeight="400"
                p="4px 8px"
                mr="3px"
                _hover={{
                  color: '#36F',
                  border: '1px solid #36F'
                }}
                cursor="pointer"
              >
                评价说明
              </Box>
            </Dropdown>
          ) : (
            <Box h="31px" display="block" />
          )}
        </Flex>
        <MyTable
          columns={columns}
          dataSource={data}
          loading={isFetching}
          pagination={false}
          rowKey="id"
          boxStyle={{
            pt: 0,
            mt: '-5px',
            mb: data.length > 0 && status !== TeacherStatusType.completed ? '70px' : 0
          }}
          pageConfig={{ showPaginate: false }}
          expandable={{
            defaultExpandAllRows: true,
            expandIcon: CustomExpandIcon
          }}
          ref={tableRef}
        />
        {data.length > 0 && status !== TeacherStatusType.completed && (
          <Flex
            pos="absolute"
            bottom="0"
            right="0"
            p="10px 20px 21px 20px"
            w="100%"
            bgColor="#fff"
            justifyContent="flex-end"
          >
            {!isEvaluated ? (
              <>
                <Button variant="grayBase" mr={3} onClick={onReset}>
                  重置
                </Button>
                <Button onClick={handleSubmit} isLoading={isLoading}>
                  提交评价
                </Button>
              </>
            ) : (
              <Button onClick={handleEdit} isLoading={isLoading}>
                修改评价
              </Button>
            )}
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default Stats;
