import MyModal from '@/components/MyModal';
import { Box, Button, Flex } from '@chakra-ui/react';
import AdjustGroupList, { GroupListRef } from '../AdjustGroupList';
import { useRef } from 'react';
import { AddIcon } from '@chakra-ui/icons';
import { getAllGroupList, getClazzStudentPage, submitGroups } from '@/api/tenant/evaluate/process';
import { useQuery } from '@tanstack/react-query';
import { EvaluateGroupType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';

const AdjustGroupModal = ({
  clazzId,
  semesterId,
  subjectId,
  ruleId,
  menuId,
  incCount,
  onSuccess,
  onClose
}: {
  clazzId: string;
  semesterId: string;
  subjectId: string;
  ruleId: string;
  menuId: string;
  incCount?: number;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const groupListRef = useRef<GroupListRef>(null);
  const groupsRef = useRef<EvaluateGroupType[]>();

  const { data: groups = [] } = useQuery(['groups'], async () => {
    const groups = await getAllGroupList({ clazzId, semesterId, subjectId, ruleId });
    const clazzStudents = await getListFromPage(getClazzStudentPage, {
      clazzId,
      semesterId,
      subjectId,
      menuId,
      ruleId
    }).then((res) => {
      return res.map((it) => {
        return {
          ...it,
          studentId: it.id,
          studentName: it.name
        } as EvaluateStudentType;
      });
    });

    const addCount = incCount || (groups.length ? 0 : 1);

    for (let i = 0; i < addCount; i++) {
      groups.push({
        id: 'new' + i,
        groupName: `第${groups.length + 1}组`
      });
    }

    groups.forEach((group) => {
      if (group.studentList) {
        group.studentList.forEach((student) => {
          const index = clazzStudents.findIndex((it) => it.studentId == student.studentId);
          if (index >= 0) {
            clazzStudents.splice(index, 1);
          }
        });
      } else {
        group.studentList = [];
      }
    });

    if (clazzStudents.length) {
      const emptyGroups = groups.filter((it) => !it.studentList?.length);
      if (!emptyGroups.length) {
        groups.push({
          id: '',
          groupName: `第${groups.length + 1}组`,
          studentList: []
        });
        emptyGroups.push(groups[groups.length - 1]);
      }
      while (clazzStudents.length) {
        emptyGroups.some((group) => {
          const student = clazzStudents.shift();
          if (!student) {
            return true;
          }
          group.studentList!.push(student);
          return false;
        });
      }
      groupsRef.current = groups;
    }

    return groups;
  });

  const onSubmit = async () => {
    console.log(groupsRef.current);

    if (groupsRef.current) {
      await submitGroups({
        clazzId,
        semesterId,
        groups: groupsRef.current
          .map((it) => {
            if (it.id.includes('new')) {
              it.id = '';
            }
            return it;
          })
          .filter((it) => it.studentList?.length)
          .map((it) => ({
            id: it.id,
            groupName: it.groupName,
            studentIds: it.studentList?.length
              ? it.studentList?.map((it) => it.studentId)
              : undefined
          }))
      });
      onSuccess?.();
    }
    onClose?.();
  };

  return (
    <MyModal
      isOpen
      isCentered
      onClose={onClose}
      maxW="96vw"
      maxH="96vh"
      hideCloseButton
      closeOnOverlayClick={false}
    >
      <Flex flexDir="column" minW="80vw" minH="80vh" pt="16px" overflow="hidden">
        <Box alignSelf="center" color="#000000" fontSize="18px" fontWeight="bold" lineHeight="32px">
          微团队分组
        </Box>
        <Box alignSelf="center" mb="8px" mt="0px" color="#606266" fontSize="18px" lineHeight="32px">
          可拖动学生名称到其他分组
        </Box>

        <AdjustGroupList
          ref={groupListRef}
          flex="1"
          mx="24px"
          groups={groups}
          onChange={(e) => {
            groupsRef.current = e;
          }}
        />

        <Flex align="center" mt="10px" px="24px" py="21px" borderTop="1px solid #E5E7EB">
          <Button
            variant="grayBase"
            w="150px"
            h="36px"
            borderRadius="150px"
            onClick={() => groupListRef.current?.addGroup(1)}
          >
            <AddIcon />
            <Box ml="10px">添加分组</Box>
          </Button>

          <Button
            ml="auto"
            variant="grayBase"
            w="150px"
            h="36px"
            borderRadius="150px"
            onClick={onClose}
          >
            取消
          </Button>

          <Button ml="16px" mr="auto" w="150px" h="36px" borderRadius="150px" onClick={onSubmit}>
            确定
          </Button>

          <Box w="150px"></Box>
        </Flex>
      </Flex>
    </MyModal>
  );
};

export default AdjustGroupModal;
