import { Box, Center, ChakraProps, Checkbox, Flex } from '@chakra-ui/react';
import Student from '../Student';
import {
  ForwardedRef,
  forwardRef,
  Fragment,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState
} from 'react';
import SvgIcon from '@/components/SvgIcon';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  useDraggable,
  useDroppable
} from '@dnd-kit/core';
import { respDims } from '@/utils/chakra';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { EvaluateEntryEnum } from '@/constants/evaluate';

export type SeatType = {
  student?: EvaluateStudentType;
};

type DndDataType = {
  student?: EvaluateStudentType;
  row: number;
  column: number;
};

export type SeatListRef = {
  addRowCount: (count: number) => void;

  addColumnCount: (count: number) => void;
};

const SeatList = (
  {
    type = 'normal',
    students,
    rowCount,
    columnCount,
    incRowCount,
    incColumnCount,
    isCheckable,
    checkedIds,
    entryType,
    onCheckChange,
    onClickStudent,
    onSeatChange,
    ...props
  }: {
    type?: 'normal' | 'evaluate' | 'adjust';
    students: EvaluateStudentType[];
    rowCount?: number;
    columnCount?: number;
    incRowCount?: number;
    incColumnCount?: number;
    isCheckable?: boolean;
    checkedIds?: string[];
    entryType: EvaluateEntryEnum;
    onCheckChange?: (checkedIds: string[]) => void;
    onClickStudent?: (student: EvaluateStudentType) => void;
    onSeatChange?: (
      students: EvaluateStudentType[],
      allStudents: EvaluateStudentType[],
      seats: SeatType[][]
    ) => void;
  } & ChakraProps,
  ref: ForwardedRef<SeatListRef>
) => {
  const [seats, setSeats] = useState<SeatType[][]>([]);

  const [draggingData, setDraggingData] = useState<DndDataType>();

  const seatNoMapRef = useRef<
    Record<EvaluateStudentType['studentId'], { rowNo?: number; colNo?: number }>
  >({});

  const { emptyRowMap, emptyColumnMap } = useMemo(() => {
    const emptyRowMap: Record<number, boolean> = {};
    const emptyColumnMap: Record<number, boolean> = {};

    if (!seats.length) {
      return { emptyRowMap, emptyColumnMap };
    }

    seats.forEach((rowSeats, row) => {
      if (rowSeats.every((seat) => !seat.student)) {
        emptyRowMap[row] = true;
      }
    });

    for (let columnIndex = 0; columnIndex < seats[0].length; columnIndex++) {
      let isEmpty = true;
      for (let rowIndex = 0; rowIndex < seats.length; rowIndex++) {
        if (seats[rowIndex][columnIndex].student) {
          isEmpty = false;
          break;
        }
      }
      if (isEmpty) {
        emptyColumnMap[columnIndex] = true;
      }
    }

    return {
      emptyRowMap,
      emptyColumnMap
    };
  }, [seats]);

  const { checkedRowMap, checkedColumnMap } = useMemo(() => {
    const checkedRowMap: Record<number, boolean> = {};
    const checkedColumnMap: Record<number, boolean> = {};
    if (!isCheckable || !checkedIds?.length || !seats.length) {
      return { checkedRowMap, checkedColumnMap };
    }

    seats.forEach((rowSeats, row) => {
      if (rowSeats.every((seat) => !seat.student || checkedIds.includes(seat.student.studentId))) {
        checkedRowMap[row] = true;
      }
    });

    for (let columnIndex = 0; columnIndex < seats[0].length; columnIndex++) {
      let isChecked = true;
      for (let rowIndex = 0; rowIndex < seats.length; rowIndex++) {
        const student = seats[rowIndex][columnIndex].student;
        if (student && !checkedIds.includes(student.studentId)) {
          isChecked = false;
          break;
        }
      }
      if (isChecked) {
        checkedColumnMap[columnIndex] = true;
      }
    }
    return { checkedRowMap, checkedColumnMap };
  }, [seats, checkedIds, isCheckable]);

  const handleCheckChange = ({ studentId: id }: EvaluateStudentType, isChecked: boolean) => {
    const ids = isChecked
      ? [...(checkedIds || []), id]
      : checkedIds?.filter((it) => it !== id) || [];
    onCheckChange?.(ids);
  };

  const handleRowCheckChange = (row: number, isChecked: boolean) => {
    const rowIds = seats[row]
      .map((seat) => seat.student?.studentId)
      .filter((it) => it !== undefined);
    const ids = isChecked
      ? [...(checkedIds || []), ...rowIds.filter((id) => !checkedIds?.includes(id))]
      : checkedIds?.filter((id) => !rowIds.includes(id)) || [];
    onCheckChange?.(ids);
  };

  const handleColumnCheckChange = (column: number, isChecked: boolean) => {
    const columnIds = seats
      .map((rowSeats) => rowSeats[column].student?.studentId)
      .filter((it) => it !== undefined);
    const ids = isChecked
      ? [...(checkedIds || []), ...columnIds.filter((id) => !checkedIds?.includes(id))]
      : checkedIds?.filter((id) => !columnIds.includes(id)) || [];
    onCheckChange?.(ids);
  };

  const addRowCount = (count: number) => {
    setSeats((state) => {
      const newState = [...state];
      for (let rowIndex = state.length; rowIndex < state.length + count; rowIndex++) {
        const rowSeats: SeatType[] = [];
        for (let columnIndex = 0; columnIndex < state[0].length; columnIndex++) {
          rowSeats.push({});
        }
        newState.push(rowSeats);
      }

      return newState;
    });
  };

  const removeRow = (row: number) => {
    setSeats((state) => state.filter((_, index) => index !== row));
  };

  const addColumnCount = (count: number) => {
    setSeats((state) => {
      const newState = [...state];
      for (let rowIndex = 0; rowIndex < state.length; rowIndex++) {
        const oldColumnCount = state[rowIndex].length;
        const rowSeats = [...state[rowIndex]];
        for (
          let columnIndex = oldColumnCount;
          columnIndex < oldColumnCount + count;
          columnIndex++
        ) {
          rowSeats.push({});
        }
        newState[rowIndex] = rowSeats;
      }
      return newState;
    });
  };

  const removeColumn = (column: number) => {
    setSeats((state) => {
      const newState = [...state];
      for (let rowIndex = 0; rowIndex < state.length; rowIndex++) {
        newState[rowIndex] = state[rowIndex].filter((_, index) => index !== column);
      }
      return newState;
    });
  };

  const handleDragStart = (e: DragStartEvent) => {
    setDraggingData(e.active.data.current as DndDataType);
  };

  const handleDragEnd = (e: DragEndEvent) => {
    const activeData = e.active.data.current as DndDataType;
    const overData = e.over?.data?.current as DndDataType;
    if (!activeData || !overData) {
      return;
    }
    const { row: activeRow, column: activeColumn } = activeData;
    const { row: overRow, column: overColumn } = overData;
    setSeats((state) => {
      const newState = [...state];
      newState[activeRow] = [...state[activeRow]];
      if (activeRow !== overRow) {
        newState[overRow] = [...state[overRow]];
      }
      newState[activeRow][activeColumn] = {
        ...newState[activeRow][activeColumn],
        student: state[overRow][overColumn].student
      };
      newState[overRow][overColumn] = {
        ...newState[overRow][overColumn],
        student: state[activeRow][activeColumn].student
      };
      return newState;
    });
  };

  useImperativeHandle(ref, () => ({
    addRowCount,
    addColumnCount
  }));

  useEffect(() => {
    // if (
    //   students.length &&
    //   students.every((student) => {
    //     const seatNo = seatNoMapRef.current[student.studentId];
    //     return seatNo && seatNo.rowNo === student.colNo && seatNo.colNo === student.colNo;
    //   })
    // ) {
    //   return;
    // }

    seatNoMapRef.current = {};
    students.forEach((student) => {
      seatNoMapRef.current[student.studentId] = { rowNo: student.rowNo, colNo: student.colNo };
    });

    let realRowCount = rowCount || 1;
    let realColumnCount = columnCount || 1;

    students.forEach((student) => {
      if (student.rowNo && student.rowNo > realRowCount) {
        realRowCount = student.rowNo;
      }
      if (student.colNo && student.colNo > realColumnCount) {
        realColumnCount = student.colNo;
      }
    });

    while (students.length > realRowCount * realColumnCount) {
      if (realColumnCount < 8) {
        realColumnCount++;
      } else {
        realRowCount++;
      }
    }

    if (incRowCount && incRowCount > 0) {
      realRowCount += incRowCount;
    }

    if (incColumnCount && incColumnCount > 0) {
      realColumnCount += incColumnCount;
    }

    const seats: SeatType[][] = [];

    for (let rowIndex = 0; rowIndex < realRowCount; rowIndex++) {
      const rowSeats: SeatType[] = [];
      seats.push(rowSeats);
      for (let columnIndex = 0; columnIndex < realColumnCount; columnIndex++) {
        rowSeats.push({});
      }
    }

    const pendingStudents: EvaluateStudentType[] = [];
    students.forEach((student) => {
      if (student.rowNo && student.colNo && !seats[student.rowNo - 1][student.colNo - 1].student) {
        seats[student.rowNo - 1][student.colNo - 1].student = student;
      } else {
        pendingStudents.push(student);
      }
    });

    // pendingStudents.forEach((student) => {
    //   for (let rowIndex = 0; rowIndex < realRowCount; rowIndex++) {
    //     for (let columnIndex = 0; columnIndex < realColumnCount; columnIndex++) {
    //       if (!seats[rowIndex][columnIndex].student) {
    //         seats[rowIndex][columnIndex].student = student;
    //         return;
    //       }
    //     }
    //   }
    // });

    setSeats(seats);
  }, [students, rowCount, columnCount]);

  useEffect(() => {
    const allStudents: EvaluateStudentType[] = [];
    const changedStudents: EvaluateStudentType[] = [];

    let rowNo = 1;
    for (let rowIndex = 0; rowIndex < seats.length; rowIndex++) {
      if (emptyRowMap[rowIndex]) {
        continue;
      }
      const rowSeats = seats[rowIndex];
      let colNo = 1;
      for (let columnIndex = 0; columnIndex < rowSeats.length; columnIndex++) {
        if (emptyColumnMap[columnIndex]) {
          continue;
        }
        let student = rowSeats[columnIndex].student;
        if (student) {
          const seatNo = seatNoMapRef.current[student.studentId];
          if (
            !seatNo ||
            seatNo.rowNo !== rowNo ||
            seatNo.colNo !== colNo ||
            student.colNo === undefined ||
            student.rowNo === undefined
          ) {
            seatNoMapRef.current[student.studentId] = { rowNo, colNo };
            student = { ...student, rowNo, colNo };
            changedStudents.push(student);
          }
          allStudents.push(student);
        }
        colNo++;
      }
      rowNo++;
    }
    onSeatChange?.(changedStudents, allStudents, seats);
  }, [seats]);

  console.log(seats, 'seats');

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragCancel={() => setDraggingData(undefined)}
    >
      <Flex direction="column" overflow="hidden" {...props}>
        <Flex w="100%" flexWrap="wrap" align="left" position="relative" zIndex="1">
          班级学生{students.length}人
        </Flex>
        <Center
          alignSelf="center"
          flexShrink="0"
          mb="16px"
          w="260px"
          h="40px"
          color="#000000"
          bgColor="#F7F7F7"
          fontSize="16px"
          fontWeight="bold"
          borderRadius="8px"
        >
          讲台
        </Center>

        <Box overflow="auto">
          <Flex pos="sticky" top="0" zIndex={2}>
            <Box
              flex="0 0 auto"
              w="34px"
              h="40px"
              bgColor="#F9FAFB"
              border="1px solid #E7E7E7"
              pos="sticky"
              left="0"
            />

            <Flex flex="1" align="stretch" ml="10px" h="40px" color="#606266" fontSize="16px">
              {seats[0]?.map((_, columnIndex) => (
                <Fragment key={columnIndex}>
                  {columnIndex > 0 && (
                    <Box
                      flex="0 0 auto"
                      h="100%"
                      bgColor="#F9FAFB"
                      borderY="1px solid #E7E7E7"
                      borderLeft="1px solid #E7E7E7"
                      {...(columnIndex % 2 === 0
                        ? { w: '22px', borderRight: '1px solid #E7E7E7' }
                        : {
                            w: 0
                          })}
                    />
                  )}

                  <Center
                    flex="1 0 auto"
                    w={Student.HoverBoxW}
                    bgColor="#F9FAFB"
                    borderY="1px solid #E7E7E7"
                    borderLeft={columnIndex === 0 ? '1px solid #E7E7E7' : undefined}
                    borderRight={
                      columnIndex === seats[0].length - 1 ? '1px solid #E7E7E7' : undefined
                    }
                    boxSizing="content-box"
                    pos="relative"
                  >
                    {isCheckable && !emptyColumnMap[columnIndex] && (
                      <Checkbox
                        mr={respDims(6)}
                        isChecked={!!checkedColumnMap[columnIndex]}
                        colorScheme="primary"
                        size="lg"
                        onChange={(e) => handleColumnCheckChange(columnIndex, e.target.checked)}
                      />
                    )}

                    <Box
                      {...(isCheckable && {
                        cursor: 'pointer',
                        onClick: () =>
                          handleColumnCheckChange(columnIndex, !checkedColumnMap[columnIndex])
                      })}
                    >{`第${columnIndex + 1}列`}</Box>

                    {type === 'adjust' && seats[0].length > 1 && emptyColumnMap[columnIndex] && (
                      <Center
                        border="1px solid #E7E7E7"
                        borderRadius="50%"
                        cursor="pointer"
                        pos="absolute"
                        w="16px"
                        h="16px"
                        right="16px"
                        top="0"
                        bottom="0"
                        my="auto"
                        onClick={() => removeColumn(columnIndex)}
                      >
                        <SvgIcon name="close" w="12px" h="12px" />
                      </Center>
                    )}
                  </Center>
                </Fragment>
              ))}
            </Flex>
          </Flex>

          {seats.map((rowSeats, rowIndex) => (
            <Flex key={rowIndex}>
              <Center
                flex="0 0 auto"
                flexDir="column"
                w="34px"
                color="#606266"
                bgColor="#F9FAFB"
                fontSize="15px"
                borderLeft="1px solid #E7E7E7"
                borderRight="1px solid #E7E7E7"
                borderBottom="1px solid #E7E7E7"
                pos="sticky"
                left="0"
                zIndex={1}
              >
                {isCheckable && !emptyRowMap[rowIndex] && (
                  <Checkbox
                    mt={respDims(6)}
                    isChecked={!!checkedRowMap[rowIndex]}
                    colorScheme="primary"
                    size="lg"
                    onChange={(e) => handleRowCheckChange(rowIndex, e.target.checked)}
                  />
                )}

                <Box
                  {...(isCheckable && {
                    cursor: 'pointer',
                    onClick: () => handleRowCheckChange(rowIndex, !checkedRowMap[rowIndex])
                  })}
                >{`${rowIndex + 1}排`}</Box>

                {type === 'adjust' && seats.length > 1 && emptyRowMap[rowIndex] && (
                  <Center
                    border="1px solid #E7E7E7"
                    borderRadius="50%"
                    cursor="pointer"
                    pos="absolute"
                    w="16px"
                    h="16px"
                    bottom="16px"
                    left="0"
                    right="0"
                    mx="auto"
                    onClick={() => removeRow(rowIndex)}
                  >
                    <SvgIcon name="close" w="12px" h="12px" />
                  </Center>
                )}
              </Center>

              <Flex
                flex="1"
                align="stretch"
                ml="10px"
                borderLeft="1px solid #E7E7E7"
                borderRight="1px solid #E7E7E7"
                borderBottom={rowIndex === seats.length - 1 ? '1px solid #E7E7E7' : undefined}
              >
                {rowSeats.map((seat, index) => (
                  <Fragment key={seat.student?.studentId ? `id-${seat.student.studentId}` : index}>
                    {index > 0 && (
                      <Box
                        flex="0 0 auto"
                        h="100%"
                        borderLeft="1px solid #E7E7E7"
                        {...(index % 2 === 0
                          ? { w: '22px', borderRight: '1px solid #E7E7E7' }
                          : {
                              w: 0
                            })}
                      />
                    )}

                    <Seat
                      key={index}
                      flex="1 0 auto"
                      h={type === 'adjust' ? '140px' : '160px'}
                      isDraggable={type === 'adjust' && !!seat.student}
                      isDroppable={type === 'adjust'}
                      row={rowIndex}
                      column={index}
                      student={seat.student}
                    >
                      {seat.student ? (
                        <Student.Hover
                          entryType={entryType}
                          type={type === 'evaluate' ? 'evaluate' : 'normal'}
                          student={seat.student}
                          {...(type === 'evaluate'
                            ? {
                                isCheckable,
                                isClickCheck: true,
                                isChecked: !!checkedIds?.includes(seat.student.studentId),
                                cursor: 'pointer',
                                onCheckChange: handleCheckChange,
                                onClick: onClickStudent
                              }
                            : {})}
                        />
                      ) : (
                        <Student.Hover type="seat" entryType={entryType} />
                      )}
                    </Seat>
                  </Fragment>
                ))}
              </Flex>
            </Flex>
          ))}
        </Box>
      </Flex>

      <DragOverlay dropAnimation={null}>
        {draggingData?.student && (
          <Seat row={0} column={0}>
            <Student.Hover student={draggingData.student} entryType={entryType} />
          </Seat>
        )}
      </DragOverlay>
    </DndContext>
  );
};

const Seat = ({
  isDraggable,
  isDroppable,
  row,
  column,
  student,
  children,
  ...props
}: {
  isDraggable?: boolean;
  isDroppable?: boolean;
  row: number;
  column: number;
  student?: EvaluateStudentType;
  children?: ReactNode;
} & ChakraProps) => {
  const id = `${row}-${column}`;
  const data = { row, column, student };
  const {
    isDragging,
    attributes,
    listeners,
    setNodeRef: setDragRef
  } = useDraggable({
    id,
    data,
    disabled: !isDraggable
  });

  const { isOver, setNodeRef: setDropRef } = useDroppable({
    id,
    data,
    disabled: !isDroppable
  });

  const styles: ChakraProps =
    isDragging || isOver
      ? {
          opacity: 0.5,
          bgColor: 'primary.50'
        }
      : {};

  return (
    <Center
      ref={(e) => {
        setDragRef(e);
        setDropRef(e);
      }}
      {...(isDraggable && { ...attributes, ...listeners })}
      {...styles}
      {...props}
    >
      {children}
    </Center>
  );
};

export default forwardRef(SeatList);
