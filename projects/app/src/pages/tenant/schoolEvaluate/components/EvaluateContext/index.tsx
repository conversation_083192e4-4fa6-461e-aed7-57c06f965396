import React, { createContext, useContext, useState, ReactNode } from 'react';
import { PathItemType } from '@/types/cloud';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import { EvaluateClazzType, EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import { useQuery } from '@tanstack/react-query';
import { getClazzIndactorList } from '@/api/tenant/evaluate/process';
import { useUpdateEffect } from 'ahooks';

export type FooterRouterMenuProps = {
  clazz: EvaluateClazzType;
};

interface EvaluateContextProps {
  enterType: EvaluateEntryEnum;
  FooterRouterMenu?: React.ComponentType<FooterRouterMenuProps>;
  menuId: string;
  semesterId: string;
  ruleId: string;
  subjectId: string;
  gradeId: string;
  clazzId: string;
  isSubjectEntry: boolean;
  indactors: EvaluateIndactorType[];
  clazzName: string;
  isClazzTeacher?: boolean;

  refetchIndactors: () => void;
  setRuleId: (ruleId: string) => void;
  setSubjectId: (subjectId: string) => void;
}

interface EvaluateProviderProps {
  enterType: EvaluateEntryEnum;
  children: ReactNode;
  gradeId: string;
  clazzId: string;
  menuId: string;
  semesterId: string;
  clazzName: string;
  subjectId?: string;
  ruleId?: string;
  isSubjectEntry: boolean;
  FooterRouterMenu?: React.ComponentType<FooterRouterMenuProps>;
  isClazzTeacher?: boolean;
}

const EvaluateContext = createContext<EvaluateContextProps | undefined>(undefined);

export const EvaluateProvider: React.FC<EvaluateProviderProps> = ({
  enterType,
  children,
  FooterRouterMenu,
  gradeId,
  clazzId,
  menuId,
  semesterId,
  clazzName,
  ruleId: defaultRuleId,
  subjectId: defaultSubjectId,
  isSubjectEntry,
  isClazzTeacher
}) => {
  const [ruleId, setRuleId] = useState<string>(defaultRuleId || '');
  const [subjectId, setSubjectId] = useState<string>(defaultSubjectId || '');

  const { data: indactors = [], refetch: refetchIndactors } = useQuery(
    ['indactors', clazzId, subjectId, ruleId, menuId],
    () =>
      getClazzIndactorList({
        deptId: clazzId,
        ruleId: ruleId! || '',
        semesterId,
        menuId
      }),
    {
      enabled: isSubjectEntry
        ? !!(clazzId && ruleId && semesterId && menuId && subjectId)
        : !!(menuId && clazzId && ruleId && semesterId)
    }
  );

  useUpdateEffect(() => {
    setSubjectId(subjectId);
  }, [defaultSubjectId]);

  useUpdateEffect(() => {
    setRuleId(defaultRuleId || '');
  }, [defaultRuleId]);

  return (
    <EvaluateContext.Provider
      value={{
        enterType,
        FooterRouterMenu,
        menuId,
        semesterId,
        ruleId,
        subjectId,
        gradeId,
        clazzId,
        isSubjectEntry,
        indactors,
        clazzName,
        isClazzTeacher,
        refetchIndactors,
        setRuleId,
        setSubjectId
      }}
    >
      {children}
    </EvaluateContext.Provider>
  );
};

export const useEvaluateContext = () => {
  const context = useContext(EvaluateContext);
  if (!context) {
    throw new Error('useEvaluateContext must be used within a EvaluateProvider');
  }
  return context;
};

export default EvaluateProvider;
