import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Select, Spin } from 'antd';
import { useSystemStore } from '@/store/useSystemStore';
import { Box, HStack } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import SemesterSelect from '../SemesterSelect';
import styles from '@/pages/index.module.scss';
import { PeriodType as periodType, PeriodTypeMap } from '@/constants/api/tenant/evaluate/rule';
import { RuleListByClazzIdResponse } from '@/types/api/tenant/evaluate/process';
import { getRuleList, getRuleListByClazzId } from '@/api/tenant/evaluate/process';
import { useUpdateEffect } from 'ahooks';
import { treeToList } from '@/utils/tree';
import { useRoutes } from '@/hooks/useRoutes';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';

const { Option } = Select;

export enum SourceType {
  Instant = 1,
  Period = 2
}

export type EvaluateFilterValue = {
  semester: string | undefined;
  periodType: number | undefined;
  ruleId: string | undefined;
  evaluatedName?: string | undefined;
  evaluatorName?: string | undefined;
};

interface EvaluateFilterSelectProps {
  value: EvaluateFilterValue;
  onChange: (value: EvaluateFilterValue) => void;
  menuId: string;
  clazzId?: string;
  enterType: EvaluateEntryEnum;
  projectType?: 'teacher' | 'clazz';
}

const EvaluateFilterSelect: React.FC<EvaluateFilterSelectProps> = ({
  value: form,
  onChange,
  menuId,
  clazzId,
  projectType = 'clazz',
  enterType
}) => {
  const { currentSemester } = useSystemStore();
  const [periodTypeList, setPeriodTypeList] = useState<periodType[]>([]);
  const [timerangeList, setTimerangeList] = useState<RuleListByClazzIdResponse[]>([]);
  const { entryTree, setEntryTree } = useSystemStore();
  useQuery(['entranceTree'], setEntryTree);
  const reflectionId = useMemo(() => {
    const info = treeToList(entryTree).find(
      (item) => item?.name == evaluateEntryMapEvaluateInfoType[enterType].name
    );
    return info?.id?.toString();
  }, [entryTree, enterType]);

  const onFormChange = useCallback(
    (field: string, value?: string | number) => {
      const newForm = { ...form, [field]: value };
      console.log(field, value);

      // 清空依赖的下拉选项
      if (field === 'semester') {
        newForm.periodType = undefined;
        newForm.ruleId = undefined;
      } else if (field === 'periodType') {
        console.log('periodType', value);
        newForm.ruleId = undefined;
      }

      onChange(newForm);
    },
    [onChange, form]
  );

  // 初始化学期
  useEffect(() => {
    if (currentSemester?.id && !form.semester) {
      onChange({
        ...form,
        semester: currentSemester?.id
      });
    }
  }, [currentSemester, form.semester]);

  const {
    isLoading: isLoadingFrequencies,
    data: ruleList = [],
    refetch
  } = useQuery(
    ['frequencies', form.semester, reflectionId],
    () => {
      if (projectType === 'clazz') {
        return getRuleListByClazzId({
          semesterId: form.semester || '',
          menuId: menuId,
          clazzId: clazzId!,
          reflectionId: reflectionId!
        });
      } else {
        return getRuleList({
          semesterId: form.semester || '',
          menuId: menuId,
          sourceType: SourceType.Instant,
          reflectionId:
            treeToList(entryTree)
              .find(
                (item) =>
                  item?.name ==
                  evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name
              )
              ?.id?.toString() || ''
        });
      }
    },
    {
      enabled: !!(form.semester && menuId && clazzId && reflectionId),
      onSuccess: (data) => {
        if (data.length > 0) {
          console.log('数据加载成功', data);

          // 设置频次列表
          const list = Array.from(new Set(data.map((item) => item.periodType)));
          setPeriodTypeList(list as periodType[]);

          // 如果当前没有选择频次或者选择的频次不在列表中，自动选择第一个
          const shouldSetPeriodType =
            !form.periodType || !list.includes(form.periodType as periodType);

          if (shouldSetPeriodType && list.length > 0) {
            const newPeriodType = list[0];

            // 过滤出当前频次下的时段列表
            const filteredTimeRanges = data.filter((item) => item.periodType === newPeriodType);
            setTimerangeList(filteredTimeRanges);

            // 如果有时段，自动选择第一个
            const newRuleId =
              filteredTimeRanges.length > 0 ? filteredTimeRanges[0].ruleId.toString() : undefined;

            // 更新表单
            onChange({
              semester: form.semester,
              periodType: newPeriodType,
              ruleId: newRuleId
            });
          }
        }
      }
    }
  );

  useUpdateEffect(() => {
    if (form.semester && reflectionId) {
      refetch();
    }
  }, [form.semester, reflectionId]);

  // 当频次改变时，更新时段列表
  useEffect(() => {
    if (form.periodType && ruleList.length > 0) {
      const filteredTimeranges = ruleList.filter((item) => item.periodType == form.periodType);
      setTimerangeList(filteredTimeranges);

      // 如果当前没有选择时段，或者选择的时段不在过滤后的列表中，自动选择第一个
      const currentRuleIdExists = filteredTimeranges.some(
        (item) => item.ruleId.toString() === form.ruleId
      );

      if ((!form.ruleId || !currentRuleIdExists) && filteredTimeranges.length > 0) {
        onFormChange('ruleId', filteredTimeranges[0].ruleId.toString());
      }
    } else {
      setTimerangeList([]);
    }
  }, [form.periodType, ruleList]);

  // 确定是否显示频次选择器
  const showPeriodTypeSelect = periodTypeList.length > 0;

  // 确定是否显示时段选择器
  const showTimerangeSelect = timerangeList.length > 0;

  return (
    <HStack className={`${styles['my-form']} ${styles['evaluate-filter-form']}`}>
      <SemesterSelect value={form.semester} onChange={(value) => onFormChange('semester', value)} />

      <Select
        value={form.periodType}
        style={{
          width: 200,
          border: 'none',
          borderRadius: '10px'
        }}
        placeholder="请选择频率"
        onChange={(value) => onFormChange('periodType', value)}
        loading={isLoadingFrequencies}
      >
        {periodTypeList.map((freq) => (
          <Option key={freq} value={freq}>
            {`评价频次:${PeriodTypeMap[freq].label}`}
          </Option>
        ))}
      </Select>

      <Select
        value={form.ruleId}
        style={{
          width: 380,
          border: 'none',
          borderRadius: '10px'
        }}
        placeholder="请选择时间"
        onChange={(value) => onFormChange('ruleId', value)}
      >
        {timerangeList.map((time) => (
          <Option key={time.ruleId.toString()} value={time.ruleId.toString()}>
            {`评价时段：${time.startTime.split(':')[0]}:${time.startTime.split(':')[1]} - ${time.endTime.split(':')[0]}:${time.endTime.split(':')[1]}`}
          </Option>
        ))}
      </Select>
    </HStack>
  );
};

export default EvaluateFilterSelect;
