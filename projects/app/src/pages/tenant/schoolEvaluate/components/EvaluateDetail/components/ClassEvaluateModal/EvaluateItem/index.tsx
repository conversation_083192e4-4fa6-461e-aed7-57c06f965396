import { IndactorTypeEnum } from '@/constants/api/tenant/evaluate/process';
import { EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import { Center, ChakraProps, Flex, Image } from '@chakra-ui/react';

const EvaluateItem = ({
  item,
  onClick,
  ...props
}: {
  item: EvaluateIndactorType;
  onClick?: (item: EvaluateIndactorType) => void;
} & ChakraProps) => {
  return (
    <Flex
      direction="column"
      align="center"
      w="80px"
      mb="30px"
      pos="relative"
      cursor="pointer"
      onClick={() => onClick?.(item)}
      {...props}
    >
      <Image
        src={item.iconFile?.fileUrl || '/imgs/v2/ai_avatar.svg'}
        w="70px"
        h="70px"
        alt=""
        fallbackStrategy="onError"
        fallbackSrc={'/imgs/v2/ai_avatar.svg'}
        borderRadius="50%"
      />

      <Flex
        mt="-6px"
        w="100%"
        h="26px"
        align="center"
        bgColor="#FFF6CC"
        fontSize="14px"
        lineHeight="22px"
        borderRadius="50px"
        pos="relative"
      >
        <Center
          flex="1 0 0"
          color={item.indactorType === IndactorTypeEnum.Good ? '#71D4FF' : '#FF8471'}
        >
          {`${item?.score! > 0 ? '+' : ''}${item.score}`}
        </Center>
      </Flex>

      <Center
        mt="6px"
        w="100%"
        color="#303133"
        fontSize="16px"
        fontWeight="bold"
        whiteSpace="nowrap"
      >
        {item.name}
      </Center>
    </Flex>
  );
};

export default EvaluateItem;
