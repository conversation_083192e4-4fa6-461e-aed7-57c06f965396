import { useEffect, useRef, useState } from 'react';
import {
  Box,
  BoxProps,
  Center,
  Flex,
  Input,
  InputGroup,
  InputRightElement
} from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import Students, { StudentsRef } from './components/Students';
import SubTabs from '../SubTabs';
import MainTabs from '../MainTabs';
import Groups, { GroupsRef } from './components/Groups';
import Breadcrumb from '../Breadcrumb';
import { useQuery } from '@tanstack/react-query';
import { getEvaluateSubjectList } from '@/api/tenant/evaluate/process';
import EvaluateFilterSelect, { EvaluateFilterValue } from '../EvaluateFilterSelect';
import { useEvaluateContext } from '../EvaluateContext';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import { Select } from 'antd';
import styles from '@/pages/index.module.scss';
import { EvaluateSubjectType } from '@/types/api/kanban';

const viewTypes = [
  {
    label: '学生',
    value: 'student'
  },
  {
    label: '微团队',
    value: 'group'
  }
];

const studentViewTypes = [
  {
    label: '按学号顺序排列',
    value: 'code'
  },
  {
    label: '按座位排列',
    value: 'seat'
  }
];

const EvaluateDetail = ({
  isSubjectEntry = false,
  isCulturalKnowledge = false,
  ...props
}: {
  isSubjectEntry?: boolean;
  isCulturalKnowledge?: boolean;
} & BoxProps) => {
  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: undefined,
    periodType: undefined,
    ruleId: undefined
  });
  const {
    ruleId,
    subjectId,
    gradeId,
    clazzId,
    menuId,
    semesterId,
    clazzName,
    isClazzTeacher,
    setRuleId,
    setSubjectId,
    enterType
  } = useEvaluateContext();

  const [viewType, setViewType] = useState('student' as 'student' | 'group');

  const [searchKey, setSearchKey] = useState('');

  const [studentViewType, setStudentViewType] = useState('code' as 'code' | 'seat');

  const studentsRef = useRef<StudentsRef>(null);

  const groupsRef = useRef<GroupsRef>(null);
  const [subjects, setSubjects] = useState<EvaluateSubjectType[]>([]);

  useEffect(() => {
    setRuleId(filterForm.ruleId || '');
  }, [filterForm.ruleId, setRuleId]);

  const { data: fetchedSubjects = [], refetch: refetchSubjects } = useQuery(
    ['subjects', gradeId, clazzId, ruleId, menuId, semesterId],
    () => getEvaluateSubjectList({ deptId: clazzId, ruleId: ruleId || '', menuId, semesterId }),
    {
      enabled: !!(clazzId && ruleId && menuId && semesterId),
      onSuccess: (data) => {
        setSubjects(data || []);
        setSubjectId(data[0]?.id || '');
      }
    }
  );

  useEffect(() => {
    setSubjectId(subjectId);
  }, [subjectId, setSubjectId]);

  return (
    <Flex
      direction="column"
      w="100%"
      h="100%"
      bgColor="transparent"
      borderRadius={respDims(20)}
      pos="relative"
      {...props}
    >
      <Flex flex="1" direction="column" h="100%">
        {enterType !== EvaluateEntryEnum.ClassroomPerformance &&
          enterType !== EvaluateEntryEnum.SemesterReview && (
            <Flex justifyContent="space-between" mx={respDims(32)} my={respDims(18)} mt="24px">
              <Breadcrumb list={[{ label: '班级列表' }, { label: clazzName }]} />

              <EvaluateFilterSelect
                menuId={menuId}
                clazzId={clazzId}
                value={filterForm}
                enterType={enterType}
                onChange={(value) => {
                  setFilterForm(value);
                }}
              ></EvaluateFilterSelect>
            </Flex>
          )}
        <Flex direction="column" pt="24px" bgColor="#fff" pos="relative" flex={1}>
          <Flex align="center" mx={respDims(32)}>
            <MainTabs
              value={subjectId}
              list={subjects}
              labelKey="name"
              valueKey="id"
              onChange={setSubjectId}
            />
          </Flex>

          <Flex
            mx={respDims(32)}
            mt={subjectId ? respDims(14) : '-4px'}
            justifyContent="space-between"
            alignItems="center"
          >
            <Flex alignItems="center">
              <SubTabs
                alignSelf="self-start"
                value={viewType}
                list={viewTypes}
                mr="26px"
                onChange={setViewType}
              />
              {viewType === 'student' && (
                <InputGroup w="auto" h="36px">
                  <Input
                    w="100%"
                    borderRadius="10px"
                    bgColor="rgba(0,0,0,0.03)"
                    boxShadow="none"
                    border="none"
                    h="36px"
                    fontSize="14px"
                    outline="none"
                    _focus={{
                      bgColor: 'rgba(0,0,0,0.03)',
                      border: 'none',
                      outline: 'none',
                      boxShadow: 'none'
                    }}
                    placeholder="请输入学生名称"
                    _placeholder={{
                      fontSize: '14px',
                      color: '#718096'
                    }}
                    value={searchKey}
                    onChange={(e) => setSearchKey(e.target.value)}
                  />
                  <InputRightElement>
                    <SvgIcon name="search" h="36px" pb="2px" />
                  </InputRightElement>
                </InputGroup>
              )}
            </Flex>
            {viewType === 'student' && (
              <>
                <Box flex="1"></Box>
                {studentViewType === 'seat' && isClazzTeacher && (
                  <Box ml={respDims(16)}>
                    <MyMenu
                      placement="left-start"
                      Button={
                        <Center
                          w={respDims('34fpx')}
                          h={respDims('34fpx')}
                          bgColor="rgba(0,0,0,0.03)"
                          borderRadius="50%"
                          cursor="pointer"
                        >
                          <SvgIcon name="settings" />
                        </Center>
                      }
                      menuList={[
                        {
                          label: '调整学生座位',
                          onClick: () => studentsRef.current?.showAdjustSeats()
                        },
                        {
                          label: '快速新增座位',
                          onClick: () => studentsRef.current?.showAddSeats()
                        }
                      ]}
                    />
                  </Box>
                )}

                <Box
                  ml={respDims(16)}
                  className={styles['my-form']}
                  flex="0 0 auto"
                  w={respDims(247)}
                  pos="relative"
                >
                  <Select
                    value={studentViewType}
                    style={{ width: '100%', border: 'none', borderRadius: '10px' }}
                    onChange={setStudentViewType}
                  >
                    {studentViewTypes.map((item) => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Box>
              </>
            )}

            {viewType === 'group' && isClazzTeacher && (
              <Box ml="auto">
                <MyMenu
                  Button={
                    <Center
                      w={respDims('34fpx')}
                      h={respDims('34fpx')}
                      bgColor="rgba(0,0,0,0.03)"
                      borderRadius="50%"
                      cursor="pointer"
                    >
                      <SvgIcon name="settings" />
                    </Center>
                  }
                  menuList={[
                    {
                      label: '调整学生分组',
                      onClick: () => groupsRef.current?.showAdjustGroups()
                    },
                    {
                      label: '快速新增分组',
                      onClick: () => groupsRef.current?.showAddGroups()
                    }
                  ]}
                />
              </Box>
            )}
          </Flex>
          <Box flex="1" mt={respDims(16)} overflow="hidden">
            {viewType === 'student' && (
              <Students
                ref={studentsRef}
                searchKey={searchKey}
                viewType={studentViewType}
                isCulturalKnowledge={isCulturalKnowledge}
              />
            )}
            {viewType === 'group' && <Groups ref={groupsRef} searchKey={searchKey} />}
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default EvaluateDetail;
