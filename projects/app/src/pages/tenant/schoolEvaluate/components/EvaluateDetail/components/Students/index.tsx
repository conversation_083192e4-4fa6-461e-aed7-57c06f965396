import { ChakraProps } from '@chakra-ui/system';
import { Box, Center, Checkbox, Flex, HStack } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import EvaluateModal from '../ClassEvaluateModal/EvaluateModal';
import { Toast } from '@/utils/ui/toast';
import { MessageBox } from '@/utils/ui/messageBox';
import StudentList from '../../../StudentList';
import SeatList from '../../../SeatList';
import SeatCountModal from '../../../SeatCountModal';
import AdjustSeatModal from '../../../AdjustSeatModal';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import {
  getClazzStudentListByCode,
  getClazzStudentListBySeat,
  getClazzStudentPage,
  resetClazzEvaluate
} from '@/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import TeacherSayEvaluateDrawer from '../TeacherSayEvaluateModal';
import { useEvaluateContext } from '../../../EvaluateContext';
import LevelEvaluateModal from '../LevelEvaluateModal';
import ImportPanelV2 from '@/components/ImportPanelV2';

export type StudentsRef = {
  showAddSeats: () => void;

  showAdjustSeats: () => void;
};

const Students = (
  {
    viewType = 'code',
    searchKey,
    isCulturalKnowledge = false,
    ...props
  }: {
    viewType?: 'code' | 'seat';
    searchKey?: string;
    semesterId?: string;
    isCulturalKnowledge?: boolean;
  } & ChakraProps,
  ref: ForwardedRef<StudentsRef>
) => {
  const [checkType, setCheckType] = useState<'evaluate' | 'reset' | ''>('');

  const {
    enterType,
    semesterId,
    subjectId,
    ruleId,
    clazzId,
    isSubjectEntry,
    gradeId,
    indactors,
    FooterRouterMenu,
    clazzName,
    isClazzTeacher,
    menuId
  } = useEvaluateContext();
  const { openOverlay } = useOverlayManager();
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const {
    data: codeStudents,
    isFetched: isCodeStudentFetched,
    refetch: refetchCodeStudents
  } = useQuery(['codeStudents', clazzId, subjectId, ruleId, semesterId], () => {
    if (!!(clazzId && ruleId && semesterId && (isSubjectEntry ? subjectId : true))) {
      return getClazzStudentListByCode({
        clazzId,
        subjectId,
        ruleId: ruleId,
        semesterId,
        entrance: enterType
      });
    }
    return [];
  });

  const {
    data,
    isFetched: isSeatStudentFetched,
    refetch: refetchSeatStudents
  } = useQuery(['seatStudents', clazzId, subjectId, ruleId, semesterId], () => {
    if (!!(clazzId && ruleId && semesterId && (isSubjectEntry ? subjectId : true))) {
      return getClazzStudentListBySeat({
        clazzId,
        subjectId,
        ruleId: ruleId,
        semesterId,
        entrance: enterType
      });
    }
    return {
      studentSeatList: [],
      clazzId,
      colNum: 0,
      rowNum: 0
    };
  });

  const { studentSeatList: seatStudents, colNum, rowNum } = data || {};
  const [isHideEvaluated, setIsHideEvaluated] = useState(false);

  const originStudents = useMemo(() => {
    return (viewType === 'code' ? codeStudents : seatStudents) || [];
  }, [viewType, codeStudents, seatStudents]);

  const students = useMemo(() => {
    if (!isHideEvaluated) {
      return originStudents || [];
    } else {
      return originStudents?.filter((it) => !it.evaluationList?.length) || [];
    }
  }, [isHideEvaluated, originStudents]);

  useEffect(() => {
    if (isHideEvaluated) {
      setCheckedIds([]);
    }
  }, [isHideEvaluated]);

  const refetchStudents = viewType === 'code' ? refetchCodeStudents : refetchSeatStudents;

  const onEvaluate = (students: EvaluateStudentType[]) => {
    if (
      enterType === EvaluateEntryEnum.SemesterReview ||
      enterType === EvaluateEntryEnum.AdaptiveLearning ||
      enterType === EvaluateEntryEnum.GoalAchievement
    ) {
      openOverlay({
        Overlay: LevelEvaluateModal,
        props: {
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          students,
          indactors,
          enterType,
          menuId,
          ruleId,
          semesterId,
          onSuccess: () => {
            refetchStudents();
            setCheckedIds([]);
            onCancelCheck();
            setIsHideEvaluated(false);
          }
        }
      });
    } else if (enterType === EvaluateEntryEnum.TeacherComments) {
      openOverlay({
        Overlay: TeacherSayEvaluateDrawer,
        props: {
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          students,
          indactors,
          enterType,
          menuId,
          ruleId,
          semesterId,
          onSuccess: () => {
            refetchStudents();
            setCheckedIds([]);
            setIsHideEvaluated(false);
          }
        }
      });
    } else if (enterType === EvaluateEntryEnum.ClassroomPerformance) {
      openOverlay({
        Overlay: EvaluateModal,
        props: {
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          students,
          indactors,
          menuId,
          ruleId,
          semesterId,
          onSuccess: () => {
            refetchStudents();
            setCheckedIds([]);
            setIsHideEvaluated(false);
          }
        }
      });
    }
  };

  const onClickStudent = (student: EvaluateStudentType) => onEvaluate([student]);

  const onClickSelectAll = () => {
    setCheckedIds((state) =>
      state.length && state.length === students.length ? [] : students.map((it) => it.studentId)
    );
  };

  const onClickEvaluate = () => {
    if (!checkedIds.length) {
      Toast.info('请先选择学生');
      return;
    }

    onEvaluate(students.filter((it) => checkedIds.includes(it.studentId)));
  };

  const onClickReset = () => {
    const ids = students
      .filter((it) => checkedIds.includes(it.studentId))
      .map((it) => it.studentId);
    clazzId &&
      (isSubjectEntry ? subjectId : true) &&
      ids.length &&
      ruleId &&
      semesterId &&
      menuId &&
      MessageBox.confirm({
        title: '提示',
        content: '重新计分将已评分数清零，且不可恢复，点评记录不会删除，确定重新计分吗？',
        onOk: () => {
          resetClazzEvaluate({
            evaluatedIds: ids,
            gradeId,
            clazzId,
            semesterId,
            ruleId,
            enterType,
            menuId,
            subjectId
          }).then(() => {
            refetchStudents();
            setCheckedIds([]);
            onCancelCheck();
            setIsHideEvaluated(false);
          });
        }
      });
  };

  const onAdjustSeat = async (options?: {
    rowCount?: number;
    columnCount?: number;
    incRowCount?: number;
    incColumnCount?: number;
  }) => {
    let students = codeStudents;
    if (!isCodeStudentFetched) {
      students = await getListFromPage(getClazzStudentPage, { clazzId }).then((res) => {
        const newStudents = res.map((item) => {
          return {
            studentCode: item.studentCode,
            studentSex: item.studentSex,
            studentId: item.id!,
            studentName: item.name!,
            avatarUrl: item.avatarUrl
          };
        });
        return newStudents;
      });
    }

    if (seatStudents?.length) {
      students?.forEach((student) => {
        const found = seatStudents.find(
          (seatStudent) => seatStudent.studentId === student.studentId
        );
        if (found) {
          student.seatId = found.seatId;
          student.rowNo = found.rowNo;
          student.colNo = found.colNo;
        }
      });
    }

    openOverlay({
      Overlay: AdjustSeatModal,
      props: {
        clazzId,
        semesterId: semesterId || '',
        students: students || [],
        rowCount: options?.rowCount || rowNum,
        columnCount: options?.columnCount || colNum,
        incRowCount: options?.incRowCount,
        incColumnCount: options?.incColumnCount,
        onSuccess: () => {
          refetchStudents();
        }
      }
    });
  };

  const onInitSeat = () => {
    openOverlay({
      Overlay: SeatCountModal,
      props: {
        clazzId,
        onSuccess: async (props) => {
          onAdjustSeat(props);
        }
      }
    });
  };

  const onCancelCheck = () => {
    setCheckType('');
    setCheckedIds([]);
    setIsHideEvaluated(false);
  };

  useImperativeHandle(ref, () => ({
    showAddSeats: () => {
      openOverlay({
        Overlay: SeatCountModal,
        props: {
          onSuccess: ({ rowCount, columnCount }) =>
            onAdjustSeat({
              rowCount: rowNum,
              columnCount: colNum,
              incRowCount: rowCount,
              incColumnCount: columnCount
            })
        }
      });
    },
    showAdjustSeats: () => onAdjustSeat()
  }));

  useEffect(() => {
    onCancelCheck();
  }, [subjectId]);

  const filteredStudents = students.filter((student) => student.studentName.includes(searchKey!));

  const handleImportSeat = () => {
    const appendParams = {
      clazzId,
      semesterId: semesterId || '',
      subjectId,
      ruleId: ruleId || '',
      gradeId,
      enterType
    };
    openOverlay({
      Overlay: ImportPanelV2,
      props: {
        title: '导入学生座位表',
        templateUrl: '/huayun-evaluation/clazz/student/clazzSeat/downloadTemplate',
        importUrl: '/huayun-evaluation/clazz/student/clazzSeat/import',
        appendParams,
        onUploadSuccess: () => {
          refetchStudents();
        }
      }
    });
  };
  return (
    <Flex direction="column" w="100%" h="100%" justifyContent="space-between" {...props}>
      {viewType === 'code' && (
        <StudentList
          flex="1"
          overflowY="auto"
          maxHeight={isCulturalKnowledge ? 'calc(100vh - 361px)' : 'calc(100vh - 252px)'}
          type="evaluate"
          students={filteredStudents}
          isCheckable={!!checkType}
          checkedIds={checkedIds}
          entryType={enterType}
          onCheckChange={setCheckedIds}
          onClickStudent={onClickStudent}
        />
      )}
      {viewType === 'seat' && (
        <>
          {originStudents.length ? (
            <SeatList
              maxHeight={isCulturalKnowledge ? 'calc(100vh - 361px)' : 'calc(100vh - 252px)'}
              flex="1"
              mx={respDims(24)}
              type="evaluate"
              students={filteredStudents}
              columnCount={colNum}
              rowCount={rowNum}
              entryType={enterType}
              isCheckable={!!checkType}
              checkedIds={checkedIds}
              onCheckChange={setCheckedIds}
              onClickStudent={onClickStudent}
            />
          ) : (
            isSeatStudentFetched && (
              <Flex direction="column" flex="1" justify="center" align="center" mb="80px">
                <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
                <Box>班级暂未进行座位排列，需班主任安排</Box>
                {
                  <HStack spacing={2}>
                    <Box
                      mt={respDims(18)}
                      px={respDims(44)}
                      py={respDims(9)}
                      color="primary.500"
                      bgColor="primary.50"
                      fontSize={respDims('16fpx')}
                      lineHeight={respDims('22fpx')}
                      borderRadius={respDims(100)}
                      cursor="pointer"
                      _hover={{
                        bgColor: '#f3f5fe'
                      }}
                      onClick={onInitSeat}
                    >
                      快速设置座位
                    </Box>
                    <Box
                      mt={respDims(18)}
                      px={respDims(44)}
                      py={respDims(9)}
                      color="primary.500"
                      bgColor="primary.50"
                      fontSize={respDims('16fpx')}
                      lineHeight={respDims('22fpx')}
                      borderRadius={respDims(100)}
                      cursor="pointer"
                      _hover={{
                        bgColor: '#f3f5fe'
                      }}
                      onClick={handleImportSeat}
                    >
                      导入座位表
                    </Box>
                  </HStack>
                }
              </Flex>
            )
          )}
        </>
      )}
      {!!indactors?.length && originStudents.length > 0 && (
        <Flex
          h="53px"
          justify="center"
          align="center"
          borderTop="1px solid #E5E7EB"
          color="#4E5969"
          fontSize={rpxDim(16)}
          position="relative"
        >
          <Center position="absolute" left="20px">
            {FooterRouterMenu && (
              <FooterRouterMenu
                clazz={{
                  clazzName,
                  id: clazzId,
                  parentId: gradeId,
                  deptName: ''
                }}
              ></FooterRouterMenu>
            )}
          </Center>
          {!checkType ? (
            <>
              <Center
                w={rpxDim(200)}
                h={rpxDim(40)}
                fontWeight="bold"
                cursor="pointer"
                onClick={() => setCheckType('evaluate')}
                _hover={{
                  color: 'primary.500'
                }}
              >
                <SvgIcon name="evaluationMutiple" />
                <Box ml={respDims(10)}>多选评分</Box>
              </Center>

              <Center
                w={rpxDim(200)}
                h={rpxDim(40)}
                fontWeight="bold"
                cursor="pointer"
                onClick={() => setCheckType('reset')}
                _hover={{
                  color: 'primary.500'
                }}
              >
                <SvgIcon name="evaluationLoop" />
                <Box ml={respDims(10)}>重新记分</Box>
              </Center>
            </>
          ) : (
            <HStack spacing={respDims(16)}>
              <Center cursor="pointer" userSelect="none" onClick={onClickSelectAll}>
                <Box onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    colorScheme="primary"
                    size="lg"
                    isChecked={checkedIds.length > 0 && checkedIds.length === students.length}
                    onChange={onClickSelectAll}
                  />
                </Box>
                <Box ml={respDims(16)}>选择全部</Box>
              </Center>

              <Center
                cursor="pointer"
                userSelect="none"
                onClick={() => setIsHideEvaluated((state) => !state)}
              >
                <Box onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    colorScheme="primary"
                    size="lg"
                    isChecked={isHideEvaluated}
                    onChange={() => setIsHideEvaluated((state) => !state)}
                  />
                </Box>
                <Box ml={respDims(16)}>隐藏已评分的学生</Box>
              </Center>

              <Center
                w={respDims('292fpx')}
                h={respDims('40fpx')}
                color="primary.500"
                bgColor="primary.50"
                boxSizing="border-box"
                borderRadius={respDims(8)}
                cursor="pointer"
                onClick={() => (checkType === 'evaluate' ? onClickEvaluate() : onClickReset())}
              >
                {`${checkType === 'evaluate' ? '点评' : '重新计算'} (${checkedIds.length})`}
              </Center>

              <Center
                w={respDims('292fpx')}
                h={respDims('40fpx')}
                bgColor="#F9FAFB"
                boxSizing="border-box"
                borderRadius={respDims(8)}
                cursor="pointer"
                onClick={onCancelCheck}
              >
                取消
              </Center>
            </HStack>
          )}
          <Box></Box>
        </Flex>
      )}
    </Flex>
  );
};

export default forwardRef(Students);
