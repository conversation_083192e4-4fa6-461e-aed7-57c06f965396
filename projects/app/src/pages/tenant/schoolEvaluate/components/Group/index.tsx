import SvgIcon from '@/components/SvgIcon';
import { EvaluateGroupType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, Center, ChakraProps, Flex, Text } from '@chakra-ui/react';
import { useMemo } from 'react';
import StudentAvatar from '../StudentAvatar';
import { EvaluateEntryEnum } from '@/constants/evaluate';

const GroupBoxDms = '.8ms';

const Group = ({
  group,
  entryType,
  onClick,
  ...props
}: {
  group: EvaluateGroupType;
  entryType: EvaluateEntryEnum;
  onClick?: (group: EvaluateGroupType) => void;
} & ChakraProps) => {
  const avatarCodes = useMemo(() => {
    const codes = group.studentIds?.slice(0, 3).map(String) || [];
    if (codes.length < 3) {
      codes.push(String(group.studentNum));
    }
    if (codes.length < 3) {
      codes.push(group.groupName);
    }
    if (codes.length < 3) {
      codes.push(String(group.id));
    }
    return codes;
  }, [group]);

  const evaluatePerson = useMemo(() => {
    const set = new Set(group.evaluationList?.map((it) => it.studentId));
    return {
      evaluatedNum: set.size,
      unEvaluatedNum: group?.studentNum ? group?.studentNum - set.size : 0
    };
  }, [group]);

  const scoreSummary = group?.evaluationList?.reduce(
    (acc, evaluation) => {
      if (evaluation.score! > 0) acc.positive += evaluation.score!;
      if (evaluation.score! < 0) acc.negative += evaluation.score!;
      return acc;
    },
    { positive: 0, negative: 0 }
  );

  return (
    <Box
      border="1px solid #E5E7EB"
      borderRadius={respDims(8, GroupBoxDms)}
      boxShadow="0px 2px 4px 0px rgba(75,86,115,0.07)"
      onClick={() => onClick?.(group)}
      cursor="pointer"
      {...props}
    >
      <Flex
        h={respDims(106, GroupBoxDms)}
        px={respDims(20, GroupBoxDms)}
        align="center"
        overflow="hidden"
        borderBottom="1px solid #E5E7EB"
      >
        <Flex align="center" w={respDims(56 * 2, GroupBoxDms)}>
          {avatarCodes.map((code, index) => (
            <StudentAvatar
              key={index}
              code={code}
              w={respDims(56, GroupBoxDms)}
              h={respDims(56, GroupBoxDms)}
              pos="relative"
              left={index > 0 ? respDims(-28 * index, GroupBoxDms) : 0}
              bgColor="primary.50"
              border="1px solid #ADC8FF"
              zIndex={3 - index}
            />
          ))}
        </Flex>

        <Box flex="1" ml={respDims(8, GroupBoxDms)}>
          <Box
            color="#000000"
            fontSize={respDims(16, GroupBoxDms)}
            fontWeight="bold"
            lineHeight={respDims(24, GroupBoxDms)}
          >
            {group.groupName}
          </Box>

          <Box
            mt={respDims(8, GroupBoxDms)}
            color="#606266"
            fontSize={respDims(15, GroupBoxDms)}
            lineHeight={respDims(20, GroupBoxDms)}
          >
            组员：{group.studentNum}人
          </Box>
        </Box>
      </Flex>

      <Flex
        align="center"
        h={respDims(68, GroupBoxDms)}
        color="#606266"
        fontSize={respDims(14, GroupBoxDms)}
      >
        {entryType === EvaluateEntryEnum.ClassroomPerformance ? (
          <>
            <Center flex="1 0 0">
              <SvgIcon
                name="emojiSmile"
                w={respDims(28, GroupBoxDms)}
                h={respDims(28, GroupBoxDms)}
              />
              <Box ml={respDims(6, GroupBoxDms)}>{scoreSummary?.positive || 0}</Box>
            </Center>

            <Box w="1px" bgColor="#E5E7EB" h={respDims(22, GroupBoxDms)} />

            <Center flex="1 0 0">
              <SvgIcon
                name="emojiAngry"
                w={respDims(28, GroupBoxDms)}
                h={respDims(28, GroupBoxDms)}
              />
              <Box ml={respDims(6, GroupBoxDms)}>{scoreSummary?.negative || 0}</Box>
            </Center>
          </>
        ) : (
          <>
            <Center flex="1 0 0">
              <Text>已评价</Text>
              <Box color="#71D4FF" mx={respDims(6, GroupBoxDms)}>
                {evaluatePerson.evaluatedNum || 0}
              </Box>
              人
            </Center>

            <Box w="1px" bgColor="#E5E7EB" h={respDims(22, GroupBoxDms)} />

            <Center flex="1 0 0">
              <Text>未评价</Text>
              <Box color="#FF8471" mx={respDims(6, GroupBoxDms)}>
                {evaluatePerson.unEvaluatedNum || 0}
              </Box>
              人
            </Center>
          </>
        )}
      </Flex>
    </Box>
  );
};

export default Group;
