import { useMemo } from 'react';
import { Box, ChakraProps, Flex, useBreakpointValue } from '@chakra-ui/react';
import { respDims, rpxDim } from '@/utils/chakra';
import Student from '../Student';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import SvgIcon from '@/components/SvgIcon';

const StudentList = ({
  type = 'normal',
  students,
  isCheckable,
  checkedIds,
  columnCount,
  entryType,
  onCheckChange,
  onClickStudent,
  ...props
}: {
  type?: 'normal' | 'evaluate';
  students: EvaluateStudentType[];
  isCheckable?: boolean;
  checkedIds?: string[];
  entryType: EvaluateEntryEnum;
  columnCount?: number;
  onCheckChange?: (checkedIds: string[]) => void;
  onClickStudent?: (student: EvaluateStudentType) => void;
} & ChakraProps) => {
  const breakpointColumnCount =
    useBreakpointValue({
      base: 5,
      sm: 6,
      md: 7,
      lg: 8,
      xl: 9
    }) || 8;

  const realColumnCount = columnCount || breakpointColumnCount;

  const rows = useMemo(() => {
    const rows: (EvaluateStudentType | undefined)[][] = [];
    for (let i = 0; i < students.length; i += realColumnCount) {
      rows.push(students.slice(i, i + realColumnCount));
    }
    const fillCount = realColumnCount - (students.length % realColumnCount);
    if (fillCount !== realColumnCount) {
      for (let i = 0; i < fillCount; i++) {
        rows[rows.length - 1].push(undefined);
      }
    }
    return rows;
  }, [students, realColumnCount]);

  const handleCheckChange = ({ studentId: id }: EvaluateStudentType, isChecked: boolean) => {
    const ids = isChecked
      ? [...(checkedIds || []), id]
      : checkedIds?.filter((it) => it !== id) || [];
    onCheckChange?.(ids);
  };

  return (
    <Box w="100%" h="100%" {...props}>
      {rows.length > 0 ? (
        rows.map((row, rowIndex) => (
          <Flex key={rowIndex} justify="space-between" mb={respDims(24)}>
            {row.map((student, colIndex) =>
              student ? (
                <Student.Hover
                  key={student.studentId}
                  type={type}
                  entryType={entryType}
                  student={student}
                  isCheckable={isCheckable}
                  isClickCheck={true}
                  isChecked={!!checkedIds?.includes(student.studentId)}
                  ml={colIndex === 0 ? respDims(24) : 0}
                  mr={colIndex === realColumnCount - 1 ? respDims(24) : 0}
                  onCheckChange={handleCheckChange}
                  onClick={onClickStudent}
                />
              ) : (
                <Box
                  key={`col-${colIndex}`}
                  w={Student.HoverBoxW}
                  h="1px"
                  mr={colIndex === realColumnCount - 1 ? respDims(24) : 0}
                />
              )
            )}
          </Flex>
        ))
      ) : (
        <Flex flexDir="column" justify="center" align="center" w="100%" h="100%">
          <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
          <Box> 暂无数据 </Box>
        </Flex>
      )}
    </Box>
  );
};

export default StudentList;
