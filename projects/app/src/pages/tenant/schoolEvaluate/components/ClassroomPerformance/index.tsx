import { serviceSideProps } from '@/utils/i18n';
import { useMemo, useRef, useState } from 'react';
import { Box, BoxProps, Center, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import MyMenu from '@/components/MyMenu';
import Students, { StudentsRef } from './components/Students';
import MainTabs from '../MainTabs';
import { getClazzIndactorList, getEvaluateSubjectList } from '@/api/tenant/evaluate/process';
import { useQuery } from '@tanstack/react-query';
import { deserializeData } from '@/utils/tools';
import styles from '@/pages/index.module.scss';
import { Select } from 'antd';
import { EvaluateSubjectType } from '@/types/api/kanban';

const studentViewTypes = [
  {
    label: '按学号顺序排列',
    value: 'code'
  },
  {
    label: '按座位排列',
    value: 'seat'
  }
];

const ClassroomPerformance = ({
  isSubjectEntry = false,
  gradeId,
  clazzId,
  menuId,
  semesterId,
  clazzName,
  ruleId,
  isClazzTeacher
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  menuId: string;
  semesterId: string;
  clazzName: string;
  isSubjectEntry?: boolean;
  ruleId: string;
} & BoxProps) => {
  const [studentViewType, setStudentViewType] = useState('code' as 'code' | 'seat');

  const studentsRef = useRef<StudentsRef>(null);
  const [subjectId, setSubjectId] = useState('');
  const [subjects, setSubjects] = useState<EvaluateSubjectType[]>([]);

  const queryKey = useMemo(
    () => ['subjects', gradeId, clazzId, ruleId, menuId, semesterId],
    [gradeId, clazzId, ruleId, menuId, semesterId]
  );
  const { data: fetchedSubjects = [], refetch: refetchSubjects } = useQuery(
    queryKey,
    () => getEvaluateSubjectList({ deptId: clazzId, ruleId: ruleId || '', menuId, semesterId }),
    {
      enabled: !!(clazzId && ruleId && menuId && semesterId),
      onSuccess: (data) => {
        setSubjects(data || []);
        setSubjectId(data?.[0]?.id || '');
      }
    }
  );

  const { data: indactors = [], refetch: refetchIndactors } = useQuery(
    ['indactors', clazzId, subjectId, ruleId, menuId],
    () =>
      getClazzIndactorList({
        deptId: clazzId,
        ruleId: ruleId! || '',
        semesterId,
        menuId
      }),
    {
      enabled: isSubjectEntry
        ? !!(clazzId && ruleId && semesterId && menuId && subjectId)
        : !!(menuId && clazzId && ruleId && semesterId)
    }
  );

  return (
    <Flex direction="column" w="100%" h="80vh" pt={respDims(24)} bgColor="#ffffff">
      <Flex align="center" mx={respDims(24)}>
        {
          <>
            <Flex align="center" flex="1">
              <MainTabs
                value={subjectId}
                list={subjects}
                labelKey="name"
                valueKey="id"
                onChange={setSubjectId}
              />
            </Flex>

            {studentViewType === 'seat' && isClazzTeacher && (
              <Box ml={respDims(16)}>
                <MyMenu
                  placement="left-start"
                  Button={
                    <Center
                      w={respDims('34fpx')}
                      h={respDims('34fpx')}
                      bgColor="rgba(0,0,0,0.03)"
                      borderRadius="50%"
                      cursor="pointer"
                    >
                      <SvgIcon name="settings" />
                    </Center>
                  }
                  menuList={[
                    {
                      label: '调整学生座位',
                      onClick: () => studentsRef.current?.showAdjustSeats()
                    },
                    {
                      label: '快速新增座位',
                      onClick: () => studentsRef.current?.showAddSeats()
                    }
                  ]}
                />
              </Box>
            )}

            <Box
              ml={respDims(16)}
              className={styles['my-form']}
              flex="0 0 auto"
              w={respDims(247)}
              pos="relative"
            >
              <Select
                value={studentViewType}
                style={{ width: '100%', border: 'none', borderRadius: '10px' }}
                onChange={setStudentViewType}
              >
                {studentViewTypes.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </Box>
          </>
        }
      </Flex>

      <Box flex="1" pt={respDims(16)} overflow="hidden" pos="relative">
        <Students
          ref={studentsRef}
          flex="1 0 0"
          viewType={studentViewType}
          isClazzTeacher={isClazzTeacher}
          gradeId={gradeId}
          clazzId={clazzId}
          clazzName={clazzName}
          subjectId={subjectId}
          menuId={menuId}
          ruleId={ruleId}
          semesterId={semesterId}
          subjectName={subjects.find((it) => it.id === subjectId)?.name}
          indactors={indactors}
        />
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default ClassroomPerformance;
