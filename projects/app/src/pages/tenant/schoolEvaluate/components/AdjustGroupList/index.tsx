import { Box, Center, ChakraProps, Flex, Input } from '@chakra-ui/react';
import { EvaluateGroupType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import SvgIcon from '@/components/SvgIcon';
import styles from '@/styles/variable.module.scss';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  useDraggable,
  useDroppable
} from '@dnd-kit/core';
import StudentAvatar from '../StudentAvatar';
import { Toast } from '@/utils/ui/toast';
import MyTooltip from '@/components/MyTooltip';

export type GroupListRef = {
  addGroup: (count: number) => void;
};

const GroupBoxDms = '.8ms';

const AdjustGroupList = (
  {
    groups,
    onChange,
    ...props
  }: {
    groups: EvaluateGroupType[];
    onChange?: (groups: EvaluateGroupType[]) => void;
  } & ChakraProps,
  ref: ForwardedRef<GroupListRef>
) => {
  const [innerGroups, setInnerGroups] = useState<EvaluateGroupType[]>(groups);

  const [draggingStudent, setDraggingStudent] = useState<EvaluateStudentType>();

  const listRef = useRef<HTMLDivElement>(null);

  const handleDragStart = (e: DragStartEvent) => {
    setDraggingStudent(e.active.data.current as EvaluateStudentType);
  };

  const handleDragEnd = (e: DragEndEvent) => {
    setDraggingStudent(undefined);
    const { student, group: activeGroup } = (e.active.data.current || {}) as {
      student: EvaluateStudentType;
      group: EvaluateGroupType;
    };
    const overGroup = e.over?.data?.current as EvaluateGroupType;
    if (!student || !activeGroup || !overGroup || activeGroup === overGroup) {
      return;
    }
    const groups = innerGroups.map((group) => {
      if (group === activeGroup) {
        return {
          ...group,
          studentList: group.studentList!.filter((it) => it.studentId !== student.studentId)
        };
      } else if (group === overGroup) {
        return { ...group, studentList: [...group.studentList!, student] };
      }
      return group;
    });
    setInnerGroups(groups);
    onChange?.(groups);
  };

  const handleGroupChange = (group: EvaluateGroupType) => {
    const groups = innerGroups.map((it) => (it.id === group.id ? group : it));
    setInnerGroups(groups);
    onChange?.(groups);
  };

  const addGroup = (count: number) => {
    const groups: EvaluateGroupType[] = [...innerGroups];
    for (let i = 0; i < count; i++) {
      groups.push({
        id: 'new' + (i + groups.length),
        groupName: `第${groups.length + 1}组`,
        studentList: []
      });
    }
    setInnerGroups(groups);
    onChange?.(groups);
  };

  const removeGroup = (groupIndex: number) => {
    if (innerGroups[groupIndex].studentList?.length) {
      Toast.error('该组有学生，无法删除');
      return;
    }
    const groups = innerGroups.filter((it, index) => index !== groupIndex);
    setInnerGroups(groups);
    onChange?.(groups);
  };

  useImperativeHandle(ref, () => ({
    addGroup
  }));

  useEffect(() => {
    setInnerGroups(groups);
  }, [groups]);

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragCancel={() => setDraggingStudent(undefined)}
    >
      <Flex align="center" overflow="hidden" {...props}>
        <Center
          flexShrink="0"
          w={respDims(40, GroupBoxDms)}
          h={respDims(40, GroupBoxDms)}
          border="1px solid #E4E7ED"
          borderRadius="50%"
          cursor="pointer"
          onClick={() => {
            if (listRef.current) {
              listRef.current.scrollLeft = Math.max(
                0,
                listRef.current.scrollLeft - listRef.current.clientWidth
              );
            }
          }}
        >
          <SvgIcon name="chevronLeft" w={respDims(28, GroupBoxDms)} h={respDims(28, GroupBoxDms)} />
        </Center>

        <Flex
          ref={listRef}
          flex="1 0 0"
          alignSelf="stretch"
          mx={respDims(16, GroupBoxDms)}
          overflowX="scroll"
        >
          {innerGroups.map((group, index) => (
            <Group
              key={group.id || `group-${index}`}
              group={group}
              groupIndex={index}
              alignSelf="stretch"
              flex="0 0 auto"
              ml={index > 0 ? respDims(16, GroupBoxDms) : 0}
              onRemove={() => removeGroup(index)}
              onChange={handleGroupChange}
            />
          ))}
        </Flex>

        <Center
          ml="auto"
          w={respDims(40, GroupBoxDms)}
          h={respDims(40, GroupBoxDms)}
          border="1px solid #E4E7ED"
          borderRadius="50%"
          cursor="pointer"
          onClick={() => {
            if (listRef.current) {
              listRef.current.scrollLeft = Math.min(
                listRef.current.scrollWidth,
                listRef.current.scrollLeft + listRef.current.clientWidth
              );
            }
          }}
        >
          <SvgIcon
            name="chevronRight"
            w={respDims(28, GroupBoxDms)}
            h={respDims(28, GroupBoxDms)}
          />
        </Center>
      </Flex>

      <DragOverlay dropAnimation={null}>
        {draggingStudent && <Student student={draggingStudent} />}
      </DragOverlay>
    </DndContext>
  );
};

const Group = ({
  group,
  groupIndex,
  onRemove,
  onChange,
  ...props
}: {
  group: EvaluateGroupType;
  groupIndex: number;
  onRemove?: () => void;
  onChange?: (group: EvaluateGroupType) => void;
} & ChakraProps) => {
  const [editingName, setEditingName] = useState<string>();

  const { isOver, setNodeRef: setDropRef } = useDroppable({
    id: groupIndex,
    data: group
  });

  return (
    <Flex
      flexDir="column"
      w={respDims(250, GroupBoxDms)}
      borderRadius={respDims(14, GroupBoxDms)}
      bgColor={isOver ? 'primary.50' : '#F8FAFC'}
      overflow="hidden"
      {...props}
    >
      <Flex
        h={respDims(48, GroupBoxDms)}
        px={respDims(10, GroupBoxDms)}
        align="center"
        bgColor="#E1EBF8"
        overflow="hidden"
      >
        {editingName !== undefined ? (
          <>
            <Input
              flex="1"
              h={respDims(32, GroupBoxDms)}
              bgColor="#ffffff"
              border="1px solid #D6E4FF"
              borderRadius={respDims(8, GroupBoxDms)}
              outline="none"
              boxShadow="none"
              _hover={{
                border: '1px solid #D6E4FF',
                boxShadow: 'none'
              }}
              _focus={{
                border: '1px solid #D6E4FF',
                boxShadow: 'none'
              }}
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
            />
            <Box
              ml={respDims(16, GroupBoxDms)}
              color="#0052D9"
              fontSize={respDims(14, GroupBoxDms)}
              lineHeight={respDims(22, GroupBoxDms)}
              cursor="pointer"
              onClick={() => {
                const name = editingName.trim();
                if (!name) {
                  return;
                }
                name !== group.groupName && onChange?.({ ...group, groupName: name });
                setEditingName(undefined);
              }}
            >
              完成
            </Box>
            <Box
              ml={respDims(16, GroupBoxDms)}
              color="#909399"
              fontSize={respDims(14, GroupBoxDms)}
              lineHeight={respDims(22, GroupBoxDms)}
              cursor="pointer"
              onClick={() => setEditingName(undefined)}
            >
              取消
            </Box>
          </>
        ) : (
          <>
            <Box flex="1" overflow="hidden" textOverflow="ellipsis">
              {group.groupName}
            </Box>

            <SvgIcon
              ml={respDims(16, GroupBoxDms)}
              name="editLine"
              w={respDims(16, GroupBoxDms)}
              h={respDims(16, GroupBoxDms)}
              cursor="pointer"
              onClick={() => setEditingName(group.groupName)}
            />

            <SvgIcon
              ml={respDims(16, GroupBoxDms)}
              name="trash"
              w={respDims(16, GroupBoxDms)}
              h={respDims(16, GroupBoxDms)}
              cursor="pointer"
              onClick={() => onRemove?.()}
            />
          </>
        )}
      </Flex>

      <Box
        ref={setDropRef}
        flex="1"
        mt={respDims(5, GroupBoxDms)}
        py={respDims(10, GroupBoxDms)}
        overflow="hidden"
      >
        <Flex
          h="100%"
          flexWrap="wrap"
          alignContent="flex-start"
          px={respDims(10, GroupBoxDms)}
          py={respDims(4, GroupBoxDms)}
          overflowY="scroll"
          mr={[`-${styles.scrollbarSmWidth}`, `-${styles.scrollbarWidth}`]}
        >
          {group?.studentList?.map((student, index) => (
            <Student
              key={student.studentId}
              flex="0 0 auto"
              student={student}
              group={group}
              ml={index % 2 === 0 ? 0 : respDims(6, GroupBoxDms)}
              mt={index > 1 ? respDims(10, GroupBoxDms) : 0}
            />
          ))}
        </Flex>
      </Box>
    </Flex>
  );
};

const Student = ({
  student,
  group,
  ...props
}: { student: EvaluateStudentType; group?: EvaluateGroupType } & ChakraProps) => {
  const {
    isDragging,
    attributes,
    listeners,
    setNodeRef: setDragRef
  } = useDraggable({
    id: student.studentId,
    data: { student, group }
  });

  return (
    <Flex
      ref={setDragRef}
      flexDir="column"
      justify="center"
      align="center"
      w={respDims(112, GroupBoxDms)}
      h={respDims(98, GroupBoxDms)}
      bgColor="#FFFFFF"
      borderRadius={respDims(14, GroupBoxDms)}
      opacity={isDragging ? 0.5 : undefined}
      {...attributes}
      {...listeners}
      {...props}
    >
      <StudentAvatar
        student={student}
        w={respDims(40, GroupBoxDms)}
        h={respDims(40, GroupBoxDms)}
        borderRadius="50%"
      />

      <MyTooltip
        label={
          student.studentCode
            ? `${student.studentName}(${student.studentCode.substring(student.studentCode.length - 2)})`
            : student.studentName
        }
      >
        <Box
          mt={respDims(6, GroupBoxDms)}
          color="#303133"
          fontSize={respDims(14, GroupBoxDms)}
          fontWeight="bold"
          textAlign="center"
          style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: '1',
            whiteSpace: 'normal'
          }}
          lineHeight={respDims(22, GroupBoxDms)}
        >
          {student.studentCode
            ? `${student.studentName}(${student.studentCode.substring(student.studentCode.length - 2)})`
            : student.studentName}
        </Box>
      </MyTooltip>
    </Flex>
  );
};

export default forwardRef(AdjustGroupList);
