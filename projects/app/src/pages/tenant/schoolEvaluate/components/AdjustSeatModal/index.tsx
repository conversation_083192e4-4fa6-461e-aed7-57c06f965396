import MyModal from '@/components/MyModal';
import SeatList, { SeatListRef, SeatType } from '../SeatList';
import { Box, Button, Flex } from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import MyMenu from '@/components/MyMenu';
import { useRef, useState } from 'react';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { submitSeats } from '@/api/tenant/evaluate/process';
import { EvaluateEntryEnum } from '@/constants/evaluate';

const AdjustSeatModal = ({
  clazzId,
  semesterId,
  students,
  rowCount,
  columnCount,
  incRowCount,
  incColumnCount,
  onSuccess,
  onClose
}: {
  clazzId: string;
  semesterId: string;
  students: EvaluateStudentType[];
  rowCount?: number;
  columnCount?: number;
  incRowCount?: number;
  incColumnCount?: number;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const seatListRef = useRef<SeatListRef>(null);
  const [seats, setSeats] = useState<SeatType[][]>([]);
  const changedRef = useRef<Record<string, EvaluateStudentType>>({});
  const allStudentsRef = useRef<EvaluateStudentType[]>([]);
  const onSubmit = () => {
    const students = Object.values(changedRef.current);

    if (!students.length && seats.length == rowCount && seats[0].length == columnCount) {
      onClose?.();
      return;
    }

    submitSeats({
      clazzId,
      semesterId,
      rowNum: seats.length,
      colNum: seats[0].length,
      clazzSeats: allStudentsRef.current.map((student) => ({
        id: student.seatId,
        studentId: student.studentId,
        rowNo: student.rowNo!,
        colNo: student.colNo!
      }))
    }).then(() => {
      onSuccess?.();
      onClose?.();
    });
  };

  return (
    <MyModal
      isOpen
      isCentered
      onClose={onClose}
      maxW="96vw"
      maxH="96vh"
      hideCloseButton
      closeOnOverlayClick={false}
    >
      <Flex flexDir="column" minW="80vw" minH="80vh" pt="16px" overflow="hidden">
        <Box alignSelf="center" color="#000000" fontSize="18px" fontWeight="bold" lineHeight="32px">
          学生座位设置
        </Box>
        <Box alignSelf="center" my="8px" color="#606266" fontSize="18px" lineHeight="32px">
          可拖动学生名称到对应座位
        </Box>

        <SeatList
          ref={seatListRef}
          flex="1"
          mx="24px"
          type="adjust"
          entryType={EvaluateEntryEnum.ClassroomPerformance}
          students={students}
          rowCount={rowCount}
          columnCount={columnCount}
          incRowCount={incRowCount}
          incColumnCount={incColumnCount}
          onSeatChange={(
            students: EvaluateStudentType[],
            allStudents: EvaluateStudentType[],
            seats: SeatType[][]
          ) => {
            students.forEach((student) => {
              changedRef.current[student.studentId] = student;
            });
            allStudentsRef.current = allStudents;
            setSeats(seats);
          }}
        />

        <Flex align="center" mt="10px" px="24px" py="21px" borderTop="1px solid #E5E7EB">
          <MyMenu
            placement="right-end"
            Button={
              <Button variant="grayBase" w="150px" h="36px" borderRadius="150px">
                <AddIcon />
                <Box ml="10px">添加</Box>
              </Button>
            }
            menuList={[
              {
                label: '添加排',
                onClick: () => seatListRef.current?.addRowCount(1)
              },
              { label: '添加列', onClick: () => seatListRef.current?.addColumnCount(1) }
            ]}
          />

          <Button
            ml="auto"
            variant="grayBase"
            w="150px"
            h="36px"
            borderRadius="150px"
            onClick={onClose}
          >
            取消
          </Button>

          <Button ml="16px" mr="auto" w="150px" h="36px" borderRadius="150px" onClick={onSubmit}>
            确定
          </Button>

          <Box w="150px"></Box>
        </Flex>
      </Flex>
    </MyModal>
  );
};

export default AdjustSeatModal;
