import { EvaluateGroupType } from '@/types/api/tenant/evaluate/process';
import { Box, Grid } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import Group from '../Group';
import { respDims } from '@/utils/chakra';
import { EvaluateEntryEnum } from '@/constants/evaluate';

const GroupList = ({
  groups,
  entryType,
  onClickGroup,
  ...props
}: {
  groups: EvaluateGroupType[];
  entryType: EvaluateEntryEnum;
  onClickGroup?: (group: EvaluateGroupType) => void;
} & ChakraProps) => {
  return (
    <Box {...props}>
      <Grid
        gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
        gridGap={respDims(16)}
      >
        {groups.map((group) => (
          <Group key={group.id} group={group} onClick={onClickGroup} entryType={entryType} />
        ))}
      </Grid>
    </Box>
  );
};

export default GroupList;
