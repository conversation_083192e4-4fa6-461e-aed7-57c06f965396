import { Flex, HStack } from '@chakra-ui/react';
import SubTabs from '../components/SubTabs';
import { useMemo, useState } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import { respDims } from '@/utils/chakra';
import SubjectSelect from '../components/SubjectSelect';
import Breadcrumb from '../components/Breadcrumb';
import { EvaluateSubjectType } from '@/types/api/tenant/evaluate/process';
import { deserializeData } from '@/utils/tools';
import ClazzPanel from '../../achievement/record/components/ClazzPanel';
import StudentPanel from '../../culturalKnowledge/classroomPerformanceRecord/components/StudentPanel';

const tabList = [
  {
    label: '班级作业',
    value: 'clazz'
  },
  {
    label: '学生作业',
    value: 'student'
  }
];

const HomeworkRecord = ({
  gradeId,
  menuId,
  semesterId,
  ruleId,
  clazzId,
  clazzName,
  tab = 'clazz'
}: {
  gradeId: string;
  menuId: string;
  semesterId: string;
  ruleId: string;
  clazzId: string;
  clazzName: string;
  tab?: string;
}) => {
  const [activeTab, setActiveTab] = useState(tab);

  const [subject, setSubject] = useState<EvaluateSubjectType>();

  const breadcrumbList = useMemo(
    () => [
      { label: '班级列表' },
      { label: clazzName },
      { label: tabList.find((item) => item.value === activeTab)?.label! }
    ],
    [clazzName, activeTab]
  );

  return (
    <Flex
      flexDir="column"
      w="100%"
      h="100%"
      bgColor="#ffffff"
      p={respDims(24)}
      borderRadius={respDims(20)}
    >
      <Breadcrumb list={breadcrumbList} mb={respDims(18)} />

      <HStack spacing={respDims(16)} mb={respDims(20)}>
        <SubTabs alignSelf="flex-start" list={tabList} value={activeTab} onChange={setActiveTab} />
        <SubjectSelect clazzId={clazzId} value={subject} onChange={setSubject} hasAll={false} />
      </HStack>
      {activeTab === 'clazz' && (
        <ClazzPanel
          flex="1 0 0"
          clazzName={clazzName}
          gradeId={gradeId}
          menuId={menuId}
          semesterId={semesterId}
          ruleId={ruleId}
          clazzId={clazzId}
          subjectId={subject?.id}
        />
      )}
      {activeTab === 'student' && (
        <StudentPanel
          flex="1 0 0"
          clazzName={clazzName}
          gradeId={gradeId}
          menuId={menuId}
          semesterId={semesterId}
          ruleId={ruleId}
          clazzId={clazzId}
          subjectId={subject?.id}
        />
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default HomeworkRecord;
