import { getEvaluateSubjectList } from '@/api/tenant/evaluate/process';
import MySelect from '@/components/MySelect';
import { EvaluateSubjectType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Center } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

const SubjectSelect = ({
  clazzId,
  value,
  onChange,
  hasAll,
  ...props
}: {
  clazzId: string;
  value?: EvaluateSubjectType;
  hasAll?: boolean;
  onChange?: (value?: EvaluateSubjectType) => void;
} & ChakraProps) => {
  const { data } = useQuery(['subjects'], () => getEvaluateSubjectList({ deptId: clazzId }));

  const subjects = useMemo(() => {
    const subjects = data?.map((it) => ({ label: it.name, value: it.id })) || [];
    if (hasAll) {
      subjects.push({ label: '全部学科', value: 'all' });
    }
    if (data && data[0]) {
      !value?.id && onChange?.(data[0]);
    }
    return subjects;
  }, [data, hasAll]);

  return (
    <Center h={respDims('36fpx')} overflow="hidden" borderRadius={respDims(8)} {...props}>
      <MySelect
        list={subjects}
        value={value?.id || 'all'}
        border="none"
        bgColor="rgba(0,0,0,0.03)"
        minW={respDims('120fpx')}
        placeholder="请选择学科"
        borderRadius="50px"
        h={respDims('36fpx')}
        onchange={(e) => onChange?.(e === 'all' ? undefined : data?.find((it) => it.id === e))}
      />
    </Center>
  );
};

export default SubjectSelect;
