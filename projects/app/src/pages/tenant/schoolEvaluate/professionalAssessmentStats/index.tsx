import { serviceSideProps } from '@/utils/i18n';
import { Box, Button, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useRoutes } from '@/hooks/useRoutes';
import { useSystemStore } from '@/store/useSystemStore';
import { useMemo, useRef, useState, useEffect } from 'react';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { TableProps } from 'antd';
import SvgIcon from '@/components/SvgIcon';
import MyTooltip from '@/components/MyTooltip';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { getTeacherStatisticsPage } from '@/api/tenant/evaluate/process';
import { treeToList } from '@/utils/tree';
import { TeacherStatisticsType } from '@/types/api/tenant/evaluate/process';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';
import { EvaluateFilterValue } from '../professionalAssessmentEvaluation';
import { FilterForm } from './components/FilterForm';
const ProfessionalAssessmentStats = () => {
  const { currentSemester, entryTree, setEntryTree } = useSystemStore();
  const tableRef = useRef<MyTableRef>(null);
  const { routeGroup } = useRoutes();
  const router = useRouter();

  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: currentSemester?.id,
    periodType: undefined,
    ruleId: undefined,
    evaluatedName: undefined
  });
  console.log(filterForm, 'filterForm');

  const { data: entranceTreeData } = useQuery(['entranceTree'], setEntryTree, {
    staleTime: 1000 * 60 * 5 // 5分钟缓存
  });

  console.log(entryTree, 'entryTree');

  const reflectionId = useMemo(() => {
    const info = treeToList(entryTree).find(
      (item) =>
        item?.name ==
        evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name
    );
    return info?.id?.toString();
  }, [entryTree]);

  const columns: TableProps<TeacherStatisticsType>['columns'] = useMemo(() => {
    return [
      {
        title: '被评价教师',
        key: 'evaluatedName',
        dataIndex: 'evaluatedName',
        render: (_: any, record: TeacherStatisticsType) => {
          return (
            <Box>
              {record.evaluatedName + (record.subjectName ? `(${record.subjectName})` : '')}
            </Box>
          );
        }
      },
      {
        title: '参与评价人数量',
        key: 'evaluatorCount',
        dataIndex: 'evaluatorCount',
        render: (text: number) => {
          return <Box>{text >= 0 && text !== null ? text : '-'}</Box>;
        }
      },
      {
        title: '最高分',
        key: 'maxScore',
        dataIndex: 'maxScore',
        render: (text: number) => {
          return <Box>{text >= 0 && text !== null ? text : '-'}</Box>;
        }
      },
      {
        title: '最低分',
        key: 'minScore',
        dataIndex: 'minScore',
        render: (text: number) => {
          return <Box>{text >= 0 && text !== null ? text : '-'}</Box>;
        }
      },
      {
        title: (
          <Flex alignItems="center">
            <Box mr="9px">综合得分</Box>
            <MyTooltip
              label={
                <Flex direction="column" p="15px 10px 15px 15px">
                  <Box
                    fontSize="17px"
                    pb="14px"
                    fontWeight="500"
                    color="#000"
                    borderBottom="1px solid #E5E7EB"
                  >
                    综合得分计算公式
                  </Box>
                  <Flex direction="column" mt="20px">
                    <Flex alignItems="center">
                      <Box w="18px" h="10px" mr="12px" bg="#165DFF" borderRadius="50%" />
                      <Box
                        color="#4E5969"
                        ml="8px"
                        fontSize="14px"
                        fontWeight="400"
                        lineHeight="22px"
                      >
                        综合得分=（参与评价人得分之和-最高分-最低分）/（参与评价人-2）
                      </Box>
                    </Flex>
                    <Flex alignItems="center" mt="16px">
                      <Box w="15px" h="10px" mr="12px" bg="#165DFF" borderRadius="50%" />
                      <Box
                        color="#4E5969"
                        ml="8px"
                        fontSize="14px"
                        fontWeight="400"
                        lineHeight="22px"
                      >
                        参与评价人得分=（评价指标A 数量/总评价指标数量）* 100%
                      </Box>
                    </Flex>
                  </Flex>
                </Flex>
              }
              bgColor="#fff"
              placement="top"
            >
              <SvgIcon name="file2Query" w="14px" h="14px" />
            </MyTooltip>
          </Flex>
        ),
        key: 'score',
        dataIndex: 'score',
        defaultSortOrder: 'descend',
        showSorterTooltip: false,
        sorter: {
          compare: (a, b) => {
            const scoreA = a.score === null || a.score === undefined ? -Infinity : Number(a.score);
            const scoreB = b.score === null || b.score === undefined ? -Infinity : Number(b.score);
            return scoreA - scoreB;
          },
          multiple: 1
        },
        render: (text: number) => {
          return <Box>{text >= 0 && text !== null ? text : '-'}</Box>;
        }
      },
      {
        title: '查看明细',
        key: 'action',
        width: 220,
        render: (_: any, row: TeacherStatisticsType) => (
          <Flex>
            {row.evaluatorCount && row.evaluatorCount > 0 && row.evaluatorCount !== null ? (
              <Button
                color="#0052D9"
                variant="link"
                onClick={() =>
                  router.push({
                    pathname: `/tenant/teacherEvaluate/professionalAssessmentStats/detail`,
                    query: {
                      evaluatedId: row.evaluatedId,
                      teacherName: row.evaluatedName,
                      semester: filterForm.semester,
                      periodType: filterForm.periodType,
                      ruleId: filterForm.ruleId
                    }
                  })
                }
              >
                查看详情
              </Button>
            ) : (
              <Box>-</Box>
            )}
          </Flex>
        )
      }
    ];
  }, [filterForm]);

  return (
    <Flex
      alignItems="center"
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      border="1px solid #FFF"
      direction="column"
      h="100%"
      backdropFilter={'blur(3.700000047683716px)'}
      borderTopLeftRadius={respDims(20)}
      w="100%"
      overflow="hidden"
    >
      <Flex
        pl={respDims(32)}
        pr={respDims(32)}
        w="100%"
        alignItems="center"
        mt={respDims(23)}
        justifyContent="space-between"
      >
        <Flex alignItems="center">
          <Box w="7px" h="14px" bg="#175DFF" borderRadius="8px" />
          <Box
            color="rgba(0, 0, 0, 0.90)"
            fontFamily="PingFang SC"
            fontSize="16px"
            fontStyle="normal"
            fontWeight="500"
            lineHeight="22px"
            ml="6px"
          >
            师德和专业考核评价统计
          </Box>
        </Flex>

        <FilterForm
          value={filterForm}
          onChange={setFilterForm}
          menuId={routeGroup?.authActiveRoute?.id}
          reflectionId={reflectionId}
          projectType="teacherStats"
        />
      </Flex>

      <Flex bgColor="#fff" w="100%" h="100%" overflow="auto" mt={respDims(23)}>
        <MyTable
          columns={columns}
          api={getTeacherStatisticsPage}
          size="middle"
          rowKey={(record) => `${record.evaluatedId}_${record.score}`}
          defaultQuery={{
            ruleId: filterForm.ruleId || '',
            semesterId: filterForm.semester || '',
            menuId: routeGroup?.authActiveRoute?.id!,
            evaluatedName: filterForm.evaluatedName || ''
          }}
          queryConfig={{
            enabled: !!filterForm.semester && !!filterForm.periodType && !!filterForm.ruleId
          }}
          ref={tableRef}
          headerConfig={{
            showIfEmpty: true,
            showHeader: false
          }}
          onChange={(pagination, filters, sorter) => {
            if (!sorter) {
              tableRef.current?.reload();
            }
          }}
        />
      </Flex>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default ProfessionalAssessmentStats;
