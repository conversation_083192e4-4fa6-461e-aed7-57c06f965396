import { serviceSideProps } from '@/utils/i18n';
import { deserializeData, serializeData } from '@/utils/tools';
import EvaluateDetail from '../../components/EvaluateDetail';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import { EvaluateProvider, FooterRouterMenuProps } from '../../components/EvaluateContext';
import SvgIcon from '@/components/SvgIcon';
import { Box, Center, Flex, HStack } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import MyMenu from '@/components/MyMenu';
import { useCallback, useEffect, useState } from 'react';
import EvaluateFilterSelect, { EvaluateFilterValue } from '../../components/EvaluateFilterSelect';
import Breadcrumb from '../../components/Breadcrumb';
import ClassroomPerformance from '../../components/ClassroomPerformance/index';

const tabList = [
  {
    label: '课堂表现',
    value: 'classroomPerformance',
    entry: EvaluateEntryEnum.ClassroomPerformance
  },
  {
    label: '作业评价',
    value: 'jobEvaluation',
    entry: EvaluateEntryEnum.HomeworkEvaluation
  },
  {
    label: '学期总评',
    value: 'semesterEvaluation',
    entry: EvaluateEntryEnum.SemesterReview
  }
];

const culturalKnowledge = (props: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  menuId: string;
  clazzName: string;
  subjectId?: string;
  semesterId: string;
  ruleId: string;
}) => {
  const [activeTab, setActiveTab] = useState('classroomPerformance');
  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: props.semesterId,
    periodType: undefined,
    ruleId: props.ruleId
  });

  useEffect(() => {
    if (props.semesterId !== filterForm.semester || props.ruleId !== filterForm.ruleId) {
      setFilterForm((prevForm) => ({
        ...prevForm,
        semester: props.semesterId || prevForm.semester,
        ruleId: props.ruleId || prevForm.ruleId
      }));
    }
  }, [props.semesterId, props.ruleId]);

  const createFooterRouterMenu = (path: string) =>
    useCallback(
      ({ clazz }: FooterRouterMenuProps) => {
        const router = useRouter();
        const { userInfo } = useUserStore();
        const isClazzTeacher = clazz.teachers?.some((it) => it.id == userInfo?.tmbId);
        const commonQuery = {
          isClazzTeacher,
          gradeId: clazz.parentId,
          clazzId: clazz.id,
          menuId: props.menuId,
          semesterId: filterForm.semester || props.semesterId,
          subjectId: props.subjectId || '0',
          ruleId: filterForm.ruleId || props.ruleId,
          clazzName: clazz.clazzName
        };

        return (
          <Box p="7px 20px" borderRadius="100px" bgColor="#F2F3F5" ml="12px" cursor="pointer">
            <MyMenu
              trigger="hover"
              offset={[-20, 20]}
              width={20}
              Button={
                <HStack
                  borderRadius={respDims(4)}
                  _hover={{ bgColor: '#f3f4f6' }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <Box fontSize="14px" fontWeight="400" lineHeight="20px">
                    查看评价数据
                  </Box>
                  <SvgIcon name="chevronDown" w={respDims(14)} h={respDims(14)} />
                </HStack>
              }
              menuList={[
                {
                  icon: <SvgIcon name="emojiClassPerformance" />,
                  label: '班级评价排行',
                  onClick: () => {
                    const serializedData = serializeData({
                      ...commonQuery,
                      tab: 'clazz',
                      ruleId: filterForm.ruleId || props.ruleId
                    });
                    router.push({
                      pathname: path,
                      query: {
                        q: serializedData
                      }
                    });
                  }
                },
                {
                  icon: <SvgIcon name="emojiStudentPerformance" />,
                  label: '学生评价数据',
                  onClick: () => {
                    const serializedData = serializeData({
                      ...commonQuery,
                      tab: 'student',
                      ruleId: filterForm.ruleId || props.ruleId
                    });
                    router.push({
                      pathname: path,
                      query: {
                        q: serializedData
                      }
                    });
                  }
                }
              ]}
            />
          </Box>
        );
      },
      [filterForm]
    );

  const FooterRouterMenu = createFooterRouterMenu(
    '/tenant/teacherEvaluate/culturalKnowledge/classroomPerformanceRecord'
  );
  const SemesterFooterRouterMenu = createFooterRouterMenu(
    '/tenant/teacherEvaluate/culturalKnowledge/overallEvaluationRecord'
  );

  return (
    <Flex
      backdropFilter={'blur(3.7px)'}
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      border="1px solid #FFF"
      direction="column"
      borderRadius={respDims(20)}
      w="100%"
      h="100%"
      overflow="auto"
    >
      <Flex justifyContent="space-between" mx={respDims(32)} mb={respDims(18)} mt="24px">
        <Breadcrumb list={[{ label: '班级列表' }, { label: props.clazzName }]} />
        <EvaluateFilterSelect
          menuId={props.menuId}
          clazzId={props.clazzId}
          value={filterForm}
          enterType={tabList.find((item) => item.value === activeTab)?.entry!}
          onChange={(value) => setFilterForm(value)}
        />
      </Flex>

      <HStack spacing={respDims(16)}>
        <Flex
          py={respDims(4)}
          boxSizing="border-box"
          align="center"
          borderRadius="10px 10px 0px 0px"
          h="48px"
          {...props}
        >
          {tabList.map((item) => (
            <Center
              flex="0 0 auto"
              key={item.value}
              minW={respDims(76)}
              px={respDims(12)}
              w="148px"
              h="48px"
              boxSizing="content-box"
              color={item.value === activeTab ? '#0A0A0A' : '#757575'}
              bgColor={item.value === activeTab ? '#ffffff' : '#F4F4F4'}
              fontWeight={item.value === activeTab ? 'bold' : '400'}
              borderRadius={item.value === activeTab ? '10px 10px 0px 0px' : '0px'}
              cursor="pointer"
              userSelect="none"
              onClick={() => item.value !== activeTab && setActiveTab(item.value)}
            >
              {item.label}
            </Center>
          ))}
        </Flex>
      </HStack>

      {(activeTab === 'classroomPerformance' || activeTab === 'semesterEvaluation') && (
        <EvaluateProvider
          enterType={
            activeTab === 'classroomPerformance'
              ? EvaluateEntryEnum.ClassroomPerformance
              : EvaluateEntryEnum.SemesterReview
          }
          FooterRouterMenu={
            activeTab === 'classroomPerformance' ? FooterRouterMenu : SemesterFooterRouterMenu
          }
          isSubjectEntry={true}
          {...props}
          semesterId={filterForm.semester || ''}
          ruleId={filterForm.ruleId || ''}
        >
          <EvaluateDetail isSubjectEntry={true} h="100vh" isCulturalKnowledge={true} {...props} />
        </EvaluateProvider>
      )}
      {activeTab === 'jobEvaluation' && (
        <EvaluateProvider
          enterType={EvaluateEntryEnum.HomeworkEvaluation}
          FooterRouterMenu={FooterRouterMenu}
          isSubjectEntry={true}
          {...props}
          semesterId={filterForm.semester || props.semesterId}
          ruleId={filterForm.ruleId || ''}
        >
          <ClassroomPerformance
            isSubjectEntry={true}
            {...props}
            ruleId={filterForm.ruleId || ''}
            semesterId={filterForm.semester || props.semesterId}
            isClazzTeacher={props.isClazzTeacher}
          />
        </EvaluateProvider>
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default culturalKnowledge;
