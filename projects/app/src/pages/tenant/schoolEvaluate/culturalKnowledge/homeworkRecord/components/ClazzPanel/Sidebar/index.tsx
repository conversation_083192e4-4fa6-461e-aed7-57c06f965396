import { getHomeworkPage } from '@/api/tenant/evaluate/process';
import SvgIcon from '@/components/SvgIcon';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';

const Sidebar = ({
  onChange,
  clazzId,
  subjectId,
  ...props
}: {
  clazzId: string;
  subjectId?: string;
  onChange?: (homework?: EvaluateHomeworkType) => void;
} & ChakraProps) => {
  const [activeId, setActiveId] = useState<string>();
  const [searchKey, setSearchKey] = useState('');

  const { data: homeworks = [] } = useQuery(
    ['homeworks', clazzId, subjectId],
    () =>
      getListFromPage(getHomeworkPage, {
        clazzId,
        subjectId
      }),
    {
      enabled: !!clazzId && !!subjectId
    }
  );

  const filteredHomeworks = useMemo(() => {
    return homeworks.filter((homework) =>
      homework.name.toLowerCase().includes(searchKey.toLowerCase())
    );
  }, [homeworks, searchKey]);

  const onClickHomework = (homework?: EvaluateHomeworkType) => {
    const newActiveId = homework ? homework.id : 'all';
    if (newActiveId === activeId) {
      return;
    }
    setActiveId(newActiveId);
    onChange?.(homework);
  };

  useEffect(() => {
    if (activeId !== 'all' && !homeworks.some((it) => it.id === activeId)) {
      setActiveId('all');
      onChange?.(undefined);
    }
  }, [activeId, homeworks]);

  useEffect(() => {
    if (filteredHomeworks.length && !activeId) {
      setActiveId(filteredHomeworks[0].id);
      onChange?.(filteredHomeworks[0]);
    }
  }, [filteredHomeworks, activeId]);

  return (
    <Flex flexDir="column" w={respDims(229, '.8ms')} py={respDims(4)} bgColor="#F9FAFB" {...props}>
      <Flex flex="1 0 0" flexDir="column" overflow="auto">
        <InputGroup h={respDims('36fpx')} mx={respDims(8)} w="auto">
          <InputLeftElement>
            <SvgIcon name="search" />
          </InputLeftElement>
          <Input
            w="100%"
            borderRadius={respDims(50)}
            bgColor="rgba(0,0,0,0.03)"
            boxShadow="none"
            border="none"
            outline="none"
            _focus={{
              bgColor: 'rgba(0,0,0,0.03)',
              border: 'none',
              outline: 'none',
              boxShadow: 'none'
            }}
            placeholder="请输入作业名称"
            value={searchKey}
            onChange={(e) => setSearchKey(e.target.value)}
          />
        </InputGroup>
        <Flex
          fontSize={respDims('15fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
          pl={respDims(16)}
          pr={respDims(20)}
          py={respDims(10)}
          mx={respDims(8)}
          mt={respDims(4)}
          borderRadius={respDims(8)}
          cursor="pointer"
          color={activeId === 'all' ? '#3366ff' : '#303133'}
          bgColor={activeId === 'all' ? '#FFFFFF' : 'transparent'}
          _hover={{
            bgColor: '#FFFFFF'
          }}
          onClick={() => onClickHomework(undefined)}
        >
          <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
            全部作业
          </Box>
        </Flex>
        {filteredHomeworks.map((item, index) => (
          <Flex
            key={item.id}
            fontSize={respDims('15fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
            pl={respDims(16)}
            pr={respDims(20)}
            py={respDims(10)}
            mx={respDims(8)}
            mt={index > 0 ? respDims(4) : 0}
            borderRadius={respDims(8)}
            cursor="pointer"
            {...(item.id === activeId
              ? {
                  color: '#3366ff',
                  bgColor: '#FFFFFF'
                }
              : {
                  color: '#303133'
                })}
            _hover={{
              bgColor: '#FFFFFF'
            }}
            onClick={() => onClickHomework(item)}
          >
            <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {item.name}
            </Box>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};

export default Sidebar;
