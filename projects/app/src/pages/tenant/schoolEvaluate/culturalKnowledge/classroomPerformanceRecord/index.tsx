import { Flex, HStack } from '@chakra-ui/react';
import SubTabs from '../../components/SubTabs';
import { useEffect, useMemo, useState } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import { respDims } from '@/utils/chakra';
import SubjectSelect from '../../components/SubjectSelect';
import StudentPanel from './components/StudentPanel';
import Breadcrumb from '../../components/Breadcrumb';
import RelTimeSelect, { RelTimes, RelTimeType } from '../../components/RelTimeSelect';
import { EvaluateSubjectType } from '@/types/api/tenant/evaluate/process';
import ClazzPanel from './components/ClazzPanel';
import { Navs, NavType } from './components/ClazzPanel/Sidebar';
import { deserializeData } from '@/utils/tools';
import { useRouter } from 'next/router';
import EvaluateFilterSelect, { EvaluateFilterValue } from '../../components/EvaluateFilterSelect';
import { EvaluateEntryEnum } from '@/constants/evaluate';

const tabList = [
  {
    label: '班级表现',
    value: 'clazz'
  },
  {
    label: '学生表现',
    value: 'student'
  }
];
interface ParamsType {
  ruleId: string;
  menuId: string;
  semesterId: string;
  gradeId: string;
  clazzId: string;
  clazzName: string;
}

const ClazzRecord = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  menuId,
  semesterId,
  tab = 'clazz'
}: {
  isClazzTeacher?: boolean;
  tab?: string;
  menuId: string;
  semesterId: string;
  gradeId: string;
  clazzId: string;
  clazzName: string;
}) => {
  const [activeTab, setActiveTab] = useState(tab);
  const [nav, setNav] = useState<NavType>(Navs[0]);
  const router = useRouter();

  const [subject, setSubject] = useState<EvaluateSubjectType>();

  const [relTime, setRelTime] = useState<RelTimeType>(RelTimes[0]);

  const breadcrumbList = useMemo(
    () => [
      { label: '班级列表' },
      { label: clazzName },
      { label: tabList.find((item) => item.value === activeTab)?.label! }
    ],
    [clazzName, activeTab]
  );

  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: undefined,
    periodType: undefined,
    ruleId: undefined
  });

  return (
    <Flex
      flexDir="column"
      w="100%"
      h="100%"
      bgColor="#ffffff"
      p={respDims(24)}
      borderRadius={respDims(20)}
    >
      <Flex justifyContent="space-between" alignItems="center" my={respDims(18)}>
        <Breadcrumb list={breadcrumbList} />

        <EvaluateFilterSelect
          menuId={menuId}
          clazzId={clazzId}
          value={filterForm}
          enterType={EvaluateEntryEnum.ClassroomPerformance}
          onChange={(value) => {
            setFilterForm(value);
          }}
        ></EvaluateFilterSelect>
      </Flex>

      <HStack spacing={respDims(16)} mb={respDims(20)}>
        <SubTabs alignSelf="flex-start" list={tabList} value={activeTab} onChange={setActiveTab} />
        <SubjectSelect
          clazzId={clazzId}
          ruleId={filterForm.ruleId!}
          menuId={menuId}
          semesterId={semesterId}
          value={subject}
          onChange={setSubject}
          hasAll={false}
        />
        <RelTimeSelect
          value={relTime}
          onChange={setRelTime}
          display={nav.type == 'record' ? 'display' : 'none'}
        />
      </HStack>
      {activeTab === 'clazz' && (
        <ClazzPanel
          flex="1 0 0"
          isClazzTeacher={isClazzTeacher}
          gradeId={gradeId}
          clazzId={clazzId}
          clazzName={clazzName}
          subjectId={subject?.id || '0'}
          semesterId={semesterId}
          menuId={menuId}
          relTime={relTime}
          ruleId={filterForm.ruleId!}
          onSidebarChange={setNav}
        />
      )}
      {activeTab === 'student' && (
        <StudentPanel
          flex="1 0 0"
          isClazzTeacher={isClazzTeacher}
          gradeId={gradeId}
          clazzId={clazzId}
          clazzName={clazzName}
          subjectId={subject?.id || '0'}
          semesterId={semesterId}
          menuId={menuId}
          relTime={relTime}
          ruleId={filterForm.ruleId!}
        />
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default ClazzRecord;
