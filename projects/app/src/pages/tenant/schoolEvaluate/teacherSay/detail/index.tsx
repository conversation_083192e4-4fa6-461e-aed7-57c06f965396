import { serviceSideProps } from '@/utils/i18n';
import { deserializeData, serializeData } from '@/utils/tools';
import EvaluateDetail from '../../components/EvaluateDetail';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import { EvaluateProvider, FooterRouterMenuProps } from '../../components/EvaluateContext';
import { Box } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useUserStore } from '@/store/useUserStore';
import { useCallback } from 'react';

const TeacherSayDetail = (props: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  menuId: string;
  semesterId: string;
  clazzName: string;
  subjectId?: string;
  reflectionId: string;
}) => {
  const FooterRouterMenu = useCallback(({ clazz }: FooterRouterMenuProps) => {
    const router = useRouter();
    const { userInfo } = useUserStore();
    return (
      <Box
        cursor="pointer"
        p="7px 20px"
        fontSize="14px"
        borderRadius="100px"
        bgColor="#F2F3F5"
        onClick={(e) => {
          router.push({
            pathname: '/tenant/teacherEvaluate/teacherSay/record',
            query: {
              q: serializeData({
                isClazzTeacher: clazz.teachers?.some((it) => it.id == userInfo?.tmbId),
                gradeId: clazz.parentId,
                clazzId: clazz.id,
                clazzName: clazz.clazzName,
                menuId: props.menuId,
                semesterId: props.semesterId,
                reflectionId: props.reflectionId,
                tab: 'clazz'
              })
            }
          });
        }}
      >
        <Box>查看学生评价明细</Box>
      </Box>
    );
  }, []);

  return (
    <EvaluateProvider
      enterType={EvaluateEntryEnum.TeacherComments}
      FooterRouterMenu={FooterRouterMenu}
      {...props}
      isSubjectEntry={false}
      isClazzTeacher={props.isClazzTeacher}
    >
      <EvaluateDetail
        {...props}
        backdropFilter={'blur(3.700000047683716px)'}
        boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
        border="1px solid #FFF"
      ></EvaluateDetail>
    </EvaluateProvider>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default TeacherSayDetail;
