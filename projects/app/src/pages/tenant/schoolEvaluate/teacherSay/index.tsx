import { getClazzEvaluateClazzList } from '@/api/tenant/evaluate/process';
import MyMenu from '@/components/MyMenu';
import SvgIcon from '@/components/SvgIcon';
import { useUserStore } from '@/store/useUserStore';
import { respDims } from '@/utils/chakra';
import { serviceSideProps } from '@/utils/i18n';
import { serializeData } from '@/utils/tools';
import { Box, Center, Flex, Grid, IconButton } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import SemesterSelect from '../components/SemesterSelect';
import { useMemo, useState } from 'react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { useSystemStore } from '@/store/useSystemStore';
import { useRoutes } from '@/hooks/useRoutes';
import { EvaluateClazzType } from '@/types/api/tenant/evaluate/process';
import { EvaluatePermissionEnum } from '@/constants/api/tenant/evaluate/process';
import MyBox from '@/components/common/MyBox';
import { treeToList } from '@/utils/tree';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';

const Achievement = () => {
  const router = useRouter();

  const { userInfo } = useUserStore();

  const { semesterListTermLevel } = useSystemStore();

  const { routeGroup } = useRoutes();

  const [currentSemester, setCurrentSemester] = useState<string | undefined>(
    semesterListTermLevel.find((it) => it.isCurrent === 1)?.id
  );

  const { entryTree, setEntryTree } = useSystemStore();
  useQuery(['entranceTree'], setEntryTree);

  const reflectionId = useMemo(() => {
    const info = treeToList(entryTree).find(
      (item) =>
        item.name == evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.TeacherComments].name
    );
    return info?.id?.toString();
  }, [entryTree]);

  const {
    data,
    isFetched: isClazzesFetched,
    isFetching,
    refetch
  } = useQuery(
    ['clazzes', reflectionId, currentSemester, routeGroup?.authActiveRoute?.id],
    async () => {
      const [clazzes] = await Promise.all([
        getClazzEvaluateClazzList({
          semesterId: currentSemester!,
          menuId: routeGroup?.authActiveRoute?.id!,
          reflectionId: reflectionId!
        })
      ]);
      return {
        clazzes
      };
    },
    {
      enabled: !!currentSemester && !!routeGroup?.authActiveRoute?.id && !!reflectionId
    }
  );

  const { clazzes } = data || { clazzes: [] };

  const handleSemesterChange = (value: string | undefined) => {
    setCurrentSemester(value);
  };

  return (
    <MyBox
      w="100%"
      h="100%"
      bgColor="#fff"
      pt={respDims(12)}
      display="flex"
      flexDir="column"
      borderRadius="24px 0px 0px 0px"
      overflow="hidden"
      isLoading={isFetching}
    >
      {/* <LevelEvaluateModal students={[]} clazzId='' gradeId=''></LevelEvaluateModal> */}
      <Box px={respDims(24)} py={respDims(12)}>
        <SemesterSelect value={currentSemester} onChange={handleSemesterChange} />
      </Box>
      <Box w="100%" h="100%" overflow="auto" flex="1" px={respDims(24)} py={respDims(12)}>
        {!!clazzes?.length ? (
          <Grid
            gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
            gridGap={respDims(18)}
          >
            {clazzes.map((clazz: EvaluateClazzType) => (
              <Box
                key={clazz.id}
                bgColor="#ffffff"
                border="1px solid #E5E7EB"
                borderRadius="20px"
                overflow="hidden"
                _hover={{
                  boxShadow: '0px 2px 4px 0px rgba(75,86,115,0.07)'
                }}
                cursor={
                  clazz.permissionType === EvaluatePermissionEnum.Evaluate ? 'pointer' : 'default'
                }
                onClick={() =>
                  clazz.permissionType === EvaluatePermissionEnum.Evaluate &&
                  router.push({
                    pathname: '/tenant/teacherEvaluate/teacherSay/detail',
                    query: {
                      q: serializeData({
                        isClazzTeacher: clazz.teachers?.some((it) => it.id == userInfo?.tmbId),
                        gradeId: clazz.parentId,
                        clazzId: clazz.id,
                        clazzName: clazz.parentName + clazz.deptName,
                        menuId: routeGroup?.authActiveRoute?.id,
                        semesterId: currentSemester
                      })
                    }
                  })
                }
              >
                <Flex
                  h={respDims(50, '.9ms')}
                  px={respDims(20)}
                  justify="space-between"
                  align="center"
                  borderBottom="1px solid #E5E7EB"
                >
                  <Box
                    color="#000000"
                    fontSize={respDims('16fpx')}
                    fontWeight="bold"
                    overflow="hidden"
                    textOverflow="ellipsis"
                  >
                    {clazz.parentName + clazz.deptName}
                  </Box>
                </Flex>

                <Flex alignItems="center" w="100%" mx={respDims(20)} mt={respDims(16)}>
                  <Box
                    color="#4E5969"
                    fontSize={respDims('16fpx')}
                    fontWeight="500"
                    lineHeight={respDims('24fpx')}
                    overflow="hidden"
                    textOverflow="ellipsis"
                    whiteSpace="nowrap"
                    mr="34px"
                  >
                    班主任: {clazz.teacherNames}
                  </Box>

                  <Flex
                    color="#4E5969"
                    fontSize={respDims('16fpx')}
                    fontWeight="500"
                    lineHeight={respDims('24fpx')}
                  >
                    <Box>学生:</Box>
                    <Center>{clazz.studentNum || 0}</Center>
                  </Flex>
                </Flex>

                <Flex alignItems="center" m="40px 20px 16px 20px">
                  {clazz.permissionType === EvaluatePermissionEnum.Evaluate && (
                    <Box
                      color="#4E5969"
                      _hover={{
                        color: '#36F',
                        backgroundColor: '#f3f6fe'
                      }}
                      fontSize="14px"
                      fontWeight="500"
                      p="7px 16px"
                      textAlign="center"
                      bgColor="#F2F3F5"
                      borderRadius="50px"
                      flex="1"
                      mr="16px"
                      cursor="pointer"
                      onClick={() =>
                        router.push({
                          pathname: '/tenant/teacherEvaluate/teacherSay/detail',
                          query: {
                            q: serializeData({
                              isClazzTeacher: clazz.teachers?.some(
                                (it) => it.id == userInfo?.tmbId
                              ),
                              gradeId: clazz.parentId,
                              clazzId: clazz.id,
                              clazzName: clazz.parentName + clazz.deptName,
                              menuId: routeGroup?.authActiveRoute?.id,
                              semesterId: currentSemester
                            })
                          }
                        })
                      }
                    >
                      评价
                    </Box>
                  )}

                  {clazz.permissionType === EvaluatePermissionEnum.Look && (
                    <Box flex="1" overflow="hidden">
                      <MyMenu
                        trigger="hover"
                        offset={[100, 10]}
                        Button={
                          <Flex w="100%" flex="1" align="center">
                            <Box
                              flex="1"
                              w="100%"
                              color="#4E5969"
                              _hover={{
                                color: '#36F',
                                backgroundColor: '#f3f6fe'
                              }}
                              fontSize="14px"
                              fontWeight="500"
                              textAlign="center"
                              bgColor="#F2F3F5"
                              borderRadius="50px"
                              cursor="pointer"
                            >
                              查看
                              <IconButton
                                aria-label="Expand"
                                icon={<ChevronDownIcon />}
                                variant="ghost"
                                color="#646c79"
                                _hover={{
                                  color: '#36F'
                                }}
                              />
                            </Box>
                          </Flex>
                        }
                        menuList={[
                          {
                            icon: <SvgIcon name="emojiClassPerformance" />,
                            label: '学生评价明细',
                            onClick: () =>
                              router.push({
                                pathname: '/tenant/teacherEvaluate/teacherSay/record',
                                query: {
                                  q: serializeData({
                                    isClazzTeacher: clazz.teachers?.some(
                                      (it) => it.id == userInfo?.tmbId
                                    ),
                                    gradeId: clazz.parentId,
                                    clazzId: clazz.id,
                                    clazzName: clazz.parentName + clazz.deptName,
                                    menuId: routeGroup?.authActiveRoute?.id,
                                    semesterId: currentSemester,
                                    reflectionId,
                                    tab: 'clazz'
                                  })
                                }
                              })
                          }
                        ]}
                      />
                    </Box>
                  )}

                  {clazz.permissionType === EvaluatePermissionEnum.Evaluate && (
                    <MyMenu
                      trigger="hover"
                      offset={[0, 10]}
                      Button={
                        <Center
                          aria-label="Expand"
                          bgColor="#F2F3F5"
                          borderRadius="50px"
                          w="35px"
                          h="35px"
                          cursor="pointer"
                          color="#646c79"
                          _hover={{
                            color: '#36F',
                            backgroundColor: '#f3f6fe'
                          }}
                        >
                          <ChevronDownIcon />
                        </Center>
                      }
                      menuList={[
                        ...(clazz.permissionType === EvaluatePermissionEnum.Evaluate
                          ? [
                              {
                                icon: <SvgIcon name="evaluate" />,
                                label: '评价',
                                onClick: () =>
                                  router.push({
                                    pathname: '/tenant/teacherEvaluate/teacherSay/detail',
                                    query: {
                                      q: serializeData({
                                        isClazzTeacher: clazz.teachers?.some(
                                          (it) => it.id == userInfo?.tmbId
                                        ),
                                        gradeId: clazz.parentId,
                                        clazzId: clazz.id,
                                        clazzName: clazz.parentName + clazz.deptName,
                                        menuId: routeGroup?.authActiveRoute?.id,
                                        semesterId: currentSemester,
                                        reflectionId,
                                        tab: 'clazz'
                                      })
                                    }
                                  })
                              }
                            ]
                          : []),
                        {
                          icon: <SvgIcon name="emojiClassPerformance" />,
                          label: '学生评价明细',
                          onClick: () =>
                            router.push({
                              pathname: '/tenant/teacherEvaluate/teacherSay/record',
                              query: {
                                q: serializeData({
                                  isClazzTeacher: clazz.teachers?.some(
                                    (it) => it.id == userInfo?.tmbId
                                  ),
                                  gradeId: clazz.parentId,
                                  clazzId: clazz.id,
                                  clazzName: clazz.parentName + clazz.deptName,
                                  menuId: routeGroup?.authActiveRoute?.id,
                                  semesterId: currentSemester,
                                  reflectionId,
                                  tab: 'clazz'
                                })
                              }
                            })
                        }
                      ]}
                    />
                  )}
                </Flex>
              </Box>
            ))}
          </Grid>
        ) : (
          <Flex flexDir="column" justify="center" align="center" w="100%" h="100%">
            <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
            <Box> 暂无评价的班级 </Box>
          </Flex>
        )}
      </Box>
    </MyBox>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}
export default Achievement;
