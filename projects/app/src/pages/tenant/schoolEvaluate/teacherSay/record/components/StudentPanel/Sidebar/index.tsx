import { getClazzStudentListByCode, getClazzStudentPage } from '@/api/tenant/evaluate/process';
import SvgIcon from '@/components/SvgIcon';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex, Input, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';

const Sidebar = ({
  clazzId,
  ruleId,
  semesterId,
  onChange,
  ...props
}: {
  clazzId: string;
  ruleId?: string;
  semesterId: string;
  onChange?: (student?: EvaluateStudentType) => void;
} & ChakraProps) => {
  const [activeId, setActiveId] = useState<string>();
  console.log(ruleId, 'ruleId');

  const [searchKey, setSearchKey] = useState('');

  const { data: students = [] } = useQuery(
    ['students', clazzId],
    () =>
      getClazzStudentListByCode({
        clazzId,
        ruleId,
        entrance: EvaluateEntryEnum.TeacherComments,
        semesterId: semesterId
      }),
    {
      enabled: !!clazzId && !!ruleId
    }
  );

  const filteredStudents = useMemo(() => {
    return students.filter((student) =>
      student.studentName.toLowerCase().includes(searchKey.toLowerCase())
    );
  }, [students, searchKey]);

  const onClickStudent = (student: EvaluateStudentType) => {
    if (student.studentId === activeId) {
      return;
    }
    setActiveId(student.studentId);
    onChange?.(student);
  };

  useEffect(() => {
    if (filteredStudents.length && !activeId) {
      setActiveId(filteredStudents[0].studentId);
      onChange?.(filteredStudents[0]);
    }
  }, [filteredStudents, activeId]);

  return (
    <Flex flexDir="column" w={respDims(229, '.8ms')} py={respDims(4)} bgColor="#F9FAFB" {...props}>
      <InputGroup h={respDims('36fpx')} mx={respDims(8)} w="auto">
        <InputLeftElement>
          <SvgIcon name="search" />
        </InputLeftElement>
        <Input
          w="100%"
          borderRadius={respDims(50)}
          bgColor="rgba(0,0,0,0.03)"
          boxShadow="none"
          border="none"
          outline="none"
          _focus={{
            bgColor: 'rgba(0,0,0,0.03)',
            border: 'none',
            outline: 'none',
            boxShadow: 'none'
          }}
          placeholder="请输入学生姓名"
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
        />
      </InputGroup>

      <Flex mt={respDims(14)} flex="1 0 0" flexDir="column" overflow="auto" pos="relative">
        {filteredStudents.length > 0 ? (
          filteredStudents.map((item, index) => (
            <Flex
              key={item.studentId}
              fontSize={respDims('15fpx')}
              lineHeight={respDims('22fpx')}
              fontWeight="bold"
              pl={respDims(16)}
              pr={respDims(20)}
              py={respDims(10)}
              mx={respDims(8)}
              mt={index > 0 ? respDims(4) : 0}
              borderRadius={respDims(8)}
              cursor="pointer"
              {...(item.studentId === activeId
                ? {
                    color: '#3366ff',
                    bgColor: '#FFFFFF'
                  }
                : {
                    color: '#303133'
                  })}
              _hover={{
                bgColor: '#FFFFFF'
              }}
              onClick={() => onClickStudent(item)}
            >
              <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
                {item.studentName}
              </Box>
            </Flex>
          ))
        ) : (
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            color="#a9a9ac"
          >
            暂无学生
          </Box>
        )}
      </Flex>
    </Flex>
  );
};

export default Sidebar;
