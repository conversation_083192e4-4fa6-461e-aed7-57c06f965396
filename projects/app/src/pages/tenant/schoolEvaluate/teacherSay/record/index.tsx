import { Box, Flex, HStack } from '@chakra-ui/react';
import SubTabs from '../../components/SubTabs';
import { useMemo, useState } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import { respDims } from '@/utils/chakra';
import SubjectSelect from '../../components/SubjectSelect';
import StudentPanel from './components/StudentPanel';
import Breadcrumb from '../../components/Breadcrumb';
import RelTimeSelect, { RelTimes, RelTimeType } from '../../components/RelTimeSelect';
import { EvaluateSubjectType } from '@/types/api/tenant/evaluate/process';
import { deserializeData } from '@/utils/tools';
import EvaluateFilterSelect, { EvaluateFilterValue } from '../../components/EvaluateFilterSelect';
import { EvaluateEntryEnum } from '@/constants/evaluate';

const tabList = [
  {
    label: '班级评价排行',
    value: 'clazz'
  },
  {
    label: '学生评价明细',
    value: 'student'
  }
];

const TeacherSayRecord = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  menuId,
  semesterId,
  tab = 'clazz'
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName: string;
  menuId: string;
  tab?: string;
  semesterId: string;
}) => {
  const [activeTab, setActiveTab] = useState(tab);

  const [subject, setSubject] = useState<EvaluateSubjectType>();

  const [relTime, setRelTime] = useState<RelTimeType>(RelTimes[0]);
  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: undefined,
    periodType: undefined,
    ruleId: undefined
  });
  const breadcrumbList = useMemo(
    () => [{ label: '班级列表' }, { label: clazzName }, { label: '学生评价明细' }],
    [clazzName, activeTab]
  );

  return (
    <Flex
      flexDir="column"
      w="100%"
      h="100%"
      backdropFilter={'blur(3.700000047683716px)'}
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      border="1px solid #FFF"
      borderRadius={respDims(24)}
    >
      <Flex justifyContent="space-between" alignItems="center" mx={respDims(32)} my={respDims(18)}>
        <Breadcrumb list={breadcrumbList} />

        <EvaluateFilterSelect
          enterType={EvaluateEntryEnum.TeacherComments}
          menuId={menuId}
          clazzId={clazzId}
          value={filterForm}
          onChange={(value) => {
            setFilterForm(value);
          }}
        ></EvaluateFilterSelect>
      </Flex>
      <Box bgColor="#ffffff" p={respDims(24)} flex="1">
        <StudentPanel
          flex="1 0 0"
          isClazzTeacher={isClazzTeacher}
          gradeId={gradeId}
          clazzId={clazzId}
          clazzName={clazzName}
          menuId={menuId}
          semesterId={semesterId}
          ruleId={filterForm.ruleId}
          subjectId={subject?.id}
          relTime={relTime}
        />
      </Box>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default TeacherSayRecord;
