import { getStudentIndicatorTree } from '@/api/tenant/evaluate/process';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { Box, Center, ChakraProps, Flex, Image, Text, VStack } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { RelTimeType } from '../../../../components/RelTimeSelect';
import { useRouter } from 'next/router';
import { treeToList } from '@/utils/tree';
import MyBox from '@/components/common/MyBox';

const Stats = ({
  type,
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  studentId,
  relTime,
  title,
  ruleId,
  menuId,
  semesterId,
  subTitle,
  ...props
}: {
  type: 'clazz' | 'student';
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  studentId?: string;
  relTime?: RelTimeType;
  title?: string;
  ruleId?: string;
  menuId?: string;
  semesterId?: string;
  subTitle?: string;
} & ChakraProps) => {
  const router = useRouter();

  const { data: indicatorTree, isFetching: indicatorTreeLoading } = useQuery(
    ['indactorDetail', studentId],
    () =>
      getStudentIndicatorTree({
        evaluatedId: studentId,
        menuId,
        semesterId,
        clazzId,
        ruleId: ruleId!
      }),
    { enabled: !!(studentId && ruleId && semesterId && menuId) }
  );

  const achievementIndicator = useMemo(() => {
    return treeToList(indicatorTree || [])?.find((it) => it.name === '突出成绩记载');
  }, [indicatorTree]);

  const teacherSayIndicator = useMemo(() => {
    return treeToList(indicatorTree || [])?.find((it) => it.name === '班主任寄语');
  }, [indicatorTree]);

  if (!achievementIndicator && !teacherSayIndicator) {
    return (
      <Flex {...props} minH="500px">
        <Center w="100%" h="100%" flexDir="column">
          <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
          <Text>暂无数据</Text>
        </Center>
      </Flex>
    );
  }

  return (
    <Flex {...props} minH="500px">
      <MyBox
        isLoading={indicatorTreeLoading}
        px={respDims(24)}
        py={respDims(10)}
        flex="1 0 0"
        flexDir="column"
        display="flex"
        alignItems="stretch"
        borderRight={'1px solid #E5E7EB'}
      >
        <Flex align="center">
          <Box
            w={respDims(7)}
            h={respDims('14fpx')}
            bgColor="#175DFF"
            borderRadius={respDims(8)}
          ></Box>
          <Box
            ml={respDims(6)}
            color="rgba(0,0,0,0.9)"
            fontSize={respDims('16fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
          >
            {title}
          </Box>
        </Flex>

        <VStack spacing={4} mt={4}>
          <Box width="100%">
            <Flex
              display="inline-flex"
              borderRadius="50px"
              alignItems="center"
              py={respDims(8, 4)}
              px={respDims(22, 16)}
              bg="linear-gradient(90deg, #FFF8DF 0%, rgba(255, 248, 223, 0.23) 100%)"
            >
              <SvgIcon name="evaluateSayLabel" w="20px" h="20px" mr={2}></SvgIcon>
              <Box fontSize={respDims(16, 14)} fontWeight="500">
                突出成绩记载
              </Box>
            </Flex>
          </Box>
          <Box
            width="100%"
            borderRadius="10px"
            py={respDims(20)}
            px={respDims(16)}
            bgColor="#F9FAFB"
          >
            {achievementIndicator?.comment || '暂无突出成绩记载'}
          </Box>
          <Box width="100%">
            <Flex
              display="inline-flex"
              borderRadius="50px"
              alignItems="center"
              py={respDims(8, 4)}
              px={respDims(22, 16)}
              bg="linear-gradient(90deg, #FFF8DF 0%, rgba(255, 248, 223, 0.23) 100%)"
            >
              <SvgIcon name="evaluateSayLabel2" w="20px" h="20px" mr={2}></SvgIcon>
              <Box fontSize={respDims(16, 14)} fontWeight="500">
                班主任寄语
              </Box>
            </Flex>
          </Box>
          <Box
            width="100%"
            borderRadius="10px"
            py={respDims(20)}
            px={respDims(16)}
            bgColor="#F9FAFB"
          >
            {teacherSayIndicator?.comment || '暂无班主任寄语'}
          </Box>
          <Box width="100%" fontSize={respDims(16, 14)} fontWeight="500">
            班主任签名：
          </Box>
          <Box width="100%">
            <Image
              w={respDims(263, 230)}
              alt=""
              borderRadius="8px"
              border="1px solid #E5E7EB"
              // border="none!important"
              src={teacherSayIndicator?.signFile?.fileUrl}
              height={respDims(150, 130)}
            ></Image>
          </Box>
          <Flex width="100%" alignItems="center">
            <Box color="#2b1900" fontWeight="500" fontSize={respDims(16, 14)}>
              评价时间：
            </Box>
            <Box fontSize={respDims(16, 14)}>
              {achievementIndicator?.updateTime || teacherSayIndicator?.updateTime}
            </Box>
          </Flex>
        </VStack>
      </MyBox>
    </Flex>
  );
};

export default Stats;
