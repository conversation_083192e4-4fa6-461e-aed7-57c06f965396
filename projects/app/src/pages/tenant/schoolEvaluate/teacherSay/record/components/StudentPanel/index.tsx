import { ChakraProps, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useState } from 'react';
import Sidebar from './Sidebar';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import Stats from '../Stats';
import { RelTimeType } from '../../../../components/RelTimeSelect';

const StudentPanel = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  semesterId,
  relTime,
  ruleId,
  menuId,
  ...props
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  ruleId?: string;
  indactorId?: string;
  menuId?: string;
  semesterId: string;
  relTime?: RelTimeType;
} & ChakraProps) => {
  const [student, setStudent] = useState<EvaluateStudentType>();

  return (
    <Flex
      align="stretch"
      border="1px solid #E5E7EB"
      borderRadius={respDims(14)}
      overflow="hidden"
      h="100%"
      {...props}
    >
      <Sidebar
        borderRight="1px solid 1px solid #E5E7EB"
        clazzId={clazzId}
        ruleId={ruleId}
        semesterId={semesterId}
        onChange={setStudent}
      />
      <Stats
        flex="1 0 0"
        type="student"
        title={student?.studentName}
        subTitle={relTime?.label}
        isClazzTeacher={isClazzTeacher}
        gradeId={gradeId}
        clazzId={clazzId}
        ruleId={ruleId}
        menuId={menuId}
        semesterId={semesterId}
        clazzName={clazzName}
        subjectId={subjectId}
        studentId={student?.studentId}
        relTime={relTime}
      />
    </Flex>
  );
};

export default StudentPanel;
