import { serviceSideProps } from '@/utils/i18n';
import { deserializeData, serializeData } from '@/utils/tools';
import EvaluateDetail from '../../components/EvaluateDetail';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import EvaluateContext, {
  EvaluateProvider,
  FooterRouterMenuProps
} from '../../components/EvaluateContext';
import MyMenu from '@/components/MyMenu';
import { Box, Center, HStack, MenuButton } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useRouter } from 'next/router';
import { useCallback } from 'react';
import { EvaluateClazzType } from '@/types/api/tenant/evaluate/process';
import { useUserStore } from '@/store/useUserStore';

const LearnAdaptabilityDetail = (props: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  menuId: string;
  semesterId: string;
  reflectionId: string;
  clazzName: string;
  subjectId?: string;
}) => {
  const FooterRouterMenu = useCallback(({ clazz }: FooterRouterMenuProps) => {
    const router = useRouter();
    const { userInfo } = useUserStore();
    return (
      <Box p="7px 20px" borderRadius="100px" bgColor="#F2F3F5" ml="12px" cursor="pointer">
        <MyMenu
          trigger="hover"
          offset={[-20, 20]}
          width={20}
          Button={
            <HStack
              borderRadius={respDims(4)}
              _hover={{
                bgColor: '#f3f4f6'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <Box fontSize="14px" fontWeight="400" lineHeight="20px">
                查看评价数据
              </Box>

              <SvgIcon name="chevronDown" w={respDims(14)} h={respDims(14)} />
            </HStack>
          }
          menuList={[
            {
              icon: <SvgIcon name="emojiClassPerformance" />,
              label: '班级评价排行',
              onClick: () =>
                router.push({
                  pathname: '/tenant/teacherEvaluate/learnAdaptability/record',
                  query: {
                    q: serializeData({
                      isClazzTeacher: clazz.teachers?.some((it) => it.id == userInfo?.tmbId),
                      gradeId: clazz.parentId,
                      clazzId: clazz.id,
                      clazzName: clazz.clazzName,
                      menuId: props.menuId,
                      semesterId: props.semesterId,
                      reflectionId: props.reflectionId,
                      tab: 'clazz'
                    })
                  }
                })
            },
            {
              icon: <SvgIcon name="emojiStudentPerformance" />,
              label: '学生评价数据',
              onClick: () =>
                router.push({
                  pathname: '/tenant/teacherEvaluate/learnAdaptability/record',
                  query: {
                    q: serializeData({
                      isClazzTeacher: clazz.teachers?.some((it) => it.id == userInfo?.tmbId),
                      gradeId: clazz.parentId,
                      clazzId: clazz.id,
                      clazzName: clazz.clazzName,
                      menuId: props.menuId,
                      semesterId: props.semesterId,
                      reflectionId: props.reflectionId,
                      tab: 'student'
                    })
                  }
                })
            }
          ]}
        />
      </Box>
    );
  }, []);

  return (
    <EvaluateProvider
      enterType={EvaluateEntryEnum.AdaptiveLearning}
      FooterRouterMenu={FooterRouterMenu}
      {...props}
      isSubjectEntry={false}
    >
      <EvaluateDetail
        {...props}
        backdropFilter={'blur(3.700000047683716px)'}
        boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
        border="1px solid #FFF"
      ></EvaluateDetail>
    </EvaluateProvider>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default LearnAdaptabilityDetail;
