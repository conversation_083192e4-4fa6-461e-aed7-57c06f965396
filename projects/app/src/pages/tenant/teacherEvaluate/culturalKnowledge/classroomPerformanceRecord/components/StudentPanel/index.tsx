import { ChakraProps, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useState } from 'react';
import Sidebar from './Sidebar';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import Stats from '../Stats';
import { RelTimeType } from '../../../../components/RelTimeSelect';

const StudentPanel = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  semesterId,
  menuId,
  ruleId,
  relTime,
  ...props
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  relTime?: RelTimeType;
} & ChakraProps) => {
  const [student, setStudent] = useState<EvaluateStudentType>();
  return (
    <Flex
      align="stretch"
      border="1px solid #E5E7EB"
      borderRadius={respDims(14)}
      overflow="hidden"
      {...props}
    >
      <Sidebar
        borderRight="1px solid 1px solid #E5E7EB"
        clazzId={clazzId}
        onChange={(e) => {
          setStudent(e);
        }}
      />
      <Stats
        flex="1 0 0"
        type="student"
        title={student?.name}
        subTitle={relTime?.label}
        isClazzTeacher={isClazzTeacher}
        gradeId={gradeId}
        clazzId={clazzId}
        clazzName={clazzName}
        subjectId={subjectId}
        studentId={student?.id}
        menuId={menuId}
        ruleId={ruleId}
        semesterId={semesterId}
        relTime={relTime}
      />
    </Flex>
  );
};

export default StudentPanel;
