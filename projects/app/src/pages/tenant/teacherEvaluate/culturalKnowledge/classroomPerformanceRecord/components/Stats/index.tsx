import {
  getEvaluationClazzPage,
  getEvaluationClazzStatistics,
  getEvaluationStudentPage,
  getEvaluationStudentStatistics,
  removeClazzEvaluate
} from '@/api/tenant/evaluate/process';
import SvgIcon from '@/components/SvgIcon';
import { EvaluationClazzPageType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { MessageBox } from '@/utils/ui/messageBox';
import { Box, Center, ChakraProps, Flex, HStack, Image } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import EChartsReact from 'echarts-for-react';
import { useEffect, useMemo, useState } from 'react';
import { RelTimeType } from '../../../../components/RelTimeSelect';
import { IndactorTypeEnum, IndactorTypeNameMap } from '@/constants/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';
import dayjs from 'dayjs';
import { Toast } from '@/utils/ui/toast';
import { useRouter } from 'next/router';
import { serializeData } from '@/utils/tools';

const Stats = ({
  type,
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  studentId,
  relTime,
  title,
  subTitle,
  menuId,
  ruleId,
  semesterId,
  ...props
}: {
  type: 'clazz' | 'student';
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  studentId?: string;
  relTime?: RelTimeType;
  title?: string;
  subTitle?: string;
  menuId?: string;
  ruleId?: string;
  semesterId?: string;
} & ChakraProps) => {
  const router = useRouter();

  const [indactorIndex, setIndactorIndex] = useState(0);

  const queryEnabled = !!(
    clazzId &&
    subjectId &&
    menuId &&
    ruleId &&
    semesterId &&
    (type == 'student' ? studentId : true)
  );

  const {
    data: stats = { indicatorStatistics: [], sumIndicatorStatistics: [] },
    refetch: refetchStats
  } = useQuery(
    ['stats', clazzId, subjectId, menuId, ruleId, semesterId, type],
    () => {
      if (type === 'clazz') {
        return getEvaluationClazzStatistics({
          clazzId,
          subjectId,
          menuId,
          ruleId,
          semesterId
        }).then((res) => {
          const totalScore = res.indicatorStatistics.reduce((acc, it) => it.sumScore + acc, 0);
          return {
            ...res,
            indicatorStatistics: [
              {
                indicatorName: '全部点评',
                sumScore: totalScore,
                indicatorId: 'total',
                iconFile: {
                  fileUrl: '/imgs/evaluate/stats_all.png'
                }
              },
              ...res.indicatorStatistics
            ]
          };
        });
      } else {
        return getEvaluationStudentStatistics({
          clazzId,
          subjectId,
          menuId,
          ruleId,
          semesterId,
          studentId
        }).then((res) => {
          const totalScore = res.indicatorStatistics.reduce((acc, it) => it.sumScore + acc, 0);
          return {
            ...res,
            indicatorStatistics: [
              {
                indicatorName: '全部点评',
                sumScore: totalScore,
                indicatorId: 'total',
                iconFile: {
                  fileUrl: '/imgs/evaluate/stats_all.png'
                }
              },
              ...res.indicatorStatistics
            ]
          };
        });
      }
    },
    {
      enabled: queryEnabled
    }
  );

  const {
    data: records = [],
    isFetched: isRecordsFetched,
    refetch: refetchRecords
  } = useQuery(
    [
      'records',
      clazzId,
      subjectId,
      relTime,
      stats.indicatorStatistics?.[indactorIndex]?.indicatorId,
      type,
      studentId
    ],
    () => {
      const apiFunction = type === 'clazz' ? getEvaluationClazzPage : getEvaluationStudentPage;
      return getListFromPage(apiFunction, {
        ...(type === 'student' ? { studentId } : {}),
        clazzId,
        semesterId,
        menuId,
        ruleId,
        subjectId
      }).then((res) => {
        const now = dayjs();
        return res.map((it) => {
          const time = dayjs(it.updateTime);
          let relTime = '';
          if (now.valueOf() - time.valueOf() <= 3 * 60000) {
            relTime = '刚刚';
          } else if (now.isSame(time, 'date')) {
            relTime = `今天${time.format('HH:mm')}`;
          } else if (now.subtract(1, 'day').isSame(time, 'date')) {
            relTime = `昨天${time.format('HH:mm')}`;
          } else {
            relTime = time.format('MM-DD HH:mm');
          }
          return { ...it, relTime };
        });
      });
    },
    {
      enabled: queryEnabled
    }
  );

  const { goodValue, goodPercent, badValue, badPercent, goodValueOrigin, badValueOrigin } =
    useMemo(() => {
      const goodStats = stats.sumIndicatorStatistics.find(
        (it) => it.parentName === IndactorTypeNameMap[IndactorTypeEnum.Good]
      );
      const badStats = stats.sumIndicatorStatistics.find(
        (it) => it.parentName === IndactorTypeNameMap[IndactorTypeEnum.Bad]
      );

      const goodValueOrigin = goodStats?.sumScore || 0;
      const goodValue = Math.abs(goodValueOrigin);

      const badValueOrigin = badStats?.sumScore || 0;
      const badValue = Math.abs(badValueOrigin);

      const totalValue = goodValue + badValue;
      const goodPercent = totalValue ? Math.round((goodValue / totalValue) * 100) : 0;
      const badPercent = totalValue ? 100 - goodPercent : 0;

      return {
        goodValue,
        goodPercent,
        badValue,
        badPercent,
        goodValueOrigin,
        badValueOrigin
      };
    }, [stats]);

  const option = useMemo(() => {
    const totalValue = goodValue + badValue;

    const startAngle = totalValue ? ((goodValue / totalValue) * 360) / 2 + 180 : 270;

    return {
      series: [
        {
          type: 'pie',
          radius: '100%',
          center: ['50%', '50%'],
          startAngle: startAngle,
          silent: true,
          labelLine: { show: false },
          label: { show: false },
          data: [
            {
              value: goodValue,
              name: '表扬',
              itemStyle: {
                color: '#6CCDCD'
              }
            },
            {
              value: badValue,
              name: '待改进',
              itemStyle: {
                color: '#6286F1'
              }
            }
          ]
        }
      ]
    };
  }, [goodValue, badValue]);

  const handleRemove = (record: EvaluationClazzPageType) => {
    MessageBox.confirm({
      title: '提示',
      content: `删除点评记录将不可恢复,将减去学生该次评分,确定删除吗?`,
      onOk: () => {
        removeClazzEvaluate({
          id: record.id,
          menuId,
          semesterId
        }).then(() => {
          refetchStats();
          refetchRecords();
          Toast.success('删除成功');
        });
      }
    });
  };

  const onEvaluate = () => {
    router.replace({
      pathname: '/tenant/teacherEvaluate/culturalKnowledge/detail',
      query: {
        q: serializeData({
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          menuId,
          ruleId,
          semesterId
        })
      }
    });
  };

  useEffect(() => {
    setIndactorIndex(0);
  }, [clazzId, subjectId, studentId, relTime]);

  useEffect(() => {
    if (studentId && type === 'student' && queryEnabled) {
      refetchRecords();
      refetchStats();
    }
  }, [studentId, type, clazzId, subjectId, menuId, ruleId, semesterId]);

  return (
    <Flex {...props}>
      <Flex
        px={respDims(24)}
        py={respDims(10)}
        flex="1 0 0"
        flexDir="column"
        align="stretch"
        borderRight={'1px solid #E5E7EB'}
      >
        <Flex align="center">
          <Box
            w={respDims(7)}
            h={respDims('14fpx')}
            bgColor="#175DFF"
            borderRadius={respDims(8)}
          ></Box>
          <Box
            ml={respDims(6)}
            color="rgba(0,0,0,0.9)"
            fontSize={respDims('16fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
          >
            {title}
          </Box>
        </Flex>

        <Center pt={respDims(40)} pb={respDims(24)}>
          <EChartsReact option={option} style={{ width: '178px', height: '178px' }} />
        </Center>

        <HStack
          spacing={respDims(30)}
          justify="center"
          align="center"
          color="#909399"
          fontSize={respDims('14fpx')}
          lineHeight={respDims('20fpx')}
        >
          <HStack spacing={respDims(6)} align="center">
            <SvgIcon name="emojiSmile" w={respDims('26fpx')} h={respDims('26fpx')} />
            <Box color="#71D4FF">{goodValueOrigin > 0 ? `+${goodValue}` : goodValueOrigin}</Box>
            <Box>{`表扬(${goodPercent}%)`}</Box>
          </HStack>

          <HStack spacing={respDims(6)} align="center">
            <SvgIcon name="emojiAngry" w={respDims('26fpx')} h={respDims('26fpx')} />
            <Box color="#FF846B">{badValueOrigin > 0 ? `+${badValue}` : badValueOrigin}</Box>
            <Box>{`待改进(${badPercent}%)`}</Box>
          </HStack>
        </HStack>

        <Box flex="1 0 0" overflow="auto" mt={respDims(24)}>
          {stats.indicatorStatistics.length > 0 ? (
            stats.indicatorStatistics.map((item, index) => (
              <Flex
                key={item.indicatorId}
                align="center"
                h={respDims('60fpx')}
                px={respDims(26)}
                mt={index > 0 ? respDims(12) : 0}
                // bgColor={index === indactorIndex ? 'primary.50' : '#F9FAFB'}
                bgColor="#F9FAFB"
                fontSize={respDims('16fpx')}
                lineHeight={respDims('22fpx')}
                borderRadius={8}
                // cursor="pointer"
                // _hover={{
                //   bgColor: 'primary.50'
                // }}
                // onClick={() => setIndactorIndex(index)}
              >
                <Image
                  src={
                    index == 0
                      ? '/imgs/evaluate/stats_all.png'
                      : item.iconFile?.fileUrl || '/imgs/evaluate/default.png'
                  }
                  alt=""
                  w={respDims('40fpx')}
                  h={respDims('40fpx')}
                  borderRadius="50%"
                />
                <Box
                  flex="1 0 0"
                  ml={respDims(12)}
                  color="#303133"
                  fontWeight="bold"
                  whiteSpace="nowrap"
                  overflow="hidden"
                  textOverflow="ellipsis"
                >
                  {item.indicatorName}
                </Box>
                <Box
                  ml={respDims(12)}
                  color={!item.sumScore ? '#606266' : item.sumScore > 0 ? '#71D4FF' : '#FF846B'}
                >
                  {item.sumScore > 0 ? `+${item.sumScore}` : item.sumScore}分
                </Box>
              </Flex>
            ))
          ) : (
            <Flex
              align="center"
              h={respDims('60fpx')}
              px={respDims(26)}
              bgColor="primary.50"
              fontSize={respDims('16fpx')}
              lineHeight={respDims('22fpx')}
              borderRadius={8}
              cursor="pointer"
              _hover={{
                bgColor: 'primary.50'
              }}
            >
              <Image
                src="/imgs/evaluate/stats_all.png"
                alt=""
                w={respDims('40fpx')}
                h={respDims('40fpx')}
                borderRadius="50%"
              />
              <Box
                flex="1 0 0"
                ml={respDims(12)}
                color="#303133"
                fontWeight="bold"
                whiteSpace="nowrap"
                overflow="hidden"
                textOverflow="ellipsis"
              >
                全部点评
              </Box>
              <Box ml={respDims(12)} color="#606266">
                0分
              </Box>
            </Flex>
          )}
        </Box>
      </Flex>

      <Flex flex="1 0 0" flexDir="column" px={respDims(24)} py={respDims(10)} overflow="hidden">
        <Flex align="center">
          <Box
            w={respDims(7)}
            h={respDims('14fpx')}
            bgColor="#175DFF"
            borderRadius={respDims(8)}
          ></Box>
          <Box
            ml={respDims(6)}
            color="rgba(0,0,0,0.9)"
            fontSize={respDims('16fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
          >
            {subTitle}
          </Box>
        </Flex>

        <Box flex="1 0 0" overflow="auto" mt={respDims(16)}>
          {!records.length ? (
            <Flex w="100%" h="80%" direction="column" justify="center" align="center">
              <Image
                src="/imgs/evaluate/evaluate_empty.png"
                alt=""
                w={respDims(100)}
                h={respDims(100)}
                mb={respDims(18)}
              />
              <Box>暂无点评哦～</Box>
              <Box
                mt={respDims(18)}
                px={respDims(44)}
                py={respDims(9)}
                color="primary.500"
                bgColor="primary.50"
                fontSize={respDims(16)}
                lineHeight={respDims(22)}
                borderRadius={respDims(100)}
                cursor="pointer"
                _hover={{
                  bgColor: '#f3f5fe'
                }}
                onClick={onEvaluate}
              >
                前往点评
              </Box>
            </Flex>
          ) : (
            records.map((item, index) => (
              <Box key={index} px={respDims(20)} py={respDims(20)} borderBottom="1px solid #E5E7EB">
                <Flex align="center">
                  <Image
                    src={item.iconFile?.fileUrl || '/imgs/evaluate/default.png'}
                    w={respDims('50fpx')}
                    h={respDims('50fpx')}
                    alt=""
                    borderRadius="50%"
                  />

                  <Box mx={respDims(12)} flex="1 0 0" overflow="hidden">
                    <Flex
                      color="#303133"
                      fontSize={respDims('16fpx')}
                      lineHeight={respDims('22fpx')}
                    >
                      <Box>给{item.studentName}</Box>
                      <Box
                        ml={respDims(4)}
                        color={!item.score ? '#606266' : item.score > 0 ? '#71D4FF' : '#FF846B'}
                      >
                        {item.score! > 0 ? `+${item.score}` : item.score}
                      </Box>

                      <Box ml={respDims(4)}>因为{item.indicatorName}</Box>
                    </Flex>

                    <Box
                      mt={respDims(6)}
                      color="#909399"
                      fontSize={respDims('14fpx')}
                      lineHeight={respDims('22fpx')}
                    >
                      {item.relTime} 由 {item.evaluatorName} 点评 {item.studentName}
                    </Box>
                  </Box>

                  <SvgIcon
                    name="circleClose"
                    w={respDims('24fpx')}
                    h={respDims('24fpx')}
                    cursor="pointer"
                    onClick={() => handleRemove(item)}
                  />
                </Flex>
              </Box>
            ))
          )}
        </Box>
      </Flex>
    </Flex>
  );
};

export default Stats;
