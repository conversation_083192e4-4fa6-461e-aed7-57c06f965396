import { getClazzRankList } from '@/api/tenant/evaluate/process';
import MyTable from '@/components/MyTable';
import { IndactorTypeEnum, IndactorTypeNameMap } from '@/constants/api/tenant/evaluate/process';
import { ClazzEvaluateRecordType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { TableProps } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useCallback, useMemo } from 'react';

const Rank = ({
  clazzId,
  subjectId,
  semesterId,
  menuId,
  ruleId,
  ...props
}: {
  clazzId: string;
  subjectId?: string;
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
} & ChakraProps) => {
  const query = useMemo(
    () => ({ clazzId, subjectId, semesterId, menuId, ruleId }),
    [clazzId, subjectId, semesterId, ruleId]
  );

  const columns: ColumnsType<ClazzEvaluateRecordType> = [
    {
      title: '学生姓名',
      key: 'studentName',
      dataIndex: 'studentName'
    },
    {
      title: '总分',
      key: 'sumScore',
      dataIndex: 'sumScore',
      defaultSortOrder: 'descend',
      sorter: (a, b) => a.sumScore! - b.sumScore!
    },
    {
      title: '表扬',
      dataIndex: 'goodScore',
      key: 'goodScore',
      defaultSortOrder: 'descend',
      sorter: (a, b) => {
        const aGoodScores =
          a.subEvaluations?.filter(
            (item) => item.indicatorName === IndactorTypeNameMap[IndactorTypeEnum.Good]
          ) || [];
        const bGoodScores =
          b.subEvaluations?.filter(
            (item) => item.indicatorName === IndactorTypeNameMap[IndactorTypeEnum.Good]
          ) || [];
        const totalAGoodScore = aGoodScores.reduce((sum, item) => sum + (item.sumScore || 0), 0);
        const totalBGoodScore = bGoodScores.reduce((sum, item) => sum + (item.sumScore || 0), 0);
        return totalAGoodScore - totalBGoodScore;
      },
      render: (_, record: ClazzEvaluateRecordType) => {
        const goodScores =
          record.subEvaluations?.filter(
            (item) => item.indicatorName === IndactorTypeNameMap[IndactorTypeEnum.Good]
          ) || [];
        const totalGoodScore = goodScores.reduce((sum, item) => sum + (item.sumScore || 0), 0);
        return <Box>{totalGoodScore}</Box>;
      }
    },
    {
      title: '待改进',
      dataIndex: 'sumScore',
      key: 'badScore',
      defaultSortOrder: 'descend',
      sorter: (a, b) => {
        const aBadScores =
          a.subEvaluations?.filter(
            (item) => item.indicatorName === IndactorTypeNameMap[IndactorTypeEnum.Bad]
          ) || [];
        const bBadScores =
          b.subEvaluations?.filter(
            (item) => item.indicatorName === IndactorTypeNameMap[IndactorTypeEnum.Bad]
          ) || [];
        const totalABadScore = aBadScores.reduce((sum, item) => sum + (item.sumScore || 0), 0);
        const totalBBadScore = bBadScores.reduce((sum, item) => sum + (item.sumScore || 0), 0);
        return totalABadScore - totalBBadScore;
      },
      render: (_, record) => {
        const badScores =
          record.subEvaluations?.filter(
            (item) => item.indicatorName === IndactorTypeNameMap[IndactorTypeEnum.Bad]
          ) || [];
        const totalBadScore = badScores.reduce((sum, item) => sum + (item.sumScore || 0), 0);
        return <Box>{totalBadScore}</Box>;
      }
    }
  ];

  const TableHeader = useCallback(() => {
    return (
      <Flex align="center">
        <Box
          w={respDims(7)}
          h={respDims('14fpx')}
          bgColor="#175DFF"
          borderRadius={respDims(8)}
        ></Box>
        <Box
          ml={respDims(6)}
          color="rgba(0,0,0,0.9)"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
        >
          全部点评-排行榜
        </Box>
      </Flex>
    );
  }, []);

  return (
    <Box px={respDims(24)} py={respDims(10)} {...props}>
      <MyTable
        columns={columns}
        api={getClazzRankList}
        defaultQuery={query}
        pageConfig={{ showPaginate: false }}
        headerConfig={{
          HeaderComponent: TableHeader
        }}
        boxStyle={{
          p: 0
        }}
        rowKey="studentId"
      />
    </Box>
  );
};

export default Rank;
