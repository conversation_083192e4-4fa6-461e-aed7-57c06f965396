import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import MyTable from '@/components/MyTable';
import { TableProps } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import Sidebar from './Sidebar';
import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { getStudentHomeworkPage } from '@/api/tenant/evaluate/process';

const StudentPanel = ({
  clazzId,
  subjectId,
  semesterId,
  ruleId,
  menuId,
  ...props
}: {
  clazzId: string;
  subjectId?: string;
  semesterId: string;
  ruleId: string;
  menuId: string;
} & ChakraProps) => {
  const [student, setStudent] = useState<EvaluateStudentType>();

  const columns: TableProps['columns'] = [
    {
      title: '作业名称',
      dataIndex: 'homeworkName'
    },
    {
      title: '所属学科',
      dataIndex: 'subjectName'
    },
    {
      title: '点评人',
      dataIndex: 'evaluatorName'
    },
    {
      title: '评分',
      render: (value, record) => {
        return record.score || record.scoreLevelValue;
      }
    },
    {
      title: '评分时间',
      dataIndex: 'updateTime',
      render: (text) => dayjs(text).format('YYYY/MM/DD HH:mm')
    }
  ];

  const TableHeader = useCallback(
    () => (
      <Flex align="center">
        <Box
          w={respDims(7)}
          h={respDims('14fpx')}
          bgColor="#175DFF"
          borderRadius={respDims(8)}
        ></Box>
        <Box
          ml={respDims(6)}
          color="rgba(0,0,0,0.9)"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
        >
          {student?.studentName}
        </Box>
      </Flex>
    ),
    [student]
  );

  return (
    <Flex border="1px solid #E5E7EB" borderRadius={respDims(14)} overflow="hidden" {...props}>
      <Sidebar
        borderRight="1px solid 1px solid #E5E7EB"
        clazzId={clazzId}
        ruleId={ruleId}
        semesterId={semesterId}
        onChange={setStudent}
      />
      <MyTable
        columns={columns}
        api={getStudentHomeworkPage}
        queryConfig={{
          enabled: !!student?.studentId
        }}
        defaultQuery={{
          clazzId,
          subjectId,
          studentId: student?.studentId,
          semesterId,
          ruleId,
          current: 1,
          size: 999,
          menuId
        }}
        pageConfig={{ showPaginate: false }}
        boxStyle={{ p: respDims(16) }}
        headerConfig={{
          HeaderComponent: TableHeader
        }}
      />
    </Flex>
  );
};

export default StudentPanel;
