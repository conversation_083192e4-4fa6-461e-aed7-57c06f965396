import { Flex, HStack } from '@chakra-ui/react';
import SubTabs from '../../components/SubTabs';
import { useMemo, useState } from 'react';
import { serviceSideProps } from '@/utils/i18n';
import { respDims } from '@/utils/chakra';
import SubjectSelect from '../../components/SubjectSelect';
import ClazzPanel from './components/ClazzPanel';
import StudentPanel from './components/StudentPanel';
import Breadcrumb from '../../components/Breadcrumb';
import { EvaluateSubjectType } from '@/types/api/tenant/evaluate/process';
import { deserializeData } from '@/utils/tools';
import EvaluateFilterSelect, { EvaluateFilterValue } from '../../components/EvaluateFilterSelect';
import { EvaluateEntryEnum } from '@/constants/evaluate';

const tabList = [
  {
    label: '班级作业',
    value: 'clazz'
  },
  {
    label: '学生作业',
    value: 'student'
  }
];

const HomeworkRecord = ({
  clazzId,
  clazzName,
  ruleId,
  menuId,
  semesterId,
  tab = 'clazz'
}: {
  clazzId: string;
  clazzName: string;
  tab?: string;
  ruleId: string;
  menuId: string;
  semesterId: string;
}) => {
  const [activeTab, setActiveTab] = useState(tab);

  const [subject, setSubject] = useState<EvaluateSubjectType>();

  const breadcrumbList = useMemo(
    () => [
      { label: '班级列表' },
      { label: clazzName },
      { label: tabList.find((item) => item.value === activeTab)?.label! }
    ],
    [clazzName, activeTab]
  );

  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: undefined,
    periodType: undefined,
    ruleId: undefined
  });

  return (
    <Flex
      flexDir="column"
      w="100%"
      h="100%"
      bgColor="#ffffff"
      p={respDims(24)}
      borderRadius={respDims(20)}
    >
      <Flex justifyContent="space-between" alignItems="center" mx={respDims(32)} my={respDims(18)}>
        <Breadcrumb list={breadcrumbList} mb={respDims(18)} />

        <EvaluateFilterSelect
          menuId={menuId}
          clazzId={clazzId}
          value={filterForm}
          enterType={EvaluateEntryEnum.GoalAchievement}
          onChange={(value) => {
            setFilterForm(value);
          }}
        ></EvaluateFilterSelect>
      </Flex>

      <HStack spacing={respDims(16)} mb={respDims(20)}>
        <SubTabs alignSelf="flex-start" list={tabList} value={activeTab} onChange={setActiveTab} />
        <SubjectSelect
          clazzId={clazzId}
          ruleId={ruleId}
          menuId={menuId}
          semesterId={semesterId}
          value={subject}
          onChange={setSubject}
          hasAll={false}
        />
      </HStack>
      {activeTab === 'clazz' && (
        <ClazzPanel
          flex="1 0 0"
          clazzId={clazzId}
          semesterId={semesterId}
          menuId={menuId}
          ruleId={ruleId}
          subjectId={subject?.id || 'all'}
        />
      )}
      {activeTab === 'student' && (
        <StudentPanel
          flex="1 0 0"
          clazzId={clazzId}
          semesterId={semesterId}
          menuId={menuId}
          ruleId={ruleId}
          subjectId={subject?.id}
        />
      )}
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...deserializeData(context.query.q as string),
      ...(await serviceSideProps(context))
    }
  };
}

export default HomeworkRecord;
