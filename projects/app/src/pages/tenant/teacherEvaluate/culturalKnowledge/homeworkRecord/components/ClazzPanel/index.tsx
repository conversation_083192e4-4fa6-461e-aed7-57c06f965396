import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import Sidebar from './Sidebar';
import { respDims } from '@/utils/chakra';
import MyTable from '@/components/MyTable';
import dayjs from 'dayjs';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
  ClazzHomeworkRankListType,
  EvaluateHomeworkType
} from '@/types/api/tenant/evaluate/process';
import { getClazzHomeworkPage, getClazzHomeworkRankList } from '@/api/tenant/evaluate/process';
import { MyTableRef } from '@/components/MyTable/types';
const ClazzPanel = ({
  clazzId,
  semesterId,
  menuId,
  ruleId,
  subjectId,
  ...props
}: {
  clazzId: string;
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  subjectId?: string;
} & ChakraProps) => {
  const [homework, setHomework] = useState<EvaluateHomeworkType>();
  const tableRef = useRef<MyTableRef>(null);

  const columns = useMemo(() => {
    if (subjectId === 'all' || homework === undefined) {
      return [
        { title: '学生姓名', dataIndex: 'studentName' },
        { title: '总作业次数', dataIndex: 'homeworkCount' },
        {
          title: '总得分',
          dataIndex: 'maxScoreCount',
          render: (value: number, record: ClazzHomeworkRankListType) =>
            `${value} ${record.maxScoreName}`
        }
      ];
    }
    return [
      { title: '学生姓名', dataIndex: 'studentName' },
      { title: '点评人', dataIndex: 'evaluatorName' },
      { title: '评分', dataIndex: 'scoreLevelValue' },
      {
        title: '评分时间',
        dataIndex: 'updateTime',
        render: (text: string) => dayjs(text).format('YYYY/MM/DD HH:mm')
      }
    ];
  }, [subjectId, homework]);

  const api = useMemo(() => {
    if (subjectId === 'all' || homework === undefined) {
      return getClazzHomeworkRankList;
    }
    return getClazzHomeworkPage;
  }, [subjectId, homework]);

  const TableHeader = useCallback(
    () => (
      <Flex align="center">
        <Box
          w={respDims(7)}
          h={respDims('14fpx')}
          bgColor="#175DFF"
          borderRadius={respDims(8)}
        ></Box>
        <Box
          ml={respDims(6)}
          color="rgba(0,0,0,0.9)"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
          fontWeight="bold"
        >
          班级作业明细
        </Box>
      </Flex>
    ),
    []
  );

  return (
    <Flex border="1px solid #E5E7EB" borderRadius={respDims(14)} overflow="hidden" {...props}>
      {subjectId !== 'all' && (
        <Sidebar
          borderRight="1px solid 1px solid #E5E7EB"
          clazzId={clazzId}
          subjectId={subjectId}
          onChange={setHomework}
        />
      )}
      <MyTable
        ref={tableRef}
        columns={columns}
        rowKey="studentId"
        api={api}
        defaultQuery={{
          homeworkId: homework?.id || '',
          semesterId: semesterId || '',
          menuId: menuId || '',
          ruleId: ruleId || '',
          clazzId,
          current: !(subjectId === 'all' || homework === undefined) ? 1 : undefined,
          size: !(subjectId === 'all' || homework === undefined) ? 999 : undefined
        }}
        pageConfig={{ showPaginate: false }}
        boxStyle={{ p: respDims(16) }}
        headerConfig={{
          HeaderComponent: TableHeader
        }}
      />
    </Flex>
  );
};

export default ClazzPanel;
