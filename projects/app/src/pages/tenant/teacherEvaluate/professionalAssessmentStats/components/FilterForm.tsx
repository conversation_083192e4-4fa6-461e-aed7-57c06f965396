import { Box, Flex, HStack, Input, InputRightElement, InputGroup } from '@chakra-ui/react';
import { Select } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import { PeriodType, PeriodTypeMap } from '@/constants/api/tenant/evaluate/rule';
import { RuleListByClazzIdResponse } from '@/types/api/tenant/evaluate/process';
import styles from '../../../../index.module.scss';
import { EvaluateFilterValue } from '../../professionalAssessmentEvaluation';
import { getRuleList } from '@/api/tenant/evaluate/process';
import SemesterSelect from '../../components/SemesterSelect';
import { CloseCircleFilled } from '@ant-design/icons';
import SvgIcon from '@/components/SvgIcon';

const { Option } = Select;

export enum SourceType {
  Instant = 1,
  Period = 2
}

interface FilterFormProps {
  value: EvaluateFilterValue;
  onChange: (values: EvaluateFilterValue) => void;
  menuId?: string;
  reflectionId?: string;
  projectType?: 'teacherDetail' | 'teacherStats';
}

export const FilterForm = ({
  value,
  onChange,
  menuId,
  reflectionId,
  projectType
}: FilterFormProps) => {
  const [periodTypeList, setPeriodTypeList] = useState<PeriodType[]>([]);
  const [timerangeList, setTimerangeList] = useState<RuleListByClazzIdResponse[]>([]);
  const [searchInput, setSearchInput] = useState(value.evaluatedName || '');

  const {
    isLoading: isLoadingFrequencies,
    data: ruleList,
    refetch
  } = useQuery(
    ['frequencies', value.semester, menuId, reflectionId],
    async () => {
      if (!value.semester || !menuId || !reflectionId) return [];
      return getRuleList({
        semesterId: value.semester,
        menuId: menuId,
        reflectionId: reflectionId,
        sourceType: SourceType.Instant
      });
    },
    {
      enabled: !!(value.semester && menuId && reflectionId),
      staleTime: 1000 * 60 * 5
    }
  );

  const handleFormChange = useCallback(
    (field: keyof EvaluateFilterValue, fieldValue?: string | number) => {
      const newForm = { ...value };

      // 处理学期变化
      if (field === 'semester') {
        // 切换学期时，重置所有相关字段
        newForm.semester = fieldValue as string;
        newForm.periodType = undefined;
        newForm.ruleId = undefined;
        // 同时重置本地状态
        setPeriodTypeList([]);
        setTimerangeList([]);
      }
      // 处理周期类型变化
      else if (field === 'periodType') {
        newForm.periodType = fieldValue as number;
        newForm.ruleId = undefined;
        // 重置时间范围列表
        setTimerangeList([]);
      }
      // 处理规则ID变化
      else {
        newForm[field] = fieldValue as string;
      }

      onChange(newForm);
    },
    [value, onChange]
  );

  useEffect(() => {
    if (!ruleList?.length) {
      setPeriodTypeList([]);
      setTimerangeList([]);
      return;
    }

    const list = Array.from(new Set(ruleList.map((item) => item.periodType)));
    setPeriodTypeList(list);

    if (list.length) {
      if (!value.periodType || !list.includes(value.periodType)) {
        handleFormChange('periodType', list[0]);
      }
    }
  }, [ruleList, value.periodType]);

  useEffect(() => {
    if (!value.periodType || !ruleList?.length) {
      setTimerangeList([]);
      if (value.ruleId) {
        handleFormChange('ruleId', undefined);
      }
      return;
    }

    const filteredList = ruleList.filter((item) => item.periodType === value.periodType);
    setTimerangeList(filteredList);

    if (filteredList.length && !value.ruleId) {
      handleFormChange('ruleId', filteredList[0].ruleId.toString());
    }
  }, [value.periodType, ruleList]);

  const handleSearch = () => {
    handleFormChange('evaluatedName', searchInput);
  };
  const handleSearchDetail = () => {
    handleFormChange('evaluatorName', searchInput);
  };

  const handleClear = () => {
    setSearchInput('');
    handleFormChange('evaluatedName', '');
  };
  const handleClearDetail = () => {
    setSearchInput('');
    handleFormChange('evaluatorName', '');
  };

  return (
    <HStack className={`${styles['my-form']} ${styles['evaluate-filter-form']}`}>
      {projectType === 'teacherStats' && (
        <InputGroup w="200px">
          <Input
            placeholder="请输入被评价教师"
            h="32px"
            borderRadius="6px"
            backgroundColor="rgba(0, 0, 0, 0.03)"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            _placeholder={{ color: '#999' }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch();
              }
            }}
          />
          <InputRightElement width="50px" pr="20px">
            <Flex alignItems="center" h="100%" pl="20px">
              {searchInput && (
                <Box cursor="pointer" onClick={handleClear} color="#999" _hover={{ color: '#666' }}>
                  <SvgIcon name="close" width="16px" height="16px" />
                </Box>
              )}
              <Box
                ml={searchInput ? 2 : 0}
                cursor="pointer"
                onClick={handleSearch}
                color="#999"
                _hover={{ color: '#666' }}
              >
                <SvgIcon name="search" width="16px" height="16px" />
              </Box>
            </Flex>
          </InputRightElement>
        </InputGroup>
      )}
      {projectType === 'teacherDetail' && (
        <InputGroup w="200px">
          <Input
            placeholder="请输入评价人名称"
            h="32px"
            borderRadius="6px"
            backgroundColor="rgba(0, 0, 0, 0.03)"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearchDetail();
              }
            }}
          />
          <InputRightElement width="60px">
            <Flex alignItems="center" h="100%">
              {searchInput && (
                <Box
                  cursor="pointer"
                  onClick={handleClearDetail}
                  color="#999"
                  _hover={{ color: '#666' }}
                >
                  <SvgIcon name="close" width="16px" height="16px" />
                </Box>
              )}
              <Box
                ml={searchInput ? 2 : 0}
                cursor="pointer"
                onClick={handleSearchDetail}
                color="#999"
                _hover={{ color: '#666' }}
              >
                <SvgIcon name="search" width="16px" height="16px" />
              </Box>
            </Flex>
          </InputRightElement>
        </InputGroup>
      )}
      <SemesterSelect
        value={value.semester}
        onChange={(val) => handleFormChange('semester', val)}
      />

      <Select
        value={value.periodType}
        style={{ width: 200, border: 'none', borderRadius: '10px' }}
        placeholder="请选择频率"
        onChange={(val) => handleFormChange('periodType', val)}
        loading={isLoadingFrequencies}
      >
        {periodTypeList?.map((freq) => (
          <Option key={freq} value={freq}>
            {`评价频次:${PeriodTypeMap[freq].label}`}
          </Option>
        ))}
      </Select>

      <Select
        value={value.ruleId}
        style={{ width: 380, border: 'none', borderRadius: '10px' }}
        placeholder="请选择时间"
        onChange={(val) => handleFormChange('ruleId', val)}
      >
        {timerangeList?.map((time) => (
          <Option key={time.ruleId.toString()} value={time.ruleId.toString()}>
            {`评价时段：${time.startTime.split(':')[0]}:${time.startTime.split(':')[1]} - ${time.endTime.split(':')[0]}:${time.endTime.split(':')[1]}`}
          </Option>
        ))}
      </Select>
    </HStack>
  );
};

export default FilterForm;
