import { serviceSideProps } from '@/utils/i18n';
import { Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';
import { useRoutes } from '@/hooks/useRoutes';
import { useEffect, useMemo, useRef, useState } from 'react';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { TableProps } from 'antd';
import EvaluateFilterSelect, { EvaluateFilterValue } from '../../components/EvaluateFilterSelect';
import Breadcrumb from '../../components/Breadcrumb';
import { createStyles } from 'antd-style';
import { getTeacherDetailsPage } from '@/api/tenant/evaluate/process';
import { useRouter } from 'next/router';
import { TeacherDetailsType } from '@/types/api/tenant/evaluate/process';
import { ColumnsType } from 'antd/es/table';
import { Box } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { FilterForm } from '../components/FilterForm';
import { treeToList } from '@/utils/tree';
import { useSystemStore } from '@/store/useSystemStore';

const useStyle = createStyles(({ css, token }) => {
  const { antCls } = token as any;
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `
  };
});

const ProfessionalAssessmentStatsDetail = () => {
  const router = useRouter();
  const {
    evaluatedId,
    semester: routeSemester,
    periodType: routePeriodType,
    ruleId: routeRuleId
  } = router.query;
  const tableRef = useRef<MyTableRef>(null);
  const { routeGroup } = useRoutes();
  const { styles: stylesStatus } = useStyle();
  const { teacherName } = router.query;
  const { currentSemester, entryTree, setEntryTree } = useSystemStore();

  const { data: entranceTreeData } = useQuery(['entranceTree'], setEntryTree, {
    staleTime: 1000 * 60 * 5 // 5分钟
  });

  const reflectionId = useMemo(() => {
    const info = treeToList(entryTree).find(
      (item) =>
        item?.name ==
        evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name
    );
    return info?.id?.toString();
  }, [entryTree]);

  const breadcrumbList = useMemo(
    () => [{ label: '师德和专业考核评价数据' }, { label: teacherName as string }],
    []
  );

  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: (routeSemester as string) || currentSemester?.id,
    periodType: routePeriodType ? Number(routePeriodType) : undefined,
    ruleId: routeRuleId as string,
    evaluatorName: ''
  });

  const { data: apiData } = useQuery(
    ['teacherDetailsPage', filterForm],
    () =>
      getTeacherDetailsPage({
        ...filterForm,
        evaluatedId: evaluatedId as string,
        menuId: routeGroup?.authActiveRoute?.parent?.id!,
        semesterId: filterForm.semester || '',
        evaluatorName: filterForm.evaluatorName || ''
      }),
    {
      enabled:
        !!filterForm.semester &&
        !!filterForm.ruleId &&
        !!evaluatedId &&
        !!routeGroup?.authActiveRoute?.parent?.id
    }
  );

  const { tableData, columns: generateIndactorColumns } = useMemo(() => {
    const columns: ColumnsType<TeacherDetailsType> = [];

    const templateData = apiData?.records[0]?.projects;

    columns.push({
      title: templateData?.[0]?.projectName || '-',
      children:
        templateData?.[0]?.subEvaluations?.map((subEval, index) => ({
          title: subEval?.indicatorName || '-',
          dataIndex: `level${index + 1}`,
          key: `level${index + 1}`,
          render: (_, record) => {
            const value = record?.projects?.[0]?.subEvaluations?.[index]?.scoreLevelValue || '-';
            return (
              <Box
                ml={respDims(12)}
                bg={value !== 'A' ? '#EBF8FF' : 'transparent'}
                px={2}
                maxW={respDims(100)}
                py={1}
                borderRadius="md"
              >
                {value}
              </Box>
            );
          }
        })) || []
    });

    columns.push({
      title: templateData?.[1]?.projectName || '-',
      children:
        templateData?.[1]?.subEvaluations?.map((subEval, index) => ({
          title: subEval?.indicatorName || '-',
          dataIndex: `level${index + 1}`,
          key: `level${index + 1}`,
          render: (_, record) => {
            const value = record?.projects?.[1]?.subEvaluations?.[index]?.scoreLevelValue || '-';
            return (
              <Box
                ml={respDims(12)}
                bg={value !== 'A' ? '#EBF8FF' : 'transparent'}
                px={2}
                maxW={respDims(100)}
                py={1}
                borderRadius="md"
              >
                {value}
              </Box>
            );
          }
        })) || []
    });
    return {
      tableData: apiData?.records || [],
      columns: columns
    };
  }, [apiData]);

  const columns: TableProps<TeacherDetailsType>['columns'] = useMemo(() => {
    const baseColumns: ColumnsType<TeacherDetailsType> = [
      {
        title: '评价人姓名/评价明细项',
        key: 'evaluatorName',
        dataIndex: 'evaluatorName',
        fixed: 'left' as const,
        render: (_: any, record: TeacherDetailsType) => {
          return (
            <Box>
              {record.evaluatorName + (record.subjectName ? `(${record.subjectName})` : '')}
            </Box>
          );
        }
      },
      {
        title: '评价人',
        key: 'evaluatorName',
        dataIndex: 'evaluatorName',
        fixed: 'left' as const
      }
    ];
    const updateTimeColumns = [
      {
        title: '更新时间',
        key: 'updateTime',
        dataIndex: 'updateTime',
        render: (_: any, record: TeacherDetailsType) => {
          return <Box>{record.updateTime}</Box>;
        }
      }
    ];
    return [...baseColumns, ...generateIndactorColumns, ...updateTimeColumns];
  }, [generateIndactorColumns]);

  useEffect(() => {
    if (!routeSemester && currentSemester?.id && currentSemester.id !== filterForm.semester) {
      setFilterForm({
        semester: currentSemester.id,
        periodType: undefined,
        ruleId: undefined,
        evaluatorName: filterForm.evaluatorName || ''
      });
    }
  }, [currentSemester, routeSemester]);

  return (
    <Flex
      alignItems="center"
      backdropFilter={'blur(3.7px)'}
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      border="1px solid #FFF"
      direction="column"
      h="100%"
      borderRadius={respDims(20)}
      w="100%"
      overflow="hidden"
    >
      <Flex
        pl={respDims(32)}
        pr={respDims(32)}
        w="100%"
        alignItems="center"
        mt={respDims(23)}
        justifyContent="space-between"
      >
        <Breadcrumb list={breadcrumbList} />
        <FilterForm
          value={filterForm}
          onChange={setFilterForm}
          menuId={routeGroup?.authActiveRoute?.parent?.id}
          reflectionId={reflectionId}
          projectType="teacherDetail"
        />
      </Flex>

      <Flex bgColor="#fff" w="100%" h="100%" overflow="auto" mt={respDims(23)}>
        <MyTable
          columns={columns}
          api={getTeacherDetailsPage}
          rowKey="evaluatorId"
          ref={tableRef}
          defaultQuery={{
            ruleId: filterForm.ruleId || '',
            semesterId: filterForm.semester || '',
            menuId: routeGroup?.authActiveRoute?.parent?.id!,
            evaluatedId: evaluatedId as string,
            evaluatorName: filterForm.evaluatorName || ''
          }}
          queryConfig={{
            enabled:
              !!filterForm.semester &&
              !!filterForm.ruleId &&
              !!evaluatedId &&
              !!routeGroup?.authActiveRoute?.parent?.id
          }}
          className={stylesStatus.customTable}
          scroll={{ x: 'max-content' }}
          headerConfig={{
            showIfEmpty: true,
            showHeader: false
          }}
        />
      </Flex>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default ProfessionalAssessmentStatsDetail;
