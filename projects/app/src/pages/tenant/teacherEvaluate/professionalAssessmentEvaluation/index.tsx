import { serviceSideProps } from '@/utils/i18n';
import { Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateInfoType } from '@/constants/evaluate';
import { useRoutes } from '@/hooks/useRoutes';
import { useState, useEffect, useMemo } from 'react';
import Tabs from './components/Tabs';
import TeacherEvaluation from './components/TeacherEvaluation';
import {
  getClazzIndactorList,
  getEvaluatedBelong,
  getEvaluatedList
} from '@/api/tenant/evaluate/process';
import { useSystemStore } from '@/store/useSystemStore';
import { useQuery } from '@tanstack/react-query';
import { treeToList } from '@/utils/tree';
import { EvaluatedListType, EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import { FilterForm } from './components/FilterForm';
import { RuleListByClazzIdResponse } from '@/types/api/tenant/evaluate/process';

export type EvaluateFilterValue = {
  semester: string | undefined;
  periodType: number | undefined;
  ruleId: string | undefined;
  evaluatedName?: string | undefined;
  evaluatorName?: string | undefined;
};

const ProfessionalAssessment = () => {
  const { routeGroup } = useRoutes();
  const [currentTeachers, setCurrentTeachers] = useState<EvaluatedListType[]>([]);
  const [filterForm, setFilterForm] = useState<EvaluateFilterValue>({
    semester: undefined,
    periodType: undefined,
    ruleId: undefined,
    evaluatedName: undefined
  });
  const [activeTabKey, setActiveTabKey] = useState('all');
  const [tabItems, setTabItems] = useState<{ key: string; label: string }[]>([]);
  const { currentSemester } = useSystemStore();
  const { entryTree, setEntryTree } = useSystemStore();
  const [indactors, setIndactors] = useState<EvaluateIndactorType[]>([]);
  const [timerangeList, setTimerangeList] = useState<RuleListByClazzIdResponse[]>([]);

  useQuery(['entranceTree'], setEntryTree);

  const reflectionId = useMemo(() => {
    const info = treeToList(entryTree).find(
      (item) =>
        item?.name ==
        evaluateEntryMapEvaluateInfoType[EvaluateEntryEnum.ProfessionalAssessment].name
    );
    return info?.id?.toString();
  }, [entryTree]);

  useEffect(() => {
    if (currentSemester?.id && currentSemester.id !== filterForm.semester) {
      setFilterForm({
        semester: currentSemester.id,
        periodType: undefined,
        ruleId: undefined,
        evaluatedName: undefined
      });
    }
  }, [currentSemester]);

  const fetchTabItemsAndEvaluatedList = async (newForm: EvaluateFilterValue) => {
    try {
      if (!newForm.ruleId || !newForm.semester) {
        setTabItems([{ key: 'all', label: '全部' }]);
        setActiveTabKey('all');
        setIndactors([]);
        setCurrentTeachers([]);
        return;
      }

      const tabData = await getEvaluatedBelong({
        ruleId: newForm.ruleId,
        menuId: routeGroup?.authActiveRoute?.id!,
        semesterId: newForm.semester
      });

      const allTab = { key: 'all', label: '全部' };
      const updatedTabData = [
        allTab,
        ...tabData.map((item) => ({
          key: item.deptId || item.subjectId || '',
          label: item.deptId ? item.gradeName! + item.clazzName! : item.subjectName! + '学科组'
        }))
      ];

      setTabItems(updatedTabData);
      setActiveTabKey('all');

      const indactors = await getClazzIndactorList({
        ruleId: newForm.ruleId! || '',
        semesterId: newForm.semester!,
        menuId: routeGroup?.authActiveRoute?.id!
      });
      setIndactors(indactors);

      const teachers = await getEvaluatedList({
        menuId: routeGroup?.authActiveRoute?.id!,
        ruleId: newForm.ruleId!,
        semesterId: newForm.semester!
      });

      setCurrentTeachers(teachers);
    } catch (error) {
      setTabItems([{ key: 'all', label: '全部' }]);
      setActiveTabKey('all');
      setIndactors([]);
      setCurrentTeachers([]);
      console.error('Failed to fetch tab items or evaluated list:', error);
    }
  };

  const handleTabChange = async (key: string) => {
    setActiveTabKey(key);

    const selectedTab = tabItems.find((item) => item.key === key);

    if (selectedTab) {
      try {
        const isSubject = selectedTab.label.includes('学科组');

        const indactors = await getClazzIndactorList({
          deptId: selectedTab.key === 'all' ? undefined : isSubject ? undefined : selectedTab.key,
          ruleId: filterForm.ruleId!,
          semesterId: filterForm.semester!,
          menuId: routeGroup?.authActiveRoute?.id!
        });

        setIndactors(indactors);

        const teachers = await getEvaluatedList({
          deptId: selectedTab.key === 'all' ? undefined : isSubject ? undefined : selectedTab.key,
          menuId: routeGroup?.authActiveRoute?.id!,
          ruleId: filterForm.ruleId!,
          semesterId: filterForm.semester!,
          subjectId: selectedTab.key === 'all' ? undefined : isSubject ? selectedTab.key : undefined
        });

        setCurrentTeachers(teachers);
      } catch (error) {
        console.error('Failed to fetch evaluated list for selected tab:', error);
      }
    }
  };

  const onEvaluateSuccess = async () => {
    const teachers = await getEvaluatedList({
      menuId: routeGroup?.authActiveRoute?.id!,
      ruleId: filterForm.ruleId!,
      semesterId: filterForm.semester!
    });

    setCurrentTeachers(teachers);
  };

  return (
    <Flex
      alignItems="center"
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      direction="column"
      h="100%"
      backdropFilter={'blur(3.700000047683716px)'}
      borderTopLeftRadius={respDims(20)}
      border="1px solid #FFF"
      w="100%"
      overflow="hidden"
    >
      <Flex
        pl={respDims(32)}
        pr={respDims(32)}
        w="100%"
        alignItems="center"
        mt={respDims(23)}
        justifyContent="space-between"
      >
        <Flex overflowX="auto" overflowY="hidden" alignItems="center" mr="15px">
          <Tabs activeKey={activeTabKey} items={tabItems} onChange={handleTabChange} />
        </Flex>
        <Flex mb="12px">
          <FilterForm
            value={filterForm}
            onChange={setFilterForm}
            menuId={routeGroup?.authActiveRoute?.id}
            reflectionId={reflectionId}
            onFormChange={fetchTabItemsAndEvaluatedList}
            onTimerangeListChange={setTimerangeList}
          />
        </Flex>
      </Flex>

      <Flex
        bgColor="#fff"
        pt="24px"
        pr={respDims(32)}
        pl={respDims(32)}
        w="100%"
        h="100%"
        overflow="auto"
      >
        <TeacherEvaluation
          flex="1 0 0"
          isClazzTeacher={true}
          gradeId="1"
          clazzId="1"
          clazzName="三年级5班"
          menuId={routeGroup?.authActiveRoute?.id!}
          ruleId={filterForm.ruleId!}
          semesterId={filterForm.semester!}
          subjectId="1"
          indactors={indactors}
          teachers={currentTeachers}
          onEvaluateSuccess={onEvaluateSuccess}
          periodType={filterForm.periodType?.toString() || ''}
          relTime={
            filterForm.ruleId
              ? {
                  label: `${timerangeList?.find((item) => item.ruleId.toString() === filterForm.ruleId)?.startTime} 至 ${timerangeList?.find((item) => item.ruleId.toString() === filterForm.ruleId)?.endTime}`,
                  value: timerangeList?.find((item) => item.ruleId.toString() === filterForm.ruleId)
                    ?.startTime!,
                  getTime: () => ({
                    startTime: timerangeList?.find(
                      (item) => item.ruleId.toString() === filterForm.ruleId
                    )?.startTime!,
                    endTime: timerangeList?.find(
                      (item) => item.ruleId.toString() === filterForm.ruleId
                    )?.endTime!
                  })
                }
              : undefined
          }
        />
      </Flex>
    </Flex>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default ProfessionalAssessment;
