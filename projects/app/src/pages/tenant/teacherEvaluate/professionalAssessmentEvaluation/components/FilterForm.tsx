import { HStack } from '@chakra-ui/react';
import { Select } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import { PeriodType, PeriodTypeMap } from '@/constants/api/tenant/evaluate/rule';
import { RuleListByClazzIdResponse } from '@/types/api/tenant/evaluate/process';
import styles from '../../../../index.module.scss';
import { EvaluateFilterValue } from '../../professionalAssessmentEvaluation';
import { getRuleList } from '@/api/tenant/evaluate/process';
import SemesterSelect from '../../components/SemesterSelect';

const { Option } = Select;

export enum SourceType {
  Instant = 1,
  Period = 2
}

interface FilterFormProps {
  value: EvaluateFilterValue;
  onChange: (values: EvaluateFilterValue) => void;
  onFormChange?: (values: EvaluateFilterValue) => void;
  menuId?: string;
  reflectionId?: string;
  onTimerangeListChange?: (list: RuleListByClazzIdResponse[]) => void;
}

export const FilterForm = ({
  value,
  onChange,
  onFormChange,
  menuId,
  reflectionId,
  onTimerangeListChange
}: FilterFormProps) => {
  const [periodTypeList, setPeriodTypeList] = useState<PeriodType[]>([]);
  const [timerangeList, setTimerangeList] = useState<RuleListByClazzIdResponse[]>([]);

  const {
    isLoading: isLoadingFrequencies,
    data: ruleList,
    refetch
  } = useQuery(
    ['frequencies', value.semester, menuId, reflectionId],
    async () => {
      if (!value.semester || !menuId || !reflectionId) {
        setPeriodTypeList([]);
        setTimerangeList([]);
        onChange({
          ...value,
          periodType: undefined,
          ruleId: undefined
        });
        return [];
      }

      const data = await getRuleList({
        semesterId: value.semester,
        menuId: menuId,
        reflectionId: reflectionId,
        sourceType: SourceType.Period
      });

      if (!data || data.length === 0) {
        setPeriodTypeList([]);
        setTimerangeList([]);
        onChange({
          ...value,
          periodType: undefined,
          ruleId: undefined
        });
        return [];
      }

      if (data.length > 0) {
        const newForm = {
          ...value,
          periodType: data[0].periodType,
          ruleId: data[0].ruleId.toString()
        };
        onChange(newForm);
        onFormChange?.(newForm);
      }

      return data;
    },
    {
      enabled: !!(value.semester && menuId && reflectionId),
      staleTime: 1000 * 60 * 5
    }
  );

  const handleFormChange = useCallback(
    (field: keyof EvaluateFilterValue, fieldValue?: string | number) => {
      const newForm = { ...value };

      if (field === 'semester') {
        setPeriodTypeList([]);
        setTimerangeList([]);
        onTimerangeListChange?.([]);

        onChange({
          semester: fieldValue as string,
          periodType: undefined,
          ruleId: undefined,
          evaluatedName: undefined
        });
        return;
      } else if (field === 'periodType') {
        newForm.periodType = fieldValue as number;
        newForm.ruleId = undefined;
        setTimerangeList([]);
      } else {
        newForm[field] = fieldValue as string;
      }

      onChange(newForm);
      if (field === 'ruleId' && newForm.ruleId) {
        onFormChange?.(newForm);
      }
    },
    [value, onChange, onFormChange, onTimerangeListChange]
  );

  useEffect(() => {
    if (!ruleList?.length) {
      setPeriodTypeList([]);
      setTimerangeList([]);
      return;
    }

    const list = Array.from(new Set(ruleList.map((item) => item.periodType)));
    setPeriodTypeList(list);

    if (list.length) {
      if (!value.periodType || !list.includes(value.periodType)) {
        handleFormChange('periodType', list[0]);
      }
    }
  }, [ruleList, value.periodType]);

  useEffect(() => {
    if (value.periodType && ruleList?.length) {
      const filteredList = ruleList.filter((item) => item.periodType === value.periodType);
      setTimerangeList(filteredList);
      onTimerangeListChange?.(filteredList);

      if (filteredList.length && !value.ruleId) {
        handleFormChange('ruleId', filteredList[0].ruleId.toString());
      }
    } else {
      setTimerangeList([]);
      onTimerangeListChange?.([]);
    }
  }, [value.periodType, ruleList, onTimerangeListChange]);

  return (
    <HStack className={`${styles['my-form']} ${styles['evaluate-filter-form']}`}>
      <SemesterSelect
        value={value.semester}
        onChange={(val) => handleFormChange('semester', val)}
      />

      <Select
        value={value.periodType}
        style={{ width: 200, border: 'none', borderRadius: '10px' }}
        placeholder="请选择频率"
        onChange={(val) => handleFormChange('periodType', val)}
        loading={isLoadingFrequencies}
      >
        {periodTypeList?.map((freq) => (
          <Option key={freq} value={freq}>
            {`评价频次:${PeriodTypeMap[freq].label}`}
          </Option>
        ))}
      </Select>

      <Select
        value={value.ruleId}
        style={{ width: 380, border: 'none', borderRadius: '10px' }}
        placeholder="请选择时间"
        onChange={(val) => handleFormChange('ruleId', val)}
      >
        {timerangeList?.map((time) => (
          <Option key={time.ruleId.toString()} value={time.ruleId.toString()}>
            {`评价时段：${time.startTime.split(':')[0]}:${time.startTime.split(':')[1]} - ${time.endTime.split(':')[0]}:${time.endTime.split(':')[1]}`}
          </Option>
        ))}
      </Select>
    </HStack>
  );
};

export default FilterForm;
