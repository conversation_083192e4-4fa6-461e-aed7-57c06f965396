import SvgIcon from '@/components/SvgIcon';
import { EvaluatedListType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import {
  Box,
  ChakraProps,
  Flex,
  Image,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement
} from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';

const TeacherStatusType = {
  notStart: 0,
  processing: 1,
  completed: 2
};
const Sidebar = ({
  semesterId,
  ruleId,
  menuId,
  teacher,
  teachers,
  onChange,
  ...props
}: {
  menuId: string;
  semesterId: string;
  ruleId: string;
  teacher?: EvaluatedListType;
  onChange?: (teacher?: EvaluatedListType) => void;
  teachers: EvaluatedListType[];
} & ChakraProps) => {
  const [activeId, setActiveId] = useState<string>();
  const [searchKey, setSearchKey] = useState('');

  const filteredStudents = useMemo(() => {
    return teachers.filter((teacher) =>
      teacher.userName.toLowerCase().includes(searchKey.toLowerCase())
    );
  }, [teachers, searchKey]);

  const onClickStudent = (teacher: EvaluatedListType) => {
    if (teacher.userId === activeId) {
      return;
    }
    setActiveId(teacher.userId);
    onChange?.(teacher);
  };

  useEffect(() => {
    if (!teachers.length) {
      setActiveId(undefined);
      onChange?.(undefined);
      return;
    }

    const currentTeacherExists = activeId && teachers.some((t) => t.userId === activeId);
    if (!currentTeacherExists) {
      const firstTeacher = filteredStudents.length > 0 ? filteredStudents[0] : teachers[0];
      setActiveId(firstTeacher.userId);
      onChange?.(firstTeacher);
    }
  }, [filteredStudents, activeId, teachers]);

  return (
    <Flex flexDir="column" w={respDims(310, '.8ms')} h="97%" py="16px" bgColor="#F9FAFB" {...props}>
      <InputGroup h={respDims('36fpx')} mx={respDims(8)} w="auto">
        <Input
          w="100%"
          borderRadius={respDims(50)}
          bgColor="rgba(0,0,0,0.03)"
          boxShadow="none"
          border="none"
          outline="none"
          _placeholder={{
            color: '#999'
          }}
          _focus={{
            bgColor: 'rgba(0,0,0,0.03)',
            border: 'none',
            outline: 'none',
            boxShadow: 'none'
          }}
          placeholder="请输入教师姓名"
          value={searchKey}
          onChange={(e) => setSearchKey(e.target.value)}
        />
        <InputRightElement>
          <SvgIcon name="search" color="#999" />
        </InputRightElement>
      </InputGroup>

      <Flex mt={respDims(14)} flex="1 0 0" flexDir="column" overflow="auto" pos="relative">
        {filteredStudents.length > 0 && menuId && ruleId ? (
          filteredStudents.map((item, index) => (
            <Flex
              key={item.userId}
              fontSize={respDims('15fpx')}
              lineHeight={respDims('22fpx')}
              fontWeight="bold"
              pl={respDims(16)}
              pr={respDims(20)}
              py={respDims(10)}
              mx={respDims(8)}
              alignItems="center"
              mt={index > 0 ? respDims(4) : 0}
              borderRadius={respDims(8)}
              cursor="pointer"
              {...(item.userId === activeId
                ? {
                    color: '#3366ff',
                    bgColor: '#FFFFFF'
                  }
                : {
                    color: '#303133'
                  })}
              _hover={{ bgColor: '#FFFFFF', '.edit-icon': { visibility: 'visible' } }}
              onClick={() => onClickStudent(item)}
            >
              <Box borderRadius="50%" overflow="hidden" bgColor="#F2F6FF" mr={respDims(8)}>
                <Image
                  src={item.avatar || '/imgs/evaluate/default_teacher.png'}
                  w="40px"
                  h="40px"
                />
              </Box>
              <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
                {item.userName}
              </Box>
              <Box
                color={
                  item.evaluationList.length
                    ? '#00B42A'
                    : item.status === TeacherStatusType.completed &&
                        item.evaluationList.length === 0
                      ? '#303133'
                      : '#3491FA'
                }
                fontSize="14px"
                lineHeight="22px"
                fontWeight="500"
                bgColor={
                  item.evaluationList.length
                    ? '#E8FFEA'
                    : item.status === TeacherStatusType.completed &&
                        item.evaluationList.length === 0
                      ? '#4E5969'
                      : '#E8F7FF'
                }
                px="8px"
                borderRadius="4px"
              >
                {item.evaluationList.length
                  ? '已评价'
                  : item.status === TeacherStatusType.completed && item.evaluationList.length === 0
                    ? '未评价'
                    : '待评价'}
              </Box>
            </Flex>
          ))
        ) : (
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            color="#a9a9ac"
          >
            暂无教师
          </Box>
        )}
      </Flex>
    </Flex>
  );
};

export default Sidebar;
