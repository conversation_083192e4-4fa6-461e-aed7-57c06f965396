import { respDims } from '@/utils/chakra';
import { useState, useRef, useEffect, useMemo } from 'react';
import { Box, Center, HStack, Text, Flex, Grid, Portal } from '@chakra-ui/react';
import SvgIcon from '@/components/SvgIcon';

const Tabs = ({
  activeKey,
  items,
  onChange
}: {
  activeKey?: string;
  items: { key: string; label: string }[];
  onChange?: (key: string) => void;
  minW?: string;
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedFromMore, setSelectedFromMore] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const moreRef = useRef<HTMLDivElement>(null);

  // 前4个固定的tab
  const fixedTabs = items.slice(0, 4);
  // 剩余的tab，可能在更多中显示
  const moreTabs = items.slice(4);

  // 判断当前激活的tab是否在前4个
  const isActiveInFixed = fixedTabs.some((item) => item.key === activeKey);

  // 当我们选中的是更多中的一项，在第5个位置显示它
  const fifthTabItem = useMemo(() => {
    // 如果有从更多中选择的项目
    if (selectedFromMore && moreTabs.some((item) => item.key === selectedFromMore)) {
      return moreTabs.find((item) => item.key === selectedFromMore);
    }
    // 否则返回null
    return null;
  }, [selectedFromMore, moreTabs]);

  // 点击"更多"下的选项
  const handleMoreItemClick = (key: string) => {
    onChange?.(key);
    setSelectedFromMore(key);
    setShowDropdown(false);
  };

  // 点击固定的tab
  const handleFixedTabClick = (key: string) => {
    // 如果点击的是固定的tab，重置selectedFromMore
    setSelectedFromMore(null);
    onChange?.(key);
  };

  // 点击已选择的"更多"项
  const handleSelectedMoreItemClick = () => {
    // 打开下拉菜单
    setShowDropdown(true);
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        moreRef.current &&
        !moreRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <HStack spacing={respDims(32)} position="relative">
      {/* 显示前4个固定Tab */}
      {fixedTabs.map((item) => (
        <Center
          key={item.key}
          h={respDims('40fpx')}
          boxSizing="border-box"
          fontSize={respDims('15fpx')}
          minW="fit-content"
          {...(item.key === activeKey
            ? {
                color: '#165dff',
                fontWeight: 'bold',
                borderBottom: '2px solid',
                borderColor: 'primary.500'
              }
            : {
                color: '#4E5969',
                borderBottom: '2px solid transparent'
              })}
          cursor="pointer"
          onClick={() => item.key !== activeKey && handleFixedTabClick(item.key)}
        >
          {item.label}
        </Center>
      ))}

      {/* 第5个位置：显示选中的更多项或"更多"按钮 */}
      {moreTabs.length > 0 &&
        (fifthTabItem ? (
          // 显示从更多中选择的项目，点击时打开下拉菜单
          <Flex
            ref={moreRef}
            alignItems="center"
            h={respDims('40fpx')}
            color={fifthTabItem.key === activeKey ? '#165dff' : '#4E5969'}
            fontWeight={fifthTabItem.key === activeKey ? 'bold' : 'normal'}
            borderBottom={fifthTabItem.key === activeKey ? '2px solid' : '2px solid transparent'}
            borderColor={fifthTabItem.key === activeKey ? 'primary.500' : 'transparent'}
            cursor="pointer"
            onClick={handleSelectedMoreItemClick}
          >
            <Text fontSize={respDims('15fpx')}>{fifthTabItem.label}</Text>
            <SvgIcon name={showDropdown ? 'chevronUp' : 'chevronDown'} w="16px" h="16px" ml="2px" />
          </Flex>
        ) : (
          // 显示"更多"按钮
          <Flex
            ref={moreRef}
            alignItems="center"
            h={respDims('40fpx')}
            color={showDropdown ? '#165dff' : '#4E5969'}
            fontWeight={showDropdown ? 'bold' : 'normal'}
            borderBottom={showDropdown ? '2px solid' : '2px solid transparent'}
            borderColor={showDropdown ? 'primary.500' : 'transparent'}
            cursor="pointer"
            onClick={() => setShowDropdown(!showDropdown)}
          >
            <Text fontSize="16px" fontWeight="400" color="#36F">
              更多
            </Text>
            <SvgIcon
              name={showDropdown ? 'chevronUp' : 'chevronDown'}
              w="16px"
              color="#36F"
              h="16px"
              ml="2px"
            />
          </Flex>
        ))}

      {/* 下拉菜单 - 网格布局 */}
      {showDropdown && (
        <Portal>
          <Box
            ref={dropdownRef}
            position="absolute"
            top="140px"
            left="544px"
            bg="white"
            boxShadow="0px 4px 10px 0px rgba(0, 0, 0, 0.10);"
            borderRadius="10px"
            p="16px"
            zIndex={1000}
            border="1px solid #E5E6EB"
            width="262px"
            maxHeight="277px"
            overflowY="auto"
          >
            <Grid templateColumns="repeat(2, 1fr)" gap="13px">
              {moreTabs.map((item) => (
                <Box
                  key={item.key}
                  p="8px 10px"
                  textAlign="center"
                  fontSize={respDims('14fpx')}
                  bg="#F2F3F5"
                  color={item.key === activeKey ? '#165dff' : '#1D2129'}
                  border={item.key === activeKey ? '1px solid #165dff' : '1px solid #F2F3F5'}
                  fontWeight={item.key === activeKey ? 'bold' : '400'}
                  borderRadius="8px"
                  cursor="pointer"
                  _hover={{ bg: 'rgba(0, 0, 0, 0.05)' }}
                  onClick={() => handleMoreItemClick(item.key)}
                >
                  {item.label}
                </Box>
              ))}
            </Grid>
          </Box>
        </Portal>
      )}
    </HStack>
  );
};

export default Tabs;
