import { ChakraProps, Flex, Box } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useEffect, useState } from 'react';
import Sidebar from './Sidebar';
import { EvaluatedListType, EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import Stats from './Stats';
import { RelTimeType } from '../../components/RelTimeSelect';

const TeacherEvaluation = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  menuId,
  ruleId,
  semesterId,
  periodType,
  relTime,
  indactors,
  teachers = [],
  onEvaluateSuccess,
  ...props
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  menuId: string;
  ruleId: string;
  semesterId: string;
  periodType: string;
  teachers: EvaluatedListType[];
  indactors: EvaluateIndactorType[];
  relTime?: RelTimeType;
  onEvaluateSuccess?: () => void;
} & ChakraProps) => {
  const [teacher, setTeacher] = useState<EvaluatedListType | undefined>(() => teachers[0]);

  useEffect(() => {
    if (!teachers.length) {
      setTeacher(undefined);
    } else if (!teacher) {
      setTeacher(teachers[0]);
    }
  }, [teachers, teacher]);

  return (
    <Flex align="stretch" borderRadius={respDims(14)} overflow="hidden" h="100%" {...props}>
      <Sidebar
        border="1px solid #E5E7EB"
        borderRadius="14px"
        semesterId={semesterId}
        ruleId={ruleId}
        teachers={teachers}
        menuId={menuId}
        teacher={teacher}
        onChange={setTeacher}
      />
      <Stats
        flex="1 0 0"
        type="student"
        title={teacher?.userName}
        subTitle={relTime?.label}
        isClazzTeacher={isClazzTeacher}
        gradeId={gradeId}
        clazzId={clazzId}
        indactors={indactors}
        clazzName={clazzName}
        subjectId={subjectId}
        periodType={periodType}
        menuId={menuId}
        onSuccess={() => {
          onEvaluateSuccess?.();
        }}
        ruleId={ruleId}
        semesterId={semesterId}
        evaluationList={teacher?.evaluationList || []}
        status={teacher?.status!}
        evaluatedId={teacher?.tmbId}
        relTime={relTime}
      />
    </Flex>
  );
};

export default TeacherEvaluation;
