import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { Fragment } from 'react';
import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import { useRouter } from 'next/router';

export type BreadcrumbItemType = {
  label: string;
};

const Breadcrumb = <T extends BreadcrumbItemType>({
  list,
  fontSize,
  lineHeight,
  onBack,
  ...props
}: {
  list: T[];
  onBack?: () => void;
} & ChakraProps) => {
  const router = useRouter();
  return (
    <Flex
      alignItems="center"
      fontSize={fontSize ?? respDims('14fpx')}
      lineHeight={lineHeight ?? respDims('22fpx')}
      overflow="hidden"
      {...props}
    >
      {list.map((item, index) => (
        <Fragment key={`${index}-${item.label}`}>
          {index === 0 && (
            <Flex
              color="#1A5EFF"
              alignItems="center"
              cursor="pointer"
              onClick={() => (onBack ? onBack() : router.back())}
            >
              <SvgIcon name="chevronLeft" w={respDims('16fpx')} h={respDims('16fpx')} />
              <Box ml={respDims(4)}>返回</Box>
            </Flex>
          )}

          <SvgIcon
            name="slash"
            w={respDims('16fpx')}
            h={respDims('16fpx')}
            mx={respDims(4)}
            color="#606266"
          />

          <Box
            {...(index === list.length - 1
              ? { color: '#303133', fontWeight: 'bold', flexShrink: '0' }
              : { color: '#606266', overflow: 'hidden', textOverflow: 'ellipsis' })}
          >
            {item.label}
          </Box>
        </Fragment>
      ))}
    </Flex>
  );
};

export default Breadcrumb;
