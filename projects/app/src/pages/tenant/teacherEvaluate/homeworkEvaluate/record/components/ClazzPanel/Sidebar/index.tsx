import { getHomeworkPage } from '@/api/tenant/evaluate/process';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';
import { getListFromPage } from '@/utils/api';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

const Sidebar = ({
  onChange,
  clazzId,
  subjectId,
  ...props
}: {
  clazzId: string;
  subjectId?: string;
  onChange?: (homework?: EvaluateHomeworkType) => void;
} & ChakraProps) => {
  const [activeId, setActiveId] = useState<string>();

  const { data: homeworks = [] } = useQuery(
    ['homeworks', clazzId, subjectId],
    () =>
      getListFromPage(getHomeworkPage, {
        clazzId,
        subjectId
      }),
    {
      enabled: !!clazzId && !!subjectId
    }
  );

  const onClickHomework = (homework: EvaluateHomeworkType) => {
    if (homework.id === activeId) {
      return;
    }
    setActiveId(homework.id);
    onChange?.(homework);
  };

  useEffect(() => {
    if (!homeworks.some((it) => it.id === activeId)) {
      setActiveId(homeworks[0]?.id);
      onChange?.(homeworks[0]);
    }
  }, [activeId, homeworks]);

  return (
    <Flex flexDir="column" w={respDims(229, '.8ms')} py={respDims(4)} bgColor="#F9FAFB" {...props}>
      <Flex flex="1 0 0" flexDir="column" overflow="auto">
        {homeworks.map((item, index) => (
          <Flex
            key={item.id}
            fontSize={respDims('15fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
            pl={respDims(16)}
            pr={respDims(20)}
            py={respDims(10)}
            mx={respDims(8)}
            mt={index > 0 ? respDims(4) : 0}
            borderRadius={respDims(8)}
            cursor="pointer"
            {...(item.id === activeId
              ? {
                  color: '#3366ff',
                  bgColor: '#FFFFFF'
                }
              : {
                  color: '#303133'
                })}
            _hover={{
              bgColor: '#FFFFFF'
            }}
            onClick={() => onClickHomework(item)}
          >
            <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {item.name}
            </Box>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};

export default Sidebar;
