import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { useEffect, useState } from 'react';

export type NavType = {
  type: 'record' | 'rank';
  label: string;
};

export const Navs: NavType[] = [
  {
    label: '点评记录',
    type: 'record'
  },
  {
    label: '评价排行',
    type: 'rank'
  }
];

const Sidebar = ({ onChange, ...props }: { onChange?: (nav: NavType) => void } & ChakraProps) => {
  const [active, setActive] = useState<string>();

  const onClickNav = (nav: NavType) => {
    if (nav.type === active) {
      return;
    }
    setActive(nav.type);
    onChange?.(nav);
  };

  useEffect(() => {
    if (!active) {
      setActive(Navs[0].type);
      onChange?.(Navs[0]);
    }
  }, [Navs, active]);

  return (
    <Flex flexDir="column" w={respDims(229, '.8ms')} py={respDims(4)} bgColor="#F9FAFB" {...props}>
      <Flex mt={respDims(14)} flex="1 0 0" flexDir="column" overflow="auto">
        {Navs.map((item, index) => (
          <Flex
            key={item.type}
            fontSize={respDims('15fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
            pl={respDims(16)}
            pr={respDims(20)}
            py={respDims(10)}
            mx={respDims(8)}
            mt={index > 0 ? respDims(4) : 0}
            borderRadius={respDims(8)}
            cursor="pointer"
            {...(item.type === active
              ? {
                  color: '#3366ff',
                  bgColor: '#FFFFFF'
                }
              : {
                  color: '#303133'
                })}
            _hover={{
              bgColor: '#FFFFFF'
            }}
            onClick={() => onClickNav(item)}
          >
            <Box flex="1 0 0" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
              {item.label}
            </Box>
          </Flex>
        ))}
      </Flex>
    </Flex>
  );
};

export default Sidebar;
