import { EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, Center, ChakraProps, Checkbox, Flex, Image, Text } from '@chakra-ui/react';
import StudentAvatar from '../StudentAvatar';
import { EvaluateEntryEnum, evaluateEntryMapEvaluateType } from '@/constants/evaluate';
import { EvaluationType } from '@/constants/api/tenant/evaluate/rule';

type StudentProps = {
  type?: 'normal' | 'evaluate' | 'seat';
  student?: EvaluateStudentType;
  isCheckable?: boolean;
  isClickCheck?: boolean;
  isChecked?: boolean;
  avatarBgColor?: string;
  entryType: EvaluateEntryEnum;
  onCheckChange?: (student: EvaluateStudentType, isChecked: boolean) => void;
  onClick?: (student: EvaluateStudentType) => void;
} & ChakraProps;

const StudentBoxDms = '.8ms';
const StudentBoxW = respDims(80, StudentBoxDms);
const StudentHoverBoxW = respDims(80 + 34 * 2, StudentBoxDms);

const Student = ({
  type = 'normal',
  student,
  isCheckable,
  isClickCheck,
  isChecked,
  entryType,
  avatarBgColor = 'primary.50',
  onCheckChange,
  onClick,
  ...props
}: StudentProps) => {
  const totalScore = student?.evaluationList?.reduce(
    (acc, evaluation) => acc + evaluation.score,
    0
  );

  const scoreSummary = student?.evaluationList?.reduce(
    (acc, evaluation) => {
      if (evaluation.score > 0) acc.positive += evaluation.score;
      if (evaluation.score < 0) acc.negative += evaluation.score;
      return acc;
    },
    { positive: 0, negative: 0 }
  );

  return (
    <Center
      w={StudentBoxW}
      onClick={() => {
        if (!student) {
          return;
        }
        if (isCheckable && isClickCheck) {
          onCheckChange?.(student, !isChecked);
        } else {
          onClick?.(student);
        }
      }}
      cursor="pointer"
      {...props}
    >
      <Flex w={StudentBoxW} direction="column" align="center" pos="relative">
        {type === 'seat' ? (
          <Image
            src="/imgs/evaluate/seat_empty.svg"
            w={StudentBoxW}
            h={StudentBoxW}
            alt=""
            borderRadius="50%"
            draggable={false}
          />
        ) : (
          <StudentAvatar
            student={student}
            w={StudentBoxW}
            h={StudentBoxW}
            borderRadius="50%"
            bgColor={avatarBgColor}
          />
        )}

        {type === 'evaluate' && !!student && (
          <Flex
            mt={respDims(-11, StudentBoxDms)}
            w="100%"
            h={respDims(26, StudentBoxDms)}
            align="center"
            bgColor="#FFF6CC"
            fontSize={respDims(14, StudentBoxDms)}
            lineHeight={respDims(22, StudentBoxDms)}
            borderRadius={respDims(50, StudentBoxDms)}
            pos="relative"
          >
            {evaluateEntryMapEvaluateType[entryType] === EvaluationType.Comment && (
              <>
                {student.evaluationList?.length ? (
                  <Center w="100%" color="#71D4FF">
                    已评价
                  </Center>
                ) : (
                  <Center w="100%" color="#FF8471">
                    待评价
                  </Center>
                )}
              </>
            )}
            {evaluateEntryMapEvaluateType[entryType] === EvaluationType.ScoreLevelValue && (
              <>
                <Center w="100%" color={student.evaluationList?.length ? '#71D4FF' : '#FF8471'}>
                  {entryType === EvaluateEntryEnum.HomeworkEvaluation &&
                  student.evaluationList?.length
                    ? student.evaluationList[student.evaluationList.length - 1].scoreLevelValue
                    : student.evaluationList?.length
                      ? '已评价'
                      : ' 待评价 '}
                </Center>
              </>
            )}
            {evaluateEntryMapEvaluateType[entryType] === EvaluationType.Score && (
              <>
                {student.evaluationList?.length ? (
                  student.evaluationList.every((evaluation) => evaluation.score === 0) ? (
                    <Center flex="1 0 0" color="#606266">
                      0
                    </Center>
                  ) : (
                    <>
                      {scoreSummary?.positive! > 0 && (
                        <Center flex="1 0 0" color="#71D4FF" fontWeight="500">
                          {scoreSummary?.positive}
                        </Center>
                      )}
                      {!!(scoreSummary?.positive! > 0 && scoreSummary?.negative! < 0) && (
                        <Box w="1px" h="100%" bgColor="#FFE984" />
                      )}
                      {scoreSummary?.negative! < 0 && (
                        <Center flex="1 0 0" color="#FF8471" fontWeight="500">
                          {scoreSummary?.negative}
                        </Center>
                      )}
                    </>
                  )
                ) : (
                  <>
                    {!!student.good && (
                      <Center flex="1 0 0" color="#71D4FF" fontWeight="500">
                        {student.good}
                      </Center>
                    )}

                    {!!(student.good && student.bad) && <Box w="1px" h="100%" bgColor="#FFE984" />}

                    {!!student.bad && (
                      <Center flex="1 0 0" color="#FF8471" fontWeight="500">
                        {student.bad}
                      </Center>
                    )}

                    {!student.good && !student.bad && (
                      <Center flex="1 0 0" color="#606266">
                        0
                      </Center>
                    )}
                  </>
                )}
              </>
            )}
          </Flex>
        )}

        <Center
          mt={respDims(6, StudentBoxDms)}
          w="100%"
          color="#303133"
          fontSize={respDims(16, StudentBoxDms)}
          fontWeight="bold"
          whiteSpace="nowrap"
        >
          {type === 'seat'
            ? '空位'
            : student
              ? student.studentCode
                ? `${student.studentName}(${student.studentCode.substring(student.studentCode.length - 2)})`
                : student.studentName
              : '　'}
        </Center>

        {isCheckable && !!student && (
          <Box pos="absolute" top="0" right="0" onClick={(e) => e.stopPropagation()}>
            <Checkbox
              colorScheme="primary"
              size="lg"
              isChecked={isChecked}
              bgColor="rgba(255,255,255,0.9)"
              onChange={(e) => onCheckChange?.(student, e.target.checked)}
            />
          </Box>
        )}
      </Flex>
    </Center>
  );
};

Student.BoxW = StudentBoxW;

// eslint-disable-next-line react/display-name
Student.Hover = (props: StudentProps) => {
  return (
    <Student
      {...props}
      w={StudentHoverBoxW}
      py={respDims(14, StudentBoxDms)}
      borderRadius={respDims(14, StudentBoxDms)}
      boxSizing="border-box"
      border="1px solid transparent"
      {...(props.type !== 'seat'
        ? {
            _hover: {
              boxShadow: '0px 1px 11px 0px rgba(0,0,0,0.09)',
              border: '1px solid #D6E4FF'
            }
          }
        : { cursor: 'default' })}
    />
  );
};

Student.HoverBoxW = StudentHoverBoxW;

export default Student;
