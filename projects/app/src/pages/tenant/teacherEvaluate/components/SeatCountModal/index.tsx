import { getClazzStudentPage } from '@/api/tenant/evaluate/process';
import MyModal from '@/components/MyModal';
import { getListFromPage } from '@/utils/api';
import { Toast } from '@/utils/ui/toast';
import { AddIcon, MinusIcon } from '@chakra-ui/icons';
import { Box, Button, Center, Flex, HStack, IconButton } from '@chakra-ui/react';
import { useState } from 'react';

export type SeatDims = {
  rowCount: number;
  columnCount: number;
};

const SeatCountModal = ({
  onSuccess,
  onClose,
  clazzId
}: {
  onSuccess?: (seatDims: SeatDims) => void;
  onClose?: () => void;
  clazzId?: string;
}) => {
  const [list, setList] = useState([
    { label: '座位列', value: 1, key: 'columnCount' as keyof SeatDims },
    { label: '座位排', value: 1, key: 'rowCount' as keyof SeatDims }
  ]);

  const onSubmit = async () => {
    const seatDims: SeatDims = { rowCount: 1, columnCount: 1 };
    list.forEach((item) => {
      seatDims[item.key] = item.value;
    });

    if (clazzId) {
      const students = await getListFromPage(getClazzStudentPage, { clazzId });
      if (students.length <= seatDims.columnCount * seatDims.rowCount) {
      } else {
        return Toast.warning('当前座位数小于班级学生数量');
      }
    }
    onSuccess?.(seatDims);
    onClose?.();
  };

  return (
    <MyModal
      title="设置座位"
      isOpen
      isCentered
      onClose={onClose}
      w="540px"
      bgImage="/imgs/evaluate/modal_bg.png"
      bgSize="100% 40%"
      bgRepeat="no-repeat"
      headerStyle={{
        background: 'transparent',
        borderBottom: '1px solid #E5E7EB'
      }}
    >
      <Box px="32px" py="24px">
        {list.map((item, index) => (
          <Flex key={item.key} justify="center" align="center" mt={index > 0 ? '28px' : '66px'}>
            <Box color="#4E5969" fontSize="14px">
              {item.label}
            </Box>
            <IconButton
              ml="20px"
              w="32px"
              h="32px"
              minW="0"
              variant="outline"
              icon={<MinusIcon />}
              aria-label=""
              borderRadius="50%"
              onClick={() =>
                item.value - 1 &&
                setList((state) =>
                  state.map((it) => (it.key === item.key ? { ...it, value: it.value - 1 } : it))
                )
              }
            />
            <Center ml="12px" w="56px" color="#303133" fontSize="16px">
              {item.value}
            </Center>

            <IconButton
              ml="12px"
              w="32px"
              h="32px"
              minW="0"
              variant="outline"
              icon={<AddIcon />}
              aria-label=""
              borderRadius="50%"
              onClick={() => {
                setList((state) =>
                  state.map((it) => (it.key === item.key ? { ...it, value: it.value + 1 } : it))
                );
              }}
            />
          </Flex>
        ))}

        <HStack justify="flex-end" mt="66px" spacing="16px">
          <Button variant="grayBase" onClick={onClose}>
            取消
          </Button>
          <Button onClick={onSubmit}>确定</Button>
        </HStack>
      </Box>
    </MyModal>
  );
};

export default SeatCountModal;
