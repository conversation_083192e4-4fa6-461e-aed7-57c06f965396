import { Select } from 'antd';
import { useSystemStore } from '@/store/useSystemStore';
import { respDims } from '@/utils/chakra';
import { useQuery } from '@tanstack/react-query';
import { HStack } from '@chakra-ui/react';
import { SelectProps } from 'antd/lib';

const { Option } = Select;

interface SemesterSelectProps extends SelectProps {
  value: string | undefined;
  onChange: (value: string | undefined) => void;
  onSelect?: (value: string | undefined) => void;
}

const SemesterSelect: React.FC<SemesterSelectProps> = ({ value, onChange, onSelect, ...props }) => {
  const { semesterListTermLevel: semesters, fetchSemesterList } = useSystemStore();
  useQuery(['semesterList'], () => fetchSemesterList(), {
    onSuccess(data) {
      const current = data.semesterListTermLevel.find((sem) => sem.isCurrent === 1);
      if (current && !value) {
        onChange(current.id);
      }
    }
  });
  const getSemesterLabel = (sem: { year: string; type: number }) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  return (
    <HStack>
      <Select
        value={value?.toString()}
        variant="filled"
        style={{
          width: 200,
          border: 'none',
          borderRadius: '10px'
        }}
        placeholder="请选择学期"
        onChange={(value) => {
          onChange(value);
          onSelect?.(value);
        }}
        {...props}
      >
        {semesters.map((sem) => (
          <Option key={sem.id} value={sem.id}>
            {getSemesterLabel(sem)}
          </Option>
        ))}
      </Select>
    </HStack>
  );
};

export default SemesterSelect;
