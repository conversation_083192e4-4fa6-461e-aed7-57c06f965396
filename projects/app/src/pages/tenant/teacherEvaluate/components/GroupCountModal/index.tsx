import MyModal from '@/components/MyModal';
import { AddIcon, MinusIcon } from '@chakra-ui/icons';
import { Box, Button, Center, Flex, HStack, IconButton } from '@chakra-ui/react';
import { useState } from 'react';

const GroupCountModal = ({
  onSuccess,
  onClose
}: {
  onSuccess?: (count: number) => void;
  onClose?: () => void;
}) => {
  const [count, setCount] = useState(1);

  const onSubmit = () => {
    onSuccess?.(count);
    onClose?.();
  };

  return (
    <MyModal title="快速分组" isOpen isCentered onClose={onClose} w="540px">
      <Box px="32px" py="24px">
        <Center mt="30px" color="#4e5969" fontSize="14px" lineHeight="22px">
          将所有学生随机分组，组数为：
        </Center>

        <Flex mt="20px" justify="center" align="center">
          <IconButton
            w="32px"
            h="32px"
            minW="0"
            variant="outline"
            icon={<MinusIcon />}
            aria-label=""
            borderRadius="50%"
            onClick={() => count - 1 && setCount(count - 1)}
          />

          <Center ml="12px" w="56px" color="#303133" fontSize="16px">
            {count}
          </Center>

          <IconButton
            ml="12px"
            w="32px"
            h="32px"
            minW="0"
            variant="outline"
            icon={<AddIcon />}
            aria-label=""
            borderRadius="50%"
            onClick={() => setCount(count + 1)}
          />
        </Flex>

        <HStack justify="flex-end" mt="66px" spacing="16px">
          <Button variant="grayBase" onClick={onClose}>
            取消
          </Button>
          <Button onClick={onSubmit}>开始分组</Button>
        </HStack>
      </Box>
    </MyModal>
  );
};

export default GroupCountModal;
