import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  ChakraProps,
  Flex,
  Popover,
  PopoverContent,
  Text,
  useDisclosure
} from '@chakra-ui/react';
import { useToast } from '@/hooks/useToast';
import { Input } from 'antd';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import { getHomeTemplatePage } from '@/api/tenant/evaluate/process';
import {
  addHomeworkTemplate,
  updateHomeworkTemplate,
  removeHomeworkTemplate
} from '@/api/tenant/evaluate/process';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';

interface AddTagPopoverProps extends ChakraProps {
  bizType: string | undefined;
  fileType: string | undefined;
  id: string | undefined | null;
  resetLabels: () => void;
  fileLabels: any[];
  subjectId: string;
  children?: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (selectedTagName: string) => void;
  selectedTagName: string;
}

const { Search } = Input;

const AddTagPopover: React.FC<AddTagPopoverProps> = ({
  fileLabels,
  isOpen,
  onConfirm,
  subjectId,
  onClose,
  selectedTagName
}) => {
  const { onOpen } = useDisclosure();
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [isCreatingTag, setIsCreatingTag] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  interface Tag {
    id: string;
    name: string;
  }

  const [tags, setTags] = useState<EvaluateHomeworkType[]>([]);
  const { toast } = useToast();
  const [hoveredTagId, setHoveredTagId] = useState<string | null>(null);
  const [editingTagId, setEditingTagId] = useState<string | null>(null);
  const [editedTagName, setEditedTagName] = useState('');

  useEffect(() => {
    setSelectedTagIds(fileLabels.map((label) => label.id));
  }, [fileLabels]);

  useEffect(() => {
    if (isOpen) {
      const selectedTag = tags.find((tag) => tag.name === selectedTagName);
      if (selectedTag) {
        setSelectedTagIds([selectedTag.id]);
      } else {
        setSelectedTagIds([]);
      }
    }
  }, [isOpen, selectedTagName, tags]);

  const handleStartEdit = (tagId: string) => {
    const tag = tags.find((tag) => tag.id === tagId);
    if (tag) {
      setEditedTagName(tag.name);
      setEditingTagId(tagId);
    }
  };

  const handleSaveEdit = async (tag: Tag) => {
    await updateHomeworkTemplate({ id: tag.id, name: editedTagName, subjectId });
    refetchSubjects();
    setEditingTagId(null);
    toast({
      title: '操作成功',
      status: 'success',
      duration: 3000
    });
  };

  const handleDeleteTag = async (tagId: string) => {
    await removeHomeworkTemplate({ id: tagId });
    refetchSubjects();
    setSelectedTagIds((prevIds) => prevIds.filter((id) => id.toString() !== tagId));
  };

  const handleTagClick = (tagId: string) => {
    setSelectedTagIds([tagId]);
  };

  const handleCreateTag = async () => {
    const trimmedTagName = newTagName.trim();
    if (trimmedTagName !== '') {
      const existingTag = Array.isArray(tags)
        ? tags?.find((tag) => tag.name === trimmedTagName)
        : null;
      if (existingTag) {
        toast({
          title: '标签已存在',
          status: 'warning',
          duration: 3000
        });
      } else {
        await addHomeworkTemplate({ name: trimmedTagName, subjectId });
        refetchSubjects();
        toast({
          title: '操作成功',
          status: 'success',
          duration: 3000
        });
      }
      setNewTagName('');
      setIsCreatingTag(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleCreateTag();
    }
  };

  const handleConfirm = async () => {
    const selectedTag = tags.find((tag) => selectedTagIds.includes(tag.id));
    if (selectedTag) {
      onConfirm(selectedTag.name);
    }
    onClose();
  };

  const handleClose = () => {
    setSelectedTagIds([]);
    onClose();
  };

  const { refetch: refetchSubjects } = useQuery(
    ['subjects'],
    () => getHomeTemplatePage({ subjectId, current: 1, size: 9999 }),
    {
      onSuccess: (data) => {
        setTags(data.records);
      }
    }
  );

  const filteredTags = Array.isArray(tags)
    ? tags.filter((tag) => tag.name.includes(searchTerm))
    : [];

  return (
    <Popover
      trigger="click"
      placement="bottom-start"
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
    >
      <PopoverContent
        w={respDims(282)}
        h={respDims(isCreatingTag ? 420 : 380)}
        boxShadow="0px 3px 10px 0px rgba(0, 0, 0, 0.11)"
        borderRadius="8px"
      >
        <Box pt={respDims(16)} pb={respDims(16)}>
          <Button
            variant="outline"
            w="88.8%"
            h={respDims(36)}
            borderRadius="4px"
            border="1px solid rgba(209, 213, 219, 1)"
            mb={respDims(8)}
            onClick={() => setIsCreatingTag(true)}
            ml={respDims(16)}
          >
            <Flex alignItems="center" justifyContent="center">
              <SvgIcon name="plus" w={respDims(14)} h={respDims(14)} mr={respDims(4)} />
              <Text fontSize={respDims(14)} color="rgba(78, 89, 105, 1)">
                新建标签
              </Text>
            </Flex>
          </Button>
          {isCreatingTag && (
            <Box
              bg="rgba(242, 243, 245, 1)"
              borderRadius="4px"
              mb={respDims(8)}
              ml={respDims(16)}
              mr={respDims(16)}
            >
              <Input
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="新建标签名"
                onPressEnter={handleKeyPress}
                addonBefore={<SvgIcon name="file2Label" w={respDims(20)} h={respDims(20)} />}
                addonAfter={
                  <SvgIcon
                    name="check"
                    w={respDims(20)}
                    h={respDims(20)}
                    cursor="pointer"
                    onClick={handleCreateTag}
                  />
                }
              />
            </Box>
          )}
          <Search
            placeholder="搜索标签"
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ width: '88.8%', marginBottom: '8px', marginLeft: '15px' }}
          />
          <Box h={respDims(200)} overflowY="auto">
            {filteredTags.map((tag) => (
              <Flex
                key={tag.id}
                alignItems="center"
                justifyContent="space-between"
                cursor="pointer"
                pl={respDims(16)}
                pr={respDims(16)}
                pt={respDims(10)}
                pb={respDims(10)}
                _hover={{
                  bg: '#F9FAFB'
                }}
                onClick={() => handleTagClick(tag.id)}
                onMouseEnter={() => setHoveredTagId(tag.id)}
                onMouseLeave={() => setHoveredTagId(null)}
              >
                {editingTagId === tag.id ? (
                  <Input
                    value={editedTagName}
                    onChange={(e) => setEditedTagName(e.target.value)}
                    onPressEnter={() => handleSaveEdit(tag)}
                    style={{ border: 'none', outline: 'none' }}
                  />
                ) : (
                  <Flex
                    alignItems="center"
                    color={selectedTagIds.includes(tag.id) ? '#1D2129' : '#606266'}
                    fontSize={respDims(14)}
                    onClick={() => handleTagClick(tag.id)}
                  >
                    <SvgIcon
                      mr={respDims(10)}
                      name="file2Label"
                      fontSize={respDims(14)}
                      w="22px"
                      h="22px"
                    />
                    {tag.name}
                  </Flex>
                )}
                <Flex alignItems="center">
                  {hoveredTagId === tag.id && editingTagId !== tag.id && (
                    <Flex>
                      <SvgIcon
                        name="editLine"
                        w="22px"
                        h="22px"
                        color="#616266"
                        cursor="pointer"
                        onClick={() => handleStartEdit(tag.id)}
                      />
                      {!selectedTagIds.includes(tag.id) && (
                        <SvgIcon
                          name="trash"
                          w="22px"
                          h="22px"
                          ml="17px"
                          color="#616266"
                          cursor="pointer"
                          onClick={() => handleDeleteTag(tag.id)}
                        />
                      )}
                    </Flex>
                  )}
                  {selectedTagIds.includes(tag.id) && (
                    <SvgIcon
                      name="check"
                      w="22px"
                      h="22px"
                      ml="17px"
                      color="#36F"
                      cursor="pointer"
                      onClick={() => handleSaveEdit(tag)}
                    />
                  )}
                </Flex>
              </Flex>
            ))}
          </Box>
          <Flex
            justifyContent="space-between"
            mt={respDims(12)}
            ml={respDims(16)}
            mr={respDims(16)}
          >
            <Button
              variant="outline"
              w="48%"
              h={respDims(36)}
              borderRadius="4px"
              onClick={handleClose}
            >
              取消
            </Button>
            <Button
              w="48%"
              h={respDims(36)}
              borderRadius="4px"
              colorScheme="blue"
              onClick={handleConfirm}
            >
              确定
            </Button>
          </Flex>
        </Box>
      </PopoverContent>
    </Popover>
  );
};

export default AddTagPopover;
