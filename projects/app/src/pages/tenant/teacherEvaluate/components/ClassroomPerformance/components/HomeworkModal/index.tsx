import { addHomework, updateHomework } from '@/api/tenant/evaluate/process';
import MyModal from '@/components/MyModal';
import { EvaluateHomeworkType } from '@/types/api/tenant/evaluate/process';
import { Box, Button, Flex, HStack } from '@chakra-ui/react';
import { Calendar, Space } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useMemo, useState } from 'react';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import weekday from 'dayjs/plugin/weekday';
import 'dayjs/locale/zh-cn';
import AddTagPopover from './components/AddTagPopover';
import { DownOutlined } from '@ant-design/icons';

dayjs.extend(customParseFormat as any);
dayjs.extend(advancedFormat as any);
dayjs.extend(weekday as any);
dayjs.extend(localeData as any);
dayjs.extend(weekOfYear as any);
dayjs.extend(weekYear as any);

const HomeworkModal = ({
  gradeId,
  clazzId,
  subjectId,
  homework,
  generateName,
  onSuccess,
  onClose
}: {
  gradeId: string;
  clazzId: string;
  subjectId: string;
  homework?: EvaluateHomeworkType;
  generateName?: (date: Dayjs, homework?: EvaluateHomeworkType, homeworkName?: string) => string;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const defaultValue = useMemo(() => {
    if (!homework?.name) {
      return undefined;
    }
    const m = /\d{8}/.exec(homework.name);

    return m
      ? dayjs(`${m[0].substring(0, 4)}-${m[0].substring(4, 6)}-${m[0].substring(6)}`)
      : undefined;
  }, [homework?.name]);

  const [date, setDate] = useState<Dayjs>();
  const [name, setName] = useState(() => {
    const match = homework?.name?.match(/^\d{8}([^作业]*)/);
    return match ? match[1].trim() : '';
  });
  const [items, setItems] = useState(['jack', 'lucy']);
  const [isOpen, setIsOpen] = useState(false);

  const handleTagConfirm = (selectedTagName: string) => {
    setName(selectedTagName);
  };
  const onSubmit = async () => {
    const selectedDate = date || dayjs();
    if (generateName) {
      const homeworkName = generateName(selectedDate, homework, name);
      if (homeworkName !== homework?.name) {
        if (homework?.id) {
          await updateHomework({
            id: homework.id,
            gradeId,
            clazzId,
            subjectId,
            assignDate: selectedDate.format('YYYY-MM-DD'),
            name: homeworkName
          });
        } else {
          await addHomework({
            gradeId,
            clazzId,
            subjectId,
            assignDate: selectedDate.format('YYYY-MM-DD'),
            name: homeworkName
          });
        }
        onSuccess?.();
      }
    }
    onClose?.();
  };

  return (
    <MyModal title="选择作业日期" isOpen onClose={onClose} position="relative">
      <Box
        p="24px"
        pt="12px"
        css={{
          '.ant-picker-calendar-mode-switch': {
            display: 'none'
          }
        }}
      >
        <Calendar
          fullscreen={false}
          defaultValue={defaultValue}
          onChange={(date) => setDate(date)}
        />

        <Flex alignItems="center" mt="30px">
          <Box fontSize="14px" fontWeight="400" color="#4E5969" mr="9px">
            作业名称
          </Box>
          <Flex
            flex="1"
            p="7px 16px"
            bgColor="#F6F6F6"
            borderRadius="8px"
            alignItems="center"
            onClick={() => {
              setIsOpen(true);
            }}
            width="100%"
          >
            <Box flex="1" color="#0A0A0A" fontSize="14px" fontWeight="400">
              {name || '请选择作业名称'}
            </Box>
            <DownOutlined style={{ color: '#505968', fontSize: '10px' }} />
          </Flex>
          <Space
            style={{
              padding: '0 8px 4px',
              height: '230px',
              position: 'absolute',
              bottom: '240px',
              left: '80px'
            }}
          >
            <AddTagPopover
              bizType="1"
              fileType="1"
              isOpen={isOpen}
              subjectId={subjectId}
              id={homework?.id}
              onClose={() => {
                setIsOpen(false);
              }}
              width="100%"
              onConfirm={handleTagConfirm}
              fileLabels={items}
              resetLabels={() => {}}
              selectedTagName={name}
            />
          </Space>
        </Flex>

        <HStack justify="flex-end" mt="24px" spacing="16px">
          <Button variant="grayBase" onClick={onClose}>
            取消
          </Button>
          <Button onClick={onSubmit}>确定</Button>
        </HStack>
      </Box>
    </MyModal>
  );
};

export default HomeworkModal;
