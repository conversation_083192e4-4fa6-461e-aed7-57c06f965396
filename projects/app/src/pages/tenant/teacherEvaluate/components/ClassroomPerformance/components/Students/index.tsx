import { ChakraProps } from '@chakra-ui/system';
import { Box, Center, Checkbox, Flex, HStack } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import { Toast } from '@/utils/ui/toast';
import { MessageBox } from '@/utils/ui/messageBox';
import {
  EvaluateHomeworkType,
  EvaluateIndactorType,
  EvaluateStudentType
} from '@/types/api/tenant/evaluate/process';
import HomeworkList from '../HomeworkList';
import {
  getClazzStudentListByCode,
  getClazzStudentListBySeat,
  resetClazzEvaluate
} from '@/api/tenant/evaluate/process';
import EvaluateModal from '../EvaluateModal';
import StudentList from '../../../StudentList';
import AdjustSeatModal from '../../../AdjustSeatModal';
import SeatCountModal from '../../../SeatCountModal';
import SeatList from '../../../SeatList';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import MyMenu from '@/components/MyMenu';
import { useRouter } from 'next/router';
import { serializeData } from '@/utils/tools';

export type StudentsRef = {
  showAddSeats: () => void;

  showAdjustSeats: () => void;
};

const Students = (
  {
    viewType = 'code',
    subjectName,
    ruleId,
    clazzId,
    subjectId,
    isClazzTeacher,
    gradeId,
    clazzName,
    indactors,
    menuId,
    semesterId,
    ...props
  }: {
    viewType?: 'code' | 'seat';
    isClazzTeacher?: boolean;
    gradeId: string;
    clazzId: string;
    clazzName: string;
    subjectId?: string;
    subjectName?: string;
    indactors?: EvaluateIndactorType[];
    menuId: string;
    ruleId: string;
    semesterId: string;
  } & ChakraProps,
  ref: ForwardedRef<StudentsRef>
) => {
  const [checkType, setCheckType] = useState<'evaluate' | 'reset' | ''>('');

  const [isHideEvaluated, setIsHideEvaluated] = useState(false);

  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const [homework, setHomework] = useState<EvaluateHomeworkType>();

  const router = useRouter();

  const { openOverlay } = useOverlayManager();

  const {
    data: codeStudents,
    isFetched: isCodeStudentFetched,
    refetch: refetchCodeStudents
  } = useQuery(
    ['codeStudents', ruleId, clazzId, homework?.id],
    () =>
      getClazzStudentListByCode({
        clazzId,
        subjectId,
        ruleId,
        semesterId,
        homeworkId: homework?.id!
      }),
    {
      enabled: !!(viewType === 'code' && clazzId && homework?.id && ruleId)
    }
  );

  const {
    data,
    isFetched: isSeatStudentFetched,
    refetch: refetchSeatStudents
  } = useQuery(
    ['seatStudents', clazzId, subjectId, ruleId, semesterId, homework?.id],
    () => {
      if (!!(clazzId && ruleId && semesterId && homework?.id)) {
        return getClazzStudentListBySeat({
          clazzId,
          subjectId,
          ruleId: ruleId,
          semesterId,
          homeworkId: homework?.id!
        });
      }
      return {
        studentSeatList: [],
        clazzId,
        colNum: 0,
        rowNum: 0
      };
    },
    {
      enabled: !!(viewType === 'seat' && clazzId && homework?.id)
    }
  );

  const { studentSeatList: seatStudents, colNum, rowNum } = data || {};

  const originStudents = useMemo(() => {
    return (viewType === 'code' ? codeStudents : seatStudents) || [];
  }, [viewType, codeStudents, seatStudents]);

  const students = useMemo(() => {
    if (!homework?.id) {
      return [];
    }

    return isHideEvaluated
      ? originStudents.filter((it) => !it.evaluationList?.length)
      : originStudents;
  }, [isHideEvaluated, originStudents, homework?.id]);

  useEffect(() => {
    if (isHideEvaluated) {
      setCheckedIds([]);
    }
  }, [isHideEvaluated]);

  const refetchStudents = viewType === 'code' ? refetchCodeStudents : refetchSeatStudents;

  const isAllChecked = useMemo(
    () => students.length > 0 && students.every((it) => checkedIds.includes(it.studentId)),
    [students, checkedIds]
  );

  const checkedCount = useMemo(
    () => checkedIds.reduce((n, id) => (students.some((it) => it.studentId === id) ? n + 1 : n), 0),
    [students, checkedIds]
  );

  const onEvaluate = (students: EvaluateStudentType[]) => {
    clazzId &&
      subjectId &&
      homework &&
      students.length &&
      openOverlay({
        Overlay: EvaluateModal,
        props: {
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          semesterId,
          ruleId,
          subjectId,
          homeworkId: homework.id,
          students,
          indactors,
          menuId,
          onSuccess: () => {
            refetchStudents();
            setCheckedIds([]);
            onCancelCheck();
            setIsHideEvaluated(false);
          }
        }
      });
  };

  const onClickStudent = (student: EvaluateStudentType) => {
    onEvaluate([student]);
  };

  const onClickSelectAll = () => {
    if (isAllChecked) {
      setCheckedIds([]);
    } else {
      setCheckedIds(students.map((it) => it.studentId));
    }
  };

  const onClickEvaluate = () => {
    if (!checkedIds.length) {
      Toast.info('请先选择学生');
      return;
    }
    onEvaluate(students.filter((it) => checkedIds.includes(it.studentId)));
  };

  const onClickReset = () => {
    const studentIds = students
      .filter((it) => checkedIds.includes(it.studentId))
      .map((it) => it.studentId);
    if (!studentIds.length) {
      Toast.info('请先选择学生');
      return;
    }
    homework &&
      subjectId &&
      MessageBox.confirm({
        title: '提示',
        content: '重新计分将已评分数清零，且不可恢复，点评记录不会删除，确定重新计分吗？',
        onOk: () => {
          resetClazzEvaluate({
            gradeId,
            menuId,
            semesterId,
            ruleId,
            clazzId,
            subjectId,
            evaluatedIds: studentIds
          }).then(() => {
            refetchStudents();
            setCheckedIds([]);
          });
        }
      });
  };

  const onAdjustSeat = async (options?: {
    rowCount?: number;
    columnCount?: number;
    incRowCount?: number;
    incColumnCount?: number;
  }) => {
    let students = codeStudents;
    if (!isCodeStudentFetched) {
      students = await getClazzStudentListByCode({
        clazzId,
        subjectId,
        ruleId,
        semesterId,
        homeworkId: homework?.id!
      });
    }

    if (seatStudents?.length) {
      students?.forEach((student) => {
        const found = seatStudents.find(
          (seatStudent) => seatStudent.studentId === student.studentId
        );
        if (found) {
          student.seatId = found.seatId;
          student.rowNo = found.rowNo;
          student.colNo = found.colNo;
        }
      });
    }

    openOverlay({
      Overlay: AdjustSeatModal,
      props: {
        clazzId,
        semesterId,
        students: students || [],
        rowCount: options?.rowCount,
        columnCount: options?.columnCount,
        incRowCount: options?.incRowCount,
        incColumnCount: options?.incColumnCount,
        onSuccess: () => {
          refetchCodeStudents();
          refetchSeatStudents();
        }
      }
    });
  };

  const onInitSeat = () => {
    openOverlay({
      Overlay: SeatCountModal,
      props: {
        onSuccess: onAdjustSeat
      }
    });
  };

  const onCancelCheck = () => {
    setCheckType('');
    setCheckedIds([]);
    setIsHideEvaluated(false);
  };

  useImperativeHandle(ref, () => ({
    showAddSeats: () => {
      openOverlay({
        Overlay: SeatCountModal,
        props: {
          onSuccess: ({ rowCount, columnCount }) =>
            onAdjustSeat({ incRowCount: rowCount, incColumnCount: columnCount })
        }
      });
    },
    showAdjustSeats: () => onAdjustSeat()
  }));

  useEffect(() => {
    onCancelCheck();
  }, [homework]);

  return (
    <Flex direction="column" w="100%" h="100%" {...props}>
      <Flex flex="1 0 0" align="stretch" mb={respDims(16)} overflow="hidden">
        <HomeworkList
          gradeId={gradeId}
          clazzId={clazzId}
          subjectId={subjectId}
          subjectName={subjectName}
          onChange={setHomework}
        />

        {!homework && (
          <Flex flexDir="column" justify="center" align="center" w="100%" h="100%">
            <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
            <Box>暂无数据</Box>
          </Flex>
        )}

        {viewType === 'code' && !!originStudents.length && (
          <StudentList
            flex="1"
            overflowY="auto"
            type="evaluate"
            entryType={EvaluateEntryEnum.HomeworkEvaluation}
            students={students}
            isCheckable={!!checkType}
            checkedIds={checkedIds}
            onCheckChange={setCheckedIds}
            onClickStudent={onClickStudent}
          />
        )}

        {viewType === 'seat' && (
          <>
            {!!originStudents.length ? (
              <SeatList
                flex="1"
                mx={respDims(24)}
                type="evaluate"
                students={students}
                isCheckable={!!checkType}
                checkedIds={checkedIds}
                onCheckChange={setCheckedIds}
                onClickStudent={onClickStudent}
                entryType={EvaluateEntryEnum.HomeworkEvaluation}
              />
            ) : (
              !!homework && (
                <Flex direction="column" flex="1" justify="center" align="center" mb="80px">
                  <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
                  <Box>班级暂未进行座位排列，需班主任安排</Box>
                  {isClazzTeacher && (
                    <Box
                      mt={respDims(18)}
                      px={respDims(44)}
                      py={respDims(9)}
                      color="primary.500"
                      bgColor="primary.50"
                      fontSize={respDims('16fpx')}
                      lineHeight={respDims('22fpx')}
                      borderRadius={respDims(100)}
                      cursor="pointer"
                      _hover={{
                        bgColor: '#f3f5fe'
                      }}
                      onClick={onInitSeat}
                    >
                      快速设置座位
                    </Box>
                  )}
                </Flex>
              )
            )}
          </>
        )}
      </Flex>

      {!!homework && !!indactors?.length && (
        <Flex
          h={respDims(78)}
          align="center"
          borderTop="1px solid #E5E7EB"
          color="#4E5969"
          fontSize={respDims('16fpx')}
          lineHeight={respDims('22fpx')}
        >
          <Box p="7px 20px" borderRadius="100px" bgColor="#F2F3F5" ml="22px" cursor="pointer">
            <MyMenu
              trigger="hover"
              offset={[-20, 20]}
              width={20}
              Button={
                <HStack
                  borderRadius={respDims(4)}
                  _hover={{ bgColor: '#f3f4f6' }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <Box fontSize="14px" fontWeight="400" lineHeight="20px">
                    查看评价数据
                  </Box>
                  <SvgIcon name="chevronDown" w={respDims(14)} h={respDims(14)} />
                </HStack>
              }
              menuList={[
                {
                  icon: <SvgIcon name="emojiClassPerformance" />,
                  label: '班级评价排行',
                  onClick: () => {
                    router.push({
                      pathname: '/tenant/teacherEvaluate/culturalKnowledge/homeworkRecord',
                      query: {
                        q: serializeData({
                          isClazzTeacher,
                          gradeId: gradeId,
                          clazzId: clazzId,
                          menuId: menuId,
                          semesterId,
                          ruleId: ruleId,
                          clazzName: clazzName,
                          tab: 'clazz'
                        })
                      }
                    });
                  }
                },
                {
                  icon: <SvgIcon name="emojiStudentPerformance" />,
                  label: '学生评价数据',
                  onClick: () => {
                    router.push({
                      pathname: '/tenant/teacherEvaluate/culturalKnowledge/homeworkRecord',
                      query: {
                        q: serializeData({
                          isClazzTeacher,
                          gradeId: gradeId,
                          clazzId: clazzId,
                          menuId: menuId,
                          semesterId,
                          ruleId: ruleId,
                          clazzName: clazzName,
                          tab: 'student'
                        })
                      }
                    });
                  }
                }
              ]}
            />
          </Box>
          {!checkType ? (
            <Flex flex=".6" justify="flex-end">
              <Center
                w={respDims(200)}
                h={respDims(40)}
                fontWeight="bold"
                cursor="pointer"
                onClick={() => setCheckType('evaluate')}
                _hover={{
                  color: 'primary.500'
                }}
              >
                <SvgIcon name="evaluationMutiple" />
                <Box ml={respDims(10)}>多选评分</Box>
              </Center>

              <Center
                w={respDims(200)}
                h={respDims(40)}
                fontWeight="bold"
                cursor="pointer"
                onClick={() => setCheckType('reset')}
                _hover={{
                  color: 'primary.500'
                }}
              >
                <SvgIcon name="evaluationLoop" />
                <Box ml={respDims(10)}>清空评分</Box>
              </Center>
            </Flex>
          ) : (
            <Flex flex="1" justifyContent="center" gap={respDims(32)}>
              <Center cursor="pointer" userSelect="none" onClick={onClickSelectAll}>
                <Box onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    colorScheme="primary"
                    size="lg"
                    isChecked={isAllChecked}
                    onChange={onClickSelectAll}
                  />
                </Box>
                <Box ml={respDims(16)}>选择全部</Box>
              </Center>

              <Center
                cursor="pointer"
                userSelect="none"
                onClick={() => setIsHideEvaluated((state) => !state)}
              >
                <Box onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    colorScheme="primary"
                    size="lg"
                    isChecked={isHideEvaluated}
                    onChange={() => setIsHideEvaluated((state) => !state)}
                  />
                </Box>
                <Box ml={respDims(16)}>隐藏已评分的学生</Box>
              </Center>

              <Center
                w={respDims('292fpx')}
                h={respDims('40fpx')}
                color="primary.500"
                bgColor="primary.50"
                boxSizing="border-box"
                borderRadius={respDims(8)}
                cursor="pointer"
                onClick={() => (checkType === 'evaluate' ? onClickEvaluate() : onClickReset())}
              >
                {`${checkType === 'evaluate' ? '评分' : '清空评分'} (${checkedCount})`}
              </Center>

              <Center
                w={respDims('292fpx')}
                h={respDims('40fpx')}
                bgColor="#F9FAFB"
                boxSizing="border-box"
                borderRadius={respDims(8)}
                cursor="pointer"
                onClick={onCancelCheck}
              >
                取消
              </Center>
            </Flex>
          )}
        </Flex>
      )}
    </Flex>
  );
};

export default forwardRef(Students);
