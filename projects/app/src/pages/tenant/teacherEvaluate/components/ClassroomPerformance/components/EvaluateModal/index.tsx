import { addClazzEvaluate, addHomeworkEvaluate } from '@/api/tenant/evaluate/process';
import MyModal from '@/components/MyModal';
import MyBox from '@/components/common/MyBox';
import { EvaluationType, HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { EvaluaScoreLevelValue } from '@/types/api/tenant/evaluate/score';
import { respDims } from '@/utils/chakra';
import { serializeData } from '@/utils/tools';
import { treeToList } from '@/utils/tree';
import { Toast } from '@/utils/ui/toast';
import {
  Box,
  Button,
  Center,
  Flex,
  HStack,
  Image,
  NumberInput,
  NumberInputField
} from '@chakra-ui/react';
import { cloneDeep } from 'lodash';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';

const EvaluateModal = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  homeworkId,
  students,
  indactors,
  menuId,
  semesterId,
  ruleId,
  onSuccess,
  onClose
}: {
  students: EvaluateStudentType[];
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId: string;
  homeworkId: string;
  indactors?: EvaluateIndactorType[];
  menuId: string;
  semesterId: string;
  ruleId: string;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const router = useRouter();

  const [levelValue, setLevelValue] = useState<EvaluaScoreLevelValue>();

  const [score, setScore] = useState<number>();

  const [data, setData] = useState(
    (cloneDeep(
      indactors?.map((item, index) => {
        item.id = index + '';
        item.name = item.projectName;
        return {
          ...item
        };
      })
    ) as EvaluateIndactorType[]) || []
  );
  const title = useMemo(
    () => `评分 ${students.map((it) => it.studentName).join('、')}`,
    [students]
  );

  const hasScoreLevelValue = (children: EvaluateIndactorType[]): boolean => {
    return children.some((child) => {
      if (child.evaluationType === EvaluationType.ScoreLevelValue) {
        return true;
      }
      return child.children ? hasScoreLevelValue(child.children) : false;
    });
  };

  const hasEvaluationType = (children: EvaluateIndactorType[], type: EvaluationType): boolean => {
    return children.some((child) => {
      if (child.evaluationType === type) {
        return true;
      }
      return child.children ? hasEvaluationType(child.children, type) : false;
    });
  };

  const getAllScoreLevelValues = (children: EvaluateIndactorType[]): EvaluaScoreLevelValue[] => {
    return children.reduce((acc: EvaluaScoreLevelValue[], child) => {
      if (child.evaluationType === EvaluationType.ScoreLevelValue && child.scoreLevelValues) {
        acc = acc.concat(child.scoreLevelValues);
      }
      if (child.children) {
        acc = acc.concat(getAllScoreLevelValues(child.children));
      }
      return acc;
    }, []);
  };

  const onSubmit = async () => {
    try {
      const indicators = treeToList(data)
        .filter((node) => {
          return node.hasSub !== HasSubIndicator.Yes && !node.hasChildren;
        })
        .map((item) => {
          if (item.scoreLevelValueId && !item.scoreLevelValue) {
            const targetValueInfo = item.scoreLevelValues.find((it) => it.id == levelValue?.id);
            item.scoreLevelId = targetValueInfo?.scoreLevelId;
            item.scoreLevelValueId = targetValueInfo?.id;
            item.scoreLevelValue = targetValueInfo?.name || '';
          }
          return {
            indicatorId: item.id,
            dimensionId: item.dimensionId,
            evaluationType: item.evaluationType,
            scoreLevelId: item.scoreLevelId,
            scoreLevelValueId: item.scoreLevelValueId,
            scoreLevelValue: item.scoreLevelValue
          };
        });
      let payload = {
        menuId: menuId,
        semesterId,
        clazzId: parseInt(clazzId),
        ruleId,
        homeworkId,
        subjectId: parseInt(subjectId || '0'),
        evaluatedIds: students.map((student) => student.studentId),
        indicators
      };
      if (
        data.some((item) => hasEvaluationType(item.children || [], EvaluationType.ScoreLevelValue))
      ) {
        if (!levelValue) {
          Toast.error('请选择等级');
          return;
        }
        try {
          await addClazzEvaluate(payload as any);
          Toast.success('评价成功');
          onSuccess?.();
          onClose?.();
        } catch (error) {
          Toast.error('评价失败');
        }
      } else {
        if (score === undefined) {
          Toast.error('请输入评分');
          return;
        }
        try {
          await addClazzEvaluate(payload as any);
          Toast.success('评价成功');
          onSuccess?.();
          onClose?.();
        } catch (error) {
          Toast.error('评价失败');
        }
      }

      // 处理返回的结果
      // if (result && result.length > 0) {
      //   const alreadyEvaluatedNames = result.map((item) => item.name).join('、');
      //   Toast.info(`以下学生在周期内已经评价过: ${alreadyEvaluatedNames}`);
      // }

      onSuccess?.();
      onClose?.();
    } catch (error) {
      console.error('评分提交失败:', error);
      Toast.error('评分提交失败，请稍后重试');
    }
  };

  useEffect(() => {
    const indactor = data?.some((item) => item.children?.some((child) => child.evaluationType))
      ? data.flatMap((item) => item.children).find((child) => child?.evaluationType)
      : undefined;

    if (indactor?.evaluationType === EvaluationType.ScoreLevelValue) {
      const levelValue = indactor?.scoreLevelValues?.find(
        (it) => it.id == indactor?.scoreLevelValueId
      );

      setLevelValue(levelValue);
    } else if (indactor?.evaluationType === EvaluationType.Score) {
      setScore(indactor?.score);
    }
  }, [indactors, students]);

  return (
    <MyModal
      isOpen
      isCentered
      title={
        <Box maxW="12em" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
          {title}
        </Box>
      }
      onClose={onClose}
    >
      {hasEvaluationType(data, EvaluationType.ScoreLevelValue) && data.length === 0 ? (
        <Center flexDir="column" w="600px" h="400px">
          <Flex align="flex-end">
            <Image src="/imgs/evaluate/evaluate_empty.png" alt="" w="24px" h="24px" />
            <Box ml="6px" color="#303133" fontSize="16px" lineHeight="22px">
              未设置评价指标，请先至评价规则中设置指标。
            </Box>
          </Flex>
          <HStack mt="38px" spacing="16px">
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() =>
                router.replace({
                  pathname: '/tenant/evaluate/process/homework/record',
                  query: {
                    q: serializeData({
                      isClazzTeacher,
                      gradeId,
                      clazzId,
                      clazzName,
                      tab: 'clazz'
                    })
                  }
                })
              }
            >
              班级作业记录
            </Center>
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() =>
                router.replace({
                  pathname: '/tenant/evaluate/process/homework/record',
                  query: {
                    q: serializeData({
                      isClazzTeacher,
                      gradeId,
                      clazzId,
                      clazzName,
                      tab: 'student'
                    })
                  }
                })
              }
            >
              学生作业记录
            </Center>
          </HStack>
        </Center>
      ) : (
        <Box px={respDims(32)} pb={respDims(24)}>
          <MyBox
            display="flex"
            // isLoading={!data && isLoading}
            h={respDims(120)}
            flexDir="column"
            justifyContent="center"
          >
            {data.length === 0 && <Center>暂无评分指标</Center>}

            {data.some((item) =>
              hasEvaluationType(item.children || [], EvaluationType.ScoreLevelValue)
            ) && (
              <>
                <Box>等级</Box>
                <HStack mt={respDims(10)} spacing={respDims(65)}>
                  {getAllScoreLevelValues(data.flatMap((item) => item.children || [])).map((it) => (
                    <Center
                      key={it.id}
                      minW={respDims('32fpx')}
                      minH={respDims('32fpx')}
                      fontWeight="bold"
                      borderWidth="1px"
                      borderStyle="solid"
                      borderRadius="50%"
                      fontSize={respDims('15fpx')}
                      lineHeight={respDims('15fpx')}
                      cursor="pointer"
                      {...(it.id === levelValue?.id
                        ? {
                            color: 'primary.500',
                            bgColor: 'primary.50',
                            borderColor: 'primary.500'
                          }
                        : {
                            color: '#000000',
                            bgColor: '#F6F6F6',
                            borderColor: '#F6F6F6'
                          })}
                      onClick={() => {
                        setLevelValue(it); // 直接设置选中的等级值
                      }}
                    >
                      {it.name}
                    </Center>
                  ))}
                </HStack>
              </>
            )}

            {data.some((item) => hasEvaluationType(item.children || [], EvaluationType.Score)) && (
              <>
                <Box>评分</Box>
                <NumberInput
                  mt={respDims(10)}
                  min={data
                    .flatMap((item) => item.children || [])
                    .reduce((min, child) => Math.min(min, child.scoreMin || Infinity), Infinity)}
                  max={data
                    .flatMap((item) => item.children || [])
                    .reduce((max, child) => Math.max(max, child.scoreMax || -Infinity), -Infinity)}
                  value={score ?? ''}
                  onChange={(e, v) => setScore(isNaN(v) ? undefined : v)}
                >
                  <NumberInputField />
                </NumberInput>
              </>
            )}
          </MyBox>
          <HStack justify="flex-end" mt="24px" spacing="16px">
            <Button variant="grayBase" onClick={onClose}>
              取消
            </Button>
            <Button onClick={onSubmit}>确定</Button>
          </HStack>
        </Box>
      )}
    </MyModal>
  );
};

export default EvaluateModal;
