import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Center, ChakraProps, Flex, HStack, Popover, Button } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { Radio } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { getStudentIndicatorTree } from '@/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import CustomExpandIcon from '@/pages/tenant/evaluateItemManage/components/customExpandIcon';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import RadioStyles from '@/pages/index.module.scss';
import { treeTraverse } from '@/utils/tree';
import { HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import { RelTimeType } from '@/pages/tenant/teacherEvaluate/components/RelTimeSelect';
import { EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';

const Stats = ({
  type,
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  studentId,
  menuId,
  ruleId,
  semesterId,
  relTime,
  title,
  subTitle,
  ...props
}: {
  type: 'clazz' | 'student';
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  studentId?: string;
  menuId?: string;
  ruleId?: string;
  semesterId?: string;
  relTime?: RelTimeType;
  title?: string;
  subTitle?: string;
} & ChakraProps) => {
  const tableRef = useRef<MyTableRef>(null);

  const { data: indicatorTree, isFetching } = useQuery(
    ['indactorDetail', studentId],
    () =>
      getStudentIndicatorTree({
        evaluatedId: studentId,
        menuId,
        ruleId: ruleId!,
        semesterId,
        clazzId
      }).then((res) => {
        let i = 0;
        treeTraverse(res, (node, parent, level) => {
          // 生成id
          node.id = (i++).toString();
        });
        return res;
      }),
    { enabled: !!(studentId && ruleId && semesterId && menuId) }
  );
  console.log(indicatorTree);

  const columns: ColumnsType<EvaluateIndactorType> = [
    {
      title: '评价内容',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => {
        return record.name || record.projectName;
      }
    },
    {
      title: '评分',
      dataIndex: 'score',
      key: 'score',
      render: (_, record) => {
        return record.hasSub !== HasSubIndicator.Yes && !record.hasChildren ? (
          <Box className={RadioStyles['evaluate-radio']}>
            <Radio.Group value={record.scoreLevelValueId} disabled>
              <HStack>
                {record.scoreLevelValues.map((item) => (
                  <Radio.Button key={item.id} value={item.id}>
                    {item.name}
                  </Radio.Button>
                ))}
              </HStack>
            </Radio.Group>
          </Box>
        ) : (
          <></>
        );
      }
    }
  ];

  return (
    <Flex {...props} h="100%">
      <Flex
        px={respDims(24)}
        py={respDims(10)}
        flex="1 0 0"
        flexDir="column"
        align="stretch"
        borderRight={'1px solid #E5E7EB'}
      >
        <Flex align="center">
          <Box
            w={respDims(7)}
            h={respDims('14fpx')}
            bgColor="#175DFF"
            borderRadius={respDims(8)}
          ></Box>
          <Box
            ml={respDims(6)}
            color="rgba(0,0,0,0.9)"
            fontSize={respDims('16fpx')}
            lineHeight={respDims('22fpx')}
            fontWeight="bold"
          >
            {title}
          </Box>
        </Flex>
        <MyTable
          columns={columns}
          dataSource={indicatorTree}
          loading={isFetching}
          pagination={false}
          rowKey="id"
          boxStyle={{
            pt: 0
          }}
          // rowSelection={rowSelection}
          pageConfig={{ showPaginate: false }}
          expandable={{
            defaultExpandAllRows: true,
            expandIcon: CustomExpandIcon
          }}
          ref={tableRef}
        />
      </Flex>
    </Flex>
  );
};

export default Stats;
