import React, { useCallback, useMemo, useState, useRef, useEffect } from 'react';
import {
  Drawer,
  Drawer<PERSON><PERSON>,
  DrawerFooter,
  DrawerHeader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  DrawerCloseButton,
  Button,
  Flex,
  Box,
  Center,
  HStack
} from '@chakra-ui/react';
import { Form, Input, Radio, Checkbox, Popover } from 'antd';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import styles from '@/pages/index.module.scss';
import { useRequest } from '@/hooks/useRequest';
import { getChatMessages } from '@/utils/chat';
import { streamFetch } from '@/utils/fetch';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import MyBox from '@/components/common/MyBox';
import MyTable from '@/components/MyTable';
import { ColumnsType } from 'antd/es/table';
import { MyTableRef } from '@/components/MyTable/types';
import CustomExpandIcon from '@/pages/tenant/evaluateItemManage/components/customExpandIcon';
import RadioStyles from './index.module.scss';
import { Toast } from '@/utils/ui/toast';
import { cloneDeep } from 'lodash';
import { HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';
import { addClazzEvaluate, getStudentIndicatorTree } from '@/api/tenant/evaluate/process';
import {
  EvaluateEntryEnum,
  evaluateEntryMapEvaluateInfoType,
  evaluateEntryMapEvaluateType
} from '@/constants/evaluate';
import { treeToList, treeTraverse } from '@/utils/tree';
import { useQuery } from '@tanstack/react-query';

const LevelEvaluateModal = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  students,
  indactors,
  enterType,
  menuId,
  semesterId,
  ruleId,
  onSuccess,
  onClose
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  students: EvaluateStudentType[];
  indactors?: EvaluateIndactorType[];
  enterType: EvaluateEntryEnum;
  menuId: string;
  semesterId: string;
  ruleId: string;
  onSuccess?: () => void;
  onClose?: () => void;
}) => {
  const title = useMemo(
    () => `点评 ${students.map((it) => it.studentName).join('、')}`,
    [students]
  );

  const [form] = Form.useForm();
  const [loadingField, setLoadingField] = useState<string | null>(null);
  const tableRef = useRef<MyTableRef>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchGrade, setBatchGrade] = useState<string | null>(null);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [selectedRowScoreValues, setSelectedRowScoreValues] = useState<any[]>([]);
  const [data, setData] = useState(
    cloneDeep(
      indactors?.map((item, index) => {
        item.id = index + '';
        item.name = item.projectName;

        return {
          ...item
        };
      })
    ) || []
  );

  // 回显
  useQuery(
    ['indactorOldDetailLevel', students],
    () =>
      getStudentIndicatorTree({
        evaluatedId: students[0].studentId,
        menuId,
        semesterId,
        clazzId,
        ruleId: ruleId!
      }),
    {
      enabled: students.length === 1,
      onSuccess: (data) => {
        if (data && data.length) {
          const oldIndactors = data?.map((item, index) => {
            item.id = index + '';
            item.name = item.projectName;

            return {
              ...item
            };
          });

          setData(oldIndactors || []);
        }
      }
    }
  );

  useEffect(() => {
    setPopoverOpen(false);
  }, [selectedRowKeys]);

  const columns: ColumnsType<EvaluateIndactorType> = [
    {
      title: '评价内容',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '评分',
      dataIndex: 'score',
      key: 'score',
      render: (_, record) => {
        return record.hasSub !== HasSubIndicator.Yes && !record.hasChildren ? (
          <Box className={RadioStyles['evaluate-radio']}>
            <Radio.Group
              value={record.scoreLevelValueId}
              onChange={(target) => {
                const targetValueInfo = record.scoreLevelValues.find(
                  (item) => item.id == target.target.value
                );
                // record.grade = target.target.value;
                record.scoreLevelId = targetValueInfo?.scoreLevelId || '';
                record.scoreLevelValueId = targetValueInfo?.id || '';
                record.scoreLevelValue = targetValueInfo?.name || '';
                setData([...data]);
              }}
            >
              <HStack>
                {record.scoreLevelValues.map((item) => (
                  <Radio.Button key={item.id} value={item.id}>
                    {item.name}
                  </Radio.Button>
                ))}
              </HStack>
            </Radio.Group>
          </Box>
        ) : (
          <></>
        );
      }
    }
  ];

  const availableScoreLevels = useMemo(() => {
    if (selectedRowScoreValues.length === 0) return [];
    const firstScoreValues = selectedRowScoreValues.find((item) => item.length > 0);

    return selectedRowScoreValues.every((scoreValues) =>
      scoreValues.length > 0
        ? JSON.stringify(scoreValues) === JSON.stringify(firstScoreValues)
        : true
    )
      ? firstScoreValues
      : [];
  }, [selectedRowScoreValues]);

  const isBatchGradeDisabled = availableScoreLevels?.length === 0;

  const rowSelection = {
    checkStrictly: false,
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
    onSelectAll: (selected: boolean, selectedRows: any[], info: any) => {
      const scoreLevelValues = selectedRows.find(
        (item) => item.scoreLevelValues.length > 0
      )?.scoreLevelValues;
      setSelectedRowKeys(selectedRowKeys);
      if (selected) {
        setSelectedRowScoreValues((prev) => [...prev, scoreLevelValues]);
      } else {
        setSelectedRowScoreValues((prev) => prev.filter((values) => values !== scoreLevelValues));
      }
    },
    onSelect: (record: any, selected: boolean, selectedRows: any[]) => {
      const scoreLevelValues = selectedRows.find(
        (item) => item.scoreLevelValues.length > 0
      )?.scoreLevelValues;
      setSelectedRowKeys(selectedRowKeys);
      if (selected) {
        setSelectedRowScoreValues((prev) => [...prev, scoreLevelValues]);
      } else {
        setSelectedRowScoreValues((prev) =>
          prev.filter((values) => values !== record.scoreLevelValues)
        );
      }
    },
    getCheckboxProps: (record: any) => ({
      // disabled: !record.isLeaf
    })
  };

  const handleBatchGradeChange = (e: any) => {
    setBatchGrade(e.target.value);
  };

  const handleBatchGradeConfirm = () => {
    if (!batchGrade) {
      return Toast.info('请选择等级');
    }
    console.log('batchGrade', batchGrade);

    const targetValueInfo = availableScoreLevels.find((item: any) => item.id == batchGrade);
    console.log(selectedRowKeys, data);

    treeTraverse(data, (item) => {
      if (selectedRowKeys.includes(item.id || '')) {
        item.scoreLevelId = targetValueInfo?.scoreLevelId || '';
        item.scoreLevelValueId = targetValueInfo?.id || '';
        item.scoreLevelValue = targetValueInfo?.name || '';
      }
    });
    setData([...data]);
    setSelectedRowKeys([]);
    setBatchGrade(null);
    setPopoverOpen(false);
  };

  const batchGradeContent = (
    <Box className={RadioStyles['evaluate-radio']}>
      <HStack spacing={6}>
        <Radio.Group onChange={handleBatchGradeChange} value={batchGrade}>
          <HStack spacing={6}>
            {availableScoreLevels?.map((item: any) => (
              <Radio.Button key={item.id} value={item.id}>
                {item.name}
              </Radio.Button>
            ))}
          </HStack>
        </Radio.Group>
        <Center>
          <SvgIcon
            name="close"
            color="#09244B"
            w="28px"
            h="28px"
            onClick={() => setPopoverOpen(false)}
            cursor="pointer"
          ></SvgIcon>
        </Center>
        <Center>
          <SvgIcon
            name="check"
            color="#09244B"
            w="28px"
            h="28px"
            onClick={handleBatchGradeConfirm}
            cursor="pointer"
          ></SvgIcon>
        </Center>
      </HStack>
    </Box>
  );

  const handleSubmit = async () => {
    const indicators = treeToList(data)
      .filter((node) => {
        return node.hasSub !== HasSubIndicator.Yes && !node.hasChildren;
      })
      .map((item) => {
        if (item.scoreLevelValueId && !item.scoreLevelValue) {
          const targetValueInfo = item.scoreLevelValues.find(
            (it) => it.id == item.scoreLevelValueId
          );
          item.scoreLevelId = targetValueInfo?.scoreLevelId;
          item.scoreLevelValueId = targetValueInfo?.id;
          item.scoreLevelValue = targetValueInfo?.name || '';
        }
        return {
          indicatorId: item.id,
          dimensionId: item.dimensionId,
          evaluationType: item.evaluationType,
          scoreLevelId: item.scoreLevelId,
          scoreLevelValueId: item.scoreLevelValueId,
          scoreLevelValue: item.scoreLevelValue
        };
      });

    // 校验所有需要评价的项是否都有 scoreLevelValueId 且不等于 0
    const invalidIndicators = indicators.filter(
      (indicator) => !indicator.scoreLevelValueId || indicator.scoreLevelValueId == '0'
    );
    if (invalidIndicators.length > 0) {
      return Toast.error('所有评价项都必须有评分');
    }

    const payload = {
      entrance: enterType,
      menuId: menuId,
      semesterId,
      clazzId: parseInt(clazzId),
      ruleId,
      subjectId: parseInt(subjectId || '0'),
      evaluatedIds: students.map((student) => student.studentId),
      indicators
    };

    try {
      await addClazzEvaluate(payload as any);
      Toast.success('评价成功');
      onSuccess?.();
      onClose?.();
    } catch (error) {
      Toast.error('评价失败');
    }
  };

  return (
    <>
      <Drawer
        isOpen={true}
        placement="right"
        onClose={() => onClose?.()}
        size="md"
        closeOnOverlayClick={false}
      >
        <DrawerOverlay />
        <DrawerContent minW="800px">
          <DrawerCloseButton />
          <DrawerHeader borderBottom={'1px solid #E5E6EB'}>{title}</DrawerHeader>

          <DrawerBody>
            <Flex fontSize={respDims(20)} fontWeight={500} alignItems="center" mt={3}>
              <Box bgColor="primary.500" w="7px" h="14px" mr={2} borderRadius="8px"></Box>
              {evaluateEntryMapEvaluateInfoType[enterType].name}
            </Flex>
            <MyTable
              columns={columns}
              dataSource={data}
              pagination={false}
              rowKey="id"
              boxStyle={{
                pt: 0
              }}
              rowSelection={rowSelection}
              pageConfig={{ showPaginate: false }}
              expandable={{
                defaultExpandAllRows: true,
                expandIcon: CustomExpandIcon,
                childrenColumnName: 'children'
              }}
              ref={tableRef}
            />
          </DrawerBody>

          <DrawerFooter
            borderTop={'1px solid #E5E6EB'}
            display="flex"
            justifyContent="space-between"
          >
            <Popover
              content={batchGradeContent}
              title=""
              trigger={'click'}
              open={popoverOpen}
              onOpenChange={() => {
                if (selectedRowKeys.length) {
                  setPopoverOpen(true);
                }
              }}
              overlayStyle={{ zIndex: 2000 }}
            >
              <Button
                variant={'primaryShallow'}
                px={respDims(50)}
                isDisabled={selectedRowKeys.length == 0 || isBatchGradeDisabled}
              >
                批量评分
              </Button>
            </Popover>
            <Box>
              <Button variant="grayBase" mr={3} onClick={onClose}>
                取消
              </Button>

              <Button colorScheme="blue" onClick={() => handleSubmit()}>
                确认
              </Button>
            </Box>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default LevelEvaluateModal;
