import MyModal from '@/components/MyModal';
import { Box, Center, Flex, HStack, Image } from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { addClazzEvaluate } from '@/api/tenant/evaluate/process';
import { useRouter } from 'next/router';
import { serializeData } from '@/utils/tools';
import SubTabs from '../../../../SubTabs';
import { respDims } from '@/utils/chakra';
import EvaluateItem from '../EvaluateItem';
import { treeToList } from '@/utils/tree';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { Toast } from '@/utils/ui/toast';
import EvaluateEffect from '../EvaluateEffect';

type TargetIndactorType = Omit<EvaluateIndactorType, 'children'> & {
  items: EvaluateIndactorType[];
};
const findTargetChildren = (
  children: EvaluateIndactorType[],
  targetNames: string[]
): TargetIndactorType[] => {
  return children.reduce((acc: TargetIndactorType[], item: EvaluateIndactorType) => {
    if (targetNames.includes(item.name!)) {
      acc.push({
        ...item,
        items: item.children ? findTargetChildren(item.children, targetNames) : []
      });
    }
    if (item.children) {
      acc = acc.concat(findTargetChildren(item.children, targetNames));
    }
    return acc;
  }, []);
};

const EvaluateModal = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  students,
  indactors,
  menuId,
  ruleId,
  semesterId,
  onSuccess,
  onClose
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId: string;
  students: EvaluateStudentType[];
  indactors?: EvaluateIndactorType[];
  menuId: string;
  ruleId: string;
  semesterId: string;
  onSuccess?: (evaluate: EvaluateIndactorType) => void;
  onClose?: () => void;
}) => {
  const router = useRouter();

  const [activeTab, setActiveTab] = useState('');
  const [activeTabName, setActiveTabName] = useState('');

  const [isClosing, setIsClosing] = useState(false);

  const { openOverlay } = useOverlayManager();

  const tabList = useMemo(() => {
    return treeToList(indactors || [])
      ?.filter((child) => ['待改进', '表扬'].includes(child.name!))
      .map((child) => ({
        id: child.id,
        name: child.name,
        items: child.children
      }));
  }, [indactors]);

  const tabItems = useMemo(() => {
    const selectedTab = tabList.find((it) => it.id === activeTab);
    setActiveTabName(selectedTab?.name || '');
    return selectedTab?.items || tabList[0]?.items || [];
  }, [tabList, activeTab]);

  const title = useMemo(
    () => `点评 ${students.map((it) => it.studentName).join('、')}`,
    [students]
  );

  const handleEvaluate = async (item: EvaluateIndactorType) => {
    const targetValueInfo = item.scoreLevelValues.find((it) => it.id == item.scoreLevelValueId);
    const indicator = {
      indicatorId: item.id,
      dimensionId: item.dimensionId,
      evaluationType: item.evaluationType,
      scoreLevelId: targetValueInfo?.scoreLevelId,
      scoreLevelValueId: targetValueInfo?.id,
      scoreLevelValue: item.scoreLevelValue,
      score: item?.score || 0
    };

    const payload = {
      menuId: menuId,
      semesterId,
      clazzId: parseInt(clazzId),
      ruleId,
      subjectId: parseInt(subjectId || '0'),
      evaluatedIds: students.map((student) => student.studentId),
      indicators: [indicator]
    };

    try {
      await addClazzEvaluate(payload as any);
      Toast.success('评价成功');
      setIsClosing(true);
      onSuccess?.(item);
      openOverlay({
        Overlay: EvaluateEffect,
        props: {
          item,
          students,
          activeTabName,
          onClose
        }
      });
    } catch (error) {
      Toast.error('评价失败');
    }
  };

  useEffect(() => setActiveTab((state) => (state ? state : tabList[0]?.id || '')), [tabList]);

  const handleToClassPage = (tab: 'clazz' | 'student') =>
    router.push({
      pathname: '/tenant/teacherEvaluate/culturalKnowledge/classroomPerformanceRecord',
      query: {
        q: serializeData({
          isClazzTeacher,
          gradeId,
          clazzId,
          clazzName,
          menuId,
          semesterId,
          tab: tab
        })
      }
    });

  return (
    <MyModal
      isOpen={!isClosing}
      isCentered
      title={
        <Box ml="5px" maxW="12em" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">
          {title}
        </Box>
      }
      maxW="684px"
      w="684px"
      h="473"
      onClose={onClose}
    >
      {tabItems && tabItems.length === 0 ? (
        <Center flexDir="column" w="100%" h="100%">
          <Flex align="flex-end">
            <Image src="/imgs/evaluate/evaluate_empty.png" alt="" w="24px" h="24px" />
            <Box ml="6px" color="#303133" fontSize="16px" lineHeight="22px">
              未设置评价指标，请先至评价规则中设置指标。
            </Box>
          </Flex>
          <HStack mt="38px" spacing="16px">
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() => handleToClassPage('clazz')}
            >
              班级表现
            </Center>
            <Center
              w="183px"
              h="40px"
              color="primary.500"
              fontSize="16px"
              fontWeight="bold"
              bgColor="primary.50"
              borderRadius="100px"
              cursor="pointer"
              onClick={() => handleToClassPage('student')}
            >
              学生表现
            </Center>
          </HStack>
        </Center>
      ) : (
        <Flex direction="column" w="100%" pl="28px" overflow="hidden">
          <Flex mt="24px" justifyContent="space-between" alignItems="center">
            <SubTabs
              labelKey="name"
              valueKey="id"
              value={activeTab}
              list={tabList}
              onChange={setActiveTab}
            />
            <Box
              mr={respDims(32)}
              fontSize={respDims(16, 14)}
              onClick={() => handleToClassPage('student')}
              color="#4E5969"
              cursor="pointer"
            >
              查看学生表现
            </Box>
          </Flex>

          <Flex flex="1" my="30px" align="center" mr="-84px" flexWrap="wrap" overflowY="auto">
            {tabItems.map((item: EvaluateIndactorType, index) => (
              <EvaluateItem
                key={item.id}
                mr={(index + 1) % 5 ? '54.8px' : 0}
                mt={index >= 5 ? '30px' : 0}
                item={item}
                onClick={() => handleEvaluate(item)}
              />
            ))}
          </Flex>
        </Flex>
      )}
    </MyModal>
  );
};

export default EvaluateModal;
