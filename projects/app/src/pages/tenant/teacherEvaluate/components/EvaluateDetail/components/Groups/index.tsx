import { ChakraProps } from '@chakra-ui/system';
import { Box, Flex, HStack } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import GroupCountModal from '../../../GroupCountModal';
import AdjustGroupModal from '../../../AdjustGroupModal';
import { ForwardedRef, forwardRef, useImperativeHandle } from 'react';
import { EvaluateGroupType, EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import GroupList from '../../../GroupList';
import GroupEvaluateModal from '../../../GroupEvaluateModal';
import EvaluateModal from '../ClassEvaluateModal/EvaluateModal';
import SvgIcon from '@/components/SvgIcon';
import { useQuery } from '@tanstack/react-query';
import { getGroupList } from '@/api/tenant/evaluate/process';
import { useEvaluateContext } from '../../../EvaluateContext';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import TeacherSayEvaluateDrawer from '../TeacherSayEvaluateModal';
import LevelEvaluateModal from '../LevelEvaluateModal';
import ImportPanelV2 from '@/components/ImportPanelV2';

export type GroupsRef = {
  showAddGroups: () => void;
  showAdjustGroups: () => void;
};

const Groups = (
  {
    searchKey,
    ...props
  }: {
    searchKey?: string;
    indactors?: EvaluateIndactorType[];
  } & ChakraProps,
  ref: ForwardedRef<GroupsRef>
) => {
  const {
    enterType,
    semesterId,
    subjectId,
    ruleId,
    clazzId,
    isSubjectEntry,
    gradeId,
    indactors,
    menuId,
    clazzName,
    isClazzTeacher
  } = useEvaluateContext();
  const {
    data: groups = [],
    isFetched: isGroupsFetched,
    refetch: refetchGroups
  } = useQuery(
    ['groups', clazzId, subjectId, ruleId, semesterId],
    () => {
      if (!!(clazzId && ruleId && semesterId && (isSubjectEntry ? subjectId : true))) {
        return getGroupList({
          clazzId,
          subjectId,
          ruleId,
          semesterId,
          entrance: enterType
        });
      }
      return [];
    },
    {}
  );

  const { openOverlay } = useOverlayManager();

  const onAdjustGroup = (options?: { incCount?: number }) => {
    openOverlay({
      Overlay: AdjustGroupModal,
      props: {
        clazzId,
        semesterId,
        subjectId,
        ruleId,
        menuId,
        incCount: options?.incCount,
        onSuccess: () => {
          refetchGroups();
        }
      }
    });
  };

  const onInitGroup = () => {
    openOverlay({
      Overlay: GroupCountModal,
      props: {
        onSuccess: (count) => onAdjustGroup({ incCount: count })
      }
    });
  };

  const onClickGroup = (group: EvaluateGroupType) => {
    if (!gradeId || !clazzId || !(isSubjectEntry ? subjectId : true)) {
      return;
    }

    if (indactors && !indactors.length) {
      openOverlay({
        Overlay: EvaluateModal,
        props: {
          isClazzTeacher,
          menuId,
          ruleId,
          semesterId,
          gradeId,
          clazzId,
          clazzName,
          subjectId,
          students: group.studentList || [],
          indactors
        }
      });
      return;
    }

    openOverlay({
      Overlay: GroupEvaluateModal,
      props: {
        group,
        clazzId,
        ruleId,
        entrance: enterType,
        semesterId,
        onEvaluate(students) {
          if (
            enterType === EvaluateEntryEnum.SemesterReview ||
            enterType === EvaluateEntryEnum.AdaptiveLearning ||
            enterType === EvaluateEntryEnum.GoalAchievement
          ) {
            openOverlay({
              Overlay: LevelEvaluateModal,
              props: {
                isClazzTeacher,
                gradeId,
                clazzId,
                clazzName,
                subjectId,
                students,
                indactors,
                enterType,
                menuId,
                ruleId,
                semesterId,
                onSuccess: () => {
                  refetchGroups();
                }
              }
            });
          } else if (enterType === EvaluateEntryEnum.TeacherComments) {
            openOverlay({
              Overlay: TeacherSayEvaluateDrawer,
              props: {
                isClazzTeacher,
                gradeId,
                clazzId,
                clazzName,
                subjectId,
                students,
                indactors,
                enterType,
                menuId,
                ruleId,
                semesterId,
                onSuccess: () => {
                  refetchGroups();
                }
              }
            });
          } else if (enterType === EvaluateEntryEnum.ClassroomPerformance) {
            openOverlay({
              Overlay: EvaluateModal,
              props: {
                isClazzTeacher,
                menuId,
                ruleId,
                semesterId,
                gradeId,
                clazzId,
                clazzName,
                subjectId,
                students,
                indactors,
                onSuccess: () => {
                  refetchGroups();
                }
              }
            });
          }
        }
      }
    });
  };

  const handleImportGroup = () => {
    openOverlay({
      Overlay: ImportPanelV2,
      props: {
        title: '导入分组表',
        templateUrl: '/huayun-evaluation/clazz/student/clazzGroup/downloadTemplate',
        importUrl: '/huayun-evaluation/clazz/student/clazzGroup/import',
        appendParams: {
          clazzId,
          semesterId,
          subjectId,
          ruleId,
          enterType
        },
        onUploadSuccess: () => {
          refetchGroups();
        }
      }
    });
  };

  useImperativeHandle(ref, () => ({
    showAddGroups: () => {
      openOverlay({
        Overlay: GroupCountModal,
        props: {
          onSuccess: (count) => onAdjustGroup({ incCount: count })
        }
      });
    },
    showAdjustGroups: () => onAdjustGroup()
  }));

  const filteredGroups = groups.filter((group) => group.groupName.includes(searchKey!));

  return (
    <Flex direction="column" w="100%" h="100%" {...props}>
      {groups.length ? (
        <GroupList
          flex="1 0 0"
          groups={filteredGroups}
          entryType={enterType}
          mx={respDims(24)}
          mb={respDims(24)}
          overflowY="scroll"
          onClickGroup={onClickGroup}
        />
      ) : (
        isGroupsFetched && (
          <Flex direction="column" flex="1" justify="center" align="center" mb="80px">
            <SvgIcon name="empty" w={respDims(100)} h={respDims(100)} mb={respDims(18)} />
            <Box>班级暂未进行团队分组，需班主任安排</Box>
            {isClazzTeacher && (
              <HStack spacing={2}>
                <Box
                  mt={respDims(18)}
                  px={respDims(44)}
                  py={respDims(9)}
                  color="primary.500"
                  bgColor="primary.50"
                  fontSize={respDims('16fpx')}
                  lineHeight={respDims('22fpx')}
                  borderRadius={respDims(100)}
                  cursor="pointer"
                  _hover={{
                    bgColor: '#f3f5fe'
                  }}
                  onClick={onInitGroup}
                >
                  快速随机分组
                </Box>
                <Box
                  mt={respDims(18)}
                  px={respDims(44)}
                  py={respDims(9)}
                  color="primary.500"
                  bgColor="primary.50"
                  fontSize={respDims('16fpx')}
                  lineHeight={respDims('22fpx')}
                  borderRadius={respDims(100)}
                  cursor="pointer"
                  onClick={handleImportGroup}
                >
                  导入分组表
                </Box>
              </HStack>
            )}
          </Flex>
        )
      )}
    </Flex>
  );
};

export default forwardRef(Groups);
