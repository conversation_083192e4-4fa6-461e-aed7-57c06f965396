import React, { useC<PERSON>back, useMemo, useState, useEffect, useRef } from 'react';
import {
  Drawer,
  Drawer<PERSON><PERSON>,
  DrawerFooter,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  DrawerCloseButton,
  Button,
  useDisclosure,
  Flex,
  Image,
  Box,
  Center,
  Text
} from '@chakra-ui/react';
import { Form, Input } from 'antd';
import QRCode from 'qrcode';
import {
  AddClazzEvaluateProps,
  EvaluateIndactorType,
  EvaluateStudentType
} from '@/types/api/tenant/evaluate/process';
import styles from '@/pages/index.module.scss';
import { useRequest } from '@/hooks/useRequest';
import { getChatMessages } from '@/utils/chat';
import { streamFetch } from '@/utils/fetch';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';
import MyBox from '@/components/common/MyBox';
import { nanoid } from 'nanoid';
import { getToken } from '@/utils/auth';
import { Toast } from '@/utils/ui/toast';
import { getUploadSignatureInfo } from '@/api/tenant/evaluate/teacherSay';
import { getFileMeta } from '@/api/file';
import { useEvaluateContext } from '../../../EvaluateContext';
import { EvaluateEntryEnum } from '@/constants/evaluate';
import { addClazzEvaluate, getStudentIndicatorTree } from '@/api/tenant/evaluate/process';
import { treeToList } from '@/utils/tree';
import { HasSubIndicator, NeedSignature } from '@/constants/api/tenant/evaluate/rule';
import { useQuery } from '@tanstack/react-query';

const TeacherSayEvaluateDrawer = ({
  enterType,
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  subjectId,
  students,
  indactors,
  onSuccess,
  onClose,
  menuId,
  ruleId,
  semesterId
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName?: string;
  subjectId?: string;
  students: EvaluateStudentType[];
  indactors?: EvaluateIndactorType[];
  enterType: EvaluateEntryEnum;
  onSuccess?: () => void;
  onClose?: () => void;
  menuId?: string;
  ruleId?: string;
  semesterId?: string;
}) => {
  const title = useMemo(
    () => `点评 ${students.map((it) => it.studentName).join('、')}`,
    [students]
  );

  const [form] = Form.useForm();
  const [loadingField, setLoadingField] = useState<string | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [signatureId, setSignatureId] = useState<string | null>(nanoid());
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isQrCodeExpired, setIsQrCodeExpired] = useState<boolean>(false);

  // 回显
  useQuery(
    ['indactorOldDetail', students],
    () =>
      getStudentIndicatorTree({
        evaluatedId: students[0].studentId,
        menuId,
        semesterId,
        clazzId,
        ruleId: ruleId!
      }),
    {
      enabled: students.length === 1,
      onSuccess: (data) => {
        const oldAchievementIndicator = treeToList(data || [])?.find(
          (it) => it.name === '突出成绩记载'
        );
        const oldTeacherSayIndicator = treeToList(data || [])?.find(
          (it) => it.name === '班主任寄语'
        );
        if (oldAchievementIndicator) {
          form.setFieldValue('achievement', oldAchievementIndicator?.comment);
        }
        if (oldTeacherSayIndicator) {
          form.setFieldValue('sendWord', oldTeacherSayIndicator?.comment);
          form.setFieldValue('signFileKey', oldTeacherSayIndicator?.signFile?.fileKey);
          setImageSrc(oldTeacherSayIndicator?.signFile?.fileUrl || null);
        }
      }
    }
  );

  const achievementIndicator = useMemo(() => {
    return treeToList(indactors || [])?.find((it) => it.name === '突出成绩记载');
  }, [indactors]);

  const teacherSayIndicator = useMemo(() => {
    return treeToList(indactors || [])?.find((it) => it.name === '班主任寄语');
  }, [indactors]);

  let timer = useRef<NodeJS.Timeout | undefined>(undefined);

  const { mutate: onOptimizePrompt } = useRequest({
    mutationFn: async (field: string) => {
      const originText = form.getFieldValue(field);
      if (!originText) {
        Toast.info('请先输入内容');
        return Promise.reject();
      }
      const content = `原来的描述：【${originText}】；帮我优化扩写描述内容，只回答扩写完后内容，不需要其他赘述`;
      const { messages } = getChatMessages({
        text: content
      });
      return streamFetch({
        url: '/huayun-ai/system/fast/structuredPrompt',
        onMessage: () => {},
        abortCtrl: new AbortController(),
        data: {
          messages,
          appId: '67457ecf3d721f6a2faa0e4c'
        }
      }).then(({ responseText: text }) => {
        return { str: text, field: field };
      });
    },
    onSuccess: ({ str, field }) => {
      console.log(str, field);
      form.setFieldValue(field, str);
      setLoadingField(null);
    },
    onError() {
      setLoadingField(null);
    }
  });

  const onFinish = async (values: any) => {
    const indicators = [];
    if (achievementIndicator) {
      indicators.push({
        evaluationType: achievementIndicator.evaluationType,
        dimensionId: achievementIndicator.dimensionId,
        indicatorId: achievementIndicator.id,
        comment: values.achievement,
        signFileKey: ''
      });
    }
    if (teacherSayIndicator) {
      indicators.push({
        evaluationType: teacherSayIndicator.evaluationType,
        dimensionId: teacherSayIndicator.dimensionId,
        indicatorId: teacherSayIndicator.id,
        comment: values.sendWord,
        signFileKey: values.signFileKey
      });
    }

    if (!indactors?.length) {
      Toast.info('请先填写评价内容');
      return;
    }

    const payload: AddClazzEvaluateProps = {
      evaluatedIds: students.map((student) => student.studentId),
      clazzId,
      subjectId,
      indicators,
      menuId,
      ruleId,
      semesterId,
      entrance: enterType
    };

    try {
      await addClazzEvaluate(payload);
      Toast.success('评价成功');
      onSuccess?.();
      onClose?.();
    } catch (error) {
      Toast.error('评价失败');
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const handleOptimize = useCallback(
    (field: string) => {
      setLoadingField(field);
      onOptimizePrompt(field);
    },
    [onOptimizePrompt]
  );

  const getUploadSignature = useCallback(async () => {
    timer && clearTimeout(timer.current);
    const res = await getUploadSignatureInfo(signatureId!);
    if (!res) {
      timer.current = setTimeout(() => {
        getUploadSignature();
      }, 2000);
    } else {
      const fileMeta = await getFileMeta(res);
      if (fileMeta) {
        form.setFieldValue('signFileKey', res);
        setImageSrc(fileMeta.fileUrl);
      }
    }
  }, [signatureId, timer]);

  const generateQrCode = useCallback(async () => {
    try {
      // 判断环境
      const isDev = process.env.NODE_ENV === 'development';
      const host = isDev ? 'http://***********:8081' : location.origin;
      const url = `${host}/evaluate/pages/signature.html?signatureId=${signatureId}&token=${getToken()}`; // 替换为实际的签名页面URL
      console.log(url);

      const qrCodeDataUrl = await QRCode.toDataURL(url);
      setQrCodeUrl(qrCodeDataUrl);
      setIsQrCodeExpired(false);
    } catch (error) {
      console.error('Failed to generate QR code', error);
    }
  }, [signatureId, getUploadSignature]);

  useEffect(() => {
    generateQrCode();

    return () => {
      timer && clearTimeout(timer.current);
    };
  }, [generateQrCode, timer]);

  useEffect(() => {
    qrCodeUrl && getUploadSignature();
  }, [qrCodeUrl, getUploadSignature]);

  const handleResign = () => {
    setImageSrc(null);
    setSignatureId(nanoid());
    generateQrCode();
  };

  return (
    <>
      <Drawer
        isOpen={true}
        placement="right"
        onClose={() => onClose?.()}
        size="md"
        closeOnOverlayClick={false}
      >
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader borderBottom={'1px solid #E5E6EB'}>{title}</DrawerHeader>

          <DrawerBody>
            <Flex fontSize={respDims(20)} fontWeight={500} mb={1} alignItems="center" my={3}>
              <Box bgColor="primary.500" w="7px" h="14px" mr={2} borderRadius="8px"></Box>
              老师的话
            </Flex>
            <Form
              form={form}
              name="teacher_say_evaluate"
              onFinish={onFinish}
              onFinishFailed={onFinishFailed}
              layout="vertical"
              className={styles['my-form']}
            >
              {achievementIndicator && (
                <>
                  <Flex alignItems="center" justifyContent="space-between" mb={3}>
                    <Box color="#4e5969">突出成绩记载</Box>
                    {/* <Flex
                      justifyContent="center"
                      alignItems="center"
                      bg="linear-gradient(90deg, #2072FF 0%, #28D3FF 100%)"
                      borderRadius="50px"
                      color="#fff"
                      cursor="pointer"
                      px={respDims(17)}
                      py={respDims(5)}
                      fontSize={respDims(12, 10)}
                      onClick={() => handleOptimize('achievement')}
                    >
                      <SvgIcon name="optimizePrompt" mr={respDims(5)}></SvgIcon>
                      {'AI一键生成'}
                    </Flex> */}
                  </Flex>
                  <MyBox isLoading={loadingField === 'achievement'}>
                    <Form.Item name="achievement">
                      <Input.TextArea placeholder="请输入" rows={7} />
                    </Form.Item>
                  </MyBox>
                </>
              )}
              {teacherSayIndicator && (
                <>
                  <Flex alignItems="center" justifyContent="space-between" mb={3}>
                    <Box
                      color="#4e5969"
                      _before={{
                        content: '"*"',
                        color: '#F53F3F'
                      }}
                    >
                      班主任寄语
                    </Box>

                    {/* <Flex
                      justifyContent="center"
                      alignItems="center"
                      bg="linear-gradient(90deg, #2072FF 0%, #28D3FF 100%)"
                      borderRadius="50px"
                      color="#fff"
                      cursor="pointer"
                      px={respDims(17)}
                      py={respDims(5)}
                      fontSize={respDims(12, 10)}
                      onClick={() => handleOptimize('sendWord')}
                    >
                      <SvgIcon name="optimizePrompt" mr={respDims(5)}></SvgIcon>
                      {'AI一键生成'}
                    </Flex> */}
                  </Flex>
                  <MyBox isLoading={loadingField === 'sendWord'}>
                    <Form.Item
                      name="sendWord"
                      rules={[{ required: true, message: '请输入班主任寄语' }]}
                    >
                      <Input.TextArea placeholder="请输入" rows={7} />
                    </Form.Item>
                  </MyBox>

                  <Box
                    color="#4e5969"
                    _before={{
                      content: '"*"',
                      color: '#F53F3F'
                    }}
                    mb={3}
                  >
                    班主任签名（微信扫码手写签名）
                  </Box>
                  <Form.Item
                    name="signFileKey"
                    label=""
                    rules={[{ required: true, message: '请扫码签名' }]}
                  >
                    <Flex flexDir="column" justifyContent="center">
                      {imageSrc ? (
                        <>
                          <Image
                            src={imageSrc}
                            alt="Signature"
                            w="263px"
                            h="150px"
                            borderRadius="8px"
                            border="1px solid #E5E7EB"
                          />
                          <Text color="primary.500" cursor="pointer" mt={2} onClick={handleResign}>
                            重新签名
                          </Text>
                        </>
                      ) : (
                        <>
                          {qrCodeUrl && !isQrCodeExpired ? (
                            <Image src={qrCodeUrl} alt="QR Code" w="175px" h="175px" />
                          ) : (
                            <Box w="175px" h="175px" bg="gray.200" />
                          )}
                          {isQrCodeExpired ? (
                            <Text
                              color="primary.500"
                              cursor="pointer"
                              mt={2}
                              onClick={handleResign}
                            >
                              二维码失效，重新获取签名二维码
                            </Text>
                          ) : (
                            <Center w="175px" mt={1} color="#4E5969" fontSize={respDims(14, 12)}>
                              请用微信扫码
                            </Center>
                          )}
                        </>
                      )}
                    </Flex>
                  </Form.Item>
                </>
              )}
            </Form>
          </DrawerBody>

          <DrawerFooter borderTop={'1px solid #E5E6EB'}>
            <Button variant="outline" mr={3} onClick={onClose}>
              取消
            </Button>
            <Button colorScheme="blue" onClick={() => form.submit()}>
              确认
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default TeacherSayEvaluateDrawer;
