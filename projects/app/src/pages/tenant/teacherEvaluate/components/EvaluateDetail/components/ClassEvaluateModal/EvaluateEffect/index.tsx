import { Box, Center, Flex, Image, Text } from '@chakra-ui/react';
import StudentAvatar from '../../../../StudentAvatar';
import { EvaluateIndactorType, EvaluateStudentType } from '@/types/api/tenant/evaluate/process';
import { useMemo, useState, useEffect } from 'react';
import { IndactorTypeEnum, IndactorTypeNameMap } from '@/constants/api/tenant/evaluate/process';
import { UseLogo } from '@/constants/api/tenant/evaluate/rule';

const EvaluateEffect = ({
  item,
  students,
  activeTabName,
  onClose
}: {
  item: EvaluateIndactorType;
  activeTabName?: string;
  students: EvaluateStudentType[];
  onClose?: () => void;
}) => {
  const name = useMemo(() => students.map((it) => it.studentName).join('、'), [students]);
  const [timeLeft, setTimeLeft] = useState(5);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      onClose?.();
    }
  }, [timeLeft, onClose]);

  return (
    <Center
      flexDir="column"
      pos="fixed"
      left="0"
      right="0"
      top="0"
      bottom="0"
      zIndex="9999"
      bgColor="rgba(0,0,0,0.47)"
      onClick={onClose}
    >
      <Box
        w="397px"
        h="264px"
        pos="relative"
        bgImage={
          activeTabName == '表扬' ? '/imgs/evaluate/good_bg.svg' : '/imgs/evaluate/bad_bg.svg'
        }
      >
        <Center
          pos="absolute"
          left="300px"
          top="46px"
          right="36px"
          bottom="198px"
          overflow="hidden"
          fontSize="14px"
          color="#1F1F1F"
        >
          {`${activeTabName} x1`}
        </Center>

        <Flex pos="absolute" left="114px" right="114px" top="42px" h="154px" overflow="hidden">
          {students.length == 1 ? (
            <StudentAvatar w="170px" h="170px" pos="relative" student={students[0]} />
          ) : (
            <Image
              w="170px"
              h="170px"
              alt="0"
              src="/imgs/evaluate/avatar/boyngirl.png"
              borderRadius="50%"
            />
          )}
        </Flex>

        <Center
          pos="absolute"
          left="65px"
          top="194px"
          right="65px"
          h="55px"
          px="30px"
          overflow="hidden"
        >
          <Box
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            color="#ffffff"
            fontSize="20px"
            fontWeight="500"
          >
            {name}
          </Box>
        </Center>
      </Box>
      <Flex mt="17px" w="355px" align="center">
        <Box mr="8px" flex="1" h="1px" bgColor="rgba(255,255,255,0.7)"></Box>
        {item?.iconFileKey && item.isUseLogo == UseLogo.Yes && (
          <Image src={item.iconFile?.fileUrl} w="24px" h="24px" borderRadius="50%" alt="" />
        )}
        <Box ml="10px" fontSize="24px" color="#EBEBEB" fontWeight="bold">
          {item.name}
        </Box>
        <Box ml="8px" flex="1" h="1px" bgColor="rgba(255,255,255,0.7)"></Box>
      </Flex>
      <Text mt="10px" color="#EBEBEB" fontSize="14px">
        倒计时关闭: {timeLeft} 秒
      </Text>
    </Center>
  );
};

export default EvaluateEffect;
