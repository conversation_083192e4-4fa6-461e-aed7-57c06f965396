import MySelect from '@/components/MySelect';
import { respDims } from '@/utils/chakra';
import { Center } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';
import dayjs from 'dayjs';

export type RelTimeType = {
  label: string;
  value: string;
  getTime: () => { startTime: string; endTime: string };
};

export const RelTimes: RelTimeType[] = [
  {
    label: '本周',
    value: 'week',
    getTime: () => {
      const now = dayjs();
      return {
        startTime: now.startOf('week').format('YYYY-MM-DD'),
        endTime: now.endOf('week').format('YYYY-MM-DD')
      };
    }
  },
  {
    label: '今天',
    value: 'today',
    getTime: () => {
      const now = dayjs();
      return {
        startTime: now.format('YYYY-MM-DD'),
        endTime: now.format('YYYY-MM-DD')
      };
    }
  },
  {
    label: '上周',
    value: 'preWeek',
    getTime: () => {
      const now = dayjs().subtract(7, 'day');
      return {
        startTime: now.startOf('week').format('YYYY-MM-DD'),
        endTime: now.endOf('week').format('YYYY-MM-DD')
      };
    }
  },
  {
    label: '本月',
    value: 'month',
    getTime: () => {
      const now = dayjs();
      return {
        startTime: now.startOf('month').format('YYYY-MM-DD'),
        endTime: now.endOf('month').format('YYYY-MM-DD')
      };
    }
  }
];

const RelTimeSelect = ({
  value,
  onChange,
  ...props
}: { value?: RelTimeType; onChange?: (value: RelTimeType) => void } & ChakraProps) => {
  return (
    <Center h={respDims('36fpx')} overflow="hidden" borderRadius={respDims(8)} {...props}>
      <MySelect
        list={RelTimes}
        value={value?.value}
        border="none"
        placeholder="请选择时间段"
        borderRadius="50px"
        bgColor="rgba(0,0,0,0.03)"
        minW={respDims('120fpx')}
        h={respDims('36fpx')}
        onchange={(e) => onChange?.(RelTimes.find((it) => it.value === e)!)}
      />
    </Center>
  );
};

export default RelTimeSelect;
