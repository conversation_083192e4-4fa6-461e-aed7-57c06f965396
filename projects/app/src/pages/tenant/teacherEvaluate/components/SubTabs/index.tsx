import { respDims } from '@/utils/chakra';
import { Center, Flex } from '@chakra-ui/react';
import { ChakraProps } from '@chakra-ui/system';

const SubTabs = <DT extends Record<PropertyKey, any>, VT extends any = string>({
  list,
  value,
  labelKey = 'label' as keyof DT,
  valueKey = 'value' as keyof DT,
  onChange,
  ...props
}: {
  list?: DT[];
  value?: VT;
  labelKey?: keyof DT;
  valueKey?: keyof DT;
  onChange?: (value: VT) => void;
} & ChakraProps) => {
  return (
    <Flex
      px={respDims(6)}
      py={respDims(4)}
      h={respDims('38fpx')}
      boxSizing="border-box"
      display="inline-flex"
      align="center"
      bgColor="#F9FAFB"
      borderRadius={respDims(50)}
      {...props}
    >
      {list?.map((item) => (
        <Center
          flex="0 0 auto"
          key={item[valueKey]}
          h="100%"
          minW={respDims(76)}
          px={respDims(12)}
          boxSizing="content-box"
          {...(item[valueKey] === value
            ? { color: '#165ddf', bgColor: '#ffffff', fontWeight: 'bold' }
            : { color: '#4e5969' })}
          borderRadius={respDims(50)}
          cursor="pointer"
          userSelect="none"
          onClick={() => item[valueKey] !== value && onChange?.(item[valueKey])}
        >
          {item[labelKey]}
        </Center>
      ))}
    </Flex>
  );
};

export default SubTabs;
