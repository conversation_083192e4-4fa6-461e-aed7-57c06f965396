import { ChakraProps, Flex } from '@chakra-ui/react';
import { respDims } from '@/utils/chakra';
import { useState } from 'react';
import { Navs, NavType } from './Sidebar';
import { RelTimeType } from '../../../../components/RelTimeSelect';
import Rank from './Rank';

const ClazzPanel = ({
  isClazzTeacher,
  gradeId,
  clazzId,
  clazzName,
  menuId,
  ruleId,
  semesterId,
  subjectId,
  relTime,
  onSidebarChange,
  ...props
}: {
  isClazzTeacher?: boolean;
  gradeId: string;
  clazzId: string;
  clazzName: string;
  menuId: string;
  semesterId: string;
  ruleId: string;
  subjectId?: string;
  relTime?: RelTimeType;
  onSidebarChange?: (nav: NavType) => void;
} & ChakraProps) => {
  const [nav, setNav] = useState<NavType>(Navs[0]);

  return (
    <Flex
      align="stretch"
      border="1px solid #E5E7EB"
      borderRadius={respDims(14)}
      overflow="hidden"
      {...props}
    >
      <Rank
        flex="1 0 0"
        clazzId={clazzId}
        subjectId={subjectId}
        ruleId={ruleId}
        menuId={menuId}
        semesterId={semesterId}
        relTime={relTime}
      />
    </Flex>
  );
};

export default ClazzPanel;
