import { getClazzEvaluateRankList, getClassRankList } from '@/api/tenant/evaluate/process';
import MyTable from '@/components/MyTable';
import { IndactorTypeEnum, IndactorTypeNameMap } from '@/constants/api/tenant/evaluate/process';
import { RelTimeType } from '@/pages/tenant/teacherEvaluate/components/RelTimeSelect';
import { ClazzEvaluateRecordType, RankDataTree } from '@/types/api/tenant/evaluate/process';
import { respDims } from '@/utils/chakra';
import { Box, ChakraProps, Flex } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { TableProps } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useCallback, useMemo } from 'react';

const Rank = ({
  clazzId,
  subjectId,
  relTime,
  semesterId,
  ruleId,
  menuId,
  ...props
}: {
  clazzId: string;
  subjectId?: string;
  relTime?: RelTimeType;
  semesterId: string;
  ruleId: string;
  menuId: string;
} & ChakraProps) => {
  const query = useMemo(
    () => ({ clazzId, subjectId, ...relTime?.getTime() }),
    [clazzId, subjectId, relTime]
  );

  const { data, isFetching } = useQuery(
    ['indactorDetail', semesterId, menuId, ruleId, clazzId],
    () =>
      getClassRankList({ semesterId, menuId, ruleId, clazzId }).then((res) => {
        const columns: ColumnsType<any> = [];
        if (res[0] && res[0].children) {
          res[0].children.forEach((it) => {
            columns.push({
              title: it.projectName,
              dataIndex: it.projectName,
              key: it.projectName,
              render: (value, record) => `${value || 0} ${record[`${it.projectName}Name`] || ''}`
            });
          });
        }

        const statisticData = res.map((it) => {
          const item: Record<string, any> = {
            ...it
          };
          it.children?.forEach((project, index) => {
            item[project.projectName || ''] = project.maxScoreCount;
            item[`${project.projectName}Name`] = project.maxScoreName;
          });

          delete item.children;
          delete it.children;

          return {
            ...item,
            indactorId: it.indicatorId,
            indactorName: it.indicatorName,
            evaluateType: it.evaluationType
          };
        });

        return {
          columns,
          statisticData: statisticData as any
        };
      }),
    {
      enabled: !!(semesterId && menuId && ruleId && clazzId)
    }
  );

  const { statisticData, columns: projuectColumns } = useMemo(
    () => data || { columns: [], statisticData: [] },
    [data]
  );

  const columns: ColumnsType<ClazzEvaluateRecordType> = [
    {
      title: '学生姓名',
      dataIndex: 'studentName',
      key: 'name'
    },
    {
      title: '综合得分',
      dataIndex: 'maxScoreCount',
      key: 'maxScoreCount',
      render: (value) => value + 'A'
    },
    ...projuectColumns,
    {
      title: '评价时间',
      dataIndex: 'updateTime',
      key: 'updateTime'
    }
  ];

  return (
    <Box px={respDims(24)} py={respDims(10)} {...props}>
      <MyTable
        columns={columns}
        loading={isFetching}
        // api={getClazzEvaluateRankList}
        dataSource={statisticData || []}
        defaultQuery={query}
        pageConfig={{ showPaginate: false }}
        headerConfig={{
          // HeaderComponent: TableHeader,
          showIfEmpty: true
        }}
        boxStyle={{
          p: 0
        }}
        rowKey="studentId"
      />
    </Box>
  );
};

export default Rank;
