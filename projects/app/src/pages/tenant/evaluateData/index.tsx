import { respDims } from '@/utils/chakra';
import { Box } from '@chakra-ui/react';
import { useState } from 'react';
import DataDetails from './components/DataDetails';
import { serviceSideProps } from '@/utils/i18n';
import Tabs from './components/Tabs';

const EvaluateData = () => {
  const [activeTabKey, setActiveTabKey] = useState('dataDetails');

  const tabItems = [
    {
      key: 'dataDetails',
      label: '评价数据明细',
      Component: DataDetails
    }
  ];

  const activeTab = tabItems.find((it) => it.key === activeTabKey) || tabItems[0];

  return (
    <Box
      h="100%"
      pt={respDims(16)}
      borderRadius={respDims(24)}
      bgColor="#FFFFFF"
      backdropFilter="blur(3.700000047683716px)"
      boxShadow="0px 0px 20.3px 0px rgba(172, 172, 172, 0.10)"
      background="rgba(255, 255, 255, 0.06)"
      border="1px solid #FFF"
    >
      <activeTab.Component
        tabs={<Tabs activeKey={activeTabKey} items={tabItems} onChange={setActiveTabKey}></Tabs>}
      />
    </Box>
  );
};

export async function getServerSideProps(context: any) {
  return {
    props: {
      ...(await serviceSideProps(context))
    }
  };
}

export default EvaluateData;
