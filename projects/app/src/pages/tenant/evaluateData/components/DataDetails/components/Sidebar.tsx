import { listDimension } from '@/api/tenant/evaluate/dimension';
import { evaluationDelete } from '@/api/tenant/evaluate/evaluate';
import MyTable from '@/components/MyTable';
import { MyTableRef } from '@/components/MyTable/types';
import { EvaluationType } from '@/constants/api/tenant/evaluate/rule';
import { useOverlayManager } from '@/hooks/useOverlayManager';
import { PagingData } from '@/types';
import { respDims } from '@/utils/chakra';
import { MessageBox, promisifyConfirm } from '@/utils/ui/messageBox';
import { Toast } from '@/utils/ui/toast';
import { Box, Button, Center, Flex, FlexProps } from '@chakra-ui/react';
import { TableProps, Tree } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import RemarkDetail from './RemarkDetail';
import { SubjectType } from '@/types/api/tenant/evaluate/project';
import styles from './index.module.scss';
import { EvaluaViewListNewList } from '@/types/api/tenant/evaluate/indicator';
import { evaluaViewListPageNew } from '@/api/tenant/evaluate/evaluate';
import { useRoutes } from '@/hooks/useRoutes';
import { EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';
import { createStyles } from 'antd-style';
import MyTooltip from '@/components/MyTooltip';

const useStyle = createStyles(({ css, token }) => {
  const { antCls } = token as any;
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `
  };
});
interface TreeNodeData {
  title: string;
  key: string;
  children?: TreeNodeData[];
  isLeaf?: boolean;
  clazzId?: string;
  subjects?: SubjectType[];
}

interface SearchParams {
  semesterId?: string;
  clazzId?: string;
  gradeName?: string;
  clazzName?: string;
  evaluatorId?: string;
  evaluateeId?: string;
  subjectId?: string;
  searchKey?: string;
  typeId?: string;
}

interface SidebarProps {
  tableRef: React.RefObject<MyTableRef>;
  onProjectSelect: (text: string) => void;
  searchParams: SearchParams;
  onSubjectsData?: (subjects: SubjectType[]) => void;
  selectedKeys: string[];
  setSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
}

const Sidebar: React.FC<SidebarProps> = ({
  tableRef,
  onProjectSelect,
  searchParams,
  onSubjectsData,
  selectedKeys,
  setSelectedKeys
}) => {
  const { styles: stylesStatus } = useStyle();
  const [treeData, setTreeData] = useState<TreeNodeData[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [selectedClazzId, setSelectedClazzId] = useState<string | null>(null);
  const [firstChildKey, setFirstChildKey] = useState<string | null>(null);
  const [maxLevel, setMaxLevel] = useState(1);
  const { openOverlay } = useOverlayManager();
  const [dimensionData, setDimensionData] = useState<TreeNodeData[]>([]);

  const { routeGroup } = useRoutes();

  const colors = [
    { bg: '#E8F7FF', color: '#3491FA' },
    { bg: '#E8FFFB', color: '#0FC6C2' },
    { bg: '#F5E8FF', color: '#722ED1' },
    { bg: '#FFFCE8', color: '#F7BA1E' },
    { bg: '#FFE8F1', color: '#F5319D' }
  ];

  const generateIndactorColumns = useCallback(
    (currentMaxLevel: number): ColumnsType<EvaluaViewListNewList> => {
      const columns: ColumnsType<EvaluaViewListNewList> = [];
      let indexMap = {
        1: '一级评价指标',
        2: '二级评价指标',
        3: '三级评价指标',
        4: '四级评价指标',
        5: '五级评价指标',
        6: '六级评价指标',
        7: '七级评价指标'
      } as any;
      for (let i = 1; i <= currentMaxLevel; i++) {
        columns.push({
          title: indexMap[`${i}`],
          key: `indactor${i}`,
          dataIndex: `indactor${i}`,
          render: (_, record: EvaluaViewListNewList) => {
            const indactorLevels: any[] = [];
            let currentIndactor: any = record?.children?.[0];

            // 遍历嵌套的 children
            while (currentIndactor) {
              indactorLevels.push(currentIndactor);
              currentIndactor = currentIndactor.children && currentIndactor.children[0];
            }

            const targetIndactor = indactorLevels[i - 1];
            const colorIndex = (i - 1) % colors.length; // 确保颜色索引在范围内

            return targetIndactor ? (
              <Flex ml={respDims(12)}>
                {(targetIndactor.hasChildren ||
                  targetIndactor.evaluationType === EvaluationType.Score) && (
                  <Center
                    h={respDims(28)}
                    px={respDims(8)}
                    borderRadius="4px"
                    mr={respDims(4)}
                    bg={colors[colorIndex].bg}
                    color={colors[colorIndex].color}
                    textAlign="center"
                  >
                    {targetIndactor.scoreRate ? `${Number(targetIndactor.scoreRate) * 100}%` : '-'}
                  </Center>
                )}

                <Box className="textEllipsis">{targetIndactor.name}</Box>
              </Flex>
            ) : (
              <Box ml={respDims(12)}>-</Box>
            );
          }
        });
      }

      return columns;
    },
    []
  );

  const fetchFirstLevelData = async () => {
    try {
      if (!searchParams?.typeId) {
        return;
      }

      console.log('Fetching first level data with typeId:', searchParams?.typeId);

      const response = await listDimension({
        typeId: searchParams?.typeId!
      });

      console.log('First level data response:', response);

      const formattedData: any = response.map((item) => ({
        title: item.name,
        key: item.id!
        // children: []
      }));

      setTreeData(formattedData);
      setSelectedKeys([]); // 重置 selectedKeys
      setFirstChildKey(null); // 重置 firstChildKey
      if (formattedData.length > 0) {
        setExpandedKeys([formattedData[0].key]);
        setSelectedKeys([formattedData[0].key]);
        setFirstChildKey(formattedData[0].key);
        setSelectedProjectId(formattedData[0].key);
        setSelectedClazzId(formattedData[0].clazzId || null);
        onProjectSelect(formattedData[0].key);
      }
    } catch (error) {
      console.error('Failed to fetch first level data:', error);
    }
  };

  const onLoadData = async (treeNode: TreeNodeData): Promise<void> => {
    // 这里可以根据需要加载更多数据
  };

  const updateTreeData = (
    list: TreeNodeData[],
    key: string,
    children: TreeNodeData[]
  ): TreeNodeData[] => {
    return list.map((node) => {
      if (node.key === key) {
        return { ...node, children };
      }
      return node;
    });
  };

  const onExpand = (expandedKeysValue: any) => {
    setExpandedKeys(expandedKeysValue);
  };

  const getMaxLevel = (data: EvaluaViewListNewList[], level = 1) => {
    let maxLevel = level;
    data?.forEach((item) => {
      if (item.children && item.children.length > 0) {
        const childMaxLevel = getMaxLevel(item.children as any, level + 1);
        maxLevel = Math.max(maxLevel, childMaxLevel);
      }
    });
    return maxLevel;
  };

  const onSelect = (selectedKeysValue: any, info: any) => {
    if (selectedKeysValue.length === 0) {
      return; // 禁止取消选中
    }
    const selectedChildData = dimensionData.find((child) => child.key === selectedKeysValue[0]);
    if (selectedChildData) {
      onSubjectsData!(selectedChildData.subjects || []);
    }
    setSelectedKeys(selectedKeysValue);
    if (selectedKeysValue.length > 0 && info.node.isLeaf) {
      const selectedNode = info.node;
      setSelectedProjectId(selectedNode.key);
      setSelectedClazzId(selectedNode.clazzId || null);
      onProjectSelect(selectedNode.key);
      setFirstChildKey(selectedNode.key);
    }
  };

  const itemStyle: FlexProps = {
    px: respDims(16),
    py: respDims(4),
    mb: respDims(4),
    align: 'center',
    borderRadius: respDims(8),
    cursor: 'pointer'
  };
  console.log(searchParams, selectedKeys);

  const onDelete = useCallback(
    (data: EvaluaViewListNewList) => {
      promisifyConfirm({
        title: '操作确认',
        content: '确定删除该评价数据？'
      }).then(async () => {
        await evaluationDelete({
          id: data.id,
          dimensionId: selectedKeys[0] as string,
          semesterId: searchParams.semesterId || ''
        });
        tableRef.current?.reload();
        Toast.success('删除成功');
      });
    },
    [selectedKeys, searchParams, tableRef]
  );

  const onRemarkDetail = (lastIndactor: EvaluateIndactorType) => {
    openOverlay({
      Overlay: RemarkDetail,
      props: {
        lastIndactor: lastIndactor,
        onClose: () => {}
      }
    });
  };

  const columns: TableProps['columns'] = useMemo(() => {
    const baseColumns: ColumnsType<EvaluaViewListNewList> = [
      {
        title: '评价方',
        key: 'evaluatorName',
        dataIndex: 'evaluatorName',
        width: 120,
        ellipsis: true,
        fixed: 'left'
      },
      {
        title: '被评价方',
        key: 'evaluatedName',
        dataIndex: 'evaluatedName',
        width: 120,
        ellipsis: true,
        fixed: 'left'
      },
      {
        title: '项目名称',
        key: 'projectName',
        dataIndex: 'projectName',
        width: 120,
        ellipsis: true,
        render: (text, record) => (
          <>
            <Flex>
              <Center
                h={respDims(28)}
                px={respDims(8)}
                borderRadius="4px"
                mr={respDims(4)}
                bg="#E8FFFB"
                color="#0FC6C2"
                textAlign="center"
              >
                {record.scoreRate ? `${Number(record.scoreRate) * 100}%` : '-'}
              </Center>
              <MyTooltip overflowOnly placement="top">
                <Box className="textEllipsis">{record.projectName}</Box>
              </MyTooltip>
            </Flex>
          </>
        )
      }
    ];

    const indactorColumns = generateIndactorColumns(maxLevel);

    const actionColumns: ColumnsType<EvaluaViewListNewList> = [
      {
        title: '评价内容',
        key: 'evaluationType',
        dataIndex: 'evaluationType',
        width: 120,
        ellipsis: true,
        render: (_, record: EvaluaViewListNewList) => {
          let lastIndactor = record as any;
          while (lastIndactor.children && lastIndactor.children.length > 0) {
            lastIndactor = lastIndactor.children[0];
          }
          if (lastIndactor.hasChildren) {
            return '-';
          }
          switch (lastIndactor.evaluationType) {
            case EvaluationType.Score:
              return <Box>{lastIndactor.score}</Box>;
            case EvaluationType.ScoreLevelValue:
              return <Box>{record.scoreLevelValue}</Box>;
            case EvaluationType.Comment:
              return (
                <Button color="#0052D9" variant="link" onClick={() => onRemarkDetail(lastIndactor)}>
                  查看
                </Button>
              );
          }
        }
      },
      // {
      //   title: '学科',
      //   dataIndex: 'subjectName',
      //   key: 'subjectName',
      //   width: 120,
      //   ellipsis: true
      // },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        ellipsis: true,
        render: (value) => <>{value ? dayjs(value).format('YYYY/MM/DD HH:mm') : ''}</>
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        fixed: 'right',
        render: (_, record: EvaluaViewListNewList) => (
          <Flex>
            <Button
              onClick={() => onDelete(record)}
              variant="link"
              colorScheme="primary"
              color="#F53F3F"
            >
              删除
            </Button>
          </Flex>
        )
      }
    ];

    return [...baseColumns, ...indactorColumns, ...actionColumns];
  }, [maxLevel, generateIndactorColumns]);

  const queryConfig = (data: PagingData<EvaluaViewListNewList>) => {
    const newMaxLevel = getMaxLevel(data?.records);
    setMaxLevel(newMaxLevel - 1);
  };

  useEffect(() => {
    const fetchData = async () => {
      await fetchFirstLevelData();
      if (firstChildKey) {
        tableRef.current?.reload();
      }
    };

    fetchData();
  }, [searchParams.typeId]);

  useEffect(() => {
    if (firstChildKey) {
      tableRef.current?.reload();
    }
  }, [firstChildKey]);

  useEffect(() => {
    if (selectedKeys[0]) {
      tableRef.current?.reload();
    }
  }, [selectedKeys[0]]);

  return (
    <Flex
      w="100%"
      bg="#fff"
      overflow="hidden"
      justifyContent="space-between"
      alignItems="center"
      h="100%"
      className={styles['custom-tree']}
    >
      <Box borderRight="1px solid #E5E7EB" w="210px" p="8px 0px 100px 0" h="100%">
        <Tree
          loadData={onLoadData}
          treeData={treeData}
          expandedKeys={treeData.map((item) => item.key)}
          onExpand={onExpand}
          onSelect={onSelect}
          selectedKeys={selectedKeys}
          defaultSelectedKeys={firstChildKey ? [firstChildKey] : []}
          titleRender={(nodeData: TreeNodeData) => (
            <Flex
              {...itemStyle}
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitBoxOrient: 'vertical',
                WebkitLineClamp: '1',
                whiteSpace: 'normal'
              }}
              justify="space-between"
              minW={nodeData.isLeaf ? respDims(160) : respDims(190)}
            >
              <Box flex="1 0 0">{nodeData.title}</Box>
            </Flex>
          )}
        />
      </Box>
      <Box flex="1" h="100%" overflow="hidden">
        {selectedKeys[0] && (
          <MyTable
            ref={tableRef}
            rowKey="id"
            api={evaluaViewListPageNew}
            boxStyle={{ px: 4, py: 0 }}
            className={stylesStatus.customTable}
            defaultQuery={{
              ...searchParams,
              dimensionId: selectedKeys[0] as string
            }}
            headerConfig={{
              showIfEmpty: true,
              showHeader: false
            }}
            expandable={{
              childrenColumnName: 'null'
            }}
            emptyConfig={{
              EmptyPicComponent: () => <></>
            }}
            queryConfig={{
              enabled: !!(routeGroup?.authActiveRoute?.id! && firstChildKey! && selectedKeys[0]),
              onSuccess: (data) => {
                queryConfig(data as PagingData<EvaluaViewListNewList>);
              }
            }}
            columns={columns}
          />
        )}
      </Box>
    </Flex>
  );
};

export default Sidebar;
