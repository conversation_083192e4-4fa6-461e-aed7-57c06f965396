import MyModal from '@/components/MyModal';
import { Box, ModalBody } from '@chakra-ui/react';
import { EvaluaIndactorVO } from '@/types/api/tenant/evaluate/indicator';
import { EvaluateIndactorType } from '@/types/api/tenant/evaluate/process';

const RemarkDetail = ({
  lastIndactor,
  onClose
}: {
  lastIndactor: EvaluateIndactorType;
  onClose: () => void;
}) => {
  return (
    <MyModal title="查看评价内容" isOpen onClose={onClose} w="600px" minH="200px" overflow="auto">
      <ModalBody>
        <Box
          fontFamily="PingFang SC, PingFang SC"
          fontWeight="500"
          fontSize="16px"
          color="rgba(0,0,0,0.9)"
        >
          评语
        </Box>
        <Box
          fontFamily="PingFang SC, PingFang SC"
          fontWeight="400"
          fontSize="14px"
          mt="8px"
          color="#606266"
        >
          {lastIndactor?.comment}
        </Box>
      </ModalBody>
    </MyModal>
  );
};

export default RemarkDetail;
