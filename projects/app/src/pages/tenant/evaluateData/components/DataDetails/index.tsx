import {
  Button,
  Center,
  Flex,
  HStack,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement
} from '@chakra-ui/react';
import { ReactNode, useEffect, useRef, useState } from 'react';
import Search, { SearchRef } from '../Search';
import Sidebar from './components/Sidebar';
import { MyTableRef } from '@/components/MyTable/types';
import { SubjectType } from '@/types/api/tenant/evaluate/rule';
import { respDims } from '@/utils/chakra';
import SvgIcon from '@/components/SvgIcon';

interface DataDetailsProps {
  tabs: ReactNode;
}

interface SearchParams {
  year?: string;
  term?: number;
  clazzId?: string;
  gradeName?: string;
  clazzName?: string;
  evaluatorId?: string;
  evaluatedId?: string;
  subjectId?: string;
  searchKey?: string;
  typeId?: string;
}

const DataDetails: React.FC<DataDetailsProps> = ({ tabs }) => {
  const tableRef = useRef<MyTableRef>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [subjectsData, setSubjectsData] = useState<SubjectType[]>([]);
  const [innerValue, setInnerValue] = useState<string>('');
  const searchRef = useRef<SearchRef>(null);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const handleSearch = (params: SearchParams & { projectId?: string }) => {
    if (tableRef.current) {
      if (Object.values(params).every(Boolean)) {
        tableRef.current.setQuery(params);
        tableRef.current.reload();
      }
    }
  };

  const handleProjectSelect = (projectId: string) => {
    setSelectedProjectId(projectId);
  };

  const handleSearchParamsChange = (params: SearchParams) => {
    setSearchParams(params);
  };

  useEffect(() => {
    if (selectedKeys.length > 0) {
      setSelectedProjectId('');
    }
  }, [selectedKeys]);

  useEffect(() => {
    if (!!selectedProjectId && Object.values(searchParams).every(Boolean)) {
      handleSearch({
        ...searchParams,
        projectId: selectedProjectId
      });
    }
  }, [selectedProjectId, searchParams]);

  return (
    <Flex w="100%" h="100%" direction="column" alignItems="baseline" overflow="hidden">
      <Flex direction="column" w="100%">
        <Flex justifyContent="space-between" alignItems="center" px={respDims(24)}>
          {tabs}
          <HStack alignItems="center">
            <InputGroup
              w="220px"
              h="36px"
              borderRadius={respDims(8)}
              boxSizing="border-box"
              overflow="hidden"
            >
              <InputRightElement h="100%">
                <SvgIcon name="search" color="#4E5969" />
              </InputRightElement>

              <Input
                value={innerValue}
                h="100%"
                placeholder={'请输入末级评价指标名称'}
                color="#000"
                background="rgba(0, 0, 0, 0.03)"
                fontSize={respDims('14fpx')}
                border="none"
                _focus={{
                  border: 'none',
                  outline: 'none',
                  boxShadow: 'none'
                }}
                _placeholder={{
                  color: '#999',
                  fontSize: 'inherit'
                }}
                onChange={(e) => {
                  setInnerValue(e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    e.stopPropagation();
                  }
                }}
              />
            </InputGroup>
            <Center>
              <Button
                onClick={() => {
                  searchRef.current?.handleSearch();
                }}
                variant="outline"
                p="0 20px"
                h="32px"
                colorScheme="primary"
                bg="#fff"
              >
                搜索
              </Button>
              {/* <Button
                onClick={() => {
                  setInnerValue('');
                  searchRef.current?.handleReset();
                }}
                p="0 30px"
                h="32px"

                variant={'grayBase'}
                ml={respDims(16)}
                mr={respDims(16)}
              >
                重置
              </Button> */}
            </Center>
          </HStack>
        </Flex>
        <Search
          mt={respDims(12)}
          px={respDims(24)}
          pt={respDims(24)}
          bg="#fff"
          onSearch={handleSearchParamsChange}
          placeholder="请输入评价指标"
          subjectsData={subjectsData}
          innerValue={innerValue}
          ref={searchRef}
          selectedKeys={selectedKeys}
          setSelectedKeys={setSelectedKeys}
        />
      </Flex>

      <Flex
        mt="0px"
        flex="1"
        w="100%"
        overflow="hidden"
        bg="#fff"
        px={respDims(24)}
        pb={respDims(12)}
      >
        <Sidebar
          tableRef={tableRef}
          searchParams={searchParams}
          onProjectSelect={handleProjectSelect}
          onSubjectsData={(subjects) => {
            setSubjectsData(subjects);
          }}
          selectedKeys={selectedKeys}
          setSelectedKeys={setSelectedKeys}
        />
      </Flex>
    </Flex>
  );
};

export default DataDetails;
