import SvgIcon from '@/components/SvgIcon';
import { respDims } from '@/utils/chakra';
import {
  Button,
  Center,
  ChakraProps,
  Flex,
  Input,
  InputGroup,
  InputLeftElement
} from '@chakra-ui/react';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { getClientSemesterPage } from '@/api/tenant/teamManagement/semester';
import { Cascader, Select } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { getClientSchoolDeptTree, getClientStudentPage } from '@/api/tenant/teamManagement/student';
import {
  ClientStudentPageType,
  DepartmentTree,
  SubDeptType
} from '@/types/api/tenant/teamManagement/student';
import { getTenantUserPage } from '@/api/tenant';
import { TenantUserType } from '@/types/api/tenant';
import { SubjectType } from '@/types/api/tenant/evaluate/rule';
import { listDimenType } from '@/api/tenant/evaluate/dimension';
import { listPageProjectV2 } from '@/api/tenant/evaluate/project';
import { EvaluaProjectV2 } from '@/types/api/tenant/evaluate/project';
import { DimenType } from '@/types/api/tenant/evaluate/dimension';
import { getEntranceTree, getDimensionReflectionList } from '@/api/tenant/evaluate/rule';
import { DimensionReflectionVO } from '@/types/api/tenant/evaluate/rule';
import { treeFind, treeFindPaths } from '@/utils/tree';
import { EvaluateeTypeMap, EvaluateeType } from '@/constants/api/tenant/evaluate/rule';

interface Semester {
  id: string;
  year: string;
  type: 1 | 2;
  isCurrent: 0 | 1;
}

interface TreeSelectDataItem {
  value: string;
  title: string;
  children?: TreeSelectDataItem[];
  isLeaf: boolean;
}

interface ProjectsType {
  name: string;
  id: string;
}

interface SearchProps extends ChakraProps {
  value?: string;
  placeholder?: string;
  innerValue?: string;
  onChange?: (text: string) => void;
  subjectsData?: SubjectType[];
  onSearch: (params: {
    semesterId?: string;
    clazzId?: string;
    gradeName?: string;
    clazzName?: string;
    evaluatorId?: string;
    evaluatedId?: string;
    searchKey?: string;
    subjectId?: string;
    typeId?: string;
    projectId?: string;
  }) => void;
  selectedKeys?: string[];
  setSelectedKeys?: React.Dispatch<React.SetStateAction<string[]>>;
}

export type SearchRef = {
  handleSearch: () => void;
  handleReset: () => void;
};

const Search = (
  {
    value = '',
    placeholder,
    onChange,
    onSearch,
    innerValue,
    subjectsData,
    selectedKeys = [],
    setSelectedKeys,
    ...props
  }: SearchProps,
  ref: ForwardedRef<SearchRef>
) => {
  const { Option } = Select;
  const [studentList, setStudentList] = useState<ClientStudentPageType[]>();
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [tenantUserList, setTenantUserList] = useState<TenantUserType[]>([]);
  const [currentSemester, setCurrentSemester] = useState<string | undefined>(undefined);
  const [defaultSemester, setDefaultSemester] = useState<Semester | null>(null);
  const [evaluateDataList, setEvaluateDataList] = useState<{ title: string; value: string }[]>([]);
  const [selectedEvaluator, setSelectedEvaluator] = useState<string | undefined>(undefined);
  const [selectedEvaluated, setSelectedEvaluated] = useState<string | undefined>(undefined);
  const [selectedClazzId, setSelectedClazzId] = useState<string>('');
  const [selectedGradeName, setSelectedGradeName] = useState<string>('');
  const [selectedClazzName, setSelectedClazzName] = useState<string>('');
  const [subjectId, setSubjectId] = useState<string | undefined>(undefined);
  const [dimensionTypes, setDimensionTypes] = useState<{ id: string; name: string }[]>([]);
  const [typeId, setTypeId] = useState<string | undefined>(undefined);
  const [projects, setProjects] = useState<{ id: string; name: string }[]>([]);
  const [selectedProject, setSelectedProject] = useState<string | undefined>(undefined);
  const [reflectionInfo, setReflectionInfo] = useState<DimensionReflectionVO | null>(null);
  const [evaluatedType, setEvaluatedType] = useState<string | null>(null);
  const [evaluatorType, setEvaluatorType] = useState<string | null>(null);

  const { isLoading: isTreeLoading, data: treeData } = useQuery(
    ['treeData'],
    getClientSchoolDeptTree
  );

  const findFullPath = (tree: DepartmentTree, targetId: string): string[] => {
    for (const node of tree) {
      if (node.id === targetId) {
        return [node.id];
      }
      if (node.children) {
        const childPath = findFullPath(node.children, targetId);
        if (childPath.length > 0) {
          return [node.id, ...childPath];
        }
      }
    }
    return [];
  };

  const fetchSemesters = async () => {
    try {
      const response = await getClientSemesterPage({ current: 1, size: 999 });
      setSemesters(response.records || []);
      const current = response.records.find((sem) => sem.isCurrent === 1);
      if (current) {
        setCurrentSemester(current.id);
        setDefaultSemester(current);
      }
    } catch (error) {}
  };

  const handleSemesterChange = (value: string | undefined) => {
    setCurrentSemester(value);
    handleSearch();
  };

  const getSemesterLabel = (sem: Semester) => {
    const typeMap: Record<number, string> = { 1: '第一学期', 2: '第二学期' };
    return `${sem.year}学年${typeMap[sem.type] || ''}`;
  };

  const handleSearch = () => {
    const searchParams = {
      semesterId: currentSemester,
      gradeName: selectedGradeName,
      clazzName: selectedClazzName,
      evaluatorId: selectedEvaluator, //评价人id
      evaluatedId: selectedEvaluated, //被评价人id
      searchKey: innerValue,
      subjectId: subjectId,
      typeId,
      projectId: selectedProject
    };
    onSearch(searchParams);
  };

  const handleReset = () => {
    setSelectedEvaluator(undefined);
    setSelectedEvaluated(undefined);
    setSubjectId(undefined);
    setSelectedClazzId('');
    setSelectedGradeName('');
    setSelectedClazzName('');
    const searchParams = {
      semesterId: currentSemester,
      clazzId: '',
      gradeName: '',
      clazzName: '',
      typeId,
      searchKey: ''
    };
    onSearch(searchParams);
  };

  // 获取评价入口树
  const { data: entranceTree } = useQuery(['entranceTree'], () =>
    getEntranceTree({}).then((res) => {
      return res;
    })
  );

  // 根据维度类型ID获取维度-评价入口关系列表
  const { data: dimensionReflectionData } = useQuery(
    ['dimensionReflection', typeId],
    () => getDimensionReflectionList({ typeId: typeId || '' }),
    {
      enabled: !!typeId
    }
  );

  // 当selectedKeys变化时，更新反射信息
  useEffect(() => {
    if (selectedKeys && selectedKeys.length > 0 && dimensionReflectionData) {
      const selectedDimensionId = selectedKeys[0];
      const reflection = dimensionReflectionData.find(
        (item) => item.dimensionId.toString() === selectedDimensionId
      );

      if (reflection) {
        setReflectionInfo(reflection);
        setEvaluatedType(reflection.evaluatedType);
        setEvaluatorType(reflection.evaluatorType);
        console.log('找到维度对应的评价入口信息:', reflection);
        // 清空已选择的评价方和被评价方
        setSelectedEvaluator(undefined);
        setSelectedEvaluated(undefined);
      } else {
        setReflectionInfo(null);
        setEvaluatedType(null);
        setEvaluatorType(null);
      }
    }
  }, [selectedKeys, dimensionReflectionData]);

  // 获取学生数据的查询，根据evaluatedType判断是否需要查询学生
  useQuery(
    ['studentPage', evaluatedType, evaluatorType],
    () => getClientStudentPage({ current: 1, size: 9999 }),
    {
      enabled:
        Number(evaluatedType) === EvaluateeType.Student ||
        Number(evaluatorType) === EvaluateeType.Student,
      onSuccess(data) {
        const list = data.records.map((res) => {
          return {
            label: res.name,
            value: res.id,
            ...res
          };
        });

        setStudentList(list);
      }
    }
  );

  // 判断是否是教师类型的角色（除了学生外的所有类型都是教师相关角色）
  const isTeacherType = (type: string | null) => {
    if (!type) return false;
    return Number(type) !== EvaluateeType.Student;
  };

  // 获取教师数据的查询，根据evaluatedType判断是否需要查询教师
  useQuery(
    ['teachers', evaluatedType, evaluatorType],
    () =>
      getTenantUserPage({
        current: 1,
        searchKey: '',
        searchType: '',
        size: 9999,
        status: ''
      }),
    {
      enabled: isTeacherType(evaluatedType) || isTeacherType(evaluatorType),
      onSuccess(data) {
        const list = data.records.map((res) => {
          return {
            label: res.username,
            value: res.id,
            ...res
          };
        });
        setTenantUserList(list);
      }
    }
  );

  const fetchDimensionTypes = async () => {
    const response = await listDimenType({ id: '' });
    return response;
  };

  useQuery(['dimensionTypes'], fetchDimensionTypes, {
    onSuccess(data) {
      const list = data.map((item: DimenType) => ({
        id: item.id || '',
        name: item.name
      }));
      setDimensionTypes(list);
      if (list.length > 0) {
        setTypeId(list[0].id); // 默认选中第一项
      }
    }
  });

  useQuery(
    ['projects', typeId, selectedKeys],
    () => listPageProjectV2({ dimensionId: selectedKeys?.[0] || '' }),
    {
      enabled: !!selectedKeys?.[0], // 仅在selectedKeys存在时启用查询
      onSuccess(data) {
        const list = data.map((item: EvaluaProjectV2) => ({
          id: item.id.toString(), // 将 number 转换为 string
          name: item.name
        })) as ProjectsType[];
        setProjects(list);
        setSelectedProject(undefined); // 每次维度变化时清空项目选择
      }
    }
  );

  useEffect(() => {
    if (selectedKeys && selectedKeys.length > 0) {
      // 当维度选择发生变化时，重置项目选择
      setSelectedProject(undefined);
    }
  }, [selectedKeys]);

  useEffect(() => {
    fetchSemesters();
  }, []);

  useEffect(() => {
    if (subjectsData) {
      setSubjectId(undefined);
    }
  }, [subjectsData]);

  useEffect(() => {
    const convertToTreeSelectData = (data: DepartmentTree): TreeSelectDataItem[] => {
      return data.map((item) => ({
        value: item.id,
        title: item.deptName,
        children: item.children ? convertToTreeSelectData(item.children) : undefined,
        isLeaf: !item.children || item.children.length === 0
      }));
    };

    getClientSchoolDeptTree().then((res) => {
      const convertedData = convertToTreeSelectData(res);
      setEvaluateDataList(convertedData);
    });
  }, []);

  useEffect(() => {
    if (defaultSemester) {
      handleSearch();
    }
  }, [
    defaultSemester,
    selectedClazzId,
    selectedEvaluator,
    selectedEvaluated,
    selectedProject,
    subjectId,
    typeId
  ]);

  useImperativeHandle(ref, () => ({
    handleSearch,
    handleReset
  }));

  // 根据选择的路径获取年级名称和班级名称
  const findClazzAndGradeNames = (
    treeData: DepartmentTree | undefined,
    pathIds: string[]
  ): { gradeName: string; clazzName: string } => {
    if (!treeData || !pathIds || pathIds.length === 0) {
      return { gradeName: '', clazzName: '' };
    }

    // 默认值
    let result = { gradeName: '', clazzName: '' };

    // 使用treeFindPaths找到选中节点的完整路径
    const selectedId = pathIds[pathIds.length - 1]; // 获取最后一个ID（当前选中节点）
    const pathsMap = treeFindPaths(treeData || [], [selectedId], 'id', 'children');

    // 获取完整路径
    const fullPath = pathsMap[selectedId] || [];

    if (fullPath.length > 0) {
      // 在路径中找到年级（subDeptType === SubDeptType.Grade）
      const gradeNode = fullPath.find((node) => node.subDeptType === SubDeptType.Grade);
      if (gradeNode) {
        result.gradeName = gradeNode.deptName;
      }

      // 在路径中找到班级（subDeptType === SubDeptType.Class）
      const classNode = fullPath.find((node) => node.subDeptType === SubDeptType.Class);
      if (classNode) {
        result.clazzName = classNode.deptName;
      }
    }

    return result;
  };

  return (
    <Flex w="100%" justifyContent="flex-end" {...props}>
      <Flex flexWrap="wrap" w="100%" alignItems="center">
        <Select
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          placeholder="请选择维度类型"
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={dimensionTypes.map((dim) => ({
            label: dim.name,
            value: dim.id
          }))}
          value={typeId}
          allowClear={false}
          onChange={(value) => setTypeId(value)}
        />

        <Select
          showSearch
          value={currentSemester}
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          placeholder="请选择学期"
          onChange={handleSemesterChange}
        >
          {semesters.map((sem) => (
            <Option key={sem.id} value={sem.id}>
              {getSemesterLabel(sem)}
            </Option>
          ))}
        </Select>

        <Cascader
          options={treeData || []}
          variant="filled"
          showSearch
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          fieldNames={{
            label: 'deptName',
            value: 'id',
            children: 'children'
          }}
          onChange={(value) => {
            const pathIds = value as string[];
            const selectedValue = pathIds?.length > 0 ? pathIds[pathIds.length - 1] : '';
            setSelectedClazzId(selectedValue);

            // 获取年级名称和班级名称
            const { gradeName, clazzName } = findClazzAndGradeNames(treeData, pathIds);
            console.log('年级和班级信息:', { gradeName, clazzName, pathIds });
            setSelectedGradeName(gradeName);
            setSelectedClazzName(clazzName);
          }}
          value={selectedClazzId ? findFullPath(treeData || [], selectedClazzId) : []}
          dropdownStyle={{ overflowX: 'auto', zIndex: 2000 }}
          maxTagCount="responsive"
          allowClear
          onClear={() => {
            setSelectedClazzId('');
            setSelectedGradeName('');
            setSelectedClazzName('');
          }}
          placeholder="请选择评价年级和班级"
        />

        <Select
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          placeholder="请选择评价方"
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={
            isTeacherType(evaluatorType)
              ? tenantUserList?.map((user) => ({
                  label: user.username,
                  value: user.id
                }))
              : Number(evaluatorType) === EvaluateeType.Student
                ? studentList?.map((student) => ({
                    label: student.name,
                    value: student.id
                  }))
                : []
          }
          value={selectedEvaluator}
          dropdownStyle={{ zIndex: 9999 }}
          allowClear
          onChange={(value) => setSelectedEvaluator(value)}
        />

        <Select
          value={selectedEvaluated}
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          placeholder="请选择被评价方"
          options={
            isTeacherType(evaluatedType)
              ? tenantUserList?.map((user) => ({
                  label: user.username,
                  value: user.id
                }))
              : Number(evaluatedType) === EvaluateeType.Student
                ? studentList?.map((student) => ({
                    label: student.name,
                    value: student.id
                  }))
                : []
          }
          allowClear
          onChange={(value) => setSelectedEvaluated(value)}
          dropdownStyle={{ zIndex: 9999 }}
        />

        <Select
          style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
          variant="filled"
          placeholder="请选择项目"
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={projects.map((project) => ({
            label: project.name,
            value: project.id
          }))}
          value={selectedProject}
          allowClear
          onChange={(value) => setSelectedProject(value)}
        />

        {subjectsData && subjectsData?.length > 0 && (
          <Select
            value={subjectId}
            style={{ width: '200px', marginRight: '19px', marginBottom: '10px' }}
            variant="filled"
            showSearch
            fieldNames={{
              label: 'subjectName',
              value: 'subjectId'
            }}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            placeholder="请选择学科"
            options={subjectsData.map((subject) => ({
              label: subject.subjectName,
              value: subject.subjectId
            }))}
            allowClear
            onChange={(value) => setSubjectId(value)}
            dropdownStyle={{ zIndex: 9999 }}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default forwardRef(Search);
