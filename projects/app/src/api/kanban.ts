import { PagingData } from '@/types';
import { hashStr } from '@/utils/string';
import {
  GetEvaluateSubjectListProps,
  EvaluateSubjectType,
  GetSubjectSummarizationGraphProps,
  GetSubjectStatisticsType,
  GetSubjectSummarizationTableType,
  GetGradeListType,
  GetSubjectListType,
  TestQualityAnalysisType,
  TestQualityAnalysisData,
  GetSubjectSummarizationGraphType,
  GetTeacherType,
  GetConclusionProps,
  SaveOrUpdateConclusionProps,
  GetConclusionType,
  GetSubjectListProps,
  GetSubjectLevelProps,
  SubjectLevelResponse,
  ClassGradeInfo,
  GradeComparisonResponse,
  RecentReportResponse,
  GetRecentReportProps,
  CreatePPTParams
} from '@/types/api/kanban';
import { POST } from '@/utils/request';
import { baseExamConfig, baseAuthConfig, baseToolConfig } from '@/constants/api/auth';
import { AxiosProgressEvent, AxiosResponse } from 'axios';

// 获取测试信息(筛选区)
export const getTestQualityAnalysis = (data: TestQualityAnalysisData) =>
  POST<any>(`/exam/report/class/subject/queryReport`, data, baseExamConfig);

// 获取科目信息(筛选区)
export const getQueryReportSubjects = (data: any) =>
  POST<any>(`/exam/report/class/subject/queryReportSubjects`, data, baseExamConfig);

// 统计区信息
export const getSubjectStatistics = (data: GetSubjectSummarizationGraphProps) =>
  POST<GetSubjectStatisticsType>('/exam/report/class/subject/statistic', data, baseExamConfig);

// 数据汇总图表
export const getSubjectSummarizationGraph = (data: GetSubjectSummarizationGraphProps) =>
  POST<GetSubjectSummarizationGraphType[]>(
    '/exam/report/class/subject/summarizationGraph',
    data,
    baseExamConfig
  );

export const getSubjectSummarizationTable = (data: GetSubjectSummarizationGraphProps) =>
  POST<GetSubjectSummarizationTableType[]>(
    '/exam/report/class/subject/summarizationTable',
    data,
    baseExamConfig
  );

export const exportSubjectSummarizationTable = (
  data: GetSubjectSummarizationGraphProps,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void }
) =>
  POST<AxiosResponse<Blob>>('/exam/report/class/subject/summarizationTableExport', data, {
    timeout: 480000,
    responseType: 'blob',
    ...config,
    ...baseExamConfig
  });

// 获取年级列表 废弃
export const getGradeList = (data: { semesterId: string }) =>
  POST<GetGradeListType[]>('/schoolDept/manage/getGradeList', data, baseAuthConfig);

// 获取科目列表 废弃
export const getSubjectList = (data: GetSubjectListProps) =>
  POST<GetSubjectListType[]>('/schoolDept/manage/getSubjectList', data, baseAuthConfig);

//获取当前身份
export const getTeacherType = (data: { gradeId: string; semesterId: string }) =>
  POST<GetTeacherType>('/schoolDept/manage/getTeacherType', data, baseAuthConfig);

//获取面板的AI结论
export const getConclusion = (data: GetConclusionProps) =>
  POST<GetConclusionType[]>('/exam/report/conclusion/batchQuery', data, baseExamConfig);

//AI结论保存和编辑
export const saveOrUpdateConclusion = (data: SaveOrUpdateConclusionProps) =>
  POST('/exam/report/conclusion/saveOrUpdate', data, baseExamConfig);

// 学科等级分段
export const getSubjectLevel = (data: GetSubjectLevelProps) =>
  POST<SubjectLevelResponse[]>('/exam/report/class/subject/subjectLevel', data, baseExamConfig);

//导出学科等级分段
export const exportSubjectLevel = (data: GetSubjectLevelProps) =>
  POST<AxiosResponse<Blob>>('/exam/report/class/subject/subjectLevelTableExport', data, {
    timeout: 480000,
    responseType: 'blob',
    ...baseExamConfig
  });

export const getGradeComparison = (data: GetSubjectLevelProps) =>
  POST<GradeComparisonResponse>('/exam/report/class/subject/gradeComparison', data, baseExamConfig);

export const getRecentReport = (data: GetRecentReportProps) =>
  POST<RecentReportResponse[]>(
    '/exam/report/class/subject/queryRecentReport',
    data,
    baseExamConfig
  );

// 近三次测试标准分对比
export interface GetStandardTestComparisonProps {
  year: string; // 学年
  term: number; // 学期
  gradeName: string; // 年级名称
  subjectNames: string[]; // 学科名称列表
  examId: string; // 考试ID列表
  startDate: string; // 开始日期
  endDate: string; // 结束日期
}

export interface StandardTestComparisonItem {
  index: number;
  className: string;
  examId: string;
  examName: string;
  standardScore: number;
}

export const getStandardTestComparison = (data: GetStandardTestComparisonProps) =>
  POST<StandardTestComparisonItem[]>(
    '/exam/report/class/subject/standardTestComparison',
    data,
    baseExamConfig
  );

// 成绩等级分段
export interface GetGradeDivisionProps {
  year: string; // 学年
  term: number; // 学期
  teacherType: number; // 教师类型
  gradeId: number; // 年级ID
  gradeName: string; // 年级名称
  subjectNames: string[]; // 学科名称列表
  examType: string; // 考试类型
  examId: string; // 考试ID
  score: number; // 分段值
}

export interface Section {
  sort: number;
  studentNumber: number;
  ratio: number;
  leftSection: string;
  rightSection: string;
  leftScore: number;
  rightScore: number;
}

export interface GradeDivisionResponse {
  index: number;
  className: string;
  sectionList: Section[];
}

export const getGradeDivision = (data: GetGradeDivisionProps) =>
  POST<GradeDivisionResponse[]>('/exam/report/class/subject/gradeDivision', data, baseExamConfig);

// 成绩等级分段导出
export interface GetGradeDivisionExportProps {
  year: string; // 学年
  term: number; // 学期
  teacherType: number; // 教师类型
  gradeId: number; // 年级ID
  gradeName: string; // 年级名称
  subjectNames: string[]; // 学科名称列表
  examType: string; // 考试类型
  examId: string; // 考试ID
  score: number; // 分段值
}

export const getGradeDivisionExport = (data: GetGradeDivisionExportProps) =>
  POST<AxiosResponse<Blob>>('/exam/report/class/subject/gradeDivisionTableExport', data, {
    timeout: 480000,
    responseType: 'blob',
    ...baseExamConfig
  });

// 根据所选学科，查看是否有编辑结论权限
export interface GetTeacherTypeBySubjectProps {
  semesterId: number; // 学期ID
  gradeId: number; // 年级ID
  subjectNames: string[]; // 学科名称列表
}

export const getTeacherTypeBySubject = (data: GetTeacherTypeBySubjectProps) =>
  POST<number>('/exam/report/conclusion/getTeacherTypeBySubject', data, baseExamConfig);


// 创建PPT
export const createPPT = (data: CreatePPTParams) =>
  POST<AxiosResponse<Blob>>('/ppt/create', data, {
    timeout: 480000,
    responseType: 'blob',
    ...baseToolConfig
  });