import { POST, GET } from '@/utils/request';
import { hashStr } from '@/utils/string';
import { baseAuthConfig } from '@/constants/api/auth';
import {
  LoginRes,
  PostLoginProps,
  DingConfig,
  QywxConfig,
  WxTicketType,
  WxUnBindType,
  LoginQRCodeParams,
  TocWxQRCodeResultType
} from '@/types/api/auth';

export const getTokenLogin = () =>
  POST<LoginRes>('/client/auth/token', {}, { maxQuantity: 1, ...baseAuthConfig });

export const postLogin = ({ password, ...props }: PostLoginProps) =>
  POST<LoginRes>(
    '/client/auth/login',
    {
      ...props,
      password: hashStr(password)
    },
    baseAuthConfig
  );

export const loginOut = () => POST('/client/auth/logout', {}, { noToast: true, ...baseAuthConfig });

//根据当前域名获取租户配置的钉钉应用信息
export const getDingConfig = () => GET<DingConfig>(`/ding/dingConfig`, undefined, baseAuthConfig);

//根据当前域名获取租户配置的企业微信应用信息
export const getQywxConfig = () => POST<QywxConfig>(`/qywx/qywxConfig`, {}, baseAuthConfig);

export const tocWxIsTocTenant = () =>
  GET<{ data: boolean }>(`/toc/wx/isTocTenant`, '', { isResponseData: true, ...baseAuthConfig });

export const getTocWxQRCodeResult = (ticket: string) =>
  GET<TocWxQRCodeResultType>(
    `/toc/wx/getLoginQRCodeResult?ticket=${ticket}`,
    undefined,
    baseAuthConfig
  );

export const TocWxLoginByOpenId = (openId: string) =>
  GET<LoginRes>(`/toc/wx/loginByOpenId?openId=${openId}`, undefined, baseAuthConfig);

export const getTocWxLoginQRCode = () =>
  GET<LoginQRCodeParams>(`/toc/wx/getLoginQRCode`, undefined, baseAuthConfig);

//获取微信临时二维码
export const getLoginQRCode = () =>
  GET<LoginQRCodeParams>(`/wx/getLoginQRCode`, undefined, baseAuthConfig);

export const getBindQRCode = () => GET(`/wx/getBindQRCode`, undefined, baseAuthConfig);

//轮询获取微信扫码结果
export const getQRCodeResult = (ticket: string) =>
  GET<WxTicketType>(`/wx/getLoginQRCodeResult?ticket=${ticket}`, undefined, baseAuthConfig);

export const loginByUnionId = (unionId: string) =>
  GET<LoginRes>(`/wx/loginByUnionId?unionId=${unionId}`, undefined, baseAuthConfig);

export const wxUserInfo = () => GET<LoginRes>(`/wx/userInfo`, undefined, baseAuthConfig);

export const wxBind = (unionId: string) =>
  POST<WxUnBindType>(`/wx/wxBind?unionId=${unionId}`, '', {
    isResponseData: true,
    ...baseAuthConfig
  });

export const wxUnBind = () =>
  POST<WxUnBindType>(`/wx/wxUnBind`, '', { isResponseData: true, ...baseAuthConfig });

export const wxIsBindWx = () => GET(`/wx/isBindWx`, undefined, baseAuthConfig);

export const tocWxNeedPay = () => GET(`/toc/wx/needPay`, undefined, baseAuthConfig);

export const tocWxPayNative = () => GET(`/toc/wx/payNative`, undefined, baseAuthConfig);

export const tocWxPayResult = (codeUrl: string) =>
  GET<boolean>(`/toc/wx/payResult?codeUrl=${codeUrl}`, undefined, baseAuthConfig);

export const thirdLogin = (data: { code: string; orgCode: string }) =>
  POST<LoginRes>('/client/auth/code', data, { noToast: true, ...baseAuthConfig });
