import { PagingData } from '@/types';
import { hashStr } from '@/utils/string';
import {
  TenantType,
  UpdateTenantDetailParams,
  DeptListType,
  CreateDeptParams,
  UpdateDeptParams,
  CreateTenantUserParams,
  UpdateTenantUserParams,
  DetailTenantUserParams,
  UpdateStatusTenantUserParams,
  RoleListType,
  SortDeptParams,
  TransferResourcesParams,
  GetTenantUserPageParams,
  TenantUserType,
  IndustryListType,
  ReplaceDeptParams,
  CreateRoleParams,
  CreateRoleType,
  CreateRoleMenuParams,
  DetailRoleType,
  RoleCountUserType,
  RoleUpdateUserParams,
  MenuListType
} from '@/types/api/tenant';
import { POST } from '@/utils/request';
import { baseAuthConfig } from '@/constants/api/auth';

export const getTenantDetail = () => POST<TenantType>(`/client/tenant/detail`, {}, baseAuthConfig);

export const updateTenantDetail = (data: UpdateTenantDetailParams) =>
  POST(`/client/tenant/update`, data, baseAuthConfig);

export const getDeptList = () => POST<DeptListType>(`/client/dept/list`, {}, baseAuthConfig);

export const createDept = (data: CreateDeptParams) =>
  POST(`/client/dept/create`, data, baseAuthConfig);

export const updateDept = (data: UpdateDeptParams) =>
  POST(`/client/dept/update`, data, baseAuthConfig);

export const deleteDept = ({ id }: { id: string }) =>
  POST(`/client/dept/delete`, { id }, baseAuthConfig);

export const sortDept = (data: SortDeptParams) => POST(`/client/dept/sort`, data, baseAuthConfig);

export const replaceDept = (data: ReplaceDeptParams) =>
  POST(`/client/dept/replace`, data, baseAuthConfig);

export const getTenantUserPage = (data: GetTenantUserPageParams) =>
  POST<PagingData<TenantUserType>>(`/client/tenant/user/page`, data, baseAuthConfig);

export const createTenantUser = ({ password, ...data }: CreateTenantUserParams) =>
  POST(`/client/tenant/user/create`, { password: hashStr(password), ...data }, baseAuthConfig);

export const updateTenantUser = ({ password, ...data }: UpdateTenantUserParams) => {
  const payload = password ? { ...data, password: hashStr(password) } : { ...data };
  return POST(`/client/tenant/user/update`, payload, baseAuthConfig);
};

export const deleteTenantUser = (id: string) =>
  POST(`/client/tenant/user/delete`, { id }, baseAuthConfig);

export const updateStatusTenantUser = (data: UpdateStatusTenantUserParams) =>
  POST(`/client/tenant/user/updateStatus`, data, baseAuthConfig);

export const detailTenantUser = (id: string) =>
  POST<DetailTenantUserParams>(`/client/tenant/user/detail`, { id }, baseAuthConfig);

export const transferResources = (data: TransferResourcesParams) =>
  POST<DeptListType>(`/client/tenant/user/transferResources`, data, baseAuthConfig);

export const getIndustryList = () =>
  POST<IndustryListType>('/admin/industry/list', {}, baseAuthConfig);

export const getRoleList = (oldRoleId?: string) =>
  POST<RoleListType>('/client/role/list', { oldRoleId }, baseAuthConfig);

export const createRole = (data: CreateRoleParams) =>
  POST<CreateRoleType>(`/client/role/create`, data, baseAuthConfig);

export const updateRole = (data: CreateRoleParams) =>
  POST<CreateRoleType>(`/client/role/update`, data, baseAuthConfig);

export const createRoleMenu = (data: CreateRoleMenuParams) =>
  POST(`/client/role/menu/create`, data, baseAuthConfig);

export const deleteRoleMenu = (id: string) => POST(`/client/role/delete`, { id }, baseAuthConfig);

export const getDetailRole = (id: string) =>
  POST<DetailRoleType>(`/client/role/detail`, { id }, baseAuthConfig);

export const getRoleCountUser = (id: string) =>
  POST<RoleCountUserType>(`/client/role/count/user`, { id }, baseAuthConfig);

export const roleUpdateUser = (data: RoleUpdateUserParams) =>
  POST(`/client/role/update/user`, data, baseAuthConfig);

export const getMenuTreeList = () => POST<MenuListType>(`/client/menu/tree`, {}, baseAuthConfig);
