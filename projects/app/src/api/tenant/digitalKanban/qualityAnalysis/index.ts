import { POST } from '@/utils/request';
import {
  LevelDivisionParams,
  LevelDivisionType,
  QueryScoreLevelType,
  RankDivisionType,
  RankDivisionTypeParams,
  SetScoreLevelParams,
  SetScoreLevelType,
  WeakSubjectAnalysisParams,
  WeakSubjectAnalysisType
} from '@/types/api/tenant/digitalKanban/qualityAnalysis';
import { baseExamConfig } from '@/constants/api/auth';
import { AxiosResponse } from 'axios';

// 名次段人数对比
export const getRankDivision = (data: RankDivisionTypeParams) =>
  POST<RankDivisionType>('/exam/report/class/subject/rankDivision', data, {
    // isResponseData: true,
    ...baseExamConfig
  });

export const rankDivisionTableExport = (data: RankDivisionTypeParams) =>
  POST<AxiosResponse<Blob>>('/exam/report/class/subject/rankDivisionTableExport', data, {
    timeout: 480000,
    responseType: 'blob',
    ...baseExamConfig
  });

// 搜索分数段
export const queryScoreLevel = (data: Record<string, never> = {}) => {
  return POST<QueryScoreLevelType>('/exam/config/queryScoreLevel', data, {
    ...baseExamConfig
  });
};

// 学业等级分布
export const levelDivision = (data: LevelDivisionParams) => {
  return POST<LevelDivisionType>('/exam/report/class/subject/levelDivision', data, {
    ...baseExamConfig
  });
};

// 薄弱学科分析
export const weakSubjectAnalysis = (data: WeakSubjectAnalysisParams) => {
  return POST<WeakSubjectAnalysisType>('/exam/report/class/subject/weakSubjectAnalysis', data, {
    ...baseExamConfig
  });
};

// 获取班级
export const getClassList = (examId: string) => {
  return POST<Array<string>>(
    '/exam/report/class/subject/getClassList',
    {},
    {
      ...baseExamConfig,
      params: {
        examId
      }
    }
  );
};

// 设置分数段
export const setScoreLevel = (data: SetScoreLevelParams) => {
  return POST<SetScoreLevelType>('/exam/config/setScoreLevel', data, {
    ...baseExamConfig
  });
};
