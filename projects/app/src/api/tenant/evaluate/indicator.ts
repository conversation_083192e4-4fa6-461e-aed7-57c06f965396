import { POST } from '@/utils/request';
import {
  Indicator,
  EvaluaIndactorVO,
  EvaluaIndactorSortParams,
  TenantEvaluateIconAddParams,
  IconListType,
  EvaluaIndactorDetailParams,
  EvaluaViewListNewList
} from '@/types/api/tenant/evaluate/indicator';
import { filterTree } from '@/utils/tree';

export const getIndicatorTree = (data: { projectId: string; name?: string }) =>
  POST<EvaluaIndactorVO[]>('/evaluationManage/indicatorTree', data).then((res) => {
    const result = filterTree(res, (item) => !!item?.name?.includes(data.name || ''), {
      childProps: 'sub'
    });
    return result;
  });

export const addIndicatorCreate = (data: Indicator) =>
  POST<boolean>('/evaluationManage/indicatorCreate', data);

export const deleteIndactor = (data: { id: string }) =>
  POST<boolean>('/evaluationManage/indicatorDelete', data);

export const getIndactorsByProject = (data: { projectId: string; ruleId?: string }) =>
  POST<EvaluaIndactorVO[]>('/evalua/indactor/getIndactorsByProject', data);

export const updateIndactor = (data: Indicator) =>
  POST<boolean>('/evaluationManage/indicatorUpdate', data);
export const runSortIndicatorResort = (data: EvaluaIndactorSortParams[]) =>
  POST<boolean>('/evaluationManage/indicatorResort', data);

export const evaluaIndactorDetail = (data: EvaluaIndactorDetailParams) =>
  POST<EvaluaViewListNewList>(`/evaluationManage/indicatorDetail`, data);
export const tenantEvaluateIconList = () => POST<IconListType[]>('/tenant/evaluateIcon/list');

export const getIndicatorIconList = () =>
  POST<IconListType[]>('/evaluationManage/indicatorIconList');

export const deleteIndicatorIcon = (data: { id: string }) =>
  POST<boolean>('/evaluationManage/indicatorIconDelete', data);
export const indicatorIconCreateAdd = (data: TenantEvaluateIconAddParams) =>
  POST<boolean>('/evaluationManage/indicatorIconCreate', data);

export const getUsedIndactorsByClazzs = (data: any) =>
  POST<EvaluaIndactorVO[]>('/evalua/indactor/getUsedIndactorsByClazzs', data);
