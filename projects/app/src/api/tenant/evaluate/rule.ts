import { POST } from '@/utils/request';
import {
  AddEvaluationRuleParams,
  DeleteEvaluationRuleParams,
  EvaluationRuleDetailParams,
  EvaluationRuleListParams,
  EvaluationRuleDetail,
  CopyEvaluationRuleParams,
  TeacherType,
  UpdateEvaluationRuleParams,
  DimensionReflectionVO,
  DeptTeacherType,
  GetEvaluatorIdListParams,
  ProjectIndicatorTreeType
} from '@/types/api/tenant/evaluate/rule';
import { PagingData } from '@/types';
import { EvaluaIndactorVO } from '@/types/api/tenant/evaluate/indicator';
import { treeTraverse } from '@/utils/tree';
import { baseAuthConfig } from '@/constants/api/auth';

// 规则相关API函数
export const addEvaluationRule = (data: AddEvaluationRuleParams) =>
  POST<boolean>('/evaluationRule/create', data);

export const updateEvaluationRule = (data: UpdateEvaluationRuleParams) =>
  POST<boolean>('/evaluationRule/update', data);

// 竹香1.4.4新
export const addEvaluationRuleZhuxiang = (data: AddEvaluationRuleParams) =>
  POST<boolean>('/evaluationRule/createTeacherRule', data);

export const updateEvaluationRuleZhuxiang = (data: UpdateEvaluationRuleParams) =>
  POST<boolean>('/evaluationRule/updateTeacherRule', data);

export const deleteEvaluationRule = (data: DeleteEvaluationRuleParams) =>
  POST<boolean>('/evaluationRule/delete', data);

export const copyEvaluationRule = (data: CopyEvaluationRuleParams) =>
  POST<boolean>('/evalua/rule/copy', data);

export const getEvaluationRuleDetail = (data: EvaluationRuleDetailParams) =>
  POST<EvaluationRuleDetail>('/evaluationRule/detail', data);

export const getEvaluationRuleList = (data: EvaluationRuleListParams) =>
  POST<PagingData<EvaluationRuleDetail>>('/evaluationRule/page', data);

export const getTeacherByTree = (data: { semesterId: string }) =>
  POST<DeptTeacherType[]>('/evalua/rule/schoolDept/getTeacherByTree', data, baseAuthConfig);

export const getEvaluatorIdList = (data: GetEvaluatorIdListParams) =>
  POST<string[]>('/evaluationRule/getEvaluatorIdList', data);

export const getTeachers = (data: any) => POST<TeacherType[]>('/evalua/rule/getTeachers', data);

export const getProjectIndicatorTree = (data: { reflectionId: string }) =>
  POST<ProjectIndicatorTreeType[]>('/evaluationRule/getProjectIndicatorTree', data);

export const getDimensionReflectionList = (data: { typeId: string }) =>
  POST<DimensionReflectionVO[]>('/evaluationManage/dimensionReflectionList', data);

export const getEntranceTree = (data: any) =>
  POST<DimensionReflectionVO[]>('/evaluationRule/entranceTree', data).then((res) => {
    treeTraverse(res, (node) => {
      if (node.sub) {
        node.children = node.sub;
      }
      if (node.entrance) {
        node.children = node.entrance;
      }
    });
    return res;
  });
