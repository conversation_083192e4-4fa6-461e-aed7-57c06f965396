import { POST } from '@/utils/request';
import {
  AddDimenTypeParams,
  DeleteDimenTypeParams,
  ListDimenTypeParams,
  UpdateDimenTypeParams,
  EvaluaDimension,
  EvaluaDimensionCreateReqV2,
  DimenType
} from '@/types/api/tenant/evaluate/dimension';
import { PagingData } from '@/types';
import { EvaluaProjectV2 } from '@/types/api/tenant/evaluate/project';

// 维度相关API函数
export const addDimenType = (data: AddDimenTypeParams) =>
  POST<boolean>('/evaluationManage/dimensionCreate', data);

export const copyDimension = (data: { id: string }) =>
  POST<boolean>('/evaluationManage/indicatorCopy', data);

export const listPageProjectV2 = (data: { dimensionId: string }) =>
  POST<EvaluaProjectV2[]>('/evaluationManage/projectList', data);

export const deleteDimenType = (data: DeleteDimenTypeParams) =>
  POST<boolean>('/evalua/dimenType/delete', data);

export const listDimenType = (data: ListDimenTypeParams) =>
  POST<DimenType[]>('/evaluationManage/dimensionTypeList', data);

export const updateDimenType = (data: UpdateDimenTypeParams) =>
  POST<boolean>('/evalua/dimenType/update', data);

export const addDimension = (data: EvaluaDimension) =>
  POST<boolean>('/evaluationManage/dimensionCreate', data);

export const addDimensionV2 = (data: EvaluaDimensionCreateReqV2) =>
  POST<boolean>('/evaluationManage/dimensionCreate', data);

export const listDimension = (data: { typeId: string }) =>
  POST<EvaluaDimension[]>('/evaluationManage/dimensionList', data);

export const updateDimension = (data: EvaluaDimension) =>
  POST<boolean>('/evaluationManage/dimensionUpdate', data);

export const deleteDimension = (data: { id: string }) =>
  POST<boolean>('/evaluationManage/dimensionDelete', data);

export const sortDimension = (data: { id: string; sort: number }[]) =>
  POST<boolean>('/evaluationManage/dimensionResort', data);
