import { POST } from '@/utils/request';
import {
  EvaluaProject,
  EvaluaProjectCreateReq,
  EvaluaProjectV2
} from '@/types/api/tenant/evaluate/project';
import { PagingData } from '@/types';
import { baseAuthConfig } from '@/constants/api/auth';

// 项目相关API函数
export const addProject = (data: EvaluaProject) =>
  POST<boolean>('/evaluationManage/projectCreate', data);

export const addProjectV2 = (data: EvaluaProjectCreateReq) =>
  POST<boolean>('/evaluationManage/projectCreate', data);

export const updateProjectV2 = (data: EvaluaProjectCreateReq) =>
  POST<boolean>('/evaluationManage/projectUpdate', data);

export const listPageProjectV2 = (data: { dimensionId: string }) =>
  POST<EvaluaProjectV2[]>('/evaluationManage/projectList', data);

export const updateProject = (data: EvaluaProject) =>
  POST<boolean>('/evaluationManage/projectUpdate', data);

export const deleteProject = (data: { id: string }) =>
  POST<boolean>('/evaluationManage/projectDelete', data);
export const copyProject = (data: { id: string }) =>
  POST<boolean>('/evaluationManage/projectCopy', data);

export const schoolDeptSubjectManageSubjectsV2 = () =>
  POST<{ id: string; name: string }[]>(
    '/client/schoolDeptSubjectManage/subjects',
    {},
    baseAuthConfig
  );

export const sortProject = (data: { id: string; sort: number }[]) =>
  POST<boolean>('/evaluationManage/projectResort', data);
