import { POST } from '@/utils/request';
import { PagingData } from '@/types';
import {
  EvaluaIndactorParams,
  EvaluaViewListNewParams,
  EvaluaViewListNewList,
  HomeworkEvaluateDeleteParams,
  HomeworkClassEvaluateDeleteParams,
  DeptTeacherType,
  EvaluationDeleteParams
} from '@/types/api/tenant/evaluate/evaluate';
import { baseAuthConfig } from '@/constants/api/auth';

export const evaluaIndactorUpdateStatus = (data: EvaluaIndactorParams) =>
  POST(`/evaluationManage/indicatorUpdateStatus`, data);

export const evaluaViewListPageNew = (data: EvaluaViewListNewParams) =>
  POST<PagingData<EvaluaViewListNewList>>(`/evaluation/data/page`, data);

export const evaluaHomeworkEvaluateDelete = (data: HomeworkEvaluateDeleteParams) =>
  POST<boolean>(`/evalua/homeworkEvaluate/delete`, data);

export const evaluationDelete = (data: EvaluationDeleteParams) =>
  POST<boolean>(`/evaluation/delete`, data);

export const evaluaClassEvaluateDelete = (data: HomeworkClassEvaluateDeleteParams) =>
  POST<boolean>(`/evalua/classEvaluate/delete`, data);

export const getTeacherByTree = () =>
  POST<DeptTeacherType[]>(`/evalua/rule/schoolDept/getTeacherByTree`, baseAuthConfig);
