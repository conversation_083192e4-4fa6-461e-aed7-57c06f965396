import { baseAuthConfig } from '@/constants/api/auth';
import {
  EvaluatePermissionEnum,
  IndactorTypeEnum,
  IndactorTypeNameMap
} from '@/constants/api/tenant/evaluate/process';
import { PagingData } from '@/types';
import {
  AddClazzEvaluateProps,
  AddHomeworkProps,
  EvaluateClazzType,
  EvaluateGroupType,
  EvaluateHomeworkType,
  EvaluateIndactorType,
  EvaluateStudentType,
  EvaluateSubjectType,
  GetClazzIndactorListProps,
  GetClazzStudentListByCodeProps,
  GetClazzStudentListByGroupProps,
  GetClazzStudentListBySeatProps,
  GetGroupListProps,
  GetHomeworkPageProps,
  RemoveClazzEvaluateProps,
  RemoveHomeworkProps,
  ResetClazzEvaluateProps,
  ResetHomeworkEvaluateProps,
  SubmitGroupsProps,
  UpdateHomeworkProps,
  SubmitSeatsProps,
  AddHomeworkEvaluateProps,
  RemoveHomeworkEvaluateProps,
  ClazzStudentPageProps,
  GetAllGroupListProps,
  ClazzEvaluateStatType,
  ClazzEvaluateRankListProps,
  ClazzEvaluateRecordType,
  GetEvaluateSubjectListProps,
  GetRuleListByClazzIdProps,
  RuleListByClazzIdResponse,
  EvaluateStudentListType,
  UpdateHomeworkTemplateProps,
  AddHomeworkTemplateProps,
  GetStudentLevelListProps,
  RankDataTree,
  ClazzHomeworkPageProps,
  ClazzHomeworkPageType,
  EvaluationStudentPageProps,
  EvaluationClazzStatisticsType,
  EvaluationClazzPageProps,
  EvaluationClazzPageType,
  ClazzRankListProps,
  EvaluationStudentStatisticsProps,
  EvaluationStudentStatisticsType,
  ClazzHomeworkRankListProps,
  ClazzHomeworkRankListType,
  StudentHomeworkPageProps,
  StudentHomeworkPageType,
  GetTeacherStatisticsPageProps,
  TeacherStatisticsType,
  GetTeacherDetailsPageProps,
  TeacherDetailsType,
  GetEvaluatedBelongProps,
  EvaluatedBelongType,
  GetEvaluatedListProps,
  EvaluatedListType,
  GetTeacherIndicatorTreeProps,
  TeacherIndactorType,
  GetRuleListProps
} from '@/types/api/tenant/evaluate/process';
import {
  ClazzRankListType,
  ClientStudentPageType
} from '@/types/api/tenant/teamManagement/student';
import { POST } from '@/utils/request';

const asArray = <T>(arr: T[]) => arr || ([] as T[]);

const asClazzEvaluates = <
  T extends {
    clazzEvaluaStatises?: ClazzEvaluateStatType[];
    evaluaStatises?: ClazzEvaluateStatType[];
    good?: string;
    bad?: string;
  }
>(
  data: T[]
) => {
  data.forEach((item) => {
    const stats = item.clazzEvaluaStatises || item.evaluaStatises;
    item.good = `${stats?.find((it) => it.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Good])?.score || 0}`;
    item.bad = `${stats?.find((it) => it.indactorName === IndactorTypeNameMap[IndactorTypeEnum.Bad])?.score || 0}`;
  });
  return data;
};

export const getEvaluateSubjectList = (data: GetEvaluateSubjectListProps) =>
  POST<EvaluateSubjectType[]>('/evaluation/getSubjectsByTeacher', data).then(asArray);

export const getRuleListByClazzId = (data: GetRuleListByClazzIdProps) =>
  POST<RuleListByClazzIdResponse[]>('/evaluation/getRuleListByClazzId', data).then();

export const getRuleList = (data: GetRuleListProps) =>
  POST<RuleListByClazzIdResponse[]>('/evaluation/getRuleList', data).then();

export const getClazzStudentPage = (data: ClazzStudentPageProps) =>
  POST<PagingData<EvaluateStudentType>>('/client/student/page', data, baseAuthConfig);

// 课堂班级
export const getClazzEvaluateClazzList = (data: {
  semesterId: string;
  menuId: string;
  reflectionId: string;
}) => POST<EvaluateClazzType[]>('/evaluation/getClazzByTeacher', data);

export const submitSeats = (data: SubmitSeatsProps) => POST('/clazz/student/doSeat', data);
export const getAllGroupList = (data: GetAllGroupListProps) =>
  POST<EvaluateGroupType[]>('/clazz/student/listAllByGroup', data).then(asArray);
export const getGroupList = (data: GetGroupListProps) =>
  POST<EvaluateGroupType[]>('/clazz/student/groupList', data).then(asArray).then(asClazzEvaluates);
export const submitGroups = (data: SubmitGroupsProps) => POST('/clazz/student/doGroup', data);
export const getClazzStudentListByCode = (data: GetClazzStudentListByCodeProps) =>
  POST<EvaluateStudentType[]>('/clazz/student/listByNo', data).then(asArray).then(asClazzEvaluates);
export const getClazzStudentListBySeat = (data: GetClazzStudentListBySeatProps) =>
  POST<EvaluateStudentListType>('/clazz/student/listBySeat', data).then((res) => {
    const students = asClazzEvaluates(
      res?.studentSeatList?.flatMap((it) => it)?.filter((it) => it.studentId) || []
    );
    return {
      ...res,
      studentSeatList: students
    };
  });
export const getClazzStudentListByGroup = (data: GetClazzStudentListByGroupProps) =>
  POST<EvaluateStudentType[]>('/clazz/student/listByGroup', data)
    .then(asArray)
    .then(asClazzEvaluates);
export const getClazzIndactorList = (data: GetClazzIndactorListProps) =>
  POST<EvaluateIndactorType[]>('/evaluation/getIndicatorTree', data);
export const addClazzEvaluate = (data: AddClazzEvaluateProps) => POST('/evaluation/submit', data);
export const removeClazzEvaluate = (data: RemoveClazzEvaluateProps) =>
  POST('/evaluation/delete', data);
export const resetClazzEvaluate = (data: ResetClazzEvaluateProps) =>
  POST('/evaluation/refresh', data);
export const getClazzHomeworkPage = (data: ClazzHomeworkPageProps) =>
  POST<PagingData<ClazzHomeworkPageType>>('/evaluation/clazz/homework/page', data).then((res) => {
    return res.records;
  });
export const getStudentHomeworkPage = (data: StudentHomeworkPageProps) =>
  POST<PagingData<StudentHomeworkPageType>>('/evaluation/student/homework/page', data).then(
    (res) => {
      return res.records;
    }
  );
export const getClazzEvaluateRankList = (data: ClazzEvaluateRankListProps) =>
  POST<ClazzEvaluateRecordType[]>('/evalua/classEvaluate/clazzView/studentScoreRank', data);
export const getClazzHomeworkRankList = (data: ClazzHomeworkRankListProps) =>
  POST<ClazzHomeworkRankListType[]>('/evaluation/clazz/homework/rank', data);
export const getStudentIndicatorTree = (data: GetClazzIndactorListProps) =>
  POST<EvaluateIndactorType[]>('/evaluation/student/indicator/tree', data);
export const getEvaluationStudentPage = (data: EvaluationStudentPageProps) =>
  POST<PagingData<EvaluationClazzPageType>>('/evaluation/student/page', data);
export const getEvaluationStudentStatistics = (data: EvaluationStudentStatisticsProps) =>
  POST<EvaluationStudentStatisticsType>('/evaluation/student/statistics', data);
export const getClazzRankList = (data: ClazzRankListProps) =>
  POST<ClazzRankListType[]>('/evaluation/clazz/rank', data);
export const getEvaluationClazzStatistics = (data: ClazzStudentPageProps) =>
  POST<EvaluationClazzStatisticsType>('/evaluation/clazz/statistics', data);
export const getEvaluationClazzPage = (data: EvaluationClazzPageProps) =>
  POST<PagingData<EvaluationClazzPageType>>('/evaluation/clazz/page', data);
export const addHomework = (data: AddHomeworkProps) => POST('/homework/create', data);
export const updateHomework = (data: UpdateHomeworkProps) => POST('/homework/update', data);
export const removeHomeowk = (data: RemoveHomeworkProps) => POST('/homework/delete', data);
export const getHomeworkPage = (data: GetHomeworkPageProps) =>
  POST<PagingData<EvaluateHomeworkType>>('/homework/page', data);
export const getHomeTemplatePage = (data: { subjectId: string; current: number; size: number }) =>
  POST<PagingData<EvaluateHomeworkType>>('/homework/template/page', data);
export const addHomeworkTemplate = (data: AddHomeworkTemplateProps) =>
  POST('/homework/template/create', data);
export const updateHomeworkTemplate = (data: UpdateHomeworkTemplateProps) =>
  POST('homework/template/update', data);
export const removeHomeworkTemplate = (data: RemoveHomeworkProps) =>
  POST('/homework/template/delete', data);
export const addHomeworkEvaluate = (data: AddHomeworkEvaluateProps) =>
  POST<{ id: string; name: string }[]>('/evalua/homeworkEvaluate/doEvaluate', data);
export const removeHomeworkEvaluate = (data: RemoveHomeworkEvaluateProps) =>
  POST('/evalua/homeworkEvaluate/delete', data);
export const resetHomeworkEvaluate = (data: ResetHomeworkEvaluateProps) =>
  POST('/evalua/homeworkEvaluate/refresh', data);
export const getClassRankList = (data: GetStudentLevelListProps) =>
  POST<RankDataTree[]>('/evaluation/clazz/indicator/rank', data);

export const getTeacherStatisticsPage = (data: GetTeacherStatisticsPageProps) =>
  POST<PagingData<TeacherStatisticsType>>('/evaluation/teacher/statistics/page', data);

export const getTeacherDetailsPage = (data: GetTeacherDetailsPageProps) =>
  POST<PagingData<TeacherDetailsType>>('/evaluation/teacher/details/page', data);

export const getEvaluatedBelong = (data: GetEvaluatedBelongProps) =>
  POST<EvaluatedBelongType[]>('/evaluation/getEvaluatedBelong', data);

export const getEvaluatedList = (data: GetEvaluatedListProps) =>
  POST<EvaluatedListType[]>('/evaluation/getEvaluatedList', data);

export const getTeacherIndicatorTree = (data: GetTeacherIndicatorTreeProps) =>
  POST<EvaluateIndactorType[]>('/evaluation/teacher/indicator/tree', data);
