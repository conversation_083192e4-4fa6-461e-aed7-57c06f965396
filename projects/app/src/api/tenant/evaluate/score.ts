import { POST } from '@/utils/request';
import {
  CreateScoreLevelParams,
  UpdateScoreLevelParams,
  DeleteScoreLevelParams,
  GetScoreLevelListParams,
  EvaluaScoreLevel,
  ScoreLevelDetail
} from '@/types/api/tenant/evaluate/score';

export const createScoreLevel = (data: CreateScoreLevelParams) =>
  POST<boolean>('/evaluationManage/scoreLevelCreate', data);

export const deleteScoreLevel = (data: DeleteScoreLevelParams) =>
  POST<boolean>('/evaluationManage/scoreLevelDelete', data);

export const getScoreLevelList = (data: GetScoreLevelListParams) => {
  return POST<EvaluaScoreLevel[]>('/evaluationManage/scoreLevelList', data);
};

export const updateScoreLevel = (data: UpdateScoreLevelParams) =>
  POST<boolean>('/evaluationManage/scoreLevelUpdate', data);
export const getScoreLevelDetail = (data: { id: string }): Promise<ScoreLevelDetail> => {
  return POST<ScoreLevelDetail>('/evaluationManage/scoreLevelDetail', data);
};

export const getScoreLevelValueList = ({
  scoreLevelId
}: {
  scoreLevelId: string;
}): Promise<any[]> =>
  getScoreLevelDetail({ id: scoreLevelId }).then((res: ScoreLevelDetail) => res.values);
