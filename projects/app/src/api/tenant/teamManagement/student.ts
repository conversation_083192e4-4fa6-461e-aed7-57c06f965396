import { POST } from '@/utils/request';
import { PagingData, RequestPaging } from '@/types';

import {
  DepartmentTree,
  CreateClientSchoolDeptParasm,
  UpdateClientSchoolDeptParams,
  CreateClientStudentParams,
  UpdatelientStudentParams,
  SystemRegionSelectType,
  ClientStudentPageType,
  DetailClientStudentType,
  ChangeStatusClientStudentParams,
  ChangeClazzClientStudentParams,
  ClientStudentPageQuery
} from '@/types/api/tenant/teamManagement/student';
import { getListFromPage } from '@/utils/api';
import { baseAuthConfig } from '@/constants/api/auth';

export const getClientSchoolDeptTree = () =>
  POST<DepartmentTree>('/client/schoolDept/tree', {}, baseAuthConfig);

export const createClientSchoolDept = (data: CreateClientSchoolDeptParasm) =>
  POST('/client/schoolDept/create', data, baseAuthConfig);

export const updateClientSchoolDept = (data: UpdateClientSchoolDeptParams) =>
  POST('/client/schoolDept/update', data, baseAuthConfig);

export const setClientSchoolDeptReSort = (data: any) =>
  POST('/client/schoolDept/reSort', data, baseAuthConfig);

export const getClientStudentPage = (data: ClientStudentPageQuery) =>
  POST<PagingData<ClientStudentPageType>>('/client/student/page', data, baseAuthConfig);

export const getClientStudentList = (data: {
  stageId?: string;
  gradeId?: string;
  clazzId?: string;
}) => getListFromPage(getClientStudentPage, data, 9999);

export const getSystemRegionSelect = () =>
  POST<SystemRegionSelectType>(`/system/region/select`, undefined, baseAuthConfig);

export const getSystemRegionLazyList = (parentCode: string) =>
  POST<SystemRegionSelectType>('/system/region/lazy-list', { parentCode }, baseAuthConfig);

export const createClientStudent = (data: CreateClientStudentParams) =>
  POST('/client/student/create', data, baseAuthConfig);

export const updateClientStudent = (data: UpdatelientStudentParams) =>
  POST('/client/student/update', data, baseAuthConfig);

export const detailClientStudent = (id: string) =>
  POST<DetailClientStudentType>('/client/student/detail', { id }, baseAuthConfig);

export const changeStatusClientStudent = (data: ChangeStatusClientStudentParams) =>
  POST('/client/student/changeStatus', data, baseAuthConfig);

export const changeClazzClientStudent = (data: ChangeClazzClientStudentParams) =>
  POST('/client/student/changeClazz', data, baseAuthConfig);

export const deleteClientSchoolDept = (id: string) =>
  POST('/client/schoolDept/delete', { id }, baseAuthConfig);
