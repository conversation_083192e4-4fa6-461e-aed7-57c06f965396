import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';
import {
  ClientSchoolDeptManageTreeType,
  ClientSchoolDeptManageValid,
  ClientSchoolDeptManageTreeParams,
  ClientSchoolDeptManageBindParams,
  ClientSchoolDeptManageCopyParams,
  ClientSchoolDeptSubjectManageTreeParams,
  ClientSchoolDeptSubjectManageTreeType,
  ClientSchoolDeptSubjectManageBindParams
} from '@/types/api/tenant/teamManagement/teach';
import { baseAuthConfig } from '@/constants/api/auth';

export const getClientSchoolDeptManageTree = (data: ClientSchoolDeptManageTreeParams) =>
  POST<ClientSchoolDeptManageTreeType>('/client/schoolDeptManage/tree', data, baseAuthConfig);

export const getClientSchoolDeptManageValid = (semesterId: string) =>
  POST<ClientSchoolDeptManageValid>(
    '/client/schoolDeptManage/valid',
    { semesterId },
    baseAuthConfig
  );

export const getClientSchoolDeptManageCreate = (semesterId: string) =>
  POST('/client/schoolDeptManage/create', { semesterId }, baseAuthConfig);

export const setClientSchoolDeptManageBind = (data: ClientSchoolDeptManageBindParams) =>
  POST('/client/schoolDeptManage/bind', data, baseAuthConfig);

export const setClientSchoolDeptManageCopy = (data: ClientSchoolDeptManageCopyParams) =>
  POST('/client/schoolDeptManage/copy', data, baseAuthConfig);

export const getClientSchoolDeptSubjectManageTree = (
  data: ClientSchoolDeptSubjectManageTreeParams
) =>
  POST<ClientSchoolDeptSubjectManageTreeType>(
    '/client/schoolDeptSubjectManage/tree',
    data,
    baseAuthConfig
  );

export const setClientSchoolDeptSubjectManageBind = (
  data: ClientSchoolDeptSubjectManageBindParams
) => POST('/client/schoolDeptSubjectManage/bind', data, baseAuthConfig);
