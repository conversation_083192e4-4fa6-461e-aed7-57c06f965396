import { Semester, SemesterListOptions } from './../../../types/api/tenant/teamManagement/semester';
import { POST } from '@/utils/request';
import { PagingData, RequestPageParams } from '@/types';
import {
  ClientSemesterPageType,
  DictTree,
  ClientSemesterGetDelayList,
  CreateClientSemesterParams,
  UpdateClientSemesterParams,
  DetailClientSemester,
  SetCurrentClientSemesterParams
} from '@/types/api/tenant/teamManagement/semester';
import { getListFromPage } from '@/utils/api';
import { TermMap } from '@/constants/api/tenant/evaluate/rule';
import { baseAuthConfig } from '@/constants/api/auth';

export const getClientSemesterPage = (data: any) =>
  POST<PagingData<ClientSemesterPageType>>('/client/semester/page', data, baseAuthConfig);

export const getSemesterPage = (data: any) =>
  POST<PagingData<ClientSemesterPageType>>('/client/semester/page', data, baseAuthConfig);

export const getClientSemesterList = (data: any) =>
  getListFromPage(getClientSemesterPage, data, 9999).then((res) => {
    const list: SemesterListOptions[] = [];
    res.forEach((item) => {
      let target = list.find((it) => it.year == item.year);
      if (!target) {
        let semesterItem: SemesterListOptions = {
          ...item,
          termList: [
            {
              label: TermMap[item.type].label,
              value: item.type
            }
          ]
        };
        list.push(semesterItem);
      } else {
        target.termList.push({
          label: TermMap[item.type].label,
          value: item.type
        });
      }
    });
    return list;
  });

export const getClientSemesterListAndChild = (data: any) =>
  getListFromPage(getClientSemesterPage, data, 9999).then((res) => {
    const list: SemesterListOptions[] = [];
    res.forEach((item) => {
      let target = list.find((it) => it.year == item.year);
      if (!target) {
        let semesterItem: SemesterListOptions = {
          ...item,
          termList: [
            {
              label: TermMap[item.type].label,
              value: item.type
            }
          ]
        };
        list.push(semesterItem);
      } else {
        target.termList.push({
          label: TermMap[item.type].label,
          value: item.type
        });
      }
    });
    return {
      semesterListTermChild: list,
      semesterListTermLevel: res
    };
  });

export const getClientSemesterGetDelayList = (id: string) =>
  POST<ClientSemesterGetDelayList>('/client/semester/getDelayList', { id }, baseAuthConfig);

export const getSystemDictTree = (code: string) =>
  POST<DictTree>('/system/dict/tree', { code }, baseAuthConfig);

export const createClientSemester = (data: CreateClientSemesterParams) =>
  POST('/client/semester/create', data, baseAuthConfig);

export const updateClientSemester = (data: UpdateClientSemesterParams) =>
  POST('/client/semester/update', data, baseAuthConfig);

export const getDetailClientSemester = (id: string) =>
  POST<DetailClientSemester>('/client/semester/detail', { id }, baseAuthConfig);

export const setCurrentClientSemester = (data: SetCurrentClientSemesterParams) =>
  POST('/client/semester/setCurrent', data, baseAuthConfig);
