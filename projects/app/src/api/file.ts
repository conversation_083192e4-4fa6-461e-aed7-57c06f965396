import { baseAuthConfig } from '@/constants/api/auth';
import { FileMetaType } from '@/types/api/file';
import { POST } from '@/utils/request';
import { AxiosProgressEvent, AxiosResponse } from 'axios';

export const uploadFile = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType>('/system/file/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    },
    ...baseAuthConfig
  });

export const uploadMultipleFiles = (
  data: FormData,
  config?: {
    signal?: AbortSignal;
    onUploadProgress?: (progressEvent: AxiosProgressEvent) => void;
  }
) =>
  POST<FileMetaType[]>('/system/file/batch/upload', data, {
    timeout: 480000,
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data; charset=utf-8'
    },
    ...baseAuthConfig
  });

export const downloadFile = (
  fileKey: string,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void }
) =>
  POST<AxiosResponse>(
    '/system/file/download',
    { objectKey: fileKey },
    {
      timeout: 480000,
      responseType: 'blob',
      ...config,
      ...baseAuthConfig
    }
  );

export const downloadFileByUrl = (
  url: string,
  config?: { signal?: AbortSignal; onUploadProgress?: (progressEvent: AxiosProgressEvent) => void }
) =>
  POST<AxiosResponse>(
    url,
    {},
    {
      timeout: 480000,
      responseType: 'blob',
      ...config,
      ...baseAuthConfig
    }
  );

export const getFileList = (fileKeys: string[]) =>
  POST<FileMetaType[]>('/system/file/list', { fileKeys }, baseAuthConfig);

export const getFileMeta = (fileKey: string) =>
  POST<FileMetaType>('/system/file/detail', { objectKey: fileKey }, baseAuthConfig);
