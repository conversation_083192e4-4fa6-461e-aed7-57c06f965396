import { baseAuthConfig } from '@/constants/api/auth';
import { PagingData } from '@/types';
import {
  ResponseType,
  ClientUserValidType,
  UpdateUserProps,
  WxBindUserLoginParams,
  UserGetDeptType,
  UserDetailParams,
  UserDetailType,
  updateUserPhoneNumParams,
  UserDeptUserTreeType,
  SpaceTransferParams,
  CaptchaType,
  ClientAuthResetPwdParams,
  FirstLoginUpdatePwdParams,
  FirstLoginUpdatePwdResponse
} from '@/types/api/user';
import { GET, POST } from '@/utils/request';
import { hashStr } from '@/utils/string';

export const updateUser = (data: UpdateUserProps) =>
  POST(
    '/client/user/update',
    data.oldPassword && data.password
      ? { ...data, oldPassword: hashStr(data.oldPassword), password: hashStr(data.password) }
      : data,
    baseAuthConfig
  );
export const getClientUserSmsCode = (
  bizType: number,
  mobile: string,
  newMobile?: string,
  ticket?: string,
  moveLength?: number
) => {
  let url = `/client/user/smsCode?bizType=${bizType}&mobile=${mobile}&ticket=${ticket}&moveLength=${moveLength}`;
  if (newMobile) {
    url += `&newMobile=${newMobile}`;
  }
  return POST<ResponseType>(url, '', { isResponseData: true });
};

export const getClientUserValid = (bizType: number, mobile: string, code: number) =>
  POST<ClientUserValidType[]>(
    `/client/user/valid?bizType=${bizType}&mobile=${mobile}&code=${code}`,
    undefined,
    baseAuthConfig
  );

export const getWxBindUserLogin = (data: WxBindUserLoginParams) =>
  POST(`/wx/bindUserLogin`, data, baseAuthConfig);

export const getUpdateUserPhoneNum = (data: updateUserPhoneNumParams) =>
  POST(`/client/user/changePhoneNum`, data, baseAuthConfig);

export const clientUserGetDept = () =>
  GET<UserGetDeptType>(`/client/user/getDept`, undefined, baseAuthConfig);

export const getClientUserDetail = (data: UserDetailParams) =>
  POST<UserDetailType>(`/client/user/detail`, data, baseAuthConfig);

export const getClientTenantUserDeptUserTree = () =>
  POST<UserDeptUserTreeType[]>(`/client/tenant/user/deptUserTree`, undefined, baseAuthConfig);

export const spaceTransfer = (data: SpaceTransferParams) =>
  POST(`/cloud/space/transfer`, data, baseAuthConfig);

export const getCaptcha = () =>
  POST<CaptchaType>('/client/auth/getCaptcha', undefined, baseAuthConfig);

export const setClientAuthResetPwd = ({ password, password1, ...data }: ClientAuthResetPwdParams) =>
  POST(`/client/auth/resetPwd`, {
    password: hashStr(password),
    password1: hashStr(password1),
    ...data
  });
/** 首次登录修改密码 */
export const setFirstLoginUpdatePwd = ({ password }: FirstLoginUpdatePwdParams) =>
  POST<FirstLoginUpdatePwdResponse>(
    `/client/user/first/update`,
    {
      password: hashStr(password)
    },
    baseAuthConfig
  );
