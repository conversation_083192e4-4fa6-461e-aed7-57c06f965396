import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { POST } from '@/utils/request';
import { useToast } from '@/hooks/useToast';
import { useTranslation } from 'next-i18next';
import { getErrText } from '@/utils/string';

type StopReasonType = 'finish' | 'cancel';
const getSupportedAudioFormats = () => {
  const audioFormats = [
    'audio/mp3',
    'audio/aac',
    'audio/ogg',
    'audio/wav',
    'audio/flac',
    'audio/webm',
    'audio/mp4'
  ];

  const supportedFormats = audioFormats.filter((format) => MediaRecorder.isTypeSupported(format));

  return supportedFormats;
};

export const useSpeech = (props?: { shareId?: string }) => {
  const { shareId } = props || {};
  const { t } = useTranslation();
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const { toast } = useToast();
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isTransCription, setIsTransCription] = useState(false);
  const [audioSecond, setAudioSecond] = useState(0);
  const intervalRef = useRef<any>();
  const startTimestamp = useRef(0);
  const stopReasonRef = useRef<StopReasonType>();

  const speakingTimeString = useMemo(() => {
    const minutes: number = Math.floor(audioSecond / 60);
    const remainingSeconds: number = Math.floor(audioSecond % 60);
    const formattedMinutes: string = minutes.toString().padStart(2, '0');
    const formattedSeconds: string = remainingSeconds.toString().padStart(2, '0');
    return `${formattedMinutes}:${formattedSeconds}`;
  }, [audioSecond]);

  const renderAudioGraph = useCallback(
    (analyser: AnalyserNode, canvas: HTMLCanvasElement | null) => {
      const bufferLength = analyser.frequencyBinCount;
      const backgroundColor = 'white';
      const dataArray = new Uint8Array(bufferLength);
      analyser.getByteTimeDomainData(dataArray);
      const canvasCtx = canvas?.getContext('2d');
      const width = 300;
      const height = 200;
      if (!canvasCtx) return;
      canvasCtx.clearRect(0, 0, width, height);
      canvasCtx.fillStyle = backgroundColor;
      canvasCtx.fillRect(0, 0, width, height);
      const barWidth = (width / bufferLength) * 2.5;
      let x = 0;

      canvasCtx.moveTo(x, height / 2);
      for (let i = 0; i < bufferLength; i += 10) {
        const barHeight = (dataArray[i] / 256) * height - height * 0.15;
        canvasCtx.fillStyle = '#3370FF';
        const adjustedBarHeight = Math.max(0, barHeight);
        canvasCtx.fillRect(x, height - adjustedBarHeight, barWidth, adjustedBarHeight);
        x += barWidth + 1;
      }
    },
    []
  );

  const startSpeak = async (onFinish: (text: string) => void) => {
    if (isSpeaking || isTransCription) {
      return;
    }
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMediaStream(stream);
      const mimeType = getSupportedAudioFormats()[0];
      mediaRecorder.current = new MediaRecorder(stream, { mimeType });
      const chunks: Blob[] = [];
      setIsSpeaking(true);

      mediaRecorder.current.onstart = () => {
        startTimestamp.current = Date.now();
        setAudioSecond(0);
        intervalRef.current = setInterval(() => {
          const currentTimestamp = Date.now();
          const duration = (currentTimestamp - startTimestamp.current) / 1000;
          setAudioSecond(duration);
        }, 1000);
      };

      mediaRecorder.current.ondataavailable = (e) => {
        chunks.push(e.data);
      };

      mediaRecorder.current.onstop = async () => {
        if (stopReasonRef.current === 'cancel') {
          setIsTransCription(false);
          setIsSpeaking(false);
          return;
        }

        const formData = new FormData();
        const blob = new Blob(chunks, { type: mediaRecorder.current?.mimeType });

        const duration = Math.round((Date.now() - startTimestamp.current) / 1000);

        formData.append('file', blob, 'recording.mp4');
        formData.append('metadata', JSON.stringify({ duration, shareId }));
        formData.append('language', 'zh');
        formData.append('prompt', '以下是普通话');

        setIsTransCription(true);
        try {
          const result = await POST<string>(
            '/client/chat/huawei/cloud/audio/transcriptions',
            formData,
            {
              timeout: 60000,
              headers: {
                'Content-Type': 'multipart/form-data; charset=utf-8'
              }
            }
          );
          onFinish(result);
        } catch (error) {
          console.log('error', error);
          toast({
            status: 'warning',
            title: getErrText(error, t('common.speech.error tip'))
          });
        }
        setIsTransCription(false);
        setIsSpeaking(false);
      };

      mediaRecorder.current.onerror = (e) => {
        console.log('error', e);
        setIsSpeaking(false);
      };

      mediaRecorder.current.start();
    } catch (e) {
      console.log('error', e);
      toast({
        status: 'error',
        title: '未检测到麦克风'
      });
    }
  };

  const stopSpeak = (reason: StopReasonType = 'finish') => {
    if (mediaRecorder.current) {
      stopReasonRef.current = reason;
      mediaRecorder.current.stop();
      clearInterval(intervalRef.current);
    }
  };

  useEffect(() => {
    return () => {
      clearInterval(intervalRef.current);
      if (mediaRecorder.current && mediaRecorder.current.state !== 'inactive') {
        mediaRecorder.current.stop();
      }
      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  return {
    startSpeak,
    stopSpeak,
    isSpeaking,
    isTransCription,
    renderAudioGraph,
    stream: mediaStream,
    speakingTimeString
  };
};
