import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useKeepState } from './useKeepState';
import { useUpdateEffect } from '@chakra-ui/react';

interface Props<TData, TError, TQuery> {
  // 保存查询状态的键值，如果一个页面使用多个时需要指定
  key?: string;
  // 默认查询数据
  defaultQuery?: TQuery | (() => TQuery | undefined);
  // 是否开启
  enabled?: boolean;
  // 数据接口
  api: (query: TQuery) => Promise<TData[]>;
  // 成功回调
  onSuccess?: (data: TData[]) => void;
  // 失败回调
  onError?: (err: TError) => void;
}

interface Result<TData, TQuery> {
  query?: TQuery;
  data: TData[];
  isFetching: boolean;
  isFetched: boolean;
  isError: boolean;
  setQuery: (query?: TQuery) => void;
  refetch: () => void;
}

export function useQueryList<TData extends any, TError = unknown, TQuery = Record<string, any>>({
  key,
  defaultQuery,
  enabled = true,
  api,
  onSuccess,
  onError
}: Props<TData, TError, TQuery>): Result<TData, TQuery> {
  const { state, setState } = useKeepState({
    key,
    initState: () => ({
      query: typeof defaultQuery === 'function' ? (defaultQuery as () => TQuery)() : defaultQuery
    })
  });

  const [query, setQuery] = useState(
    state.query ||
      (typeof defaultQuery === 'function' ? (defaultQuery as () => TQuery)() : defaultQuery)
  );

  const [innerEnabled, setInnerEnabled] = useState(enabled);

  const { data, isFetching, isFetched, isError, refetch } = useQuery(
    [key, query],
    ({ queryKey: [, data] }) => api(data as TQuery),
    {
      enabled: innerEnabled,
      onSuccess,
      onError
    }
  );

  const updatedQuery = key
    ? undefined
    : typeof defaultQuery === 'function'
      ? (defaultQuery as () => TQuery)()
      : defaultQuery;

  useUpdateEffect(() => {
    setQuery(updatedQuery);
  }, [updatedQuery && JSON.stringify(updatedQuery)]);

  useUpdateEffect(() => {
    setInnerEnabled(enabled);
  }, [enabled]);

  useEffect(() => {
    setQuery(state.query);
  }, [state]);

  useEffect(() => {
    setState((state) => {
      state.query = query;
      return state;
    });
  }, [setState, query]);

  return {
    query,
    data: data || [],
    isFetching,
    isFetched,
    isError,
    setQuery: (newQuery) => {
      setQuery(newQuery);
    },
    refetch
  };
}
