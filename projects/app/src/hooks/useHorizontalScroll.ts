import { useRef, useEffect } from 'react';

export function useHorizontalScroll(scrollbarRef: React.RefObject<HTMLDivElement>) {
  useEffect(() => {
    let isDragging = false;
    let startX = 0;
    let scrollLeft = 0;

    const onMouseDown = (event: MouseEvent) => {
      isDragging = true;
      startX = event.clientX;
      const wrap = scrollbarRef.current;

      if (wrap) {
        scrollLeft = wrap.scrollLeft;
      }
    };

    const onMouseMove = (event: MouseEvent) => {
      if (isDragging) {
        const deltaX = event.clientX - startX;
        if (scrollbarRef.current) {
          scrollbarRef.current.scrollTo({
            left: scrollLeft - deltaX,
            behavior: 'auto'
          });
        }
      }
    };

    const onMouseUp = () => {
      isDragging = false;
    };

    const wrap = scrollbarRef.current;
    if (wrap) {
      wrap.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    }

    // 清理函数
    return () => {
      if (wrap) {
        wrap.removeEventListener('mousedown', onMouseDown);
      }
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
  }, [scrollbarRef]); // 依赖数组包含 scrollbarRef

  return scrollbarRef;
}
