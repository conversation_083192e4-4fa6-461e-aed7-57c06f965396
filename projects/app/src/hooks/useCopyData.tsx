import { useTranslation } from 'next-i18next';
import { useToast } from '@/hooks/useToast';

/**
 * copy text data
 */
export const useCopyData = () => {
  const { t } = useTranslation();
  const { toast } = useToast();

  return {
    copyData: async (
      data: string | { data: string; type: string }[],
      title: string | null = t('common.Copy Successful'),
      duration = 1000
    ) => {
      if (typeof data === 'string') {
        data = [{ data, type: 'text/plain' }];
      }
      try {
        if (navigator.clipboard) {
          const items: Record<string, Blob> = {};
          data.forEach(({ data, type }) => {
            items[type] = new Blob([data], { type });
          });
          await navigator.clipboard.write([new ClipboardItem(items)]);
        } else {
          throw new Error('');
        }
      } catch (error) {
        console.log(error);

        const textarea = document.createElement('textarea');
        textarea.value = (data.find((it) => it.type === 'text/plain') || data[0]).data;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body?.removeChild(textarea);
      }

      toast({
        title,
        status: 'success',
        duration
      });
    }
  };
};
