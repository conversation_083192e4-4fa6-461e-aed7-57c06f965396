import { useEffect, useState } from 'react';
import elementResizeDetectorMaker from 'element-resize-detector';

export function useResizeObserver({ selector }: { selector?: HTMLElement | string } = {}) {
  const [element, setElement] = useState<HTMLElement | null>();

  const [dimensions, setDimensions] = useState<{
    clientWidth?: number;
    clientHeight?: number;
    offsetWidth?: number;
    offsetHeight?: number;
    scrollWidth?: number;
    scrollHeight?: number;
  }>();

  useEffect(() => {
    let elem = element;
    if (!elem) {
      if (typeof selector === 'string') {
        const e = document.querySelector(selector);
        if (e instanceof HTMLElement) {
          elem = e;
        }
      } else if (selector instanceof HTMLElement) {
        elem = selector;
      }
    }

    if (!elem) {
      return;
    }

    const detector = elementResizeDetectorMaker();
    detector.listenTo(elem, (res) => {
      setDimensions({
        clientWidth: res.clientWidth,
        clientHeight: res.clientHeight,
        offsetWidth: res.offsetWidth,
        offsetHeight: res.offsetHeight,
        scrollWidth: res.scrollWidth,
        scrollHeight: res.scrollHeight
      });
    });

    return () => detector.uninstall(elem);
  }, [element, selector]);

  return {
    element,
    setElement,
    ...dimensions
  };
}
