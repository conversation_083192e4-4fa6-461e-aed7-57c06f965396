import React, { useRef, useCallback, useEffect } from 'react';
import { Box } from '@chakra-ui/react';
import { useToast } from '@/hooks/useToast';
import { useTranslation } from 'next-i18next';

export const useSelectFile = (props?: {
  fileType?: string;
  multiple?: boolean;
  capture?: boolean | 'user';
  maxCount?: number;
  directory?: boolean;
  maxSingleSize?: number; // 单个文件最大大小(字节)
  maxTotalSize?: number; // 所有文件总大小限制(字节)
}) => {
  const { t } = useTranslation();
  const {
    fileType = '*',
    multiple = false,
    capture,
    maxCount = 20,
    directory,
    maxSingleSize,
    maxTotalSize
  } = props || {};
  const { toast } = useToast();
  const SelectFileDom = useRef<HTMLInputElement>(null);
  const openSign = useRef<any>();

  const File = useCallback(
    ({ onSelect }: { onSelect: (e: File[], sign?: any) => void }) => {
      useEffect(() => {
        if (SelectFileDom.current && 'webkitdirectory' in SelectFileDom.current) {
          SelectFileDom.current['webkitdirectory'] = !!directory;
        }
      }, []);

      return (
        <Box position={'absolute'} w={0} h={0} overflow={'hidden'}>
          <input
            ref={SelectFileDom}
            type="file"
            accept={fileType}
            multiple={multiple}
            capture={capture}
            onChange={(e) => {
              if (!e.target.files || e.target.files?.length === 0) return;
              const files = Array.from(e.target.files);

              if (files.length > maxCount) {
                return toast({
                  status: 'warning',
                  title: `每次最多只能上传${maxCount}个文件`
                });
              }

              // 检查单个文件大小
              if (maxSingleSize) {
                const oversizedFiles = files.filter((file) => file.size > maxSingleSize);
                if (oversizedFiles.length > 0) {
                  return toast({
                    status: 'warning',
                    title: `以下文件超过单个文件大小限制 (${maxSingleSize / 1024 / 1024}MB): ${oversizedFiles.map((f) => f.name).join(', ')}`
                  });
                }
              }

              // 检查总文件大小
              if (maxTotalSize) {
                const totalSize = files.reduce((sum, file) => sum + file.size, 0);
                if (totalSize > maxTotalSize) {
                  return toast({
                    status: 'warning',
                    title: `所选文件总大小 (${(totalSize / 1024 / 1024).toFixed(2)}MB) 超过限制 (${maxTotalSize / 1024 / 1024}MB)`
                  });
                }
              }

              onSelect(files, openSign.current);
            }}
          />
        </Box>
      );
    },
    [fileType, maxCount, capture, multiple, directory, maxSingleSize, maxTotalSize, toast]
  );

  const onOpen = useCallback((sign?: any) => {
    openSign.current = sign;
    SelectFileDom.current && SelectFileDom.current.click();
  }, []);

  return {
    File,
    onOpen
  };
};
