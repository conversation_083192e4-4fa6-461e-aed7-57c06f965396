import React, { useContext, useRef, useCallback, createContext, useState } from 'react';
import { PortalContext } from '@/components/PortalProvider';

// 修改 Context 类型
export const CompDomContext = createContext<{
  onImageGenerated?: (id: string, loaded: boolean) => void;
  showImage?: boolean;
}>({});

const useCompDom = () => {
  const { addToPortal, removeFromPortal } = useContext(PortalContext);
  const keyRef = useRef(`portal-${Math.random().toString(36).substr(2, 9)}`);
  const domRef = useRef<HTMLElement | null>(null);

  const getCompDom = useCallback(
    (Comp: React.FC<any>, props: any = {}, timeout = 60000) => {
      return new Promise<HTMLElement | null>((resolve, reject) => {
        const key = keyRef.current;
        const wrapperId = `wrapper-${key}`;

        const imageStatus = new Map<string, boolean>();
        const onImageGenerated = (id: string, loaded: boolean) => {
          imageStatus.set(id, loaded);
        };

        const element = (
          <CompDomContext.Provider value={{ onImageGenerated, showImage: true }}>
            <div id={wrapperId} style={{ visibility: 'hidden' }}>
              <Comp {...props} />
            </div>
          </CompDomContext.Provider>
        );
        addToPortal(key, element);

        const interval = setInterval(() => {
          const wrapperElement = document.getElementById(wrapperId) as HTMLElement;
          if (wrapperElement) {
            console.log(Array.from(imageStatus.values()));

            const allImagesGenerated = Array.from(imageStatus.values()).every((status) => status);
            if (allImagesGenerated) {
              const childElement = wrapperElement.firstElementChild as HTMLElement;
              if (childElement) {
                domRef.current = childElement.cloneNode(true) as HTMLElement;
                clearInterval(interval);
                clearTimeout(timeoutId);
                resolve(domRef.current);
                // removeFromPortal(key);
              }
            }
          }
        }, 100);

        const timeoutId = setTimeout(() => {
          clearInterval(interval);
          reject(new Error('Timeout: Unable to get component DOM within the specified time.'));
          removeFromPortal(key);
        }, timeout);
      });
    },
    [addToPortal, removeFromPortal]
  );

  return { getCompDom };
};

export default useCompDom;
