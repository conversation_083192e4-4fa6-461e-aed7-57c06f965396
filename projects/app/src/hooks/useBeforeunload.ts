import { useTranslation } from 'next-i18next';
import { useEffect } from 'react';

export const useBeforeunload = (props?: {
  callback?: () => any;
  tip?: string;
  enabled?: boolean;
}) => {
  const { t } = useTranslation();

  const { tip = '系统可能不会保存您所做的更改', callback, enabled = true } = props || {};

  useEffect(() => {
    if (!enabled) return;

    const listen =
      process.env.NODE_ENV === 'production'
        ? (e: any) => {
            e.preventDefault();
            e.returnValue = tip;
            callback?.();
          }
        : (e: any) => {
            e.preventDefault();
            e.returnValue = tip;
            callback?.();
          };
    window.addEventListener('beforeunload', listen);

    return () => {
      window.removeEventListener('beforeunload', listen);
    };
  }, [tip, callback, enabled]);
};
