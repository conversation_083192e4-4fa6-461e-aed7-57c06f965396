import { useRouter } from 'next/router';
import { useEffect, useState, useCallback, useRef } from 'react';

type BucketType = {
  key: string;
  states: Record<string, any>;
};

type RouteType = {
  buckets: BucketType[];
};

type StoreType = {
  version: string;
  nextKey: number;
  routes: Record<string, RouteType>;
  global: Record<string, any>;
};

type MatchKeyType = string | string[] | RegExp;

const isBrowser = typeof window !== 'undefined';

const bucketLimit = 2;

const storeVersion = '1.0.0';

const storeKey = 'keepState';

const store: StoreType = ((): StoreType => {
  if (isBrowser) {
    try {
      const store = JSON.parse(sessionStorage.getItem(storeKey) || '{}');
      if (store?.version === storeVersion) {
        return store;
      }
    } catch {}
  }
  return {
    version: storeVersion,
    nextKey: 1,
    routes: {},
    global: {}
  };
})();

if (isBrowser) {
  window.addEventListener('beforeunload', () => {
    sessionStorage.setItem(storeKey, JSON.stringify(store));
  });
}

const removeState = (states: Record<string, any>, key: MatchKeyType, inverse?: boolean) => {
  if (Array.isArray(key)) {
    Object.keys(states).forEach((it) => {
      if (key.includes(it) === !inverse) {
        delete states[it];
      }
    });
  } else if (typeof key === 'string') {
    Object.keys(states).forEach((it) => {
      if ((it === key || key === '*') === !inverse) {
        delete states[it];
      }
    });
  } else {
    Object.keys(states).forEach((it) => {
      if (key.test(it) === !inverse) {
        delete states[it];
      }
    });
  }
};

const getRouteState = (pathname: string, bucketKey: string, key?: string) => {
  if (!pathname || !bucketKey || !key) {
    return;
  }

  const route = store.routes[pathname];
  if (!route) {
    return;
  }

  const index = route.buckets.findIndex((it) => it.key === bucketKey);
  if (index < 0) {
    return;
  }

  const bucket = route.buckets[index];
  if (index > 0) {
    route.buckets.splice(index, 1);
    route.buckets.unshift(bucket);
  }
  return bucket.states[key];
};

const setRouteState = (
  pathname: string,
  bucketKey: string,
  key: string | undefined,
  state: any
) => {
  if (!pathname || !key) {
    return;
  }

  const route = store.routes[pathname];
  if (!route) {
    if (state === undefined) {
      return;
    }
    const bucket = {
      key: `${store.nextKey++}`,
      states: {
        [key]: state
      }
    };
    store.routes[pathname] = {
      buckets: [bucket]
    };
    return bucket.key;
  }

  const index = route.buckets.findIndex((it) => it.key === bucketKey);
  if (index < 0) {
    if (state === undefined) {
      return;
    }

    if (route.buckets.length >= bucketLimit) {
      route.buckets.splice(bucketLimit - 1);
    }

    const bucket = {
      key: `${store.nextKey++}`,
      states: {
        [key]: state
      }
    };
    route.buckets.unshift(bucket);
    return bucket.key;
  }

  const bucket = route.buckets[index];
  if (index > 0) {
    route.buckets.splice(index, 1);
    route.buckets.unshift(bucket);
  }

  if (state === undefined) {
    delete bucket.states[key];
  } else {
    bucket.states[key] = state;
  }
  return bucket.key;
};

const removeRouteState = (
  pathname: string,
  bucketKey: string,
  key: MatchKeyType,
  inverse?: boolean
) => {
  if (!pathname || !bucketKey) {
    return;
  }

  const route = store.routes[pathname];
  if (!route) {
    return;
  }

  const bucket = route.buckets.find((it) => it.key === bucketKey);
  if (!bucket?.states) {
    return;
  }

  removeState(bucket.states, key, inverse);
};

const getGlobalState = (key?: string) => (key ? store.global[key] : undefined);

const setGlobalState = (key: string | undefined, state: any) => {
  if (!key) {
    return;
  }
  if (state === undefined) {
    delete store.global[key];
  } else {
    store.global[key] = state;
  }
};

const removeGlobalState = (key: MatchKeyType, inverse?: boolean) => {
  removeState(store.global, key, inverse);
};

const bucketKeyName = 'rBK';

export function useKeepState<T = any>({
  key,
  type = 'route',
  initState
}: {
  key?: string;
  type?: 'route' | 'global';
  initState: T | (() => T);
}) {
  const router = useRouter();

  const pathnameRef = useRef(router.pathname);

  const bucketKey = (router.query[bucketKeyName] as string) || '';

  const bucketKeyRef = useRef<string>(bucketKey);

  const keyRef = useRef(key);

  const typeRef = useRef(type);

  const stateRef = useRef<{ state: T }>();

  const [, refresh] = useState(false);

  if (stateRef.current === undefined) {
    stateRef.current = {
      state:
        ((type === 'route'
          ? (getRouteState(router.pathname, bucketKey, keyRef.current) as T)
          : getGlobalState(keyRef.current)) as T) ||
        (typeof initState === 'function' ? (initState as () => T)() : initState)
    };
  }

  const onSetState =
    typeRef.current === 'route'
      ? useCallback(
          (state: T | ((state: T) => T)) => {
            const newState =
              typeof state === 'function'
                ? (state as (state: T) => T)(stateRef.current!.state)
                : state;

            if (JSON.stringify(newState) !== JSON.stringify(stateRef.current!.state)) {
              stateRef.current!.state = newState;
              refresh((state) => !state);
            }

            const bucketKey = setRouteState(
              router.pathname,
              bucketKeyRef.current,
              keyRef.current,
              newState
            );

            if (bucketKey && bucketKey !== bucketKeyRef.current) {
              bucketKeyRef.current = bucketKey;
              router.replace(
                {
                  pathname: router.pathname,
                  query: {
                    ...router.query,
                    [bucketKeyName]: bucketKey
                  }
                },
                undefined,
                { shallow: true }
              );
            }
          },
          [router]
        )
      : useCallback((state: T | ((state: T) => T)) => {
          const newState =
            typeof state === 'function'
              ? (state as (state: T) => T)(stateRef.current!.state)
              : state;

          if (JSON.stringify(newState) !== JSON.stringify(stateRef.current!.state)) {
            stateRef.current!.state = newState;
            refresh((state) => !state);
          }
          setGlobalState(keyRef.current, newState);
        }, []);

  const removeState = useCallback((key: MatchKeyType, inverse?: boolean) => {
    if (typeRef.current === 'route') {
      removeRouteState(pathnameRef.current, bucketKeyRef.current, key, inverse);
    } else {
      removeGlobalState(key, inverse);
    }
  }, []);

  useEffect(() => {
    if (typeRef.current !== 'route') {
      return;
    }
    if (router.pathname === pathnameRef.current && bucketKey === bucketKeyRef.current) {
      return;
    }
    stateRef.current = {
      state:
        (getRouteState(router.pathname, bucketKey, keyRef.current) as T) ||
        (typeof initState === 'function' ? (initState as () => T)() : initState)
    };
    refresh((state) => !state);
    pathnameRef.current = router.pathname;
    bucketKeyRef.current = bucketKey;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.pathname, bucketKey]);

  return {
    state: stateRef.current!.state,
    setState: onSetState,
    removeState
  };
}
