import {
  RouteConfigType,
  RouteType,
  RouteGroupConfigType,
  RouteMapType,
  RouteUpdateFieldsType
} from '@/types/routes';
import { chatRoutes } from './chat';
import { adminRoutes } from './admin';
import { RouteGroupTypeEnum } from '@/constants/routes';
import { treeForEach } from '@/utils/tree';
import { SvgIconNameType } from '@/components/SvgIcon/data';

const UpdateFieldKeys: (keyof RouteUpdateFieldsType)[] = ['name', 'icon', 'activeIcon', 'id'];

export const getAuthRoutes = (
  routes: RouteType[],
  codes: string[],
  fieldsMap?: Record<string, RouteUpdateFieldsType>,
  parent?: RouteType
): RouteType[] => {
  if (!parent) {
    // 子菜单授权后父菜单也授权
    codes = [...codes];
    treeForEach(
      routes,
      (node) => {
        if (
          node.code &&
          !codes.includes(node.code) &&
          node.children?.some((it) => it.code && codes.includes(it.code))
        ) {
          codes.push(node.code);
        }
      },
      'children',
      false
    );
  }

  return routes
    .map((item) => {
      if (!item.unauth && item.code && !codes.includes(item.code)) {
        return undefined;
      }
      const route = { ...item, parent };
      const fields = route.code && fieldsMap?.[route.code];

      fields &&
        UpdateFieldKeys.forEach((key) => {
          const value = fields[key];
          if (value) {
            route[key] = value as SvgIconNameType;
          }
        });
      route.children = item.children?.length
        ? getAuthRoutes(item.children, codes, fieldsMap, route)
        : undefined;
      if (route.children && route.children.some((child) => child.active)) {
        route.active = true; // 设置父级为激活状态
      }

      return !item.path && !route.children?.length ? undefined : route;
    })
    .filter((item) => !!item) as RouteType[];
};

export const getNavRoutes = (routes: RouteType[], parent?: RouteType): RouteType[] =>
  routes
    .map((item) => {
      if (item.hidden) {
        return undefined;
      }
      const route = { ...item, parent };
      route.children = item.children?.length ? getNavRoutes(item.children, route) : undefined;
      return route;
    })
    .filter((item) => !!item) as RouteType[];

export const createRouteMap = (routes: RouteType[], map: RouteMapType = {}) => {
  routes.forEach((item) => {
    map[item.key] = item;
    if (item.children) {
      createRouteMap(item.children, map);
    }
  });
  return map;
};

export const traverseRoutes = <T>(
  routes: RouteType[],
  callback: (route: RouteType) => T | undefined,
  postOrder?: boolean
): T | undefined => {
  let res: T | undefined;
  routes.some((it) => {
    if (!postOrder) {
      res = callback(it);
      if (res) {
        return true;
      }
    }
    if (it.children?.length) {
      res = traverseRoutes(it.children, callback, postOrder);
      if (res) {
        return true;
      }
    }
    if (postOrder) {
      res = callback(it);
      if (res) {
        return true;
      }
    }
    return false;
  });
  return res;
};

export const traverseRouteTop = <T>(
  route: RouteType,
  callback: (it: RouteType) => T | undefined
): T | undefined => {
  let p: RouteType | undefined = route;
  while (p) {
    const res = callback(p);
    if (res) {
      return res;
    }
    p = p.parent;
  }
};

export const findActiveRoute = (routes: RouteType[], path: string, asPath: string) =>
  traverseRoutes<RouteType>(
    routes,
    (route) => {
      if (
        route.path === path ||
        route.activePrefixes?.some((it) => asPath.startsWith(it)) ||
        route.activePattern?.test(asPath)
      ) {
        return route;
      }
    },
    true
  );

export const findRouteByKey = (routes: RouteType[], key: string) => {
  let route: RouteType | undefined;
  routes.some((it) => {
    if (it.key === key) {
      route = it;
    } else {
      route = it.children?.length ? findRouteByCode(it.children, key) : undefined;
    }
    return !!route;
  });
  return route;
};

export const findRouteByCode = (routes: RouteType[], code: string) => {
  let route: RouteType | undefined;
  routes.some((it) => {
    if (it.code === code) {
      route = it;
    } else {
      route = it.children?.length ? findRouteByCode(it.children, code) : undefined;
    }
    return !!route;
  });
  return route;
};

let nextKey = 0;
const initRoutes = (routes: RouteConfigType[], parent?: RouteType): RouteType[] => {
  (routes as RouteType[]).forEach((route) => {
    route.key = `rkey-${nextKey++}`;
    route.parent = parent;
    if (route.children) {
      initRoutes(route.children, route);
    }
  });
  return routes as RouteType[];
};

export const routeGroups: RouteGroupConfigType[] = [
  {
    type: RouteGroupTypeEnum.Chat,
    routes: initRoutes(chatRoutes)
  },
  {
    type: RouteGroupTypeEnum.Admin,
    routes: initRoutes(adminRoutes)
  }
];
