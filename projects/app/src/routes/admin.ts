import { RouteConfigType } from '@/types/routes';

export const adminRoutes: RouteConfigType[] = [
  {
    name: '数智看板',
    code: 'digital_kanban',
    activeIcon: 'evaluateKanbanActive',
    inactiveIcon: 'evaluateKanbanInactive',
    children: [
      {
        name: '综合看板',
        path: '/tenant/digitalKanban/comprehensive',
        code: 'comprehensive_kanban'
      },
      {
        name: '学生看板',
        path: '/tenant/digitalKanban/student',
        code: 'student_kanban'
      },
      {
        name: '测试质量分析',
        path: '/tenant/digitalKanban/qualityAnalysis',
        code: 'qualityAnalysis_kanban'
      }
    ]
  },
  {
    name: '评价项管理',
    code: 'evaluate_item_manage',
    activeIcon: 'evaluateManageActive',
    inactiveIcon: 'evaluateManageInactive',
    children: [
      {
        name: '维度项目指标设置',
        path: '/tenant/evaluateItemManage/dimensionProjectSetting',
        code: 'dimension_project_setting'
      },
      {
        name: '评价规则设置',
        path: '/tenant/evaluateItemManage/ruleSetting',
        code: 'rule_setting'
      }
    ]
  },
  {
    name: '学生评价',
    code: 'student_evaluate',
    activeIcon: 'evaluateTeacherActive',
    inactiveIcon: 'evaluateTeacherInactive',
    children: [
      {
        name: '培养目标达成度',
        path: '/tenant/teacherEvaluate/achievement',
        code: 'achievement',
        activePrefixes: ['/tenant/teacherEvaluate/achievement'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/teacherEvaluate/achievement/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/teacherEvaluate/achievement/record',
            hidden: true
          }
        ]
      },
      {
        name: '学习适应性',
        path: '/tenant/teacherEvaluate/learnAdaptability',
        code: 'learn_adaptability',
        activePrefixes: ['/tenant/teacherEvaluate/learnAdaptability'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/teacherEvaluate/learnAdaptability/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/teacherEvaluate/learnAdaptability/record',
            hidden: true
          }
        ]
      },
      {
        name: '文化知识',
        path: '/tenant/teacherEvaluate/culturalKnowledge',
        code: 'cultural_knowledge',
        activePrefixes: ['/tenant/teacherEvaluate/culturalKnowledge'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/teacherEvaluate/culturalKnowledge/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/teacherEvaluate/culturalKnowledge/record',
            hidden: true
          },
          {
            name: '课堂表现评价记录',
            path: '/tenant/teacherEvaluate/culturalKnowledge/classroomPerformanceRecord',
            hidden: true
          },
          {
            name: '作业评价记录',
            path: '/tenant/teacherEvaluate/culturalKnowledge/homeworkRecord',
            hidden: true
          },
          {
            name: '学期总评数据',
            path: '/tenant/teacherEvaluate/culturalKnowledge/overallEvaluationRecord',
            hidden: true
          },
          {
            name: '班级作业',
            path: '/tenant/teacherEvaluate/homeworkEvaluate/record',
            hidden: true
          }
        ]
      },
      {
        name: '老师的话',
        path: '/tenant/teacherEvaluate/teacherSay',
        code: 'teacher_say',
        activePrefixes: ['/tenant/teacherEvaluate/teacherSay'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/teacherEvaluate/teacherSay/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/teacherEvaluate/teacherSay/record',
            hidden: true
          }
        ]
      }
    ]
  },
  {
    name: '老师评价',
    code: 'teacher_evaluate',
    activeIcon: 'evaluateTeacherActive',
    inactiveIcon: 'evaluateTeacherInactive',
    children: [
      {
        name: '师德和专业考核评价',
        path: '/tenant/teacherEvaluate/professionalAssessmentEvaluation',
        code: 'professional_assessment',
        activePrefixes: ['/tenant/teacherEvaluate/professionalAssessmentEvaluation']
      },
      {
        name: '师德和专业评价统计',
        path: '/tenant/teacherEvaluate/professionalAssessmentStats',
        code: 'professional_assessment_stats',
        activePrefixes: ['/tenant/teacherEvaluate/professionalAssessmentStats'],
        children: [
          {
            name: '查看详情',
            path: '/tenant/teacherEvaluate/professionalAssessmentStats/detail',
            hidden: true
          }
        ]
      }
    ]
  },
  {
    name: '学校评价',
    code: 'school_evaluate',
    activeIcon: 'evaluateTeacherActive',
    inactiveIcon: 'evaluateTeacherInactive',
    children: [
      {
        name: '本校培养目标	',
        path: '/tenant/schoolEvaluate/achievement',
        code: 'school_achievement',
        activePrefixes: ['/tenant/schoolEvaluate/achievement'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/schoolEvaluate/achievement/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/schoolEvaluate/achievement/record',
            hidden: true
          }
        ]
      },
      {
        name: '学习适应性',
        path: '/tenant/schoolEvaluate/learnAdaptability',
        code: 'school_learn_adaptability',
        activePrefixes: ['/tenant/schoolEvaluate/learnAdaptability'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/schoolEvaluate/learnAdaptability/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/schoolEvaluate/learnAdaptability/record',
            hidden: true
          }
        ]
      },
      {
        name: '文化知识',
        path: '/tenant/schoolEvaluate/culturalKnowledge',
        code: 'school_cultural_knowledge',
        activePrefixes: ['/tenant/schoolEvaluate/culturalKnowledge'],
        children: [
          {
            name: '班级详情',
            path: '/tenant/schoolEvaluate/culturalKnowledge/detail',
            hidden: true
          },
          {
            name: '评价记录',
            path: '/tenant/schoolEvaluate/culturalKnowledge/record',
            hidden: true
          },
          {
            name: '课堂表现评价记录',
            path: '/tenant/schoolEvaluate/culturalKnowledge/classroomPerformanceRecord',
            hidden: true
          }
        ]
      }
    ]
  },
  // {
  //   name: '数智看板',
  //   code: '',
  //   children: [
  //     {
  //       name: '综合看板',
  //       path: '/tenant/digitalKanban/comprehensive',
  //       code: ''
  //     },
  //     {
  //       name: '学生看板',
  //       path: '/tenant/digitalKanban/student',
  //       code: ''
  //     },
  //   ]
  // },
  {
    name: '评价数据',
    activeIcon: 'evaluateDataActive',
    inactiveIcon: 'evaluateDataInactive',
    path: '/tenant/evaluateData',
    code: 'evaluate_data'
  },
  {
    name: '基础信息',
    code: 'basic_info',
    activeIcon: 'evaluateBasicInformationActive',
    inactiveIcon: 'evaluateBasicInformationInactive',
    children: [
      // {
      //   name: '教职工管理',
      //   path: '/tenant/basicInfo/facultyManagement',
      //   code: 'faculty_management'
      // },
      {
        name: '学生管理',
        path: '/tenant/basicInfo/studentManage',
        code: 'student_manage'
      },
      {
        name: '教学管理',
        path: '/tenant/basicInfo/teachManage',
        code: 'teach_manage'
      },
      {
        name: '学期配置',
        path: '/tenant/basicInfo/termSetting',
        code: 'term_setting'
      }
    ]
  }
];
