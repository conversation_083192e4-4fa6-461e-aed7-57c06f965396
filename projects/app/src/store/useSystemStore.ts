import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { isMobile, isPhone, isTablet, mobileBreakpoint } from '@/utils/mobile';
import { getSystemFastData } from '@/api/system';

import { FastGPTDataType } from '@/types/api/system';
import {
  ClientSemesterPageType,
  SemesterListOptions
} from '@/types/api/tenant/teamManagement/semester';
import { getClientSemesterListAndChild } from '@/api/tenant/teamManagement/semester';
import { DimensionReflectionVO } from '@/types/api/tenant/evaluate/rule';
import { getEntranceTree } from '@/api/tenant/evaluate/rule';

type State = {
  lastRoute: string;
  setLastRoute: (e: string) => void;
  loading: boolean;
  setLoading: (val: boolean) => null;
  screenWidth: number;
  setScreenWidth: (val: number) => void;
  isPc?: boolean;
  initIsPc(val: boolean): void;

  isMobile?: boolean;
  isPhone?: boolean;
  isTablet?: boolean;
  entryTree: DimensionReflectionVO[];
  setEntryTree: () => void;
  // feConfigs: FastGPTFeConfigsType;
  systemVersion: string;
  // llmModelList: LLMModelItemType[];
  // datasetModelList: LLMModelItemType[];
  // vectorModelList: VectorModelItemType[];
  // audioSpeechModelList: AudioSpeechModelType[];
  // reRankModelList: ReRankModelItemType[];
  // whisperModel?: WhisperModelType;
  // initStaticData: (e: InitDateResponse) => void;

  fastGPTData?: FastGPTDataType;
  getFastGPTData: (reload?: boolean) => Promise<FastGPTDataType>;

  semesterListTermChild: SemesterListOptions[];
  setSemesterListTermChild: (list: SemesterListOptions[]) => void;
  semesterListTermLevel: ClientSemesterPageType[];
  setSemesterListTermLevel: (list: ClientSemesterPageType[]) => void;
  fetchSemesterList: (
    init?: boolean,
    data?: any
  ) => Promise<{
    semesterListTermChild: SemesterListOptions[];
    semesterListTermLevel: ClientSemesterPageType[];
  }>;

  currentSemester?: ClientSemesterPageType;
};

export const useSystemStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        lastRoute: '/',
        setLastRoute(e) {
          set((state) => {
            state.lastRoute = e;
          });
        },
        loading: false,
        setLoading: (val: boolean) => {
          set((state) => {
            state.loading = val;
          });
          return null;
        },
        screenWidth: 600,
        setScreenWidth(val: number) {
          set((state) => {
            state.screenWidth = val;
            state.isPc = !isMobile && val >= mobileBreakpoint;
          });
        },
        isPc: undefined,
        initIsPc(val: boolean) {
          if (get().isPc !== undefined) return;

          set((state) => {
            state.isPc = !isMobile && val;
          });
        },

        isMobile,
        isPhone,
        isTablet,

        feConfigs: {},
        systemVersion: '0.0.0',

        llmModelList: [],
        datasetModelList: [],
        vectorModelList: [],
        audioSpeechModelList: [],
        reRankModelList: [],
        whisperModel: undefined,
        initStaticData(res: any) {
          set((state) => {
            // state.feConfigs = res.feConfigs || {};
            state.systemVersion = res.systemVersion;
            // state.llmModelList = res.llmModels ?? state.llmModelList;
            // state.datasetModelList = state.llmModelList.filter((item) => item.datasetProcess);
            // state.vectorModelList = res.vectorModels ?? state.vectorModelList;
            // state.audioSpeechModelList = res.audioSpeechModels ?? state.audioSpeechModelList;
            // state.reRankModelList = res.reRankModels ?? state.reRankModelList;
            // state.whisperModel = res.whisperModel;
          });
        },

        fastGPTData: undefined,
        getFastGPTData: async (reload) => {
          if (!reload && get().fastGPTData) {
            return get().fastGPTData!;
          }
          const data = await getSystemFastData();
          set((state) => {
            state.fastGPTData = data;
          });
          return data;
        },

        semesterListTermChild: [],
        setSemesterListTermChild: (list: SemesterListOptions[]) => {
          set((state) => {
            state.semesterListTermChild = list;
          });
        },
        semesterListTermLevel: [],
        setSemesterListTermLevel: (list: ClientSemesterPageType[]) => {
          set((state) => {
            state.semesterListTermLevel = list;
          });
        },

        fetchSemesterList: async (init = true, data?: any) => {
          if (get().semesterListTermChild.length > 0 && !init) {
            return {
              semesterListTermChild: get().semesterListTermChild,
              semesterListTermLevel: get().semesterListTermLevel
            };
          }
          const result = await getClientSemesterListAndChild(data);
          set((state) => {
            state.semesterListTermChild = result.semesterListTermChild;
            state.semesterListTermLevel = result.semesterListTermLevel;
            state.currentSemester = result.semesterListTermLevel.find(
              (item) => item.isCurrent == 1
            );
          });
          return result;
        },
        entryTree: [],
        setEntryTree: async () => {
          if (get().entryTree.length > 0) {
            return get().entryTree;
          }

          const result = await getEntranceTree({});

          set((state) => {
            state.entryTree = result;
          });
        }
      })),
      {
        name: 'systemStore',
        partialize: () => ({})
      }
    )
  )
);
