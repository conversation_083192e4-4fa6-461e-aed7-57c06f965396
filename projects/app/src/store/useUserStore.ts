import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { UserInfoType } from '@/types/store/useUserStore';
import { UpdateUserProps } from '@/types/api/user';
import { getTokenLogin } from '@/api/auth';
import { updateUser } from '@/api/user';
import { setToken } from '@/utils/auth';

type State = {
  userInfo: UserInfoType | null;
  teacherType: any;
  initUserInfo: () => Promise<UserInfoType>;
  setUserInfo: (user: UserInfoType | null) => void;
  updateUserInfo: (user: UpdateUserProps & { avatarUrl?: string }) => Promise<void>;
  setTeacherType: (type: number) => void;
};

export const useUserStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        userInfo: null,
        teacherType: '',
        async initUserInfo() {
          const res = await getTokenLogin();
          setToken(res.accessToken);
          set((state) => {
            state.userInfo = res;
          });
          return res;
        },
        setUserInfo(user: UserInfoType | null) {
          set((state) => {
            state.userInfo = user;
          });
        },
        async updateUserInfo(user) {
          const oldInfo = (get().userInfo ? { ...get().userInfo } : null) as UserInfoType | null;
          set((state) => {
            if (!state.userInfo) return;
            state.userInfo = {
              ...state.userInfo,
              ...user,
              ...(user.avatarUrl && { avatar: user.avatarUrl })
            };
          });
          try {
            await updateUser(user);
          } catch (error) {
            set((state) => {
              state.userInfo = oldInfo;
            });
            return Promise.reject(error);
          }
        },
        setTeacherType(type: number) {
          set((state) => {
            state.teacherType = type;
          });
        }
      })),
      {
        name: 'userStore',
        partialize: (state) => ({})
      }
    )
  )
);
