export enum TenantIndustryEnum {
  Education = 1, // 教育
  Finance = 2, // 金融
  Internet = 3, // 互联网
  Medical = 4, // 医疗
  Government = 5, // 政府
  Other = 6 // 其他
}

export const TenantIndustryMap = {
  [TenantIndustryEnum.Education]: {
    value: TenantIndustryEnum.Education,
    label: '教育'
  },
  [TenantIndustryEnum.Finance]: {
    value: TenantIndustryEnum.Finance,
    label: '金融'
  },
  [TenantIndustryEnum.Internet]: {
    value: TenantIndustryEnum.Internet,
    label: '互联网'
  },
  [TenantIndustryEnum.Medical]: {
    value: TenantIndustryEnum.Medical,
    label: '医疗'
  },
  [TenantIndustryEnum.Government]: {
    value: TenantIndustryEnum.Government,
    label: '政府'
  },
  [TenantIndustryEnum.Other]: {
    value: TenantIndustryEnum.Other,
    label: '其他'
  }
};

export enum TenantMemberRoleEnum {
  Admin = 3,
  Member = 4
}

export const TenantMemberRoleMap = {
  [TenantMemberRoleEnum.Admin]: {
    value: TenantMemberRoleEnum.Admin,
    label: '设置为管理员'
  },
  [TenantMemberRoleEnum.Member]: {
    value: TenantMemberRoleEnum.Member,
    label: '设置为普通成员'
  }
};

export enum TenantMemberStatusEnum {
  Active = 1,
  Forbidden = 2
}

export const TenantMemberStatusMap = {
  [TenantMemberStatusEnum.Active]: {
    value: TenantMemberStatusEnum.Active,
    label: '启用'
  },
  [TenantMemberStatusEnum.Forbidden]: {
    value: TenantMemberStatusEnum.Forbidden,
    label: '禁用'
  }
};

export enum TenantSearchTypeEnum {
  Name = 'name',
  CustomerName = 'customerName',
  ContactName = 'contactName',
  ContactPhone = 'contactPhone'
}

export enum TenantMemberSearchTypeEnum {
  Fullname = 'fullname',
  Username = 'username',
  Email = 'email',
  Phone = 'phone'
}
