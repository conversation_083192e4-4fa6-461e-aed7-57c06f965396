export enum SexEnum {
  Male = 1,
  Female = 2,
  Unknown = 0
}

export enum IndactorTypeEnum {
  Good = 'good',
  Bad = 'bad'
}

export const IndactorTypeNameMap = {
  [IndactorTypeEnum.Good]: '表扬',
  [IndactorTypeEnum.Bad]: '待改进'
};

export enum EvaluatePermissionEnum {
  Look = 1,
  Evaluate = 2
}

export const EvaluatePermissionType = {
  [EvaluatePermissionEnum.Look]: '可查看',
  [EvaluatePermissionEnum.Evaluate]: '可评价'
};

export const EvaluatePermissionTypeMap = {
  [EvaluatePermissionEnum.Look]: '查看',
  [EvaluatePermissionEnum.Evaluate]: '评价'
};
