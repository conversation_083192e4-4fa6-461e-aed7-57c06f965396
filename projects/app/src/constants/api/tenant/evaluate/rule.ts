export enum EvaluateeType {
  TeachingAdministrator = 1,
  PrimarySchoolManagementTeacher = 2,
  MiddleSchoolManagementTeacher = 3,
  GradeDirector = 4,
  ClassTeacher = 5,
  SubjectGroupLeader = 6,
  SubjectTeacher = 7,
  Student = 8
}

// TEACHING_ADMINISTRATOR(1, "教学管理员"),
// PRIMARY_SCHOOL_MANAGEMENT_TEACHER(2, "小学段管理教师"),
// MIDDLE_SCHOOL_MANAGEMENT_TEACHER(3, "初中段管理教师"),
// GRADE_DIRECTOR(4, "年级主任"),
// CLASS_TEACHER(5, "班主任"),
// SUBJECT_GROUP_LEADER(6, "学科组长"),
// SUBJECT_TEACHER(7, "学科教师"),
// STUDENT(8, "学生");

export const EvaluateeTypeMap = {
  [EvaluateeType.TeachingAdministrator]: {
    value: EvaluateeType.TeachingAdministrator,
    label: '教学管理员'
  },
  [EvaluateeType.PrimarySchoolManagementTeacher]: {
    value: EvaluateeType.PrimarySchoolManagementTeacher,
    label: '小学段管理教师'
  },
  [EvaluateeType.MiddleSchoolManagementTeacher]: {
    value: EvaluateeType.MiddleSchoolManagementTeacher,
    label: '初中段管理教师'
  },
  [EvaluateeType.GradeDirector]: {
    value: EvaluateeType.GradeDirector,
    label: '年级主任'
  },
  [EvaluateeType.ClassTeacher]: {
    value: EvaluateeType.ClassTeacher,
    label: '班主任'
  },
  [EvaluateeType.SubjectGroupLeader]: {
    value: EvaluateeType.SubjectGroupLeader,
    label: '学科组长'
  },
  [EvaluateeType.SubjectTeacher]: {
    value: EvaluateeType.SubjectTeacher,
    label: '教师'
  },
  [EvaluateeType.Student]: {
    value: EvaluateeType.Student,
    label: '学生'
  }
};
// 评价对象匹配方式枚举
export enum MatchType {
  AutoMatch = 1,
  CustomMatch = 2
}

export enum DimenTypeStatus {
  Normal = 1,
  Disabled = 2
}

// 评价周期枚举
export enum PeriodType {
  Instant = 1,
  Daily = 2,
  Weekly = 3,
  Biweekly = 4,
  Monthly = 5,
  Semester = 6
}

// 规则状态枚举
export enum RuleStatus {
  NotStarted = 0,
  InProgress = 1,
  Finished = 2
}

// 学期枚举
export enum Term {
  FirstSemester = 1,
  SecondSemester = 2
}

// 评价方式枚举
export enum EvaluationType {
  Score = 1,
  ScoreLevelValue = 2,
  Comment = 3,
  ProfessionalAssessment = 8
}

// 是否存在下级指标枚举
export enum HasSubIndicator {
  No = 0,
  Yes = 1
}

// 评语是否需要签名枚举
export enum NeedSignature {
  No = 0,
  Yes = 1
}

// 是否使用图标枚举
export enum UseLogo {
  No = 0,
  Yes = 1
}

// 分值录入方式枚举
export enum ScoreInputType {
  Input = 1,
  Fixed = 2
}

// 项目类型枚举
export enum ProjectType {
  NonSubject = 1,
  Subject = 2
}

// 评价学段枚举
export enum EducationStage {
  PrimarySchool = 2,
  JuniorHighSchool = 3
}

// 指标类型枚举
export enum IndicatorType {
  EvaluationDimension = 1,
  EvaluationProject = 2,
  EvaluationIndicator = 3
}

// 状态枚举
export enum Status {
  Normal = 1,
  Disabled = 2
}

// 评价人类型枚举 (在评价规则评价人出参中)
export enum EvaluatorTypeInRule {
  Individual = 1,
  Class = 2
}

export enum ScoreLevelStatus {
  Disabled = 0,
  Normal = 1
}

export enum BizType {
  ClassroomEvaluation = 1,
  JobEvaluation = 2
}

export const ScoreLevelStatusMap = {
  [ScoreLevelStatus.Disabled]: {
    value: ScoreLevelStatus.Disabled,
    label: '禁用'
  },
  [ScoreLevelStatus.Normal]: {
    value: ScoreLevelStatus.Normal,
    label: '正常'
  }
};

// 评价方式映射
export const EvaluateTypeMap = {
  [EvaluationType.Score]: {
    value: EvaluationType.Score,
    label: '评分'
  },
  [EvaluationType.ScoreLevelValue]: {
    value: EvaluationType.ScoreLevelValue,
    label: '评等级'
  },
  [EvaluationType.Comment]: {
    value: EvaluationType.Comment,
    label: '评语'
  }
};

// 是否存在下级指标映射
export const HasSubIndicatorMap = {
  [HasSubIndicator.No]: {
    value: HasSubIndicator.No,
    label: '否'
  },
  [HasSubIndicator.Yes]: {
    value: HasSubIndicator.Yes,
    label: '是'
  }
};

// 评语是否需要签名映射
export const NeedSignatureMap = {
  [NeedSignature.No]: {
    value: NeedSignature.No,
    label: '否'
  },
  [NeedSignature.Yes]: {
    value: NeedSignature.Yes,
    label: '是'
  }
};

// 是否使用图标映射
export const UseLogoMap = {
  [UseLogo.No]: {
    value: UseLogo.No,
    label: '否'
  },
  [UseLogo.Yes]: {
    value: UseLogo.Yes,
    label: '是'
  }
};

// 分值录入方式映射
export const ScoreInputTypeMap = {
  [ScoreInputType.Input]: {
    value: ScoreInputType.Input,
    label: '输入'
  },
  [ScoreInputType.Fixed]: {
    value: ScoreInputType.Fixed,
    label: '固定值'
  }
};

// 项目类型映射
export const ProjectTypeMap = {
  [ProjectType.NonSubject]: {
    value: ProjectType.NonSubject,
    label: '非学科'
  },
  [ProjectType.Subject]: {
    value: ProjectType.Subject,
    label: '学科'
  }
};

// 评价学段映射
export const EducationStageMap = {
  [EducationStage.PrimarySchool]: {
    value: EducationStage.PrimarySchool,
    label: '小学'
  },
  [EducationStage.JuniorHighSchool]: {
    value: EducationStage.JuniorHighSchool,
    label: '初中'
  }
};

// 指标类型映射
export const IndicatorTypeMap = {
  [IndicatorType.EvaluationDimension]: {
    value: IndicatorType.EvaluationDimension,
    label: '评价维度'
  },
  [IndicatorType.EvaluationProject]: {
    value: IndicatorType.EvaluationProject,
    label: '评价项目'
  },
  [IndicatorType.EvaluationIndicator]: {
    value: IndicatorType.EvaluationIndicator,
    label: '评价指标'
  }
};

// 状态映射
export const StatusMap = {
  [Status.Normal]: {
    value: Status.Normal,
    label: '正常'
  },
  [Status.Disabled]: {
    value: Status.Disabled,
    label: '禁用'
  }
};

// 评价人类型映射 (在评价规则评价人出参中)
export const EvaluatorTypeInRuleMap = {
  [EvaluatorTypeInRule.Individual]: {
    value: EvaluatorTypeInRule.Individual,
    label: '人员'
  },
  [EvaluatorTypeInRule.Class]: {
    value: EvaluatorTypeInRule.Class,
    label: '班级'
  }
};

// 评价对象匹配方式映射
export const MatchTypeMap = {
  [MatchType.AutoMatch]: {
    value: MatchType.AutoMatch,
    label: '自动匹配'
  },
  [MatchType.CustomMatch]: {
    value: MatchType.CustomMatch,
    label: '自定义'
  }
};

// 评价周期映射
export const PeriodTypeMap = {
  [PeriodType.Instant]: {
    value: PeriodType.Instant,
    label: '即时'
  },
  [PeriodType.Daily]: {
    value: PeriodType.Daily,
    label: '每天'
  },
  [PeriodType.Weekly]: {
    value: PeriodType.Weekly,
    label: '每周'
  },
  [PeriodType.Biweekly]: {
    value: PeriodType.Biweekly,
    label: '每两周'
  },
  [PeriodType.Monthly]: {
    value: PeriodType.Monthly,
    label: '每月'
  },
  [PeriodType.Semester]: {
    value: PeriodType.Semester,
    label: '每学期'
  }
};

// 规则状态映射
export const RuleStatusMap = {
  [RuleStatus.NotStarted]: {
    value: RuleStatus.NotStarted,
    label: '未开始'
  },
  [RuleStatus.InProgress]: {
    value: RuleStatus.InProgress,
    label: '进行中'
  },
  [RuleStatus.Finished]: {
    value: RuleStatus.Finished,
    label: '已结束'
  }
};

// 学期映射
export const TermMap = {
  [Term.FirstSemester]: {
    value: Term.FirstSemester,
    label: '第一学期'
  },
  [Term.SecondSemester]: {
    value: Term.SecondSemester,
    label: '第二学期'
  }
};

// 状态映射
export const DimenTypeStatusMap = {
  [DimenTypeStatus.Normal]: {
    value: DimenTypeStatus.Normal,
    label: '正常'
  },
  [DimenTypeStatus.Disabled]: {
    value: DimenTypeStatus.Disabled,
    label: '禁用'
  }
};

// 评分项目类型枚举
export enum EvaluateLevelType {
  Score = 1,
  Text = 2
}

// 是否提示枚举
export enum IsHint {
  No = 0,
  Yes = 1
}

export enum EvaluateLevelStatus {
  Normal = 1,
  Disabled = 0
}

export const EvaluateLevelTypeMap = {
  [EvaluateLevelType.Score]: {
    value: EvaluateLevelType.Score,
    label: '分值设置'
  },
  [EvaluateLevelType.Text]: {
    value: EvaluateLevelType.Text,
    label: '文本设置'
  }
};

export enum EvaluatedWayEnum {
  SameClass = 1,
  SameSubjectGroup = 2
}

export const EvaluatedWayEnumMap = {
  [EvaluatedWayEnum.SameClass]: {
    value: EvaluatedWayEnum.SameClass,
    label: '同班级教师互评'
  },
  [EvaluatedWayEnum.SameSubjectGroup]: {
    value: EvaluatedWayEnum.SameSubjectGroup,
    label: '同学科组教师互评'
  }
};
