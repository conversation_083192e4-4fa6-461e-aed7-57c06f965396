// ... existing code ...
// ... existing code ...

import { EvaluationType } from './api/tenant/evaluate/rule';

export enum EvaluateEntryEnum {
  GoalAchievement = 1, // 培养目标达成度
  AdaptiveLearning, // 自适应学习
  ClassroomPerformance, // 课堂表现
  HomeworkEvaluation, // 作业评价
  SemesterReview, // 学期总评
  TeacherComments, // 老师的话
  ProfessionalAssessment // 师德和专业考核评价
}

export const evaluateEntryMapEvaluateType = {
  [EvaluateEntryEnum.GoalAchievement]: EvaluationType.ScoreLevelValue,
  [EvaluateEntryEnum.AdaptiveLearning]: EvaluationType.ScoreLevelValue,
  [EvaluateEntryEnum.ClassroomPerformance]: EvaluationType.Score,
  [EvaluateEntryEnum.HomeworkEvaluation]: EvaluationType.ScoreLevelValue,
  [EvaluateEntryEnum.SemesterReview]: EvaluationType.ScoreLevelValue,
  [EvaluateEntryEnum.TeacherComments]: EvaluationType.Comment,
  [EvaluateEntryEnum.ProfessionalAssessment]: EvaluationType.ProfessionalAssessment
};

export const evaluateEntryMapEvaluateInfoType = {
  [EvaluateEntryEnum.GoalAchievement]: {
    value: EvaluateEntryEnum.GoalAchievement,
    name: '培养目标达成度'
  },
  [EvaluateEntryEnum.AdaptiveLearning]: {
    value: EvaluateEntryEnum.AdaptiveLearning,
    name: '学习适应性'
  },
  [EvaluateEntryEnum.ClassroomPerformance]: {
    value: EvaluateEntryEnum.ClassroomPerformance,
    name: '课堂表现'
  },
  [EvaluateEntryEnum.HomeworkEvaluation]: {
    value: EvaluateEntryEnum.HomeworkEvaluation,
    name: '作业评价'
  },
  [EvaluateEntryEnum.SemesterReview]: {
    value: EvaluateEntryEnum.SemesterReview,
    name: '学期总评'
  },
  [EvaluateEntryEnum.TeacherComments]: {
    value: EvaluateEntryEnum.TeacherComments,
    name: '老师的话'
  },
  [EvaluateEntryEnum.ProfessionalAssessment]: {
    value: EvaluateEntryEnum.ProfessionalAssessment,
    name: '师德和专业考核评价'
  }
};
