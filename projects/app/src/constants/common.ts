export const LOGO_ICON = '/icon/logo.svg';

export const APP_ICON = '/imgs/v2/ai_avatar.svg';

export const DATASET_ICON = '/imgs/v2/dataset_avatar.svg';

export const HUGGING_FACE_ICON = `/imgs/model/huggingface.svg`;

export const AI_POINT_USAGE_CARD_ROUTE = '/price#point-card';

import { SvgIconNameType } from '@/components/SvgIcon/data';

export enum TrackEventName {
  WindowError = 'windowError',
  PageError = 'pageError',
  WordReadError = 'wordReadError'
}

export enum DataSource {
  Tenant = 1,
  Offical = 2,
  Personal = 3
}

export const DataSourceMap: Record<
  DataSource,
  {
    value: DataSource;
    label: string;
    description: string;
    tagColor: string;
    tagBgColor: string;
    icon?: SvgIconNameType;
  }
> = {
  [DataSource.Tenant]: {
    value: DataSource.Tenant,
    label: '专属',
    description: '来源 1，专属',
    tagColor: '#F7BA1E',
    tagBgColor: '#FFFCE8',
    icon: 'appExclusive'
  },
  [DataSource.Offical]: {
    value: DataSource.Offical,
    label: '官方',
    description: '来源 2，官方',
    tagColor: 'primary.500',
    tagBgColor: '#E8F3FF',
    icon: 'appAuthority'
  },
  [DataSource.Personal]: {
    value: DataSource.Personal,
    label: '个人',
    description: '来源 3，个人',
    tagColor: 'primary.500',
    tagBgColor: '#E8F3FF'
  }
};
