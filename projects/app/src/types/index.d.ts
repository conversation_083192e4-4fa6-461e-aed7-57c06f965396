import {
  AudioSpeechModelType,
  ChatModelItemType,
  FunctionModelItemType,
  LLMModelItemType,
  ReRankModelItemType,
  VectorModelItemType,
  WhisperModelType
} from '@fastgpt/global/core/ai/model.d';
import { TrackEventName } from '@/constants/common';
import { FastGPTFeConfigsType, SystemEnvType } from '@fastgpt/global/common/system/types';
import { SubPlanType } from '@fastgpt/global/support/wallet/sub/type';
import { ErrorStatistics } from './api/errorMessage';

export type PagingData<T> = {
  current?: number;
  size?: number;
  records: T[];
  pages: number;
  total?: number;
};

export type RequestPaging = { current: number; size: number; [key]: any };

export type RequestPageParams = { current?: number; size?: number };

export type ParentTreePathItemType = {
  parentId: string;
  parentName: string;
  finalParentId: string;
};

declare global {
  var feConfigs: FastGPTFeConfigsType;
  var systemEnv: SystemEnvType;
  var systemInitd: boolean;
  var subPlans: SubPlanType | undefined;
  var errorMessageList: ErrorStatistics[];
  var qaQueueLen: number;
  var vectorQueueLen: number;

  var llmModels: LLMModelItemType[];
  var vectorModels: VectorModelItemType[];
  var audioSpeechModels: AudioSpeechModelType[];
  var whisperModel: WhisperModelType;
  var reRankModels: ReRankModelItemType[];

  var systemVersion: string;

  interface Window {
    grecaptcha: any;
    QRCode: any;
    umami?: {
      track: (event: `${TrackEventName}`, data: any) => void;
    };
  }
}

export type SizeType = {
  width: number;
  height: number;
};

export type PaginationProps<T = {}> = T & {
  current: number;
  pageSize: number;
};
export type PaginationResponse<T = any> = {
  total: number;
  list: T[];
};
