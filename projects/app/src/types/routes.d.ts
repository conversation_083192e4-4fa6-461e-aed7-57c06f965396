import { SvgIconNameType } from '@/components/SvgIcon/data';
import { RouteGroupTypeEnum } from '@/constants/routes';

interface RouteBaseType {
  // 显示到界面中的名称
  name: string;
  // 页面路径
  path?: string;
  // 权限代码，如果不设置则不需要权限
  code?: string;
  // 图标
  icon?: SvgIconNameType;
  // 页面激活时使用的图标
  activeIcon?: SvgIconNameType;
  // 页面未激活时使用的图标
  inactiveIcon?: SvgIconNameType;
  // 激活时使用的路径前缀
  activePrefixes?: string[];
  // 激活时使用的路径正则
  activePattern?: RegExp;
  // 是否激活
  active?: boolean;
  // 是否隐藏
  hidden?: boolean;
  // 设置不需要授权，即设置了code
  unauth?: boolean;

  iframeSrc?: string;
}

export interface RouteConfigType extends RouteBaseType {
  children?: RouteConfigType[];
}

export interface RouteType extends RouteBaseType {
  key: string;
  id?: string;
  parent?: RouteType;
  children?: RouteType[];
  [key: string]: any;
}

export interface RouteGroupConfigType {
  type: RouteGroupTypeEnum;
  routes: RouteType[];
}

export type RouteMapType = Record<RouteType['key'], RouteType>;

export interface RouteGroupType {
  type: RouteGroupTypeEnum;

  // 所有路由
  routes: RouteType[];
  routeMap: RouteMapType;
  activeRoute?: RouteType;

  // 已授权路由
  authRoutes: RouteType[];
  authRouteMap: RouteMapType;
  authActiveRoute?: RouteType;

  // 用于菜单导航等
  navRoutes: RouteType[];
  navRouteMap: RouteMapType;
  navActiveRoute?: RouteType;
}

export type RouteGroupMapType = Record<RouteGroupTypeEnum, RouteGroupType>;

export type RouteUpdateFieldsType = Pick<RouteType, 'name' | 'icon' | 'activeIcon' | 'id'>;
