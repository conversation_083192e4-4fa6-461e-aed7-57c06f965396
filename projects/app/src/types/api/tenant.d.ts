import { TenantMemberStatusEnum } from '@/constants/api/tenant';
import { RequestPageParams } from '..';

// 定义 Dept 类型
export interface Dept {
  id: string; // 节点的唯一标识符
  parentId: string; // 父节点的标识符
  hasChildren: boolean; // 是否有子节点
  name: string; // 节点名称
  tenantId: string; // 租户ID
  children?: Dept[]; // 子节点数组
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
  isDeleted?: number; // 是否删除
  sort?: number; // 排序
  parentName?: string; // 父节点名称
}

// 定义 TreeNode 类型
export interface TreeNode {
  title: string;
  key: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  sort: number;
  id: string;
  name: string;
  code: string;
  action: number;
  tenantId: string;
  parentId: string;
  children?: TreeNode[];
}

export type TenantType = {
  avatar: string;
  avatarUrl: string;
  backgroundImg: string;
  backgroundImgUrl: string;
  contactId: number;
  contactName: string;
  createTime: string;
  customerName: string;
  dingAgentId: string;
  dingAgentSecret: string;
  domain: string;
  engName: string;
  fullName: string;
  fullNameImg: string;
  fullNameImgUrl: string;
  id: string;
  industry: number;
  isDeleted: number;
  name: string;
  qywxAgentId: string;
  qywxAgentSecret: string;
  qywxAppId: string;
  sidebarImg: string;
  sidebarImgUrl: string;
  sort: number;
  updateTime: string;
  isWxTenant: string;
};

export type GetTenantUserPageParams = {
  current: number; // 当前页码
  deptId?: string; // 部门ID
  searchKey: string; // 搜索关键字
  searchType: string; // 搜索类型
  size: number; // 每页大小
  status: string; // 状态
};

export type UpdateTenantDetailParams = {
  id: string;
  name: string;
  industry: number;
  domain: string;
  fullName?: string;
  avatar?: string;
  avatarUrl?: string;
  fullNameImg?: string;
  fullNameImgUrl?: string;
  backgroundImg?: string;
  backgroundImgUrl?: string;
  sidebarImg?: string;
  sidebarImgUrl?: string;
};

export type DeptListType = Dept[];

export type RoleListTypeItem = {
  createTime: string;
  source: number;
  id: string;
  info: string;
  isDeleted: number;
  name: string;
  type: number;
  updateTime: string;
};

export type RoleListType = RoleListTypeItem[];

export type SortDeptParams = {
  param: Dept[];
};

export type CreateDeptParams = {
  name: string; // 节点的唯一标识符
  parentId: string; // 父节点的标识符
  id?: string;
};

export type UpdateDeptParams = {
  name: string; // 节点的唯一标识符
  parentId: string; // 父节点的标识符
  id?: string;
};

export type CreateTenantUserParams = {
  avatar: string;
  avatarFile: {
    id: string;
    createTime: string;
    updateTime: string;
    isDeleted: number;
    fileName: string;
    fileUrl: string;
    fileKey: string;
    fileSize: number;
    fileJson: string;
    fileType: string;
  };
  avatarUrl: string; // 租户头像
  deptIds: string[];
  password: string;
  phone: string;
  roleId: string;
  tenantId: number;
  username: string;
  id?: string;
};

export type UpdateTenantUserParams = Partial<Omit<CreateTenantUserParams, 'id'>> & { id: string };

export type DetailTenantUserParams = Partial<Omit<CreateTenantUserParams>>;

export type UpdateStatusTenantUserParams = {
  id: string;
  status: number;
};

export type TransferResourcesParams = {
  id: string;
  targetPhone: string;
};

export type TenantItemType = {
  id: string; // 租户ID
  status: number;
  roleId: string;
  name: string; // 租户名称
  domain: string; // 租户域名
  customerName: string; // 商务
  avatar: string; // 租户头像
  avatarUrl: string; // 租户头像
  fullName?: string; // 全称
  fullNameImg?: string; // 全称图片
  fullNameImgUrl?: string; // 全称图片
  backgroundImg?: string; // 背景图片
  backgroundImgUrl?: string; // 背景图片
  engName?: string; // 英文名称
  qywxAppId?: string;
  qywxAgentId?: string;
  qywxAgentSecret?: string;
  dingAgentId?: string;
  dingAgentSecret?: string;
  contactName: string; // 联系人名称 新增
  contactPhone: string; // 联系人电话 新增
  industry: string; // 所属行业
  createTime: string; // 创建时间
};

export type GetTenantUserPageProps = RequestPageParams;

export type TenantUserType = {
  id: string;
  username: string;
  phone: string;
  roleName: string;
  deptName: string;
  status: TenantMemberStatusEnum;
};

export type IndustryListType = [
  {
    id: string;
    createTime: string;
    updateTime: string;
    isDeleted: number;
    name: string;
    code: number;
  }
];

export type ReplaceDeptParams = {
  replaceId: string;
  id: string;
};

export type CreateRoleParams = {
  id?: string;
  name: string;
  info: string;
  type: number;
};

export type CreateRoleType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: boolean | null;
  name: string;
  info: string;
  type: number;
  source: number;
};

export type RoleCountUserType = {
  data: number;
};

export type GetTenantUserPageParams = {
  current?: number; // 当前页码
  deptId?: string; // 部门ID
  tenantId?: string; // 租户ID
  searchKey?: string; // 搜索关键字
  searchType?: string; // 搜索类型
  size?: number; // 每页大小
  status?: string; // 状态
  keyword?: string; // 关键字
  deptSource?: {
    name?: string;
    value?: string;
    icon?: SvgIconNameType;
  };
};
export type CreateRoleMenuParams = {
  menuIds: string;
  roleId: string;
};

export type DetailRoleType = {
  createTime: string;
  id: string;
  info: string;
  isDeleted: number;
  menuIds: string;
  name: string;
  source: number;
  type: number;
  updateTime: string;
};

export type RoleUpdateUserParams = {
  oldRoleId: string;
  roleId: string;
};

export type RoleListTypeItem = {
  createTime: string;
  id: string;
  info: string;
  isDeleted: number;
  name: string;
  type: number;
  updateTime: string;
};

export type MenuListTypeItem = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  name: string;
  parentId: string;
  sort: number;
  code: string;
  action: number;
  children?: TreeNode[];
  [key: string]: any; // 允许使用字符串索引
};

export type MenuListType = MenuListTypeItem[];
