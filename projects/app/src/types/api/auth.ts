import { UserRoleEnum } from '@/constants/api/auth';

export type PostLoginProps = {
  account: string;
  password: string;
  ticket?: string;
  moveLength?: number;
};

type AppInfo = {
  appId: string;
  avatarUrl: string;
  config: number;
  createTime: string;
  createUsername: string;
  edgesJson: string;
  finalAppId: string;
  id: string;
  intro: string;
  isDefault: number;
  isDeleted: number;
  isStructuredPrompt: number;
  mode: number;
  modulesJson: string;
  name: string;
  permission: number;
  sort: number;
  sortNum: number;
  source: number;
  status: number;
  tenantId: string;
  tmbId: string;
  type: string;
  updateTime: string;
  updateUsername: string;
};

export type LoginRes = {
  accessToken: string;
  tokenType: string;
  expiresTime: number;
  userId: string;
  username: string;
  account: string;
  phone: string;
  avatar: string;
  roleId: string;
  roleName: string;
  roleType: `${UserRoleEnum}`;
  tenantId: string;
  tmbId: string;
  menuCodes: string[];
  status: string;
  defaultApp?: AppInfo;
};

export type DingLoginParams = {
  authCode: string; // 授权码
  state: string; // state值
};

export type DingConfig = {
  tenantId: string;
  clientId: string;
  clientSecret: string;
  redirectUri: string;
};

export type QywxConfig = {
  tenant_id: string;
  appid: string;
  agentid: string;
  redirectUri: string;
  secrect: string;
};

export type WxTicketType = {
  _id: string;
  ticket: string;
  unionid: string;
  createTime: Date;
};

export type WxUnBindType = {
  code: number;
};

export type LoginQRCodeParams = {
  ticket: string;
};

export type TocWxQRCodeResultType = {
  id: string;
  ticket: string;
  openid: string;
};
