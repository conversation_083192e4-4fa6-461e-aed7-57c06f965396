import { RequestPageParams } from '@/types';

export enum SubDeptType {
  Stage = 1,
  Grade = 2,
  Class = 3
}

export interface Department {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  parentId: string;
  deptValue: number;
  deptName: string;
  subDeptType: SubDeptType;
  sort: number;
  lastUpgradeTime: string;
  lastSnapTime: string;
  graduateStatus: number;
  children: Department[];
}

export type DepartmentTree = Department[];

export type CreateClientSchoolDeptParasm = {
  name: string;
  parentId: string;
};

export type UpdateClientSchoolDeptParams = {
  name: string;
  id: string;
};

export type CityItem = {
  provinceCode: string;
  provinceName: string;
  cityCode: string;
  cityName: string;
  districtCode: string;
  districtName: string;
};

export type CreateClientStudentParams = {
  avatarUrl: string;
  birthday: number;
  clazzId: number;
  code: string;
  enrollmentDate: string;
  experienceIntroduction: string;
  gradeId: number;
  name: string;
  sex: number;
  stageId: number;
  address: string;
  changeCondition: string;
  studentsFamilyRelations: {
    name: string;
    phone: string;
    type: FamilyType;
    workplace?: string;
  }[];
} & CityItem;

export enum FamilyType {
  FATHER = 1,
  MOTHER = 2,
  GUARDIAN = 3
}

export type UpdatelientStudentParams = {
  avatarUrl: string;
  birthday: string;
  clazzId: number;
  code: string;
  enrollmentDate: string;
  experienceIntroduction: string;
  gradeId: number;
  name: string;
  sex: number;
  stageId: number;
  id: string;
  address: string;
  changeCondition: string;
  studentsFamilyRelations: {
    name: string;
    phone: string;
    type: FamilyType;
    workplace?: string;
  }[];
} & CityItem;

export type SystemRegionSelectItem = {
  ancestors: string;
  children: SystemRegionSelectItem[];
  cityCode: string;
  cityName: string;
  code: string;
  districtCode: string;
  districtName: string;
  hasChildren: boolean;
  level: number;
  name: string;
  parentCode: string;
  provinceCode: string;
  provinceName: string;
  remark: string;
  sort: number;
  townCode: string;
  townName: string;
  villageCode: string;
  villageName: string;
  loading?: boolean;
  value?: string;
};

export type SystemRegionSelectType = SystemRegionSelectItem[];

export type ClientStudentPageType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  stageId: string;
  gradeId: string;
  clazzId: string;
  name: string;
  avatarUrl: string;
  code: string;
  sex: number;
  birthday: string;
  enrollmentDate: string;
  status: number;
  experienceIntroduction: string;
  provinceCode: string;
  provinceName: string;
  cityCode: string;
  cityName: string;
  districtCode: string;
  districtName: string;
  address: string;
  gradeName: string;
  clazzName: string;
};

export type ChangeStatusClientStudentParams = {
  id: string;
  status: number;
};

export type ClazzRankListType = {
  indicatorId: string;
  indicatorName: string;
  studentId: string;
  studentName: string;
  subEvaluations: {
    id: string;
    name: string;
    score: number;
  }[];
  sumScore: number;
};

export type DetailClientStudentType = {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  stageId: string;
  gradeId: string;
  clazzId: string;
  name: string;
  avatarUrl: string;
  code: string;
  sex: number;
  birthday: string;
  enrollmentDate: string;
  status: number;
  experienceIntroduction: string;
  provinceCode: string;
  provinceName: string;
  cityCode: string;
  cityName: string;
  districtCode: string;
  districtName: string;
  address: string;
  gradeName: string;
  clazzName: string;
  changeCondition: string;
  studentsFamilyRelations: {
    name: string;
    phone: string;
    type: number;
    workplace?: string;
  }[];
};

export type ChangeClazzClientStudentParams = {
  clazzId: '';
  gradeId: '';
  id: '';
};

export type ClientStudentPageQuery = {
  name?: string;
  stageId?: string;
  gradeId?: string;
  clazzId?: string;
} & RequestPageParams;
