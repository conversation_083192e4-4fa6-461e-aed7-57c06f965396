export interface RankDivisionTypeParams {
  year: string;
  term: number;
  teacherType: number;
  gradeId: string;
  gradeName: string;
  subjectNames: string[];
  examType: string;
  examId: string;
}

interface RankSection {
  rank: number;
  studentNumber: number;
}

interface ClassRankInfo {
  index: number;
  className: string;
  rankSectionList: RankSection[];
}

export type RankDivisionType = ClassRankInfo[];

export interface QueryScoreLevel {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  type: string;
  level: string;
  scoreMin: number;
  scoreMax: number;
  sort: number;
}

export type QueryScoreLevelType = QueryScoreLevel[];

export type LevelType = 'A等' | 'B等' | 'C等' | 'D等' | 'E等';

export interface ScoreLevel {
  level?: LevelType;
  scoreMin: number;
  scoreMax: number;
  sort?: number; // 可选
}

export interface SetScoreLevelParams {
  // 暂时写死为 1
  type: number;
  scoreLevelList: ScoreLevel[];
}

export interface SetScoreLevelType {
  code: number;
  success: boolean;
  data: boolean;
  msg: string;
}

export interface LevelDivisionRatio {
  sort: number;
  level: LevelType;
  ratio: number;
}

export interface ClassLevelDivision {
  index: number;
  className: string;
  levelDivisionRatios: LevelDivisionRatio[];
}

export type LevelDivisionType = ClassLevelDivision[];

export interface LevelDivisionParams {
  year: string;
  term: number;
  teacherType: number;
  gradeId: number;
  gradeName: string;
  subjectNames: string[];
  examType: string;
  examId: string;
}

export interface WeakSubjectAnalysisParams {
  year: string;
  term: number;
  semesterId: number;
  teacherType: number;
  gradeId: number;
  gradeName: string;
  examType: string;
  examId: string;
  subjectNames: string[];
  className: string;
  /**倍数 */
  multiple: number;
  /**差数 */
  difference: number;
}
export interface WeakSubjectAnalysis {
  className: string;
  userName: string;
  subjectName: string;
  score: number;
  teacherName: string;
}

export type WeakSubjectAnalysisType = WeakSubjectAnalysis[];
