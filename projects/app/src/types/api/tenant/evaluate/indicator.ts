import {
  IndicatorType,
  EvaluationType,
  HasSubIndicator,
  NeedSignature,
  UseLogo,
  Status,
  EducationStage,
  ScoreInputType,
  ScoreLevelStatus,
  BizType
} from '@/constants/api/tenant/evaluate/rule';
import { iconFileParams } from './process';
import { RequestPageParams } from '@/types';

// 评价相关类型
export interface Indicator {
  id?: string;
  name?: string;
  sortNo?: number;
  tenantId?: string;
  type: IndicatorType.EvaluationIndicator;
  createTime?: string;
  dimenTypeId?: string;
  evaluationType?: EvaluationType;
  isDeleted?: number;
  hasSub?: HasSubIndicator;
  needSign?: NeedSignature;
  required?: NeedSignature;

  isUseLogo?: UseLogo;
  parentId?: string;
  projectId?: string;
  score?: number;
  scoreInputType?: ScoreInputType;
  scoreLevel?: string;
  scoreLevelId?: string;
  scoreMax?: number;
  scoreMin?: number;
  scoreRate?: number;
  stage?: EducationStage[] | string;
  status?: Status;
  subjectIds?: number[];
  updateTime?: string;
  iconFileKey?: string;
  iconFile?: iconFileParams;
  scoreLevelValueId?: string;
  defaultLevelValue?: string;
}

export interface EvaluaIndactorVO extends Indicator {
  sub?: EvaluaIndactorVO[];
  maxScoreCount?: string;
  hasChildren?: boolean;
  parent?: EvaluaIndactorVO;
}

export interface EvaluaIndactorSortParams {
  id: string;
  sort: number;
}

export interface TenantEvaluateIconAddParams {
  fileKey: string;
}

export interface IconListType {
  fileKey: string;
  id: string;
  fileUrl: string;
}

export interface EvaluaIndactorParams {
  id: string;
  status: number;
}

export interface GetIndactorTreeByProjectParams {
  evaluationType?: number;
  gradeIds?: string[];
  id?: string;
  parentId?: string;
  parentIds?: string[];
  searchKey?: string;
  stage?: EducationStage[] | string;
  stages?: EducationStage[] | string;
  subjectId?: string;
  status?: Status;
  tenantId?: string;
  type?: number;
}

export interface EvaluaIndactorDetailParams {
  id: string;
}

export interface HomeworkEvaluateDeleteParams {
  id: string;
}

export interface HomeworkClassEvaluateDeleteParams {
  id: string;
}

export interface EvaluaViewListNewParams extends RequestPageParams {
  clazzId?: string;
  evaluateeId?: string;
  searchKey?: string;
  evaluatorId?: string;
  term?: number;
  year?: string;
  projectId?: string;
}

export type Indactor = {
  id: string;
  indactorName: string;
  parent?: Indactor;
};

export interface EvaluaViewListNewList {
  clazzId: string;
  evaluationType: EvaluationType;
  evaluatorId: string;
  gradeId: string;
  id: string;
  indactorId: string;
  indactorName: string;
  remark: string;
  score: number;
  scoreLevelId: string;
  scoreLevelValue: string;
  scoreLevelValueId: string;
  studentId: string;
  studentName: string;
  updateTime: string;
  indactor: Indactor;
  evaluatorName: string;
  scoreLevel: string;
  bizType: BizType;
  parentId: string;
  subjectName: string;
  scoreRate: string;
  projectName: string;
  evaluateType?: EvaluationType;
  children?: EvaluaIndactorVO[];
}
