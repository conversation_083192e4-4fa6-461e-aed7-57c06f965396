import { EvaluationEntrance } from '@/constants/evaluate';
import { IndactorTypeEnum, SexEnum } from '@/constants/api/tenant/evaluate/process';
import { RequestPageParams } from '@/types';
import { EvaluateType, PeriodType, EvaluationType } from '@/constants/api/tenant/evaluate/rule';
import { Indicator } from './indicator';
import { EvaluaScoreLevelValue, ScoreLevelDetail } from './score';
import { ProjectIndicatorTreeType } from './rule';
import { FileMetaType } from '../../file';

export type EvaluateSubjectType = {
  id: string;
  name: string;
};

export interface EvaluateIndactorType extends Indicator {
  indactorType: IndactorTypeEnum;
  scoreLevelValues: EvaluaScoreLevelValue[];
  comment?: string;
  signFileKey?: string;
  signFile?: FileMetaType;
  subs?: EvaluateIndactorType[];
  children?: EvaluateIndactorType[];
  hasChildren?: boolean;
  projectName?: string;
  dimensionId?: string;
  name?: string;
  id?: string;
  score?: number;
  text?: string;
  // 前端使用
  scoreLevelValue?: string;
  grade?: string;
}

export interface indactorListType {
  id: string;
  indactorName: string;
  score: number;
}

export type ClazzEvaluateStatType = {
  studentId?: string;
  indactorId?: string;
  indactorName: string;
  iconFile?: iconFileParams;
  score: number;
};
type iconFileParams = {
  fileUrl: string;
};

export type ClazzEvaluateRankListProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  clazzId?: string;
  subjectId?: string;
};

export type ClazzHomeworkRankListProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  clazzId?: string;
  subjectId?: string;
};

export type ClazzHomeworkRankListType = {
  homeworkCount: number;
  maxScoreCount: number;
  studentName: string;
  maxScoreName: string;
};

export type ClazzEvaluateRecordType = {
  id: string;
  indactorId: string;
  indicatorName: string;
  studentId: string;
  studentName: string;
  evaluateType: EvaluationType;
  evaluatorId?: string;
  evaluatorName?: string;
  score?: number;
  scoreLevelId?: string;
  scoreLevelValue?: string;
  scoreLevelValueId?: string;
  remark?: string;
  createTime?: string;
  iconFile?: iconFileParams;
  scores?: scoresListParams[];
  subEvaluations?: subEvaluationsType[];
  sumScore?: number;
  goodScore?: number;
  badScore?: number;
};

export type subEvaluationsType = {
  indicatorName: string;
  sumScore: number;
};

export type ClazzHomeworkPageType = {
  id: string;
  name: string;
  studentName: string;
  score: number;
  subjectName: string;
  updateTime: string;
  scoreLevelValue: string;
  homeworkName: string;
  evaluatorName: string;
};

export type scoresListParams = {
  indactorName: string;
  score: number;
};

export type EvaluateHomeworkType = {
  id: string;
  name: string;
};

export type EvaluateClazzType = {
  id: string;
  deptName: string;
  parentId?: string;
  parentName?: string;
  teacherNames?: string;
  teachers?: { id: string }[];
  studentNum?: number;
  clazzEvaluaStatises?: ClazzEvaluateStatType[];
  homeworkNum?: number;
  homeworkEvaluaStatises?: HomeworkEvaluateStatType[];
  sort?: number;
  permissionType?: number;

  // 以下为前端补充字段
  clazzName: string;
  good?: string;
  bad?: string;
  score?: string;
};

export type EvaluateStudentType = {
  studentId: string;
  studentCode: string;
  studentName: string;
  studentSex?: SexEnum;
  avatarUrl?: string;
  seatId?: string;
  rowNo?: number;
  colNo?: number;
  groupId?: string;
  score?: number;
  clazzEvaluaStatises?: ClazzEvaluateStatType[];
  homeworkEvaluas?: HomeworkEvaluateStatType[];
  evaluationList?: {
    achievement: string;
    evaluationType: EvaluationType;
    score: number;
    scoreLevelValue: string;
    sendWord: string;
    studentId: string;
  }[];
  good?: string;
  bad?: string;
  id?: string;
  name?: string;
};

export type EvaluateStudentListType = {
  clazzId: string;
  colNum: number;
  rowNum: number;
  studentSeatList: EvaluateStudentType[][];
};

export type EvaluateGroupType = {
  id: string;
  groupName: string;
  clazzId?: string;
  gradeId?: string;
  studentNum?: number;
  studentIds?: string[];
  tenantId?: string;
  code?: string;
  evaluaStaties?: ClazzEvaluateStatType[];
  studentList?: EvaluateStudentType[];
  evaluationList?: EvaluateStudentType[];
  // 以下为前端补充字段
  good?: string;
  bad?: string;
  studentList?: EvaluateStudentType[];
};

export type GetEvaluateSubjectListProps = {
  deptId: string;
  ruleId?: string;
  menuId?: string;
  semesterId?: string;
};

export type ClazzStudentPageProps = {
  clazzId?: string;
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  subjectId?: string;
} & RequestPageParams;

export type EvaluationStudentStatisticsProps = {
  clazzId: string;
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  subjectId?: string;
  studentId?: string;
};

export type EvaluationClazzPageProps = {
  clazzId?: string;
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  subjectId?: string;
} & RequestPageParams;

export type EvaluationClazzPageType = {
  evaluatorId: string;
  comment: string;
  evaluatorName: string;
  iconFile: iconFileParams;
  iconFileKey: string;
  id: string;
  indicatorName: string;
  studentNum: number;
  score: number;
  updateTime: string;
  studentName: string;
};

export type EvaluationClazzStatisticsType = {
  indicatorStatistics: StatisticsType[];
  sumIndicatorStatistics: StatisticsType[];
};

export type EvaluationStudentStatisticsType = {
  indicatorStatistics: StatisticsType[];
  sumIndicatorStatistics: StatisticsType[];
};

export type StatisticsType = {
  iconFile?: iconFileParams;
  iconFileKey: string;
  indicatorId: string;
  indicatorName: string;
  parentId: number;
  parentName: string;
  sumScore: number;
  scoreRate: number;
};

export type SubmitSeatsProps = {
  clazzId: string;
  semesterId: string;
  rowNum: number;
  colNum: number;
  clazzSeats?: {
    id?: string;
    studentId: string;
    rowNo: number;
    colNo: number;
  }[];
};

export type GetAllGroupListProps = {
  clazzId: string;
  semesterId: string;
  subjectId?: string;
  ruleId?: string;
  subjectId?: string;
  entrance?: EvaluationEntrance;
  studentList?: EvaluateStudentType[];
};

export type GetGroupListProps = {
  clazzId: string;
  subjectId?: string;
  ruleId?: string;
  semesterId?: string;
  entrance?: EvaluationEntrance;
};

export type SubmitGroupsProps = {
  clazzId: string;
  semesterId: string;
  groups: {
    id?: string;
    groupName: string;
    studentIds?: string[];
  }[];
};

export type GetClazzStudentListByCodeProps = {
  homeworkId?: string;
  clazzId: string;
  subjectId?: string;
  ruleId?: string;
  semesterId?: string;
  entrance?: EvaluationEntrance;
};

export type GetClazzStudentListBySeatProps = {
  clazzId: string;
  subjectId?: string;
  ruleId?: string;
  semesterId?: string;
  entrance?: EvaluationEntrance;
  homeworkId?: string;
};

export type GetClazzStudentListByGroupProps = {
  clazzId: string;
  groupIds: string[];
  ruleId?: string;
  semesterId?: string;
  id?: string;
  entrance?: EvaluationEntrance;
};

export type GetClazzIndactorListProps = {
  ruleId: string;
  semesterId?: string;
  menuId?: string;
  evaluatedId?: string;
  clazzId?: string;
  deptId?: string;
};

export type EvaluationStudentPageProps = {
  studentId?: string;
  subjectId?: string;
  ruleId?: string;
  semesterId?: string;
  menuId?: string;
  clazzId?: string;
} & RequestPageParams;

export type ClazzRankListProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  clazzId?: string;
  subjectId?: string;
};

export type AddClazzEvaluateProps = {
  evaluatedIds?: string[];
  entrance?: EvaluationEntrance;
  clazzId?: string;
  subjectId?: string;
  indactorId?: string;
  evaluateType?: EvaluationType;
  score?: number;
  achievement?: string;
  gradeId?: number;
  homeworkId?: number;
  indicatorId?: number;
  indicatorIds?: number[];
  indicators?: PartialPick<
    EvaluateIndactorType,
    | 'evaluationType'
    | 'dimensionId'
    | 'indicatorId'
    | 'comment'
    | 'signFileKey'
    | 'scoreLevelId'
    | 'scoreLevelValueId'
    | 'scoreLevelValue'
  >[];
  menuId?: string;
  permissionType?: number;
  ruleId?: string;
  score?: number;
  scoreLevelId?: string;
  scoreLevelValue?: string;
  scoreLevelValueId?: string;
  semesterId?: string;
  sendWord?: string;
  signFileKey?: string;
  evaluatedIds?: string[];
  year?: string;
};

export type RemoveClazzEvaluateProps = {
  id?: string;
  ids?: string[];
  menuId?: string;
  semesterId?: string;
};

export type ResetClazzEvaluateProps = {
  evaluatedIds: string[];
  gradeId: string;
  clazzId: string;
  subjectId: string;
  semesterId?: string;
  ruleId?: string;
  enterType?: EvaluationEntrance;
  menuId?: string;
};

export type ClazzHomeworkPageProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  clazzId?: string;
  homeworkId?: string;
  subjectId?: string;
} & RequestPageParams;

export type StudentHomeworkPageProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  clazzId?: string;
  homeworkId?: string;
  subjectId?: string;
  studentId?: string;
} & RequestPageParams;

export type StudentHomeworkPageType = {
  evaluatorId: string;
  name: string;
  studentName: string;
  score: number;
  updateTime: string;
  scoreLevelValue: string;
  evaluatorName: string;
  subjectName: string;
  homeworkName: string;
  evaluationType: EvaluationType;
};

export type GClazzEvaluateRankListProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  clazzId?: string;
  subjectId?: string;
};

export type AddHomeworkProps = {
  gradeId: string;
  clazzId: string;
  subjectId: string;
  assignDate: string;
  name: string;
};

export type UpdateHomeworkTemplateProps = {
  id: string;
  name: string;
  subjectId: string;
};

export type AddHomeworkTemplateProps = {
  name: string;
  subjectId: string;
};

export type UpdateHomeworkProps = {
  id: string;
  gradeId: string;
  clazzId: string;
  subjectId: string;
  assignDate: string;
  name: string;
};

export type RemoveHomeworkProps = {
  id: string;
};

export type GetHomeworkPageProps = {
  clazzId?: string;
  subjectId?: string;
} & RequestPageParams;

export type GetHomeworkListProps = {
  clazzId?: string;
  subjectId?: string;
};

export type GetHomeworkStudentsListByClazzProps = {
  clazzId: string;
  subjectId?: string;
};

export type GetHomeworkStudentListBySeatProps = {
  clazzId: string;
  homeworkId: string;
};

export type AddHomeworkEvaluateProps = {
  clazzId: string;
  subjectId: string;
  homeworkId: string;
  indactorId: string;
  evaluateType: EvaluationType;
  score?: number;
  scoreLevelId?: string;
  scoreLevelValue?: string;
  scoreLevelValueId?: string;
  studentIds: string[];
};

export type RemoveHomeworkEvaluateProps = {
  id?: string;
  ids?: string[];
};

export type ResetHomeworkEvaluateProps = {
  gradeId: string;
  clazzId: string;
  subjectId: string;
  homeworkId: string;
  studentIds: string[];
};

export type GetRuleListByClazzIdProps = {
  semesterId: string;
  menuId: string;
  clazzId: string;
  reflectionId: string;
};

export type GetRuleListProps = {
  semesterId: string;
  menuId: string;
  reflectionId: string;
  sourceType: SourceType;
};

export enum SourceType {
  Instant = 1,
  Period = 2
}

export type RuleListByClazzIdResponse = {
  endTime: string;
  periodType: PeriodType;
  ruleId: number;
  startTime: string;
  name?: string;
};

export type GetCommentDetailProps = {
  indactorId?: string;
  semesterId?: string;
  menuId?: string;
  permissionType?: string;
  ruleId?: string;
  studentId?: string;
  studentIds?: string[];
  clazzId?: string;
};

export type GetStudentLevelListProps = {
  semesterId?: string;
  menuId?: string;
  permissionType?: string;
  ruleId?: string;
  clazzId?: string;
};

export type RankDataTree = {
  evaluationType?: string;
  evaluatorId?: string;
  evaluatorName?: string;
  indicatorId?: string;
  indicatorName?: string;
  maxScoreCount?: number;
  parentId?: string;
  projectId?: string;
  projectName: string;
  scoreLevelValue: string;
  scoreLevelValueId: string;
  scoreMax?: number;
  scoreMin?: number;
  studentId?: string;
  studentName?: string;
  sumScore?: number;
  updateTime?: string;
  children?: ProjectIndicatorTreeType[];
  maxScoreName?: string;
};

export type GetTeacherStatisticsPageProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  tenantId?: string;
  searchKey?: string;
  evaluatedName?: string;
} & RequestPageParams;

export type TeacherStatisticsType = {
  evaluatedId: string;
  evaluatedName: string;
  evaluatorCount: number;
  maxScore: number;
  minScore: number;
  score: number;
  subjectName: string;
};

export type GetTeacherDetailsPageProps = {
  semesterId?: string;
  menuId?: string;
  ruleId?: string;
  tenantId?: string;
  searchKey?: string;
  evaluatorId?: string;
  evaluatedId?: string;
  evaluatorName?: string;
} & RequestPageParams;

export type TeacherDetailsType = {
  evaluatorName: string;
  subjectName: string;
  updateTime: string;
  projects: {
    projectId: string;
    projectName: string;
    subEvaluations?: {
      indicatorName: string;
      scoreLevelValue: string;
      subjectName: string;
      evaluatedId: string;
      evaluatorId: string;
      evaluatorName: string;
    }[];
  }[];
};

export type GetEvaluatedBelongProps = {
  ruleId: string;
  menuId: string;
  semesterId: string;
};

export type EvaluatedBelongType = {
  gradeName?: string;
  clazzName?: string;
  deptId?: string;
  subjectId?: string;
  subjectName?: string;
};

export type GetEvaluatedListProps = {
  deptId?: string;
  menuId: string;
  ruleId: string;
  semesterId: string;
  subjectId?: string;
};

export type EvaluatedListType = {
  tmbId: string;
  userId: string;
  userName: string;
  subjectName: string;
  avatarFile: { fileUrl: string };
  avatar: string;
  status: number;
  evaluationList: {
    evaluationType: EvaluationType;
    score: number;
    scoreLevelValue: string;
    sendWord: string;
  }[];
};

export type GetTeacherIndicatorTreeProps = {
  evaluatedId: string;
  menuId: string;
  ruleId: string;
  semesterId: string;
};

export type TeacherIndactorType = {
  id: string;
  name: string;
  children: TeacherIndactorType[];
};
