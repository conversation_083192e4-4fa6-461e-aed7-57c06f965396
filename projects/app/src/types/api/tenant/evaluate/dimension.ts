import { DimenTypeStatus } from '@/constants/api/tenant/evaluate/rule';

// 维度相关类型
export interface DimenType {
  id?: string;
  name: string;
  status?: DimenTypeStatus;
  tenantId?: string;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  typeId?: string | number;
}

export interface AddDimenTypeParams extends Omit<DimenType, 'id'> {}

export interface DeleteDimenTypeParams {
  id: string;
}

export interface ListDimenTypeParams {
  id?: string;
}

export type UpdateDimenTypeParams = DimenType;

export interface EvaluaDimension {
  id?: string;
  typeId?: string;
  name?: string;
  status?: number;
  tenantId?: string;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
}

export interface EvaluaDimensionCreateReq {
  id: string;
  name: string;
  status: number;
  typeId?: string | number;
}

export interface EvaluaDimensionCreateReqV2 {
  name: string;
  tenantId?: string | number;
  typeId?: string | number;
}
