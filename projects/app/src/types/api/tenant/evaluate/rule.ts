import {
  EvaluateeType,
  MatchType,
  PeriodType,
  RuleStatus,
  Term,
  ScoreLevelStatus,
  EvaluatedWayEnum
} from '@/constants/api/tenant/evaluate/rule';
import { EvaluaIndactorVO, Indicator } from './indicator';
import { RequestPageParams } from '@/types';
import { SubDeptType } from '@/pages/tenant/evaluateItemManage/ruleSetting/components/RulesTab/components/SelectPersonModal';
import { EvaluaProject } from './project';

// 规则相关类型
export interface EvaluationRule {
  createTime?: string;
  endTime?: string;
  evaluateds?: string[];
  evaluateeNum?: number;
  evaluatedType?: EvaluateeType;
  evaluators?: string[];
  evaluatorNum?: number;
  evaluatorType?: EvaluateeType;
  id?: string;
  indicators?: string[];
  isDeleted?: number;
  matchType?: MatchType;
  periodType?: PeriodType;
  name: string;
  ruleStatus?: RuleStatus;
  status?: RuleStatus;
  semesterId?: string;
  startTime?: string;
  tenantId?: string;
  term?: Term;
  updateTime?: string;
  year?: string;
  evaluateeIdsCoustom?: string[];
  evaluatorCount?: number;
  evaluatedCount?: number;
  evaluatedWays?: EvaluatedWayEnum[];
  evaluatedDetails?: {
    deptId: string;
    tmbId: string;
  }[];
  copyId: string; //前端使用

  loading?: boolean; // 是否正在请求前端使用
}

// 评价规则详情接口
export interface EvaluationRuleDetail extends EvaluationRule {
  evaluatedVOs?: EvaluaRuleEvaluatee[];
  evaluatorVOs?: EvaluaRuleEvaluator[];
  indicatorVOs?: Indicator[];
  indactorNum?: number;
  reflectionId?: string;
}

export type UpdateEvaluationRuleParams = {
  endTime?: string;
  id?: string;
  indicators?: string[];
};

// 新增评价规则参数
export type AddEvaluationRuleParams = EvaluationRule;

// 删除评价规则参数
export interface DeleteEvaluationRuleParams {
  id: string;
}

export interface CopyEvaluationRuleParams {
  id: string;
}

// 评价规则详情参数
export type EvaluationRuleDetailParams = DeleteEvaluationRuleParams;

// 评价规则列表请求参数
export interface EvaluationRuleListParams extends RequestPageParams {
  ascs?: string;
  descs?: string;
  searchKey?: string;
  tenantId?: string;
  name?: string;
}

// 评价规则被评价人
export interface EvaluaRuleEvaluatee {
  clazzId?: string;
  createTime?: string;
  id?: string;
  isDeleted?: number;
  ruleId?: string;
  tenantId?: string;
  updateTime?: string;
}

// 评价规则评价人
export interface EvaluaRuleEvaluator {
  createTime?: string;
  evaluatorId?: string;
  evaluatorType?: EvaluateeType;
  id?: string;
  isDeleted?: number;
  ruleId?: string;
  tenantId?: string;
  updateTime?: string;
}

export type SubjectType = {
  subjectId: string;
  subjectName: string;
};

export type TeacherType = {
  username: string;
  teacherName: string;
  id: string;
  subjects: SubjectType[];
  deptId: string;
};

export type DeptTeacherType = EvaluaRuleEvaluator & {
  teacherName: string;
  subDeptType: SubDeptType;
  deptName: string;
  parentId: string;
  children: DeptTeacherType[];
  teachers: TeacherType[];
  id: string;
};

export type DimensionReflectionVO = {
  createTime: string;
  dimensionId: number;
  evaluatedType: string;
  evaluatorType: string;
  id: number;
  isDeleted: number;
  menuId: number;
  entrance?: DimensionReflectionVO[];
  children: DimensionReflectionVO[];
  name: string;
  tenantId: number;
  typeId: number;
  updateTime: string;
  sub?: DimensionReflectionVO[];
};

export type GetEvaluatorIdListParams = {
  evaluateds?: string[];
  reflectionId?: string;
  semesterId?: string;
  evaluatedWays?: EvaluatedWayEnum;
};

export type ProjectIndicatorTreeType = {
  id: string;
  name: string;
  projectName?: string;
  maxScoreCount?: string;
  maxScoreName?: string;
  indicatorList: EvaluaIndactorVO[];
  children?: EvaluaIndactorVO[];
} & EvaluaProject;
