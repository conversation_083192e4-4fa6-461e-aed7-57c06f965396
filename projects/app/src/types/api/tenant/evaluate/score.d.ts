import {
  EvaluateLevelStatus,
  EvaluateLevelType,
  IsHint
} from '@/constants/api/tenant/evaluate/rule';

export interface EvaluaScoreLevel {
  id?: string;
  name: string;
  status?: number;
  createTime?: string;
  updateTime?: string;
  relatedCount?: number;
  isDeleted?: number;
  values?: EvaluaScoreLevelValue[];
}

export interface ScoreLevelDetail {
  id: number;
  name: string;
  status: EvaluateLevelStatus;
  createTime: string;
  updateTime: string;
  bindCount: number;
  type: EvaluateLevelType;
  isDeleted: number;
  values: {
    name: string;
    scoreMin: number;
    scoreMax: number;
    hint: IsHint;
    text: string;
  }[];
}

export interface EvaluaScoreLevelValue {
  id?: string;
  name: string;
  scoreLevelId?: string;
  scoreMax?: number;
  scoreMin?: number;
  isHint?: IsHint;
  sortNo?: number;
  status?: EvaluateLevelStatus;
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
  levelName?: string;
  levelStatus?: string;
  score?: number;
  text?: string;
  type?: SetType;
}

export enum SetType {
  ScoreSetting = 1,
  TextSetting = 2
}

export interface CreateScoreLevelParams {
  name: string;
  status?: number;
  hint?: IsHint;
  values: EvaluaScoreLevelValue[];
}

export interface UpdateScoreLevelParams {
  id: string;
  name: string;
  status?: number;
  values: EvaluaScoreLevelValue[];
}

export interface DeleteScoreLevelParams {
  id: string;
}

export interface GetScoreLevelListParams {}
