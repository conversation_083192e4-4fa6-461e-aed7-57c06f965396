import { ProjectType, HasSubIndicator } from '@/constants/api/tenant/evaluate/rule';

// 项目相关类型
export interface EvaluaProject {
  id?: string;
  name: string;
  indactorNum?: number;
  sortNo?: number;
  isHasSub?: HasSubIndicator;
  parentId?: string;
  projectType?: ProjectType;
  subjectIds?: string[];
  scoreRate?: number;
  subjects: SubjectType[];
  clazzId?: string;
}

export interface EvaluaProjectCreateReq {
  name: string;
  dimensionId: number;
  tenantId?: number;
  scoreRate: number;
  subjectIds: number[];
  type: number | string;
}

export interface EvaluaProjectV2 {
  id: number;
  name: string;
  dimensionId: number;
  tenantId?: number;
  type: number;
  status: number;
  scoreRate: number;
  isDeleted: number;
  createTime: string;
  updateTime: string;
}

export interface SubjectType {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  indactorId: string;
  subjectId: string;
  subjectName: string;
}
