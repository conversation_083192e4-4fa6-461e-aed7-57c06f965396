import { RequestPageParams } from '@/types';
import { Department } from '../teamManagement/teach';
import { TeacherType } from './rule';
import { EvaluaIndactorVO, Indactor } from './indicator';
import { BizType, EvaluationType } from '@/constants/api/tenant/evaluate/rule';

export interface HomeworkEvaluateDeleteParams {
  id: string;
}

export interface EvaluationDeleteParams {
  id: string;
  dimensionId: string;
  semesterId: string;
}

export interface HomeworkClassEvaluateDeleteParams {
  id: string;
}

export interface EvaluaViewListNewParams extends RequestPageParams {
  clazzId?: string;
  evaluateeId?: string;
  searchKey?: string;
  evaluatorId?: string;
  semesterId?: string;
  projectId?: string;
  menuId?: string;
  subjectId?: string;
  dimensionId?: string;
}

export type DeptTeacherType = Department & {
  teachers: TeacherType[];
  children: DeptTeacherType[];
};

export interface EvaluaViewListNewList {
  clazzId: string;
  evaluateType: EvaluationType;
  evaluatorId: string;
  gradeId: string;
  id: string;
  indactorId: string;
  indactorName: string;
  remark: string;
  score: number;
  scoreLevelId: string;
  scoreLevelValue: string;
  scoreLevelValueId: string;
  studentId: string;
  studentName: string;
  updateTime: string;
  indactor: Indactor;
  evaluatorName: string;
  scoreLevel: string;
  bizType: BizType;
  parentId: string;
  subjectName: string;
  children?: EvaluaIndactorVO[];
}

export interface EvaluaIndactorParams {
  id: string;
}

export interface EvaluaIndactorDetailParams {
  id: string;
}

export interface SubjectType {
  id: string;
  createTime: string;
  updateTime: string;
  isDeleted: number;
  tenantId: string;
  indactorId: string;
  subjectId: string;
  subjectName: string;
}
