export type EvaluateSubjectType = {
  id: string;
  name: string;
};

export type GetSubjectStatisticsType = {
  submitStudentNum: string;
  classStudentNum: string;
  standardScore: string;
  maxScore: string;
  minScore: string;
  avgScore: string;
  excellentRate: string;
  qualifiedRate: string;
  lowScoreRate: string;
};

export type GetSubjectSummarizationGraphType = {
  id: number | null; // 唯一标识
  examId: string; // 考试id
  examName: string; // 考试名称
  examTypeCode: string; // 考试类型编码
  examTypeName: string; // 考试类型名称
  schoolId: string; // 学校id
  schoolName: string; // 学校名称
  gradeCode: string; // 年级编码
  gradeName: string; // 年级名称
  classId: string; // 班级id
  className: string; // 班级名称
  subjectCode: string; // 学科编码
  subjectName: string; // 学科名称
  examTime: string; // 考试时间
  maxScore: number | null; // 班级最高分
  minScore: number | null; // 班级最低分
  avgScore: number; // 平均分
  standardDevivation: number | null; // 标准差
  classStudentNum: number | null; // 总人数
  submitStudentNum: number; // 实考人数
  reportVersion: string; // 报告版本
  standardScore: number; // 标准分数
  excellentNum: number | null; // 优秀人数
  excellentRate: number | null; // 优秀占比
  goodNum: number | null; // 良好人数
  goodRate: number | null; // 良好占比
  qualifiedNum: number | null; // 合格人数
  qualifiedRate: number | null; // 合格占比
  unqualifiedNum: number | null; // 不合格人数
  unqualifiedRate: number | null; // 不合格占比
  passNum: number | null; // 及格人数
  unpassMinScore: number | null; // 不及格最低分
  unpassMaxScore: number | null; // 不及格最高分
  excellentMinScore: number | null; // 优秀最低分
  excellentMaxScore: number | null; // 优秀最高分
  excellentScoreRate: number | null; // 优秀分值比例
  lowMinScore: number | null; // 低分最低分
  lowMaxScore: number | null; // 低分最高分
  lowScoreRate: number | null; // 低分分值比例
  passMinScore: number | null; // 及格最低分
  passMaxScore: number | null; // 及格最高分
  passScoreRate: number | null; // 及格分值比例
  goodMinScore: number | null; // 良好最低分
  goodMaxScore: number | null; // 良好最高分
  goodScoreRate: number | null; // 良好分值比例
  index: number | null; // 班级序号
  absentStudentNum: number | null; // 缺考人数
  teacherName: string; // 教师名称
  rank: string; // 名次
  overAverage: string; // 超均率
};

export type GetSubjectSummarizationGraphProps = {
  year: string;
  term: number;
  startDate: string;
  endDate: string;
  gradeName: string;
  gradeId: string;
  subjectNames: string[];
  examType?: string;
  examId?: string;
  teacherType?: number;
};
// 定义 Dept 类型
export interface Dept {
  id: string; // 节点的唯一标识符
  parentId: string; // 父节点的标识符
  hasChildren: boolean; // 是否有子节点
  name: string; // 节点名称
  tenantId: string; // 租户ID
  children?: Dept[]; // 子节点数组
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
  isDeleted?: number; // 是否删除
  sort?: number; // 排序
  parentName?: string; // 父节点名称
}

export type GetEvaluateSubjectListProps = {
  deptId: string;
};

export type GetSubjectSummarizationTableType = {
  className: string;
  teacherName: string;
  classStudentNum: number;
  submitStudentNum: number;
  fullScore: number;
  maxScore: number;
  minScore: number;
  avgScore: number;
  rank: number;
  excellentRate: number;
  qualifiedRate: number;
  lowScoreRate: number;
  aboveAvgRate: number;
};
export type GetGradeListType = {
  gradeName: string;
  gradeId: string;
};

export type GetSubjectListType = {
  subjectName: string;
  subjectId: string;
};

export type ReportType = {
  examId: string;
  examName: string;
  examTime: string;
  lable: string;
  vlaue: string;
};

export type ExamType = {
  examTypeName: string;
  value: string;
  reportList: ReportType[];
};

export type TestQualityAnalysisType = ExamType[];

export type TestQualityAnalysisData = {
  year: string;
  term: number;
  semesterId: string;
  startDate: string;
  endDate: string;
};

export type GetTeacherType = string;

export type GetConclusionProps = {
  examId: string;
  gradeId: string;
  gradeName: string;
  subjectNames: string[];
};

export type GetSubjectListProps = {
  year: string;
  term: number;
  semesterId: string;
  teacherType: number;
  gradeId: string;
  gradeName: string;
  examType: string;
  examId: string;
};

export type SaveOrUpdateConclusionProps = {
  id: string;
  examId: string;
  teacherType: number;
  gradeId: string;
  gradeName: string;
  subjectNames: string[];
  location: number; // 数据汇总为1，其他先待定
  conclusion: string; // 结论内容放这里
};

export type GetConclusionType = {
  id: string;
  location: number;
  conclusion: string;
};

export type GetSubjectLevelProps = {
  ascs?: string;
  current?: number;
  descs?: string;
  endDate?: string;
  examId?: string;
  examIds?: string[];
  examTime?: string;
  examType?: string;
  gradeId?: number;
  gradeName?: string;
  gradeNames?: string[];
  searchKey?: string;
  semesterId: number;
  size?: number;
  startDate?: string;
  subjectNames: string[];
  teacherType?: number;
  tenantId?: number;
  term: number;
  tmbId?: number;
  year: string;
};

export interface SubjectLevelItem {
  evaluation: string;
  ratio: number;
  studentNumber: number;
}

export interface SubjectLevelResponse {
  className: string;
  index: number;
  levelList: SubjectLevelItem[];
  subjectName: string;
}

// 班级分数对比
export interface ClassGradeInfo {
  absentStudentNum: number;
  avgScore: number;
  classId: string;
  className: string;
  classStudentNum: number;
  examId: string;
  examName: string;
  examTime: string;
  examTypeCode: string;
  examTypeName: string;
  excellentMaxScore: number;
  excellentMinScore: number;
  excellentNum: number;
  excellentRate: number;
  excellentScoreRate: number;
  goodMaxScore: number;
  goodMinScore: number;
  goodNum: number;
  goodRate: number;
  goodScoreRate: number;
  gradeCode: string;
  gradeName: string;
  id: number;
  index: number;
  lowMaxScore: number;
  lowMinScore: number;
  lowScoreRate: number;
  maxScore: number;
  minScore: number;
  overAverage: string;
  passMaxScore: number;
  passMinScore: number;
  passNum: number;
  passScoreRate: number;
  qualifiedNum: number;
  qualifiedRate: number;
  rank: string;
  rawAverage: number;
  reportVersion: string;
  schoolId: string;
  schoolName: string;
  standardDevivation: number;
  standardScore: number;
  subjectCode: string;
  subjectName: string;
  submitStudentNum: number;
  teacherName: string;
  unpassMaxScore: number;
  unpassMinScore: number;
  unqualifiedNum: number;
  unqualifiedRate: number;
}

export interface GradeComparisonResponse {
  classGradeInfo: ClassGradeInfo[];
  gradeAverage: number;
  targetScore: number | null;
}

export interface GetRecentReportProps {
  year: string;
  term: number;
  teacherType: number;
  semesterId: number;
  gradeId: number;
  gradeName: string;
  subjectNames: string[];
  examType: string;
  examId: string;
  examTime: string;
  startDate: string;
  endDate: string;
}

export interface RecentReportResponse {
  examTypeName: string;
  reportList: {
    avgScore: number;
    classId: string;
    className: string;
    classStudentNum: number;
    examId: string;
    examName: string;
    examTime: string;
    examTypeCode: string;
    examTypeName: string;
    excellentMaxScore: number;
    excellentMinScore: number;
    excellentNum: number;
    excellentRate: number;
    excellentScoreRate: number;
    goodMaxScore: number;
    goodMinScore: number;
    goodNum: number;
    goodRate: number;
    goodScoreRate: number;
    gradeCode: string;
    gradeName: string;
    id: number;
    lowMaxScore: number;
    lowMinScore: number;
    lowScoreRate: number;
    maxScore: number;
    minScore: number;
    passMaxScore: number;
    passMinScore: number;
    passNum: number;
    passScoreRate: number;
    qualifiedNum: number;
    qualifiedRate: number;
    reportVersion: string;
    schoolId: string;
    schoolName: string;
    standardDevivation: number;
    standardScore: number;
    subjectCode: string;
    subjectName: string;
    submitStudentNum: number;
    unpassMaxScore: number;
    unpassMinScore: number;
    unqualifiedNum: number;
    unqualifiedRate: number;
  }[];
}

export interface CreatePPTContentItem {
  title: string;
  subTitle?: string;
  imageUrl: string;
  conclusion: string;
}

export interface CreatePPTParams {
  year: string;
  grade: string;
  testName: string;
  subject: string;
  content: CreatePPTContentItem[];
  problem: string;
  measure: string;
}
export interface GetRankDivisionExcelProps {
  year: string;
  grade: string;
  testName: string;
  subject: string;
}
export type CreatePPTResponse = boolean;
