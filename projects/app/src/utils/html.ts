const applyComputedStyles = (nodes: Node[]) => {
  nodes.forEach((node) => {
    if (node instanceof HTMLElement) {
      Object.assign(node.style, window.getComputedStyle(node));
    }
    if (node.childNodes?.length) {
      applyComputedStyles(Array.from(node.childNodes));
    }
  });
};

export const cloneWithStyles = <T extends Node>(node: T) => {
  const clone = node.cloneNode(true) as T;
  applyComputedStyles([clone]);
  return clone;
};
