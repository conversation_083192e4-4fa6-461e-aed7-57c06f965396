export const ImageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

export const VideoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'];

export const AudioExtensions = ['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a', '.wma'];

export const isTheTypeFile = (file: string | File, extensions: string[], types: string[]) =>
  typeof file === 'string'
    ? extensions.includes(file.substring(file.lastIndexOf('.')).toLowerCase())
    : types.some((type) => file.type.startsWith(type));

export const isImageFile = (file: string | File) => isTheTypeFile(file, ImageExtensions, ['image']);

export const isVideoFile = (file: string | File) => isTheTypeFile(file, VideoExtensions, ['video']);

export const isAudioFile = (file: string | File) => isTheTypeFile(file, AudioExtensions, ['audio']);
