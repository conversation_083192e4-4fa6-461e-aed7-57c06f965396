import { PagingData, RequestPageParams } from '@/types';
import { replaceSensitiveText } from './tools';

export const getListFromPage = async <QueryType extends RequestPageParams, DataType>(
  api: (query: QueryType) => Promise<PagingData<DataType>>,
  query?: Omit<QueryType, keyof RequestPageParams>,
  size = 100
) => {
  let current = 1;
  let total = -1;
  const list: DataType[] = [];
  const params = { ...query, current, size };
  while (true) {
    const res = await api(params as QueryType);
    if (total < 0) {
      total = res.total || 0;
    } else if (total !== res.total) {
      params.current = 1;
      total = -1;
      list.length = 0;
      continue;
    }
    if (!res.records.length) {
      break;
    }
    list.push(...res.records);
    if (list.length >= total) {
      break;
    }
    params.current++;
  }
  return list;
};

export const getErrText = (err: any, def = ''): any => {
  const msg: string =
    typeof err === 'string'
      ? err
      : err?.response?.data?.message || err?.response?.message || err?.message || def;
  msg && console.log('error =>', msg);
  return replaceSensitiveText(msg);
};
