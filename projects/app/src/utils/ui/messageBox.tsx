import { Box } from '@chakra-ui/react';
import { Modal, ModalFuncProps } from 'antd';

type Options = ModalFuncProps & { tip?: string };

const defaultOptions: Options = {
  centered: true
};

const defaultDeleteOptions: Options = {
  title: '删除提示',
  okCancel: true,
  okText: '确定删除'
};

const handleOptions = (options: Options) => {
  if (!options.tip) {
    return { ...defaultOptions, ...options };
  }
  return {
    ...defaultOptions,
    title: options.title,
    content: (
      <Box>
        <Box>{options.content}</Box>
        <Box mt="10px" fontSize="12px">
          {options.tip}
        </Box>
      </Box>
    ),
    ...options
  };
};

export const MessageBox = {
  info: (options: Options) => Modal.info(handleOptions(options)),

  success: (options: Options) => Modal.success(handleOptions(options)),

  error: (options: Options) => Modal.error(handleOptions(options)),

  warning: (options: Options) => Modal.warning(handleOptions(options)),

  confirm: (options: Options) => Modal.confirm(handleOptions(options)),

  delete: (options: Options) =>
    Modal.warning(handleOptions({ ...defaultDeleteOptions, ...options }))
};

export const promisifyConfirm = (options: Options) => {
  return new Promise((resolve, reject) => {
    MessageBox.confirm({
      ...options,
      onOk: () => resolve(true),
      onCancel: () => reject(false)
    });
  });
};

export const promisifyDelete = (options: Options) => {
  return new Promise((resolve, reject) => {
    MessageBox.delete({
      ...options,
      onOk: () => resolve(true),
      onCancel: () => reject(false)
    });
  });
};

export const promisifyWarning = (options: Options) => {
  return new Promise((resolve, reject) => {
    MessageBox.warning({
      ...options,
      onOk: () => resolve(true),
      onCancel: () => reject(false)
    });
  });
};
