import { ToastPosition, UseToastOptions, createStandaloneToast } from '@chakra-ui/react';

type Options = Omit<UseToastOptions, 'status'>;

const defaultOptions = {
  position: 'top' as ToastPosition
};

const { toast } = createStandaloneToast();

const showToast = (options: Options | string, status: UseToastOptions['status']) =>
  toast({
    ...defaultOptions,
    ...(typeof options === 'string' ? { title: options } : options),
    status
  });

export const Toast = {
  info: (options: Options | string) => showToast(options, 'info'),
  success: (options: Options | string) => showToast(options, 'success'),
  error: (options: Options | string) => showToast(options, 'error'),
  warning: (options: Options | string) => showToast(options, 'warning'),
  loading: (options: Options | string) => showToast(options, 'loading'),
  isActive: (id: string) => toast.isActive(id),
  close: (id: string) => toast.close(id),
  closeAll: () => toast.closeAll()
};
