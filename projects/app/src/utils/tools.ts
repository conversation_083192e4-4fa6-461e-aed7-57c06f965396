import dayjs from 'dayjs';
import { customAlphabet } from 'nanoid';
import { decodeBase64, encodeBase64 } from './string';

export const serializeData = (data: Record<string, any>): string => {
  return data ? encodeBase64(JSON.stringify(data)) : '';
};

export const deserializeData = (str: string): Record<string, any> => {
  try {
    if (str) {
      const parsedData = JSON.parse(decodeBase64(str));
      if (typeof parsedData === 'object') {
        return parsedData;
      }
    }
  } catch {}
  return {};
};

/**
 * 格式化时间成聊天格式
 */
export const formatTimeToChatTime = (time: Date) => {
  const now = dayjs();
  const target = dayjs(time);

  // 如果传入时间小于60秒，返回刚刚
  if (now.diff(target, 'second') < 60) {
    return '刚刚';
  }

  // 如果时间是今天，展示几时:几秒
  if (now.isSame(target, 'day')) {
    return target.format('HH:mm');
  }

  // 如果是昨天，展示昨天
  if (now.subtract(1, 'day').isSame(target, 'day')) {
    return '昨天';
  }

  // 如果是前天，展示前天
  if (now.subtract(2, 'day').isSame(target, 'day')) {
    return '前天';
  }

  // 如果是今年，展示某月某日
  if (now.isSame(target, 'year')) {
    return target.format('M月D日');
  }

  // 如果是更久之前，展示某年某月某日
  return target.format('YYYY/M/D');
};

export const formatFileSize = (bytes: number, withoutB?: boolean): string => {
  if (bytes === 0) return '0B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + (withoutB ? sizes[i][0] : sizes[i]);
};

export function myToFixed(value: number, decimals: number): number {
  if (isNaN(value) || isNaN(decimals)) {
    throw new Error('Invalid input: value and decimals must be numbers');
  }
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
}

export const delay = (ms: number) =>
  new Promise((resolve) => {
    setTimeout(() => {
      resolve('');
    }, ms);
  });

export const getNanoid = (size = 12) => {
  const firstChar = customAlphabet('abcdefghijklmnopqrstuvwxyz', 1)();

  if (size === 1) return firstChar;

  const randomsStr = customAlphabet(
    'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
    size - 1
  )();

  return `${firstChar}${randomsStr}`;
};
export const i18nT = (key: any) => key;

export const replaceSensitiveText = (text: string) => {
  // 1. http link
  text = text.replace(/(?<=https?:\/\/)[^\s]+/g, 'xxx');
  // 2. nx-xxx 全部替换成xxx
  text = text.replace(/ns-[\w-]+/g, 'xxx');

  return text;
};
