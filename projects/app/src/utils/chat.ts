import { ChatCompletionMessageParam } from '@/fastgpt/global/core/ai/type';
import { chats2GPTMessages } from '@/fastgpt/global/core/chat/adapt';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum
} from '@/fastgpt/global/core/chat/constants';
import { ChatSiteItemType, UserChatItemValueItemType } from '@/fastgpt/global/core/chat/type';
import { nanoid } from 'nanoid';

export const getChatMessages = ({
  files = [],
  text = ''
}: {
  files?: {
    fileKey: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    fileUrl: string;
  }[];
  text?: string;
}): {
  messages: ChatCompletionMessageParam[];
  newChatList: ChatSiteItemType[];
  aiDataId: string;
} => {
  const humanDataId = nanoid();
  const aiDataId = nanoid();

  let newChatList: ChatSiteItemType[] = [
    {
      dataId: humanDataId,
      obj: ChatRoleEnum.Human,
      isShareContent: false,
      value: [
        ...(files
          ?.filter((file) => file.fileType == ChatFileTypeEnum.file)
          .map((file) => ({
            type: ChatItemValueTypeEnum.file,
            file: {
              type: ChatFileTypeEnum.file,
              name: file.fileName || '',
              url: file.fileUrl!.includes('?filename=')
                ? file.fileUrl
                : `${file.fileUrl}?filename=${file.fileName}`,
              fileId: file.fileKey || ''
            }
          })) || []),
        ...(files
          ?.filter((file) => file.fileType == ChatFileTypeEnum.image)
          .map((image) => ({
            type: ChatItemValueTypeEnum.file,
            file: {
              type: ChatFileTypeEnum.image,
              url: image.fileUrl!.includes('?filename=')
                ? image.fileUrl
                : `${image.fileUrl}?filename=${image.fileType?.replaceAll(' ', '')}`
            }
          })) || []),

        ...(text
          ? [
              {
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: text
                }
              }
            ]
          : [])
      ] as UserChatItemValueItemType[],
      status: 'finish'
    },
    {
      dataId: aiDataId,
      obj: ChatRoleEnum.AI,
      isShareContent: false,
      value: [
        {
          type: ChatItemValueTypeEnum.text,
          text: {
            content: ''
          }
        }
      ],
      status: 'loading'
    }
  ];

  const messages = chats2GPTMessages({ messages: newChatList, reserveId: true });
  return { messages, newChatList, aiDataId };
};
