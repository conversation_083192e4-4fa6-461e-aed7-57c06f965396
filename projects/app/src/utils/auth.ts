import { loginOut } from '@/api/auth';

const tokenKey = 'token';
export const clearToken = async () => {
  try {
    if (localStorage.getItem(tokenKey)) {
      return loginOut().finally(() => {
        localStorage.removeItem(tokenKey);
      });
    }
  } catch (error) {
    error;
  }
};

export const setToken = (token: string) => {
  if (typeof window === 'undefined') return '';
  localStorage.setItem(tokenKey, token);
};

export const getToken = () => {
  if (typeof window === 'undefined') return '';
  return localStorage.getItem(tokenKey) || '';
};
