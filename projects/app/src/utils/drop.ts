export type DropFileType = {
  rawFile: File;
  folder: string;
};

function traverseDropFileEntry(entry: any, folder: string): Promise<DropFileType[]> {
  if (entry.isFile) {
    return new Promise((resolve) => {
      entry.file((rawFile: File) => {
        resolve([{ rawFile, folder }]);
      });
    });
  } else if (entry.isDirectory) {
    return traverseDropDirectory(entry, folder ? `${folder}/${entry.name}` : entry.name);
  }
  return Promise.resolve([]);
}

function traverseDropDirectory(entry: any, folder: string): Promise<DropFileType[]> {
  const reader = entry.createReader();
  return new Promise((resolve, reject) => {
    let allFiles: any = [];
    const readEntries = () => {
      reader.readEntries(
        (entries: any) => {
          if (entries.length) {
            Promise.all(Array.from(entries).map((it) => traverseDropFileEntry(it, folder)))
              .then((files) => {
                allFiles = allFiles.concat(files.flat());
              })
              .finally(() => {
                readEntries();
              });
          } else {
            resolve(allFiles.flat());
          }
        },
        () => {
          resolve(allFiles.flat());
        }
      );
    };
    readEntries();
  });
}

export function getDropFiles(files: DataTransferItem[]) {
  return Promise.all(
    files.map((it: any) => {
      if (it.kind === 'file') {
        const entry = it.getAsEntry ? it.getAsEntry() : it.webkitGetAsEntry();
        if (entry) {
          return traverseDropFileEntry(entry, '');
        }
      }
      return Promise.resolve([]);
    })
  ).then((files) => files.flat());
}
